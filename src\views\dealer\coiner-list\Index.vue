<template>
  <div class="app-container flex">
    <header>
      <header-bar
        :title="$t('dealer_coin.coin_dealer_coiner_list')"
        closeType="close"
      >
        <div class="icon-wrap">
          <img
            src="@/assets/images/dealer-coin/rule.png"
            alt=""
            @click="jumpRule"
          />
        </div>
      </header-bar>
    </header>
    <main>
      <van-list
        v-model:loading="isLoading"
        :finished="finished"
        finished-text=""
        loading-text="loading..."
        :immediate-check="true"
        @load="fetchList"
      >
        <template v-if="list.length > 0">
          <div class="card flex" v-for="(item, idx) in list" :key="idx">
            <div class="avatar-warp">
              <img :src="item.user_info?.avatar" alt="" />
            </div>
            <gap :gap="8"></gap>
            <div class="content">
              <div class="name ellipsis">{{ item.user_info?.nickname }}</div>
              <div class="phone-wrap flex">
                <img v-if="item?.phone" src="@/assets/images/dealer-coin/phone.png" alt="" />
                <gap :gap="4"></gap>
                <span>{{ item?.phone }}</span>
              </div>
            </div>
            <div class="btn" @click="jumpContact(item)">
              {{ $t("dealer_coin.coin_dealer_contact") }}
            </div>
          </div>
        </template>
        <Empty
          v-else
          class="empty"
          :tips="$t('common.common_content_yet')"
        ></Empty>
      </van-list>
    </main>
  </div>
</template>

<script setup>
import { ref, unref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import dealerApi from "@/api/dealer.js";
import { useRouter } from "vue-router";

const { proxy } = getCurrentInstance();

const t = i18n.global.t;
const router = useRouter();
const page = ref(1);
const size = ref(20);
const isLoading = ref(false);
const finished = ref(false);
const list = ref([]);

const fetchList = async () => {
  try {
    isLoading.value = true;
    const resResult = await dealerApi.merchant_list({
      page: page.value,
      size: size.value,
    });
    if (resResult.code === 200) {
      list.value = unref(list).concat(resResult.data.list);
      isLoading.value = false;
      page.value += 1;
      if (resResult.data.list.length < unref(size)) finished.value = true;
    }
  } catch (error) {
    isLoading.value = false;
    finished.value = true;
  }
};

const jumpRule = () => {
  router.push({ name: "CoinerRule" });
};

const jumpContact = (row) => {
  proxy.$siyaApp("openSiyaUrl", {
    url: `siya://siya.com/app?method=goChat&chatId=${row.user_info?.user_id}&source=0`,
  });
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: $white;
  flex-direction: column;
  flex-wrap: nowrap;
  height: 100vh;
  header {
    width: 100%;
    height: 100px;
    .icon-wrap {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
      padding: 0 px2rem(16);
      img {
        width: px2rem(28);
        height: px2rem(28);
      }
    }
  }
  main {
    width: 100%;
    flex: 1;
    min-height: 0;
    padding: 0 px2rem(16);
    overflow: scroll;
    min-height: 0;
    .card {
      justify-content: space-between;
      padding: px2rem(16) px2rem(10);
      .avatar-warp {
        height: px2rem(48);
        img {
          width: px2rem(48);
          height: px2rem(48);
          border-radius: 50%;
        }
      }
      .content {
        flex: 1;
        .name {
          font-size: $fontSize15;
          color: $fontColorB1;
          max-width: calc(100vw - px2rem(190));
          margin-bottom: px2rem(4);
        }
        .phone-wrap {
          font-size: $fontSize11;
          color: $fontColorB3;
          img {
            width: px2rem(12);
            height: px2rem(12);
          }
        }
      }
      .btn {
        font-size: $fontSize13;
        color: #5f56ed;
        background: #f8f7ff;
        height: px2rem(36);
        width: px2rem(76);
        @include center();
        border-radius: $radius10;
      }
    }
  }
}
</style>
