<template>
  <div class="sub-box">
    <slot></slot>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.sub-box {
  align-self: stretch;
  border-radius: px2rem(24);
  margin: 0 px2rem(12);
  margin-top: px2rem(16);
  position: relative;
  background: linear-gradient(180deg, #ffffff 0%, #fff2d9 100%);

  &::after {
    content: "";
    position: absolute;
    inset: px2rem(2);
    background: #fffdfc;
    border-radius: px2rem(22);
    backdrop-filter: blur(px2rem(8));
  }
  &::before {
    content: "";
    position: absolute;
    z-index: 2;
    width: px2rem(362);
    height: px2rem(72);
    border-radius: px2rem(22);
    margin: px2rem(2);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, #ffebcc 100%);
  }
}
</style>
