<template>
  <div class="app-container flex" v-if="visible">
    <header class="flex">
      <div class="header-left flex">
        <img src="@/assets/images/invite/logo.png" alt="" class="logo" />
        <gap :gap="8"></gap>
        <div class="inner">
          <img src="@/assets/images/invite/siya.png" alt="" class="siya" />
          <span>{{ $t("invite.link_user_rewards") }}</span>
        </div>
      </div>
      <div class="header-btn" @click="handleDownLoad">
        {{ $t("common.common_download") }}
      </div>
    </header>
    <div class="invite-wrap flex" id="observe-el">
      <img :src="linkData.avatar" alt="" class="avatar" />
      <gap :gap="8"></gap>
      <div
        class="invite-right flex"
        :class="[currentLang === 'ar' ? 'radius-right ' : 'radius-left']"
      >
        <div class="content">{{ $t("invite.link_invite_desc") }}</div>
        <div class="btn" @click="handleDownLoad">
          {{ $t("common.common_download") }}
        </div>
        <div class="invite-inner flex" @click="handleCopy">
          {{ $t("invite.link_invite_code") }} {{ inviteCode }}
          <img
            src="@/assets/images/invite/icon-coyp.png"
            alt=""
            class="copy-icon"
          />
        </div>
      </div>
      <div class="adorn-box flex">
        <img
          src="@/assets/images/invite/adorn.png"
          alt=""
          :class="{ 'ar-img': currentLang === 'ar' }"
        />
      </div>
    </div>
    <div class="video-wrap">
      <video
        :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/invite/${languageFileMap[currentLang]}/female.mp4`"
        loop
        poster="@/assets/images/invite/poster.png"
        preload
        controls
        id="video"
        playsinline
        autoplay
        :muted="isMuted"
      ></video>
    </div>
    <div class="section-wrap chat-wrap">
      <img src="@/assets/images/invite/adorn.png" alt="" class="adorn" />
      <div class="title">
        {{ $t("invite.link_chat_earn_money") }}
      </div>
      <div class="inner-content">
        <div class="bg-content">
          <img
            :src="getImageUrl('chat.png')"
            alt=""
            :draggable="false"
            class="chat-img"
          />
          <div
            class="human-wrap"
            :class="{ 'ar-human-wrap': currentLang === 'ar' }"
          >
            <div class="reward-box">
              <img
                class="diamond-icon"
                src="@/assets/images/common/diamond.png"
                alt=""
              />
              <span class="text">+72RP</span>
              <span>(72{{ $t("common.common_diamonds") }} )</span>
            </div>
            <img src="@/assets/images/invite/human.png" alt="" class="human" />
          </div>
        </div>
      </div>
    </div>
    <div class="section-wrap withdraw-wrap">
      <img src="@/assets/images/invite/adorn.png" alt="" class="adorn" />
      <div class="title">
        {{ $t("invite.link_sign_earn_money") }}
      </div>
      <div class="inner-content flex">
        <div class="withdraw-box">
          <div
            class="item flex"
            v-for="(item, idx) in linkData.bonus_exchange_rate"
            :key="idx"
          >
            <span>{{ item.local_total_amount }} {{ linkData.currency }}</span>
            <div class="item-right flex">
              <img src="@/assets/images/common/diamond.png" alt="" />
              <gap :gap="2"></gap>
              <span>{{ item.point }}</span>
            </div>
          </div>
        </div>
        <gap :gap="8"></gap>
        <div class="withdraw-right">
          <span>100 {{ linkData.currency }}</span>
          <img
            src="@/assets/images/invite/withdraw.png"
            alt=""
            class="withdraw-img"
          />
        </div>
      </div>
    </div>
    <footer v-if="isShowDownloadButton">
      <div class="download-btn" @click="handleDownLoad">
        <span> {{ $t("common.common_download") }}</span>
        <gap :gap="4"></gap>
        <svg-icon class="download-icon" icon="download" color="#7c0001" />
      </div>
    </footer>
  </div>
</template>

<script setup>
import { i18n, resetDocumentRtl, languageFileMap } from "@/i18n/index.js";
import inviteApi from "@/api/invite.js";
import clipboard from "@/utils/clipboard";
import { isAndroid } from "@/utils/util.js";
import { nextTick, ref } from "vue";

const t = i18n.global.t;
let that = null;
let appKey = "";
const visible = ref(false);
const isMuted = ref(true);
const isShowDownloadButton = ref(false);
const currentLang = ref("en");

const linkData = ref([]);
const inviteCode = ref("");

const getUrlChannelCode = () => {
  const urlParams = new URLSearchParams(window.location.search);
  // const channelCode = urlParams.get("channelCode");
  const channelCode = urlParams.get("t").substring(7);
  return channelCode;
};

const getUrlHash = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const hash = urlParams.get("t").substring(1, 7);
  return hash;
};

const loadScript = () => {
  return new Promise((resolve, reject) => {
    if (!appKey) reject("APPKEY is null");
    const script = document.createElement("script");
    script.src = `https://res.openinstalljs.com/openinstall-${appKey}.js`;
    script.type = "text/javascript";
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

function getImageUrl(name) {
  return new URL(
    `../../assets/images/invite/${languageFileMap[currentLang.value]}/${name}`,
    import.meta.url
  ).href;
}

const handleCopy = (event) => {
  if (!inviteCode.value) return;
  clipboard(inviteCode.value, event, t("guild.copy_success")).then(() => {
    copyPopupRef.value.open();
  });
};

const handleDownLoad = () => {
  if (!that) return false;
  that.wakeupOrInstall();
};

const fetchData = async (code) => {
  if (!code) return;
  const res = await inviteApi.link_exchange_rate({ invite_code: code });
  if (res.code === 200) {
    linkData.value = res.data;
  }
};

const initObserver = () => {
  nextTick(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isShowDownloadButton.value = false;
        } else {
          isShowDownloadButton.value = true;
        }
      });
    });
    const targeEl = document.getElementById("observe-el");
    observer.observe(targeEl);
  });
};

const initVideoPlay = () => {
  nextTick(() => {
    const video = document.getElementById("video");
    video.addEventListener("play", function () {
      if (video.webkitEnterFullscreen) {
        isMuted.value = false;
        video.setAttribute("muted", false);
        video.webkitEnterFullscreen();
      } else {
        // 非 iOS 设备使用标准全屏 API
        // if (video.requestFullscreen) {
        //   video.requestFullscreen();
        // } else if (video.webkitRequestFullscreen) {
        //   video.webkitRequestFullscreen();
        // } else if (video.msRequestFullscreen) {
        //   video.msRequestFullscreen();
        // }
      }
    });
  });
};

const init = async () => {
  const res = await inviteApi.short_link_restore({
    link_hash: getUrlHash(),
    channel_code: getUrlChannelCode(),
    os: isAndroid() ? 1 : 2,
  });
  if (res.code === 200) {
    fetchData(res.data.shareCode);
    inviteCode.value = res.data.shareCode;
    appKey = res.data.app_key;
    i18n.global.locale = res.data.lang || "en";
    currentLang.value = res.data.lang || "en";
    resetDocumentRtl();
    visible.value = true;
    initObserver();
    initVideoPlay();
    loadScript()
      .then(() => {
        console.log("OpenInstall SDK 加载成功");
        new OpenInstall(
          {
            appKey, //appKey为openinstall为应用分配的唯一id（必须传入）
            channelCode: res.data.channelCode, //传入渠道编号，优先级大于链接上拼接的渠道编号
            fastInstall: false, //快速安装功能开关，true为开启，false为关闭
            onready: function () {
              //初始化成功回调方法。当初始化完成后，会自动进入
              this.schemeWakeup(); //尝试使用scheme打开App（主要用于Android以及iOS的QQ环境中）
              that = this;
              // this.wakeupOrInstall();
              // button = document.getElementById("downloadButton"); //为button对象绑定对应id的元素
              // // m.wakeupOrInstall();
              // button.onclick = function () {
              //   //对应button的点击事件
              //   m.wakeupOrInstall(); //此方法为scheme、Universal Link唤醒以及引导下载的作用（必须调用且不可额外自行跳转下载）
              //   return false;
              // };
            },
          },
          res.data
        );
      })
      .catch((error) => {
        console.error("OpenInstall SDK 加载失败:", error);
      });
  }
};

init();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: #b067ff;
  padding: px2rem(76) 0 px2rem(110);
  font-size: $fontSize15;
  position: relative;
  header {
    width: 100%;
    background: none;
    height: px2rem(64);
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(12);
    position: fixed;
    top: 0;
    left: 0;
    flex-wrap: nowrap;
    z-index: 101;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: px2rem(64);
      opacity: 0.5;
      z-index: -1;
      background: #ffffff52;
    }

    .header-left {
      flex: 1;
      flex-wrap: nowrap;
      .logo {
        width: px2rem(48);
        height: px2rem(48);
      }
      .inner {
        display: flex;
        flex-direction: column;
        font-size: $fontSize13;
        color: $white;
        .siya {
          width: px2rem(48);
          height: px2rem(23);
          margin-bottom: px2rem(2);
        }
      }
    }
    .header-btn {
      height: px2rem(34);
      line-height: px2rem(34);
      background: #fff;
      min-width: px2rem(84);
      padding: 0 px2rem(4);
      border-radius: px2rem(34);
      animation: spin-head 1.2s ease-in-out infinite;
      text-align: center;
      color: #5f56ed;
      font-weight: bold;
      white-space: nowrap;
    }
  }
  .invite-wrap {
    width: 100%;
    padding: px2rem(12);
    position: relative;
    align-items: start;
    .avatar {
      width: px2rem(56);
      height: px2rem(56);
      border-radius: 50%;
      object-fit: cover;
    }
    .invite-right {
      background-color: $white;
      border-radius: px2rem(24);
      padding: px2rem(13) px2rem(10);
      width: px2rem(279);
      flex-direction: column;
      .btn {
        background: linear-gradient(180deg, #ffc933 0%, #fe9318 100%);
        font-size: px2rem(24);
        font-weight: bold;
        height: px2rem(50);
        width: px2rem(224);
        border-radius: px2rem(50);
        line-height: px2rem(50);
        text-align: center;
        padding: 0 px2rem(12);
        color: #fff;
        margin: px2rem(12) auto;
      }
      .invite-inner {
        color: #ff7423;
        @include centerL;
        .copy-icon {
          height: px2rem(20);
          width: px2rem(20);
        }
      }
    }
    .adorn-box {
      position: absolute;
      bottom: 0;
      left: 0;
      justify-content: flex-end;
      pointer-events: none;
      width: 100%;
      img {
        height: px2rem(43);
        width: px2rem(53);
      }
      .ar-img {
        transform: scale(-1);
      }
    }
    .radius-right {
      border-top-right-radius: px2rem(8);
    }
    .radius-left {
      border-top-left-radius: px2rem(8);
    }
  }
  .video-wrap {
    border-radius: px2rem(30);
    overflow: hidden;
    background: #c690ff;
    margin: 0 0 px2rem(40);
    min-height: px2rem(610);
    border: px2rem(4) solid #c690ff;
    display: flex;
    position: relative;
    margin: px2rem(12);
    width: calc(100% - px2rem(24));
    video {
      width: 100%;
    }
  }
  .section-wrap {
    background: url("@/assets/images/invite/chat-bg.png") no-repeat;
    background-size: 100%;
    min-height: px2rem(200);
    width: calc(100% - px2rem(24));
    margin: px2rem(0) px2rem(12) px2rem(12);
    position: relative;

    // transform: rotateY(180deg);
    .adorn {
      height: px2rem(43);
      width: px2rem(53);
      position: absolute;
      transform: scaleX(-1) translate(px2rem(12));
      left: 0;
    }
    .title {
      padding: px2rem(20) 0 px2rem(0);
      margin-right: px2rem(90);
      margin-left: px2rem(12);
      margin-bottom: px2rem(12);
      min-height: px2rem(40);
      background: url("@/assets/images/invite/underline.png") no-repeat;
      background-position: v-bind(
        "currentLang==='ar'?'right bottom':'left bottom'"
      );
      background-size: px2rem(80) px2rem(16);
      color: #390844;
      font-size: px2rem(24);
      font-weight: bold;
    }
    .inner-content {
      padding: px2rem(12);
      border-radius: px2rem(24);
      background: $white;
      width: 100%;
    }
  }
  .chat-wrap {
    .bg-content {
      background: #f9f9f9;
      border-radius: px2rem(12);
      padding: px2rem(12) px2rem(12) 0 px2rem(12);
      .chat-img {
        width: 100%;
        min-height: px2rem(114);
        margin-bottom: px2rem(20);
      }
      .human-wrap {
        position: relative;
        .reward-box {
          position: absolute;
          display: flex;
          align-items: center;
          top: px2rem(-16);
          right: px2rem(46);
          .diamond-icon {
            width: px2rem(32);
            height: px2rem(32);
            // transform: translateY(px2rem(8));
          }
          span {
            color: #0f83ff;
            font-size: px2rem(15);
          }
          .text {
            font-size: px2rem(22);
            font-weight: bold;
          }
        }
        .human {
          height: px2rem(240);
        }
      }
    }

    .ar-human-wrap {
      .reward-box {
        right: px2rem(120) !important;
      }
      .human {
        transform: scaleX(-1) !important;
      }
    }
  }
  .withdraw-wrap {
    .adorn {
      left: px2rem(324);
      top: px2rem(-4);
      transform: scaleX(1);
    }
    .inner-content {
      font-weight: bold;
      .withdraw-box {
        background: #f9f9f9;
        border-radius: px2rem(16);
        padding: px2rem(8);
        flex: 1;
        .item {
          color: #5f56ed;
          font-size: $fontSize15;
          padding: 0 px2rem(8);
          background: $white;
          height: px2rem(40);
          margin-bottom: px2rem(8);
          border-radius: px2rem(8);
          justify-content: space-between;
          &:last-child {
            margin-bottom: 0;
          }
          .item-right {
            background: #efeffe;
            height: px2rem(32);
            border-radius: px2rem(32);
            padding: 0 px2rem(8);
            img {
              width: px2rem(20);
              height: px2rem(20);
            }
          }
        }
      }
      .withdraw-right {
        position: relative;
        span {
          color: #5f56ed;
          font-size: $fontSize17;
          position: absolute;
          top: px2rem(8);
          text-align: center;
          width: 100%;
        }
        .withdraw-img {
          width: px2rem(111);
          height: px2rem(152);
        }
      }
    }
  }
  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 101;
    padding: px2rem(60) px2rem(16) px2rem(30);
    background: linear-gradient(
      0deg,
      rgba(176, 103, 255, 0.64) 0%,
      rgba(176, 103, 255, 0.32) 50%,
      rgba(176, 103, 255, 0) 100%
    );

    .download-btn {
      width: 100%;
      height: px2rem(72);
      line-height: px2rem(72);
      border-radius: px2rem(72);
      color: #fff;
      font-weight: $fontWeightBold;
      text-align: center;
      background: #ffd42f;
      font-size: px2rem(28);
      box-shadow: 0px 0px px2rem(50.23) 0px #ffffff inset;
      color: #7c0001;
      animation: spin 0.8s ease-in-out infinite;
      @include center;
      .download-icon {
        font-size: px2rem(28);
        transform: translateY(px2rem(-2));
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin-head {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.2);
  }
  40% {
    transform: rotate(10deg) scale(1.2);
  }
  60% {
    transform: rotate(-10deg) scale(1.2);
  }
  80% {
    transform: scale(1.2);
  }
  90% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
