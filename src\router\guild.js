const routes = [
  {
    path: '/guild/home',
    name: 'GuildHome',
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "GuildHome"*/ '../views/guild/Home.vue')
  },
  {
    path: '/guild/detail',
    name: 'GuildDetail',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "GuildHome"*/ '../views/guild/detail/index.vue')
  },
  {
    path: '/guild/member',
    name: 'GuildMember',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "GuildHome"*/ '../views/guild/member/index.vue')
  },
  {
    path: '/guild/member/:id', // :id 是动态参数，用于标识不同的成员
    name: 'GuildMemberDetail',
    meta: {
      fullScreen: true,
    },
    component: () => import(/* webChunkName: "GuildMemberDetail" */ '../views/guild/member/detail.vue')
  }
]

export default routes;
