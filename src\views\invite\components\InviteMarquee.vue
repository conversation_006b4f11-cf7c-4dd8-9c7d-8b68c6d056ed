<template>
  <div class="marquee-container" ref="container">
    <div
      class="marquee-inner"
      :style="{ animationDuration: speed + 's' }"
      ref="track"
    >
      <div
        v-for="(item, index) in duplicatedList"
        :key="index"
        class="marquee-item"
      >
        <img :src="item.avatar" class="avatar" />
        <span class="text">{{
          $t("invite.invite_success_msg", [maskName(item.nickname)])
        }}</span>
        <span class="coin">
          <Coin :gender="userGender"></Coin>
        </span>
        <span class="amount">{{ getRandomInt(1, 100) * 150 }}</span>
        <gap :gap="4"></gap>  
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref, nextTick, inject } from "vue";
import { maskName, getRandomInt } from "@/utils/util";
import Coin from "./Coin.vue";

const props = defineProps({
  list: {
    type: Array,
    required: true,
  },
});

const userGender = inject("userGender");
// 克隆两份用于无缝滚动
const duplicatedList = computed(() => [...props.list, ...props.list]);
const container = ref(null);
const track = ref(null);
const speed = ref(0);

onMounted(async () => {
  await nextTick();

  const containerWidth = container.value.offsetWidth;
  const trackWidth = track.value.scrollWidth / 2;

  if (trackWidth < containerWidth) {
    console.warn("内容不够宽，无法实现无缝滚动。");
  }
  console.log("container:", container.value.offsetWidth);
  console.log("track:", track.value.scrollWidth / 2);
  speed.value = track.value.scrollWidth / 100;
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.marquee-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  background: transparent;
}

.marquee-inner {
  width: fit-content;
  display: flex;
  white-space: nowrap;
  gap: px2rem(12);
  animation: scroll-left linear infinite;
}

.marquee-item {
  display: flex;
  align-items: center;
  gap: px2rem(4);
  height: px2rem(28);
  padding: px2rem(4);
  border-radius: px2rem(100);
  background: var(--black-24, rgba(0, 0, 0, 0.24));
  flex-shrink: 0; /* 保证宽度不压缩 */
}

.avatar {
  width: px2rem(20);
  height: px2rem(20);
  border-radius: 50%;
  background-color: #eee;
  object-fit: cover;
}

.text {
  color: var(---White, #fff);

  /* T13/R */
  font-family: Gilroy;
  font-size: px2rem(13);
  font-style: normal;
  font-weight: 500;
  line-height: 124%; /* 16.12px */
}

.coin {
  font-size: px2rem(13);
}

.amount {
  color: #ffdb3f;
  /* T13/B */
  font-family: Gilroy;
  font-size: px2rem(13);
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

/* 动画关键帧 */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 动画关键帧 */
@keyframes scroll-right {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(50%);
  }
}

.rtl-html {
  .marquee-inner {
    animation: scroll-right linear infinite;
  }
}
</style>
