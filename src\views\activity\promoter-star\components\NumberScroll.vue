<template>
  <div class="number">
    <div v-for="text in String(currentValue)">{{ text }}</div>
  </div>
</template>

<script setup>
import { ref, watch, onUnmounted, onMounted, computed } from "vue";

// 定义props
const props = defineProps({
  targetValue: {
    type: Number,
    required: true, // 目标值，必填
  },
  startValue: {
    type: Number,
    default: 0, // 开始值
  },
  duration: {
    type: Number,
    default: 500, // 动画持续时间，默认1000ms
  },
});

// 当前显示的数字
const currentValue = ref(props.startValue);
const valueLength = computed(() => `${currentValue.value}`.length);
let timer = null;

const startScroll = (newValue) => {
  if (timer) clearInterval(timer); // 清除已有定时器

  const startValue = currentValue.value; // 起始值
  const endValue = newValue; // 目标值
  const startTime = Date.now(); // 开始时间
  const animationDuration = props.duration; // 动画时长

  timer = setInterval(() => {
    const elapsedTime = Date.now() - startTime; // 已过去的时间
    const progress = Math.min(elapsedTime / animationDuration, 1); // 动画进度（0到1）
    currentValue.value = Math.floor(
      startValue + (endValue - startValue) * progress
    ); // 更新当前值

    if (progress >= 1) {
      clearInterval(timer); // 动画结束，清除定时器
      currentValue.value = endValue; // 确保最终值精确
    }
  }, 16); // 约60fps的更新频率
};

// 监听targetValue变化，触发动画
watch(
  () => props.targetValue,
  (newValue, oldValue) => {
    startScroll(newValue);
  }
);

onMounted(() => {
  startScroll(props.targetValue);
});

// 组件销毁时清理定时器
onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>
<style lang="scss" scoped>
.number {
  display: grid;
  grid-template-columns: repeat(v-bind(valueLength), 1fr);
}
.rtl-html {
  .number {
    direction: ltr;
  }
}
</style>
