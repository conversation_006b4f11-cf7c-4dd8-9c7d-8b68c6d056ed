<template>
  <div class="container">
    <header-bar
      back-icon-show
      :show-title="true"
      :title="$t(`promoter_star.siya_star_promoter`)"
      is-scroll-change-bg
      :content-padding="false"
      :statusBarPadding="!isApp ? 10 : undefined"
      close-type="close"
      :always-show-right-icon="true"
      @emit:scrollChangeBgShow="scrollChangeBgShow"
    >
      <template #bottom v-if="showPageTitle && isApp">
        <page-tab
          style="margin-top: -1px"
          ref="pageTabRef"
          :initialTab="initialTab"
          :tabItems="tabItems"
          @tab-change="handlePageTabChange"
        ></page-tab>
      </template>
      <template #right>
        <div class="flex">
          <svg-icon
            v-if="isApp"
            class="icon-concat"
            :icon="showPageTitle ? 'contact-black' : 'contact-white'"
            :color="showPageTitle ? '#000' : '#fff'"
            @click="goFeedback()"
            v-track:click
            trace-key="star_ambassador_share"
          />
          <gap :gap="20"></gap>
          <svg-icon
            class="icon-share"
            icon="share"
            :color="showPageTitle ? '#000' : '#fff'"
            @click="handleShare()"
            v-track:click
            trace-key="star_ambassador_share"
          />
        </div>
      </template>
    </header-bar>
    <div
      class="rule-btn"
      @click="toggleHintDialog(true)"
      v-track:click
      trace-key="star_ambassador_prize_pool_click"
    >
      <div></div>
    </div>
    <div>
      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('banner-title.png')}')`,
        }"
      >
        <img :src="getImageUrl('banner-title.png')" alt="" />
      </div>
    </div>
    <div class="banner-box">
      <div class="banner-desc">
        {{ $t(`promoter_star.sign_up_to_become_siya_promoter_star`) }}
      </div>
    </div>
    <div class="prize-box" v-track:exposure trace-key="star_ambassador_expo">
      <div
        v-if="!isApp"
        class="h5-exposure"
        v-track:exposure
        trace-key="fb_share_h5_expo"
        :track-params="JSON.stringify({ share_name: '星推官_落地页' })"
      ></div>
      <div class="prize-title-box-out">
        <div class="prize-title-box">
          <div class="prize-title">{{ $t(`promoter_star.cash_prizes`) }}</div>
        </div>
      </div>
      <div class="prize-content">
        <div class="unit">$</div>
        <div
          class="number-scroll"
          :class="starInfo?.bonus > 999 ? 'f-88' : 'f-128'"
        >
          <NumberScroll
            v-if="starInfo"
            :start-value="100"
            :target-value="starInfo?.bonus"
            :duration="1000"
          />
        </div>
      </div>
      <div class="prize-button-box">
        <div class="prize-desc">
          {{
            $t(`promoter_star.now_users_signed_up`, [starInfo?.register_count])
          }}
        </div>
        <div class="prize-button-box-btn">
          <prize-button
            ref="buttonRef"
            :star-info="starInfo"
            @registerSuccess="getStarInfo()"
          ></prize-button>
        </div>
      </div>
      <canvas class="up-anima" id="pagCanvas"></canvas>
    </div>
    <div v-if="isApp && bottomButtonShow" class="bottom-button">
      <prize-button
        :star-info="starInfo"
        @registerSuccess="getStarInfo()"
      ></prize-button>
    </div>
    <div class="download-box" v-if="!isApp">
      <div class="download-tip">
        {{ $t(`promoter_star.click_to_download`) }}
      </div>
      <div
        class="download-button animation"
        @click="download"
        v-track:click
        trace-key="fb_share_h5_click"
        :track-params="JSON.stringify({ share_name: '星推官_落地页' })"
      >
        <div>{{ $t(`promoter_star.download_now`) }}</div>
        <div>
          <img src="@/assets/images/promoter-star/download.svg" />
        </div>
      </div>
    </div>
    <!-- 奖励 -->
    <block-prize
      ref="prizeRef"
      data-index="0"
      :star-info="starInfo"
    ></block-prize>
    <!-- 视频内容要求 -->
    <block-requirements
      ref="requirementsRef"
      data-index="1"
    ></block-requirements>
    <!-- 视频素材 -->
    <block-material
      v-if="isApp"
      style="z-index: 99"
      ref="materialRef"
      data-index="2"
    ></block-material>
    <!--  提示弹窗  -->
    <rule-modal
      :show="hintDialogShow"
      @confirm="toggleHintDialog(false)"
      @update:show="toggleHintDialog"
    >
    </rule-modal>
    <share-modal v-model:show="shareDialogShow"></share-modal>
  </div>
</template>

<script setup>
import {
  getCurrentInstance,
  ref,
  onMounted,
  computed,
  onUnmounted,
  nextTick,
} from "vue";
import {
  AnimationPAGInitLocal,
  downloadApp,
  smoothScrollTo,
  isAndroid,
} from "@/utils/util.js";
import RuleModal from "./components/RuleModal.vue";
import ShareModal from "./components/ShareModal.vue";
import BlockPrize from "./components/BlockPrize.vue";
import BlockRequirements from "./components/BlockRequirements.vue";
import BlockMaterial from "./components/BlockMaterial.vue";
import { i18n, checkLang } from "@/i18n/index.js";
import NumberScroll from "./components/NumberScroll.vue";
import PageTab from "./components/PageTab.vue";
import PrizeButton from "./components/PrizeButton.vue";
import activityApi from "@/api/activity.js";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();

const t = i18n.global.t;
const { appContext } = getCurrentInstance();
const pageTabRef = ref();
const prizeRef = ref();
const requirementsRef = ref();
const materialRef = ref();
const buttonRef = ref();
const blockRefs = computed(() =>
  [prizeRef, requirementsRef]
    .concat(isApp.value ? materialRef : null)
    .filter(Boolean)
);

const lang = checkLang();
function getImageUrl(name) {
  return new URL(
    `../../../assets/images/promoter-star/${lang}/${name}`,
    import.meta.url
  ).href;
}

function download() {
  downloadApp("https://app.adjust.com/1plcmo6o");
}

const initialTab = ref(0);
const tabItems = computed(() =>
  [
    {
      name: "tab1",
      title: t(`promoter_star.tab_1`),
      traceKey: "star_ambassador_rewards_tab_click",
    },
    {
      name: "tab2",
      title: t(`promoter_star.tab_2`),
      traceKey: "star_ambassador_rule_tab_click",
    },
  ]
    .concat(
      isApp.value
        ? {
            name: "tab3",
            title: t(`promoter_star.tab_3`),
            traceKey: "star_ambassador_resources_tab_click",
          }
        : null
    )
    .filter(Boolean)
);

// 下滑展示标题处理
const showPageTitle = ref(false);
const scrollChangeBgShow = (show) => {
  showPageTitle.value = show;
};

const hintDialogShow = ref(false);
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool;
};

const bottomButtonShow = ref(false);

const starInfo = ref(null);
const getStarInfo = async () => {
  const { data } = await activityApi.star_info();
  starInfo.value = data;
};

// 是否在APP内
const isApp = computed(() => {
  return appContext?.config.globalProperties.$isApp;
});

let animationObj = null;
const playAnimation = async () => {
  animationObj = await AnimationPAGInitLocal(
    "up.pag",
    "#pagCanvas",
    (_, pagFile, types) => {
      const textDoc = pagFile.getTextData(0);
      if (lang === "ar" && isAndroid()) {
        textDoc.justification =
          types.ParagraphJustification.FullJustifyLastLineFull;
      }
      textDoc.fauxBold = true;
      pagFile.replaceText(0, textDoc);
    }
  );
  animationObj.setRepeatCount(1);
  await animationObj.play();
};

let isScrolling = false;
let scrollTimer = null;
const handlePageTabChange = ({ index }) => {
  isScrolling = true;
  clearTimeout(scrollTimer);
  scrollTimer = null;
  const target = blockRefs.value[index];
  const { bottom } = pageTabRef.value.$el.getBoundingClientRect();
  smoothScrollTo(target.value.$el, bottom);
  scrollTimer = setTimeout(() => {
    isScrolling = false;
  }, 1000);
};

let scrollObserver = null;
const initScrollObserver = () => {
  scrollObserver = new IntersectionObserver(
    (entries) => {
      if (!isScrolling) {
        const current = entries.find((entry) => {
          const top = entry.boundingClientRect.top;
          return top > 0 && top < 500;
        });
        if (current) {
          const index = +current.target.dataset.index;
          initialTab.value = index;
        }
      }
    },
    {
      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1], // 触发阈值
    }
  );

  blockRefs.value.forEach((item) => {
    scrollObserver.observe(item.value.$el);
  });
};

const distroyScrollObserver = () => {
  scrollObserver.disconnect();
  scrollObserver = null;
};

let buttonObserver = null;
const initButtonObserver = () => {
  buttonObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        bottomButtonShow.value = !entry.isIntersecting;
      });
    },
    {
      threshold: [0, 0.1, 0.25, 0.5, 0.75, 1], // 触发阈值
    }
  );

  buttonObserver.observe(buttonRef.value.$el);
};

const distroyButtonObserver = () => {
  buttonObserver.disconnect();
  buttonObserver = null;
};

const shareDialogShow = ref(false);
const handleShare = () => {
  shareDialogShow.value = true;
};
const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};
onMounted(() => {
  getStarInfo();
  nextTick(() => {
    playAnimation();
    initScrollObserver();
    initButtonObserver();
  });
});

onUnmounted(() => {
  distroyScrollObserver();
  distroyButtonObserver();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.container {
  background: #bfeff5;
  min-height: 100vh;
  background-image: url("@/assets/images/promoter-star/top-picture.png");
  background-size: 100% px2rem(376);
  background-repeat: no-repeat;
  padding-bottom: px2rem(160);
  position: relative;
  overflow: hidden;
  width: 100%;
  .icon-share,
  .icon-concat {
    font-size: px2rem(28);
    color: unset;
  }
  .rule-btn {
    position: fixed;
    right: 0;
    z-index: 99;
    top: px2rem(260);
    width: px2rem(40);
    height: px2rem(32);
    border-radius: px2rem(100) 0px 0px px2rem(100);
    border: px2rem(2) solid #ffe69b;
    background: linear-gradient(180deg, #f86667 0%, #ff5253 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    div {
      flex-shrink: 0;
      width: px2rem(24);
      height: px2rem(24);
      background-image: url("@/assets/images/promoter-star/rule.png");
      background-size: 100% 100%;
    }
  }
  .banner-title {
    display: block;
    width: px2rem(366);
    margin: 0 auto;
    margin-top: px2rem(30);
    background-size: 100% 100%;
    img {
      width: px2rem(366);
      opacity: 0;
    }
  }
}
.banner-box {
  text-align: center;
  color: #062226;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(36);
  font-style: italic;
  font-weight: 900;
  line-height: normal;
  display: flex;
  justify-content: center;
  align-items: center;
  .banner-desc {
    display: flex;
    padding: px2rem(2) px2rem(8);
    justify-content: center;
    align-items: center;
    gap: px2rem(8);
    align-self: stretch;
    border-radius: px2rem(8);
    background: var(--24, rgba(255, 255, 255, 0.24));
    color: #145057;
    text-align: center;
    /* T13/R */
    font-family: Gilroy;
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: 124%; /* 16.12px */
    min-height: px2rem(36);
    width: fit-content;
    margin: 0 px2rem(12);
  }
}

.prize-box {
  position: relative;
  margin: 0 auto;
  margin-top: px2rem(32);
  width: px2rem(358);
  height: px2rem(377);
  background-image: url("@/assets/images/promoter-star/prize-bg.png");
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  .h5-exposure {
    position: absolute;
    inset: 0;
  }
  .prize-title-box-out {
    width: px2rem(306);
    height: px2rem(302);
    position: absolute;
    border-radius: px2rem(48);
    overflow: hidden;
    top: 0;
    left: px2rem(26);
    margin: 0 auto;
  }
  .prize-title-box {
    display: flex;
    justify-content: center;
    align-items: center;
    .prize-title {
      display: inline-flex;
      padding: px2rem(4) px2rem(16);
      justify-content: center;
      align-items: center;
      gap: px2rem(4);
      color: #7c0001;
      border-radius: 0px 0px px2rem(12) px2rem(12);
      background: #ffe69b;

      /* T24/B */
      font-family: Gilroy;
      font-size: px2rem(24);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
  .prize-content {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    color: #7c0001;
    text-align: center;
    font-family: Gilroy;
    font-style: normal;
    font-weight: 900;
    line-height: 1;
    margin-top: px2rem(64);
    height: px2rem(130);
    .unit {
      font-size: px2rem(64) !important;
      padding-bottom: px2rem(12);
    }
    .number-scroll {
      width: px2rem(230);
    }
    .f-128 {
      font-size: px2rem(128);
    }
    .f-88 {
      font-size: px2rem(88);
    }
  }
  .prize-button-box {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    margin-bottom: px2rem(18);
    .prize-desc {
      color: #fff;
      text-align: center;

      /* T17/R */
      font-family: Gilroy;
      font-size: px2rem(17);
      font-style: normal;
      font-weight: 500;
      line-height: 132%; /* 22.44px */
    }
    .prize-button-box-btn {
      width: px2rem(300);
      margin: 0 auto;
    }
  }
  .up-anima {
    width: px2rem(100);
    height: px2rem(100);
    position: absolute;
    right: 0;
    top: px2rem(16);
  }
}
.bottom-button {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  padding: px2rem(20);
  background: linear-gradient(
    0deg,
    #d8fbff 0%,
    rgba(216, 251, 255, 0.9) 59.99%,
    rgba(216, 251, 255, 0) 100%
  );
}
.download-box {
  position: fixed;
  left: 0;
  bottom: -1px;
  z-index: 10;
  border-radius: 26px 26px 0px 0px;
  background: #fd42a7;
  padding: px2rem(16);
  width: 100%;
  .download-tip {
    color: #fff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(15);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  @keyframes scaleAnimation {
    0% {
      transform: scale(1);
    }
    25% {
      transform: scale(0.92);
    }
    50% {
      transform: scale(1.05);
    }
    75% {
      transform: scale(0.92);
    }
    100% {
      transform: scale(1);
    }
  }
  .animation {
    animation: scaleAnimation 1.5s ease-in-out infinite;
  }
  .download-button {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: px2rem(72);
    padding: px2rem(10) 0;
    gap: px2rem(2);
    border-radius: px2rem(100);
    background: #ffd42f;
    box-shadow: 0px 0px px2rem(50) 0px #fff inset;

    color: #7c0001;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(28);
    font-style: normal;
    font-weight: 900;
    line-height: normal;
    margin-top: px2rem(8);
    & > div {
      margin-right: px2rem(6);
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: px2rem(28);
        height: px2rem(28);
      }
    }
  }
}
.rtl-html {
  .icon-share {
    transform: scaleX(-1);
  }
  .rule-btn {
    right: unset !important;
    left: 0;
    border-radius: 0 px2rem(100) px2rem(100) 0;
  }
}
</style>
