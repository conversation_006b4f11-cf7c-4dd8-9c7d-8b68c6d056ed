<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    @touchmove.prevent
    z-index="10001"
  >
    <div v-if="showDialog" class="base-modal-wrapper" @click.stop>
      <div class="box">
        <GoldBox>
          <div class="title">
            {{ $t("pk_king.congratulations") }}
          </div>
          <div class="rewards">
            <div class="reward" v-for="prize in prizes">
              <div class="cover">
                <PrizeBox :rewards="prize"></PrizeBox>
              </div>
              <div class="name">{{ prize.reward_name }}</div>
            </div>
          </div>
          <div class="desc">
            {{ $t("pk_king.task_reward_sent") }}
          </div>
          <div class="action">
            <div class="btn-confirm" @click="showDialog = false">
              <span>{{ $t("pk_king.ok") }}</span>
            </div>
          </div>
        </GoldBox>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";
import GoldBox from "./GoldBox.vue";
import PrizeBox from "./PrizeBox.vue";

const showDialog = ref(false);
const prizes = ref([]);

const handleOpen = (data) => {
  prizes.value = data || [];
  showDialog.value = true;
};

defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .box {
    width: px2rem(331);
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    position: relative;
    .bg-3 {
      position: absolute;
      top: px2rem(-30);
      width: px2rem(120);
      height: px2rem(65);
      z-index: 5;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/egypt-national-day/bg-3.png"); /* 中间部分的图片 */
      background-size: 100% 100%;
    }
    .bg-4 {
      position: absolute;
      top: px2rem(-184);
      width: px2rem(298);
      height: px2rem(298);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/egypt-national-day/bg-4.png"); /* 中间部分的图片 */
      background-size: 100% 100%;
    }
    .title {
      margin-top: px2rem(40);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(22);
      height: px2rem(28);
      font-style: italic;
      font-weight: 900;
      line-height: normal;
      background: linear-gradient(180deg, #fff 0%, #ffba24 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      padding: 0 4px;
    }
    .desc {
      margin-top: px2rem(6);
      color: rgba(235, 157, 255, 0.65);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      width: px2rem(217);
    }
    .rewards {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: px2rem(23) 0;
      .reward {
        display: flex;
        align-items: center;
        flex-direction: column;
        width: px2rem(87);
        .cover {
          width: px2rem(70);
          height: px2rem(70);
        }
        .name {
          margin-top: px2rem(12);
          width: px2rem(87);
          color: #ffa19d;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }

    .action {
      margin-top: px2rem(20);
      display: flex;
      align-items: center;
      justify-content: center;
      .btn-confirm {
        position: relative;
        z-index: 9;
        width: px2rem(160);
        height: px2rem(38);
        margin-bottom: px2rem(22);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/pk-king/ok-btn.png"); /* 中间部分的图片 */
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          text-align: center;
          text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          font-family: Gilroy;
          font-size: px2rem(18);
          font-style: normal;
          font-weight: 700;
          line-height: 100%; /* 18px */
          background: linear-gradient(180deg, #fff 14.33%, #ffc832 88.73%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}
</style>
