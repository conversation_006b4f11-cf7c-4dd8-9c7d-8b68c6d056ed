<template>
  <div class="ranking" :class="bgClass">
    <div class="top3">
      <div class="top-2">
        <div class="avatar">
          <frame-avatar
            v-if="top2"
            :avatar="top2?.avatar"
            :frame="top2?.frame"
            :size="54"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top2?.nickname || "-" }}</div>
        <template v-if="isScore">
          <div class="score">
            <span>{{ top2?.referee_score ?? 0 }}</span>
            <gap :gap="3"></gap>
            <span>+</span>
            <gap :gap="3"></gap>
            <span>{{ top2?.gift_score ?? 0 }}</span>
          </div>
          <div class="score-total">
            {{ (top2?.referee_score ?? 0) + (top2?.gift_score ?? 0) }}
          </div>
        </template>
        <template v-else>
          <div class="t-number">
            <TicketNumber :number="top2?.count"></TicketNumber>
          </div>
          <div class="v-button" v-if="showVoteBtn">
            <VoteButton
              :user-item="top2"
              :source="rankType === 1 ? 2 : 4"
              @vote="fetchData"
            ></VoteButton>
          </div>
          <div class="a-player" v-if="showAudioBtn">
            <AudioPlayer :url="top2?.media_url"></AudioPlayer>
          </div>
        </template>
      </div>
      <div class="top-1">
        <div class="avatar">
          <frame-avatar
            v-if="top1"
            :avatar="top1?.avatar"
            :frame="top1?.frame"
            :size="54"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top1?.nickname || "-" }}</div>
        <template v-if="isScore">
          <div class="score">
            <span>{{ top1?.referee_score ?? 0 }}</span>
            <gap :gap="3"></gap>
            <span>+</span>
            <gap :gap="3"></gap>
            <span>{{ top1?.gift_score ?? 0 }}</span>
          </div>
          <div class="score-total">
            {{ (top1?.referee_score ?? 0) + (top1?.gift_score ?? 0) }}
          </div>
        </template>
        <template v-else>
          <div class="t-number">
            <TicketNumber :number="top1?.count"></TicketNumber>
          </div>
          <div class="v-button" v-if="showVoteBtn">
            <VoteButton
              :user-item="top1"
              :source="rankType === 1 ? 2 : 4"
              @vote="fetchData"
            ></VoteButton>
          </div>
          <div class="a-player" v-if="showAudioBtn">
            <AudioPlayer :url="top1?.media_url"></AudioPlayer>
          </div>
        </template>
      </div>
      <div class="top-3">
        <div class="avatar">
          <frame-avatar
            v-if="top3"
            :avatar="top3?.avatar"
            :frame="top3?.frame"
            :size="54"
          ></frame-avatar>
          <div v-else class="empty-avatar">
            <svg-icon icon="vector" :size="20"></svg-icon>
          </div>
        </div>
        <div class="nickname">{{ top3?.nickname || "-" }}</div>
        <template v-if="isScore">
          <div class="score">
            <span>{{ top3?.referee_score ?? 0 }}</span>
            <gap :gap="3"></gap>
            <span>+</span>
            <gap :gap="3"></gap>
            <span>{{ top3?.gift_score ?? 0 }}</span>
          </div>
          <div class="score-total">
            {{ (top3?.referee_score ?? 0) + (top3?.gift_score ?? 0) }}
          </div>
        </template>
        <template v-else>
          <div class="t-number">
            <TicketNumber :number="top3?.count"></TicketNumber>
          </div>
          <div class="v-button" v-if="showVoteBtn">
            <VoteButton
              :user-item="top3"
              :source="rankType === 1 ? 2 : 4"
              @vote="fetchData"
            ></VoteButton>
          </div>
          <div class="a-player" v-if="showAudioBtn">
            <AudioPlayer :url="top3?.media_url"></AudioPlayer>
          </div>
        </template>
      </div>
    </div>
    <div class="score-header-block" v-if="isScore && rankAfter3List.length > 0">
      <div class="score-header">
        <span>{{ $t(`singer.qualifying_round_ranking_judges_scores`) }}</span>
        <gap :gap="5"></gap>
        <span>+</span>
        <gap :gap="5"></gap>
        <span>{{ $t(`singer.qualifying_round_ranking_gift_points`) }}</span>
      </div>
    </div>
    <div class="ranking-list" :class="`${hasMe ? 'pb103' : ''}`">
      <div
        class="rank-item"
        v-for="(item, index) in rankAfter3List"
        :key="index"
      >
        <div class="index">{{ item.rank }}</div>
        <gap :gap="8"></gap>
        <div class="avatar">
          <frame-avatar
            :avatar="item?.avatar"
            :frame="item.frame"
            :size="36"
          ></frame-avatar>
        </div>
        <gap :gap="8"></gap>
        <div class="nickname">{{ item.nickname }}</div>
        <gap :gap="8"></gap>
        <template v-if="isScore">
          <div>
            <div class="score">
              <span>{{ item?.referee_score ?? 0 }}</span>
              <gap :gap="3"></gap>
              <span>+</span>
              <gap :gap="3"></gap>
              <span>{{ item?.gift_score ?? 0 }}</span>
            </div>
            <div class="score-total">
              {{ (item?.referee_score ?? 0) + (item?.gift_score ?? 0) }}
            </div>
          </div>
        </template>
        <template v-else>
          <div class="action">
            <TicketNumber :number="item?.count"></TicketNumber>
            <div>
              <div v-if="showAudioBtn">
                <AudioPlayer :url="item.media_url"></AudioPlayer>
              </div>
              <gap :gap="4" v-if="showVoteBtn"></gap>
              <div>
                <VoteButton
                  v-if="showVoteBtn"
                  :user-item="item"
                  :source="rankType === 1 ? 2 : 4"
                  @vote="fetchData"
                ></VoteButton>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom-box" v-if="hasMe">
      <div class="bottom-inner">
        <div class="rank-item">
          <div class="index">
            {{
              userRank?.rank === -1
                ? "-"
                : (userRank?.rank ?? 0) > 99
                ? "99+"
                : userRank?.rank || "-"
            }}
          </div>
          <gap :gap="8"></gap>
          <div class="avatar">
            <frame-avatar
              :avatar="userRank?.avatar"
              :frame="userRank?.frame"
              :size="36"
            ></frame-avatar>
          </div>
          <gap :gap="8"></gap>
          <div class="nickname">
            {{ userRank?.nickname ?? "-" }}
          </div>
          <template v-if="isScore">
            <div>
              <div class="score">
                <span>{{ userRank?.referee_score ?? 0 }}</span>
                <gap :gap="3"></gap>
                <span>+</span>
                <gap :gap="3"></gap>
                <span>{{ userRank?.gift_score ?? 0 }}</span>
              </div>
              <div class="score-total">
                {{
                  (userRank?.referee_score ?? 0) + (userRank?.gift_score ?? 0)
                }}
              </div>
            </div>
          </template>
          <template v-else>
            <div class="coin-box">
              <div class="action">
                <TicketNumber :number="userRank?.count"></TicketNumber>
                <div>
                  <div v-if="showAudioBtn">
                    <AudioPlayer :url="userRank?.media_url"></AudioPlayer>
                  </div>
                  <gap :gap="4" v-if="showVoteBtn"></gap>
                  <div>
                    <VoteButton
                      v-if="showVoteBtn"
                      :user-item="userRank"
                      :source="rankType === 1 ? 2 : 4"
                      @vote="fetchData"
                    ></VoteButton>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, computed } from "vue";
import AudioPlayer from "./AudioPlayer.vue";
import TicketNumber from "./TicketNumber.vue";
import VoteButton from "./VoteButton.vue";
import singerApi from "@/api/activity.js";
const props = defineProps({
  // 1.人气榜 2.热门榜 3.应援榜; 1.晋级赛top20 2.决赛top10
  rankType: {
    type: Number,
    default: 1,
  },
  // 1.投票排行榜 2.比赛排行榜
  rankDataType: {
    type: Number,
    default: 1,
  },
  isScore: {
    type: Boolean,
    default: false,
  },
  showVoteBtn: {
    type: Boolean,
    default: true,
  },
  showAudioBtn: {
    type: Boolean,
    default: true,
  },
});
const baseData = ref(null);
const userRank = computed(() => baseData.value?.current_rank);
const hasMe = computed(() => userRank.value && userRank.value?.rank !== -1);
const rankList = computed(() => {
  return baseData.value?.rank_list ?? [];
});
const top1 = computed(() => {
  return rankList.value.find((i) => i.rank === 1);
});
const top2 = computed(() => {
  return rankList.value.find((i) => i.rank === 2);
});
const top3 = computed(() => {
  return rankList.value.find((i) => i.rank === 3);
});
const rankAfter3List = computed(() => rankList.value.slice(3));
const bgClass = computed(() => {
  const arr = [props.showVoteBtn, props.showAudioBtn].filter(Boolean);
  console.log(arr);

  const none = arr.length === 0;
  // const hasOne = arr.length === 1;
  // const hasBoth = arr.length === 2;
  return props.isScore ? "top-bg-small" : none ? "top-bg-mini" : "";
});
const fetchData = async () => {
  const handler =
    props.rankDataType === 1
      ? singerApi.singer_poll_rank
      : singerApi.singer_stage_rank;
  const params =
    props.rankDataType === 1
      ? {
          rank_type: props.rankType,
        }
      : {
          stage: props.rankType,
        };
  const res = await handler(params);
  if (res.code === 200) {
    baseData.value = res.data;
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.ranking {
  width: 100%;
}
.top3 {
  display: flex;
  justify-content: center;
  position: relative;
  z-index: 2;
  .top-2 {
    width: px2rem(108);
    height: px2rem(235);
    flex-shrink: 0;
    background-image: url("@/assets/images/activity/singer/top2.png");
    background-size: 100% 100%;
    margin-top: px2rem(32);
  }
  .top-1 {
    width: px2rem(120);
    height: px2rem(256);
    background-image: url("@/assets/images/activity/singer/top1.png");
    flex-shrink: 0;
    background-size: 100% 100%;
    .avatar {
      margin-top: px2rem(86);
    }
  }
  .top-3 {
    width: px2rem(108);
    height: px2rem(235);
    flex-shrink: 0;
    background-image: url("@/assets/images/activity/singer/top3.png");
    background-size: 100% 100%;
    margin-top: px2rem(32);
  }
  .avatar {
    display: flex;
    width: px2rem(54);
    height: px2rem(54);
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    margin: 0 auto;
    margin-top: px2rem(68);
    position: relative;
    .empty-avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: px2rem(54);
      width: px2rem(54);
      height: px2rem(54);
      font-size: px2rem(20);
      background-color: #504a58;
    }
  }
  .nickname {
    width: px2rem(88);
    height: px2rem(16);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    color: var(---White, #fff);

    /* T13/B */
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 0 auto;
    margin-top: px2rem(4);
    text-align: center;
  }
  .t-number,
  .v-button,
  .a-player {
    display: flex;
    justify-content: center;
  }
  .t-number {
    margin-top: px2rem(5);
  }
  .v-button {
    margin-top: px2rem(2);
  }
  .a-player {
    margin-top: px2rem(7);
  }
}
.ranking-list {
  padding: 0 px2rem(15);
  margin-top: px2rem(18);
  padding-bottom: px2rem(20);
  width: 100%;
  .rank-item {
    display: flex;
    height: px2rem(60);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    background: #380171;
    margin-bottom: px2rem(8);
    padding-right: px2rem(7);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .nickname {
      color: var(---White, #fff);

      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
  }
}
.pb103 {
  padding-bottom: px2rem(103);
}
.action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  & > div {
    display: flex;
    align-items: center;
  }
}
.score-header-block {
  display: flex;
  justify-content: flex-end;
  margin-top: px2rem(26);
  margin-bottom: px2rem(-18);
  padding: 0 px2rem(15);
}
.score-header {
  display: flex;
  color: #ab5cff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(9);
  font-style: normal;
  font-weight: 500;
  border-radius: px2rem(4);
  line-height: normal;
  padding: px2rem(3) px2rem(5) px2rem(8) px2rem(5);
  background: linear-gradient(180deg, #840efd 0.02%, #230153 99.98%);
}
.score {
  color: #d5b8ff;
  font-family: Gilroy;
  font-size: px2rem(12);
  font-style: normal;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: px2rem(15);
  height: px2rem(15);
  margin-top: px2rem(8);
}
.score-total {
  color: #fff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(12);
  font-style: normal;
  font-weight: 700;
  line-height: px2rem(15);
  height: px2rem(15);
}
.bottom-box {
  width: px2rem(391);
  height: px2rem(103);
  flex-shrink: 0;
  position: fixed;
  left: 0;
  bottom: px2rem(-12);
  z-index: 3;
  padding: px2rem(2);
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      #1f1f1f,
      #ffffff,
      #1f1f1f,
      #ffffff,
      #1f1f1f,
      #ffffff,
      #1f1f1f
    );
    border-radius: px2rem(14) px2rem(14) 0px 0px;
    z-index: -1;
  }
  .bottom-inner {
    overflow: hidden;
    position: relative;
    border-radius: px2rem(14) px2rem(14) 0px 0px;
    background: linear-gradient(180deg, #6b00d3 0%, #1d005b 87.46%);
    z-index: 1;
    width: 100%;
    height: 100%;
    .rank-item {
      display: flex;
      align-items: center;
      margin-left: px2rem(13);
      margin-right: px2rem(18);
      margin-top: px2rem(6);
      .index {
        width: px2rem(28);
        color: #fff;
        text-align: right;
        font-family: "DIN Next W1G";
        font-size: px2rem(17);
        font-style: italic;
        font-weight: 750;
        line-height: normal;
      }
      .avatar {
        display: flex;
        width: px2rem(46);
        height: px2rem(46);
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        position: relative;
      }
      .nickname {
        color: var(---White, #fff);

        /* T13/R */
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 500;
        line-height: 124%; /* 16.12px */
        flex-grow: 1;
      }

      .coin-box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        .coin-number {
          color: #ffda6d;
          font-family: "DIN Next W1G";
          font-size: px2rem(16);
          font-style: normal;
          font-weight: 700;
          line-height: 1.2;
          letter-spacing: -0.5px;
        }
      }
    }
    .bottom-desc {
      display: flex;
      height: px2rem(28);
      flex-direction: column;
      justify-content: center;

      position: absolute;
      right: px2rem(18);
      top: px2rem(43);
      text-align: right;
      font-family: Gilroy;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 10px */
    }
  }
}
.top-bg-small {
  .top3 {
    .top-1 {
      height: px2rem(228);
      background-image: url("@/assets/images/activity/singer/top1-228.png");
    }
    .top-2 {
      height: px2rem(211);
      background-image: url("@/assets/images/activity/singer/top2-228.png");
    }
    .top-3 {
      height: px2rem(211);
      background-image: url("@/assets/images/activity/singer/top3-228.png");
    }
  }
}
.top-bg-mini {
  .top3 {
    .top-1 {
      height: px2rem(212);
      background-image: url("@/assets/images/activity/singer/top1-212.png");
    }
    .top-2 {
      height: px2rem(198);
      background-image: url("@/assets/images/activity/singer/top2-212.png");
    }
    .top-3 {
      height: px2rem(198);
      background-image: url("@/assets/images/activity/singer/top3-212.png");
    }
  }
}
.rtl-html {
  .ranking-list {
    .rank-item {
      padding-right: unset;
      padding-left: px2rem(7);
      .index {
        text-align: left;
      }
    }
  }
}
</style>
