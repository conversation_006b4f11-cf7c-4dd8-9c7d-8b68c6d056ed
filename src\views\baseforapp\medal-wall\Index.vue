<template>
  <div class="app-container flex">
    <header-bar fontColor="#fff" fixedBgColor="#000" closeType="close"/>
    <div class="title">{{ $t("base_total.modal_wall_what_is") }}</div>
    <div class="desc">
      {{ $t("base_total.medal_wall_medal_effect") }}
    </div>
    <img src="@/assets/images/medal-wall/dogtag-card.png" alt="" />
    <div class="img-text mb-34">
      {{ $t("base_total.designation_wall_nameplate_card") }}
    </div>
    <div class="title">{{ $t("base_total.medal_wall_what_achievement") }}</div>
    <div class="desc">
      {{ $t("base_total.medal_wall_target_reward") }}
    </div>
    <img src="@/assets/images/medal-wall/badge.png" alt="" class="mb-34" />
    <div class="title">{{ $t("base_total.medal_wall_what_event_medals") }}</div>
    <div class="desc mb-34">
      {{ $t("base_total.medal_wall_obtain_instructions") }}
    </div>
    <div class="title">
      {{ $t("base_total.medal_wall_what_privilege_medals") }}
    </div>
    <div class="desc mb-34">
      {{ $t("base_total.medal_wall_obtain_way") }}
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: #000;
  flex-direction: column;
  color: #fff;
  padding: $gap16 $gap24;
  font-size: $fontSize13;
  text-align: center;
  .title {
    font-size: $fontSize17;
    font-weight: 700;
    margin-bottom: px2rem(8);
  }
  .desc {
    color: #ffffff80;
  }
  img {
    width: 100%;
    margin: px2rem(32) 0 px2rem(12) 0;
  }
}
.mb-34 {
  margin-bottom: px2rem(34) !important;
}
::v-deep(.icon-back) {
  color: $white;
}
</style>
