<template>
  <div class="app-container page-guild-home">
    <div class="page-bg">
      <img class="header-img" src="@/assets/images/guild/bg-icon.png" alt="">
    </div>
    <header-bar is-scroll-change-bg close-type="close"/>
    <main>
      <!--      公会信息      -->
      <div class="guild-info flex">
        <avatar :size="72" :url="config.avatar" border-color="#EFEFFF"/>
        <gap :gap="8"/>
        <div class="detail">
          <div class="nickname ellipsis">{{ config.nickname }}</div>
          <div class="number flex">
            <span>{{ $t('guild.agency_ID') }}: {{ config.guild_account }}</span>
            <!-- <span class="line"></span>
            <span>{{ $t('guild.members') }}: {{ config.member_cnt }}</span> -->
          </div>
        </div>
      </div>
      <!--      邀请码      -->
      <div class="invite-code-card flex">
        <img :src="langImage" alt="" @click="toggleShare">
      </div>
      <!--      成员列表      -->
      <div class="member-list-card flex" @click="toMemberUrl">
        <img class="icon-member" src="@/assets/images/guild/icon-member.png" alt="">
        <gap :gap="8" />
        <div class="member-card-body">
          <div class="member-card-title">{{ $t('guild.guild_members_list') }}</div>
          <div class="member-card-content flex">
            <img class="icon-member-group" src="@/assets/images/guild/icon-member-group.svg" alt="">
            <gap :gap="2" />
            <span>{{config.member_cnt}}</span>
          </div>
        </div>
        <gap :gap="16" />
        <img class="icon-arrow-right" src="@/assets/images/icon/icon-arrow-right-2.svg" alt="">
      </div>

      <div class="policy-panel">
        <div class="policy-card">
          <div class="policy-card-title flex">
            <div class="policy-title-text">{{ $t('guild.guild_card_title_1v1') }}</div>
            <gap :gap="4" />
            <img class="icon-question" src="@/assets/images/guild/icon-question.png" @click="toShowPolicy">
            <gap :gap="16" />
            <div class="policy-card-title-right">
              <router-link to="/guild/detail" class="policy-title-detail">{{$t('guild.common_detail')}}</router-link>
              <gap :gap="2" />
              <img class="icon-arrow-right" src="@/assets/images/icon/icon-arrow-right-2.svg" alt="">
            </div>
          </div>
          <div class="card-content">
            <div class="card-content-top">
              <div class="card-content-top-item">
                <div class="card-item-lv">
                  {{config.one2one_policy.cur_level}}
                </div>
                <div class="card-item-text">
                  {{$t('guild.guild_today_level')}}
                </div>
              </div>
              <div class="card-content-top-item card-item-gray1">
                <div class="card-item-lv blackfont">
                  {{formatNum(config.one2one_policy?.artist_income).val}}{{ formatNum(config.one2one_policy?.artist_income).unit }}
                </div>
                <div class="card-item-text">
                  {{$t('guild.guild_today_member_income')}}
                </div>
              </div>
              <div class="card-content-top-item card-item-gray2">
                <div class="card-item-lv blackfont">
                  {{formatNum(config.one2one_policy?.my_income).val}}{{ formatNum(config.one2one_policy?.my_income).unit }}
                </div>
                <div class="card-item-text">
                  {{$t('guild.guild_today_money')}}
                </div>
              </div>
            </div>
            <div class="card-content-c">
              <div class="card-content-c-top">
                <img class="icon-countdown" src="@/assets/images/guild/countdown.png" alt="">
                <gap :gap="2" />
                <span>{{$t('guild.guild_statistical_period')}}:{{config.one2one_policy?.time_circle}}</span>
              </div>
              <div class="card-content-c-text">
                <p class="gray" v-html="translatedMessage"></p>
              </div>
              <progress-bar
                :current="config.one2one_policy?.cur"
                :total="config.one2one_policy?.max"
              />
            </div>
          </div>
          <div class="policy-card-title flex">
            <div class="policy-title-text">{{$t('guild.guild_card_title_party')}}</div>
            <gap :gap="4" />
            <img class="icon-question" src="@/assets/images/guild/icon-question.png" @click="showPartyPolicy=true">
            <gap :gap="16" />
            <div class="policy-card-title-right">
              <router-link to="/guild/detail" class="policy-title-detail">{{$t('guild.common_detail')}}</router-link>
              <gap :gap="2" />
              <img class="icon-arrow-right" src="@/assets/images/icon/icon-arrow-right-2.svg" alt="">
            </div>
          </div>
          <div class="party-content">
            <div class="party-content-item">
              <div class="party-content-item-num">{{formatNum(config.party_policy.artist_income).val}}{{ formatNum(config.party_policy.artist_income).unit }}</div>
              <div class="party-content-item-text">{{$t('guild.guild_party_reached_gift')}}</div>
            </div>
            <div class="party-content-item">
              <div class="party-content-item-num">{{formatNum(config.party_policy.artist_reach_num).val}}{{ formatNum(config.party_policy.artist_reach_num).unit }}</div>
              <div class="party-content-item-text">{{$t('guild.guild_party_reached_member')}}</div>
            </div>
          </div>
        </div>
      </div>
      <van-popup  position="bottom" v-model:show="showPolicy" class="pop">
        <div v-html="policyHtml"></div>
        <div class="tips">{{$t('guild.guild_rule_tips')}}（UTC{{ config.utc }}）</div>
        <div class="table-title">
          {{$t('guild.guild_commission_rate')}}
        </div>
        <table style="width:100%">
          <tr class="table-tr">
            <th>{{$t('guild.guild_gear')}}</th>
            <th>{{$t('guild.guild_revenue_cap')}}</th>
            <th>{{$t('guild.guild_lower_limit')}}</th>
            <th>{{$t('guild.guild_proportion')}}</th>
          </tr>
          <tr class="table-tr" v-for="item in config.one2one_config" :key="item.id">
            <td>{{item.level}}</td>
            <td>{{item.max}}</td>
            <td>{{item.min}}</td>
            <td>{{item.perc}}%</td>
          </tr>
        </table>
        <van-button class="okbtn" type="primary" @click="showPolicy = false">{{$t('common.common_comfirm')}}</van-button>
      </van-popup>
      <van-popup  position="bottom" v-model:show="showPartyPolicy" class="pop">
        <div v-html="partyPolicyHtml"></div>
        <van-button class="okbtn" type="primary" @click="showPartyPolicy = false">{{$t('common.common_comfirm')}}</van-button>
      </van-popup>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick,computed } from 'vue'
import clipboard from "@/utils/clipboard";
import guildApi from '@/api/guild.js'
import { i18n, checkLang } from '@/i18n/index.js'
import { useRouter } from 'vue-router';
import { numberWithUnit } from '@/utils/util';

import en from '@/assets/images/guild/en/banner.png'
import ar from '@/assets/images/guild/ar/banner.png'
import es from '@/assets/images/guild/es/banner.png'
import id from '@/assets/images/guild/id/banner.png'
import pt from '@/assets/images/guild/pt/banner.png'
import tr from '@/assets/images/guild/tr/banner.png'
import zh_cn from '@/assets/images/guild/zh_cn/banner.png'
import zh_tw from '@/assets/images/guild/zh_tw/banner.png'

const { proxy } = getCurrentInstance()
const langMap = {
  'en': en,
  'ar': ar,
  'es': es,
  'id': id,
  'pt': pt,
  'tr': tr,
  'zh_cn': zh_cn,
  'zh_tw': zh_tw
}

const langImage = computed(() => {
  const currentLang = checkLang(); // 假设返回类似 'zh_cn'
  return langMap[currentLang] || en; // 默认英文
})

const router = useRouter();
const t = i18n.global.t
const showPolicy = ref(false)
const showPartyPolicy = ref(false)
const config = ref({
  avatar: "", // 头像
  nickname: "", // 昵称
  guild_account: "", // 公会id
  member_cnt: 0, // 成员数
  invite_code: "", // 邀请码
  one2one_config: [],
  one2one_policy: {
    artist_income: 0, // 主播收益
    my_income: 0, // 我的收益
    cur_level: "", // 当前等级
    time_circle: "", // 开始结束的时间周期
    min: 0, // 最小值
    max: 0 // 最大值
  },
  party_policy: {}
})
const toggleShare = () => {
  router.push({
    path: '/invite/index',
    query: {
      isBack: true
    }
  })
}
const toShowPolicy = () => {
  showPolicy.value = true
}
const formatNum = (val) => {
  return numberWithUnit(val)
}
const getConfig = async () => {
  const response = await guildApi.my_guild()
  if (response.code === 200) {
    config.value = response.data || {}
  }
}
const copyText = (event) => {
  const text = t('guild.download_link') + ':https://www.siyachat.com/ ' + t('guild.invitation_code') + ':' + config.value.invite_code
  const msg = t('guild.common_copy_success')
  clipboard(text,event,msg)
}
const policyHtml = computed(() => {
  let str = t('guild.guild_1v1_policy')
  let arr = str.split('\n')
  let html = ''
  arr.forEach((item, index) => {
    if(index === 0) html += `<p style="font-size:${proxy.$pxToRemPx(17)}px;text-align:center;font-weight:700;color:#000">${item}</p>`
    else html += `<p style="margin-top:${proxy.$pxToRemPx(12)}px">${item}</p>`
  })
  return html
})
const partyPolicyHtml = computed(() => {
  let str = t('guild.guild_party_policy')
  let arr = str.split('\n')
  let html = ''
  arr.forEach((item, index) => {
    if(index === 0) html += `<p style="font-size:${proxy.$pxToRemPx(17)}px;text-align:center;font-weight:700;color:#000">${item}</p>`
    else html += `<p style="margin-top:${proxy.$pxToRemPx(12)}px">${item}</p>`
  })
  return html
})
const translatedMessage = computed(() => {
  let message = ""; // 获取翻译后的文本
  if(config.value.one2one_policy.is_reach_before) message = t('guild.guild_upgrade_tomorrow');
  else message = t('guild.guild_upgrade_today');
  if(!config.value.one2one_policy.next_level && config.value.one2one_policy.is_reach_before) {
    message = t('guild.guild_upgrade_max')
    return message;
  }
  let values = [config.value.one2one_policy.cur_level,config.value.one2one_policy.max - config.value.one2one_policy.cur]; // 替换 __X__ 的值
  if(config.value.one2one_policy.is_reach_before) values.push(config.value.one2one_policy.next_level)
  const colors = ['#5F56ED','#000000', '#5F56ED']; // 每个 __X__ 的颜色
  values.forEach((value, index) => {
    message = message.replace(
      '__X__',
      `<span style="color: ${colors[index]};font-weight:700">${value}</span>`
    );
  });
  return message;
})
const toMemberUrl = () => {
  router.push('/guild/member') 
}
onMounted(() => {
  getConfig()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.page-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: px2rem(390);
  height: 100vw;
  background: linear-gradient(0deg, rgba(130, 132, 236, 0) 50%, #8284EC 100%);

  .header-img {
    position: absolute;
    top: px2rem(60);
    right: 0;
    width: px2rem(101);
    height: px2rem(144);
  }
}

.guild-info {
  margin: $gap12 $gap16 $gap20;
  position: relative;
  .detail {
    flex: 1;

    .nickname {
      width: px2rem(270);
      margin-bottom: px2rem(4);
      font-size: $fontSize17;
      font-weight: $fontWeightBold;
      line-height: px2rem(21);
      color: $fontColorB0;
    }

    .number {
      font-size: $fontSize13;
      line-height: px2rem(16);
      color: $fontColorB2;

      .line {
        width: px2rem(1);
        height: px2rem(8);
        margin: 0 $gap8;
        background-color: $fontColorB3;
      }
    }
  }
}

.invite-code-card {
  flex-wrap: nowrap;
  margin: 0 $gap16 $gap12;
  position: relative;
  height: px2rem(90);
  &>img {
    width: 100%;
    border-radius: $radius12;
    height: 100%;
  }
  .invite-code {
    font-size: px2rem(20);
    font-weight: $fontWeightBold;
    line-height: px2rem(24);
    color: $white;

    span {
      color: rgba($white, .64);

      @include fontSize13;
    }
  }

  .invite-tips {
    margin-bottom: $gap2;
    color: rgba($white, .64);

    @include fontSize13;
  }

  .invite-button {
    flex-wrap: nowrap;
    height: px2rem(36);
    padding: 0 $gap8;
    margin-left: auto;
    background-color: $white;
    border-radius: $radius10;
    white-space: nowrap;
    display: flex;

    img {
      width: px2rem(16);
      height: px2rem(16);
    }

    span {
      font-weight: $fontWeightBold;
      @include fontSize13;
    }
  }
}

.member-list-card {
  flex-wrap: nowrap;
  padding: $gap8;
  margin: 0 $gap16 $gap12;
  background-color: $white;
  border-radius: $radius12;
  position: relative;
  align-items: center;
  .icon-member {
    width: px2rem(50);
    height: px2rem(50);
    transform: translateY($gap4);
  }

  .member-card-body {
    flex: 1;
  }

  .member-card-title {
    margin-bottom: $gap4;
    font-weight: $fontWeightBold;

    @include fontSize15;
  }

  .member-card-content {
    width: fit-content;
    padding: $gap1 $gap4;
    font-size: 0;
    border-radius: $radius12;
    background-color: $bgColorB5;

    .icon-member-group {
      width: px2rem(12);
      height: px2rem(12);
    }

    span {
      color: $fontColorB2;

      @include fontSize11;
    }
  }

  .icon-arrow-right {
    width: px2rem(12);
    height: px2rem(12);
  }
}

.policy-panel {
  padding: $gap20 $gap16;
  background-color: $white;
  border-radius: px2rem(26) px2rem(26) 0 0;
  position: relative;
  height: fit-content;
}

.policy-card {
  .policy-card-title {
    flex-wrap: nowrap;
  }

  .policy-title-text {
    font-weight: $fontWeightBold;

    @include fontSize17;
  }

  .icon-question {
    width: px2rem(20);
    height: px2rem(20);
  }

  .policy-title-detail {
    color: $fontColorB3;

    @include fontSize13;
  }

  .icon-arrow-right {
    width: px2rem(12);
    height: px2rem(12);
  }
  .icon-countdown {
    width: px2rem(12);
    height: px2rem(12);
  }
  .policy-card-title-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center
  }
  .card-content {
    background-color: #EBE8FF;
    padding: px2rem(12) px2rem(8);
    margin-top: px2rem(12);
    border-radius: $radius16;
    margin-bottom: px2rem(24);
    .card-content-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: px2rem(12);
      .card-content-top-item {
        // width: 33vw;
        height: px2rem(96);
        flex: 1;
        display: inline-block;
        text-align: center;
        padding: px2rem(16) px2rem(8);
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .card-item-lv {
          font-size: px2rem(28);
          font-weight: 900;
          color: $mainColor;
          margin-bottom: px2rem(4);
        }
        .blackfont{ 
          color: $fontColorB0;
        }
        .card-item-text {
          @include fontSize13;
          color: $fontColorB2;
          line-height: 1;
        }
      } 
      .card-item-gray1 {
        background-color: #00000005;
        border-top-left-radius: $radius12;
        border-bottom-left-radius: $radius12;
        position: relative;
        &::after {
          content: '';
          width: px2rem(1);
          height: px2rem(32);
          background-color: $fontColorB4;
          position: absolute;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .card-item-gray2 {
        background-color: #00000005;
        border-top-right-radius: $radius12;
        border-bottom-right-radius: $radius12; 
      }
    }
    .card-content-c {
      background-color: #FBFAFF;
      padding: px2rem(16) px2rem(12);
      border-radius:$radius16;
      .card-content-c-top {
        color: $fontColorB3;
        @include fontSize10;
        display: flex;
      }
      .card-content-c-text {
        @include fontSize13;
        margin-top: px2rem(16);
        .gray {
          color: $fontColorB2;
          margin-bottom: px2rem(8);
        }
        .maincolor{
          color: $mainColor;
        }
      }
    }
  }
  .party-content {
    background-color: #F3E8FF;
    border-radius: $radius16;
    display: flex;
    margin-top: px2rem(12);
    .party-content-item {
      width: 50vw;
      text-align: center;
      padding: px2rem(16) px2rem(8);
      position: relative;
      .party-content-item-num {
        font-size: px2rem(28);
        font-weight: 900;
      } 
      .party-content-item-text {
        font-size: px2rem(13);
        color: $fontColorB2;
      }
      &:first-of-type::after {
        content: '';
        width: px2rem(1);
        height: px2rem(32);
        background-color: $fontColorB4;
        position: absolute;
        right: -1px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
.pop {
  font-size: $fontSize13;
  color: $fontColorB2;
  padding: px2rem(24) px2rem(16) px2rem(32);
  border-radius: px2rem(26) px2rem(26) 0 0;
  .okbtn {
    width: 100%;
    margin-top: px2rem(16);
    font-weight: $fontWeightBold;
  }
  .tips {
    color: #FF4771;
    margin-top: px2rem(6);
  }
  .table-title {
    font-size: $fontSize17;
    font-weight: $fontWeightBold;
    color: $fontColorB0;
    text-align: center;
    margin-top: px2rem(16);
    margin-bottom: px2rem(16);
  }
  .table-tr {
    text-align: center;
    th {
      padding: px2rem(12) px2rem(8);
      border: 1px solid #EBE8FF; 
    }
    td {
      border: 1px solid #EBE8FF; 
      padding: px2rem(12) px2rem(8);
    }
  }
}
.pop table {
  border-collapse: separate;
  border-spacing: 0;
  /* 设置表格整体的圆角 */
  border-radius: px2rem(12);
  /* 设置表格的边框为 1px */
  border: 1px solid #EBE8FF;
  overflow: hidden;
}

.pop table th:first-child {
  /* 设置表格左上角的圆角 */
  border-top-left-radius: px2rem(12);
}

.pop table th:last-child {
  /* 设置表格右上角的圆角 */
  border-top-right-radius: px2rem(12);
}

.pop table tr:last-child td:first-child {
  /* 设置表格左下角的圆角 */
  border-bottom-left-radius: px2rem(12);
}

.pop table tr:last-child td:last-child {
  /* 设置表格右下角的圆角 */
  border-bottom-right-radius: px2rem(12);
}

.pop table th,
.pop table td {
  /* 设置单元格的边框为 1px */
  // border: 1px solid #EBE8FF;
  border-style: solid;
  padding: px2rem(12) px2rem(8);
}

.pop table th {
  border-bottom: 0;
}

.pop table tr td {
  border-top: 0;
}
</style>
