<template>
  <div class="user-cell">
    <div class="avatar">
      <img :src="avatar" />
    </div>
    <div class="content">
      <div class="user-info">
        <div class="nickname clamp-1">{{ nickname }}</div>
        <div class="age">
          <gender-age :gender="gender" :age="age"></gender-age>
        </div>
      </div>
      <div class="user-id">
        <span>ID:</span><span>{{ userId }}</span>
      </div>
      <div class="desc">
        <div class="desc-content">
          {{ desc }}
        </div>
        <div class="desc-content f-s-0" v-if="showCoin">
          {{ formatCount(coinNumber) }}
        </div>
        <div class="coin">
          <Coin v-if="showCoin" :gender="coinGender"></Coin>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import Coin from "./Coin.vue";
import GenderAge from "./GenderAge.vue";
import { formatCount } from "@/utils/util";

const props = defineProps({
  nickname: {
    type: String,
    default: "",
  },
  avatar: {
    type: String,
    default: "",
  },
  gender: {
    type: Number,
    default: 1, // 1 男或11 女
  },
  userId: {
    type: String,
    default: "",
  },
  age: {
    type: Number,
    default: 0,
  },
  desc: {
    type: String,
    default: "",
  },
  showCoin: {
    type: Boolean,
    default: false,
  },
  coinGender: {
    type: Number,
    default: 1, // 1 男或11 女
  },
  coinNumber: {
    type: Number,
    default: 0,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  overflow: hidden;
}
.f-s-0 {
  flex-shrink: 0;
}
.user-cell {
  display: flex;
  align-items: center;
  gap: px2rem(8);

  .avatar {
    width: px2rem(56);
    height: px2rem(56);
    border-radius: px2rem(20);
    overflow: hidden;
    flex-shrink: 0;
    img {
      width: px2rem(56);
      height: px2rem(56);
      background-color: #eee;
      object-fit: cover;
    }
  }

  .content {
    width: px2rem(278);
    .user-info {
      display: flex;
      align-items: center;
      gap: px2rem(4);
      .nickname {
        color: var(---B0-Black, #000);

        /* T15/B */
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        height: px2rem(18);
        line-height: px2rem(18);
      }

      .age {
        color: var(---C1, #5f56ed);

        /* T11/R */
        font-family: Gilroy;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 500;
        line-height: 120%; /* 13.2px */
      }
    }

    .user-id {
      color: var(---B3, #999);
      display: flex;

      /* T11/R */
      font-family: Gilroy;
      font-size: px2rem(11);
      height: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 120%; /* 13.2px */
      margin-top: px2rem(4);
    }

    .desc {
      display: flex;
      align-items: center;
      gap: px2rem(2);
      height: px2rem(16);
      margin-top: px2rem(4);
      .desc-content {
        overflow: hidden;
        color: var(---B2, #666);
        text-overflow: ellipsis;

        /* T11/R */
        font-family: Gilroy;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 500;
        line-height: 120%; /* 13.2px */
      }
      .coin {
        font-size: px2rem(16);
      }
    }
  }
}
</style>
