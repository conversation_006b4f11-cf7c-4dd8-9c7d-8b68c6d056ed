<template>
  <div class="container flex">
    <header-bar
      :title="t('rules.pk_detail')"
      close-type="close"
      fontColor="#fff"
      fixedBgColor="#9156E0"
      isScrollChangeBg
    >
      <template #right>
        <img
          class="close"
          @click="handleClose"
          src="@/assets/images/game/close.png"
          alt=""
      /></template>
    </header-bar>
    <div class="content">
      <div class="rule-content" v-html="formattedRuleText"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed } from "vue";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();

const formattedRuleText = computed(() => {
  const lines = t("rules.pk_info3");
  return lines.replace(/\n/g, "<br>");
});
const handleClose = () => {
  proxy.$siyaApp("closeWebview");
};

</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  background-image: url("@/assets/images/rules/pk/bg.jpg"),
    linear-gradient(#a87cc6, #a87cc6);
  background-repeat: no-repeat, no-repeat;
  background-position: top center, top center;
  color: #fff;
  flex-direction: column;
  height: 100vh;
  .close {
    width: px2rem(24);
    height: px2rem(24);
  }
  .content {
    flex: 1;
    overflow-y: scroll;
    padding-bottom: px2rem(10);
    position: relative;
  }
  .rule-content {
    padding: px2rem(8) px2rem(16);
    margin-bottom: px2rem(10);
    font-size: $fontSize13;
    line-height: 124%;
  }
  .pk-content {
    padding: 0 px2rem(24);
    width: 100vw;
  }
}

::v-deep(.header-bar .header-bar-fixed .header-content .title) {
  text-align: center;
}
</style>
