<template>
  <div class="app-container flex">
    <header class="flex">
      <div class="header-cell">
        <div class="balance-amount flex">
          <span>{{ numberWithCommas(config.balance) }}</span>
          <gap :gap="1"></gap>
          <img class="icon-diamond" src="@/assets/images/common/diamond.png" alt="">
        </div>
        <div class="balance-title">{{ $t('income.record_balance_title') }}</div>
      </div>
      <div class="header-cell">
        <div class="balance-amount flex">
          <span>{{ numberWithCommas(config.today_income) }}</span>
          <gap :gap="1"></gap>
          <img class="icon-diamond" src="@/assets/images/common/diamond.png" alt="">
        </div>
        <div class="balance-title">{{ $t('income.record_today_title') }}</div>
      </div>
    </header>
    <main>
      <van-list
          class="list-wrap"
          v-model:loading="isLoading"
          :finished="finished"
          finished-text=""
          loading-text="loading..."
          :immediate-check="false"
          @load="getList"
      >
        <div>
          <div class="time-picker flex" @click="toggleDateDialog(true)">
            <span>{{ currentDate[0] }}-{{ currentDate[1] }}</span>
            <gap :gap="5"></gap>
            <img class="icon-arrow" src="@/assets/images/icon/icon-arrow-down-black.svg" alt="">
          </div>
          <div class="cell flex"
               v-for="(cell, idx) in resultList" :key="idx"
               v-if="!isEmpty"
          >
            <div class="cell-left">
              <div class="title">
                <span>{{ cell.name }}</span>
                <span class="active"
                      v-if="cell.nickname"
                      @click="goUserDetail(cell.user_id)"
                >
                  @{{ cell.nickname }}
                </span>
              </div>
              <div class="time">{{ formatDate(cell.create_time) }}</div>
            </div>
            <div class="cell-right flex">
              <img class="icon-coin" src="@/assets/images/common/diamond.png" alt="">
              <gap :gap="2" />
              <coin-number
                  class="amount"
                  :class="{active: cell.value.includes('+')}"
                  :num="cell.value"
                  :decimal="1"
              />
            </div>
          </div>
          <Empty v-else class="empty" :tips="$t('common.common_content_yet')"></Empty>
        </div>
      </van-list>
    </main>
  </div>
  <van-popup v-model:show="dateDialogShow"
             position="bottom"
             @click-overlay="toggleDateDialog(false)"
  >
    <van-date-picker
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        :columns-type="['year', 'month']"
        @confirm="handleChangeDate"
        @cancel="toggleDateDialog(false)"
    />
  </van-popup>
</template>

<script setup>
import { getCurrentInstance, onMounted, ref, computed } from 'vue'
import dayjs from 'dayjs'
import walletApi from '@/api/wallet.js'
import { formatDate, numberWithCommas } from '@/utils/util.js'

const { proxy } = getCurrentInstance()

const config = ref({})
const resultList = ref([])
const page = ref(1)
const pageSize = 20
const isLoading = ref(false)
const finished = ref(false)
const isEmpty = ref(false)
const getList = async () => {
  isLoading.value = true
  const response = await walletApi.diamond_record({
    page: page.value,
    size: pageSize,
    search_time: searchTime.value,
  })
  if (response.code === 200) {
    config.value = response.data
    const list = response.data.items || []
    resultList.value = resultList.value.concat(list)

    finished.value = list.length < pageSize
    isEmpty.value = resultList.value.length === 0
    page.value += 1
    // 加载状态结束
    isLoading.value = false
  }
}

const currentDate = ref(['2024', '01']);
const searchTime = computed(() => {
  const [y, m] = currentDate.value
  return dayjs(`${y}/${m}/1`).valueOf()
})
const maxDate = ref(new Date())
const minDate = ref(new Date())
const handleChangeDate = (e) => {
  currentDate.value = e.selectedValues
  resetData()
  getList()
  toggleDateDialog(false)
}

const resetData = () => {
  resultList.value = []
  page.value = 1
}

const dateDialogShow = ref(false)
const toggleDateDialog = (bool) => {
  dateDialogShow.value = bool
}

const goUserDetail = (userId) => {
  if (!userId) return
  proxy.$siyaApp('goUserDetail', { userId })
}

onMounted(() => {
  const date = dayjs()
  maxDate.value = date.toDate()
  minDate.value = date.subtract(2, "months").toDate()
  currentDate.value = [date.year(), date.month() + 1]
  getList()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  flex-direction: column;
  height: 100vh;
}

main {
  flex: 1;
  width: 100vw;
  overflow-y: scroll;
  padding: $gap16;
}

.active {
  color: $mainColor !important;
}

header {
  width: 100%;
  padding: $gap32 $gap12 $gap24;
}

.header-cell {
  flex: 1;

  .balance-title {
    margin-top: px2rem(4);
    font-size: px2rem(11);
    line-height: px2rem(13);
    text-align: center;
    color: rgba(0, 0, 0, .4);
  }

  .balance-amount {
    justify-content: center;
    font-size: px2rem(24);
    font-weight: $fontWeightBold;
    color: rgba(0, 0, 0, .8);
  }

  .icon-diamond {
    width: px2rem(26);
    height: px2rem(26);
  }
}

.list-wrap {
  overflow: hidden;
  background-color: #fff;
  border-radius: $radius16;
}

.time-picker {
  justify-content: center;
  width: px2rem(82);
  height: px2rem(30);
  margin: px2rem(10) px2rem(14);
  font-size: px2rem(13);
  color: $fontColorB1;
  background: #F9F9F9;
  border: 0.5px solid #F2F2F2;
  border-radius: $radius8;

  .icon-arrow {
    width: px2rem(12);
    height: px2rem(12);
  }
}

.cell {
  justify-content: space-between;
  //height: px2rem(80);
  padding: px2rem(19) px2rem(14);
  background-color: $white;
  border-top: 0.5px solid #F2F2F2;

  .time {
    font-size: px2rem(13);
    color: $fontColorB3;
  }

  .title {
    width: px2rem(250);
    margin-bottom: $gap8;
    font-size: px2rem(15);
    font-weight: $fontWeightBold;
    color: $fontColorB1;
  }

  .icon-coin {
    width: px2rem(16);
    height: px2rem(16);
  }

  .amount {
    font-size: px2rem(17);
    line-height: px2rem(21);
    font-weight: $fontWeightBold;
    color: $fontColorB3;
  }
}

.empty {
  padding-bottom: px2rem(110);
}
</style>
