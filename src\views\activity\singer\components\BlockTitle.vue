<template>
  <div class="block-title" :style="style">
    <gap :gap="showLine ? lineGap : 0"></gap>
    <div class="content">
      <gap :gap="iconGap"></gap>
      <span>{{ title }}</span>
      <gap :gap="iconGap"></gap>
    </div>
    <gap :gap="showLine ? lineGap : 0"></gap>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  title: {
    type: String,
  },
  showLine: {
    type: Boolean,
    default: true,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  lineWidth: {
    type: Number,
    default: 30,
  },
  iconSize: {
    type: String,
    default: "large",
  },
  fontSize: {
    type: Number,
    default: 22,
  },
  iconGap: {
    type: Number,
    default: 12,
  },
  lineGap: {
    type: Number,
    default: 4,
  },
});
const iconSizeMap = {
  large: [36, 30],
  small: [26, 22],
};
const style = computed(() => {
  const [w, h] = iconSizeMap[props.iconSize];
  const iconWidth = props.showIcon ? proxy.$pxToRemPx(w || 36) : 0;
  const iconHeight = proxy.$pxToRemPx(h || 30);
  const lineWidth = props.showLine ? proxy.$pxToRemPx(props.lineWidth) : 0;
  const fontSize = proxy.$pxToRemPx(props.fontSize);

  return {
    "--block-title-font-size": `${fontSize}px`,
    "--block-title-line-width": `${lineWidth}px`,
    "--block-title-icon-width": `${iconWidth}px`,
    "--block-title-icon-height": `${iconHeight}px`,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-title {
  display: flex;
  align-items: center;
  width: fit-content;
  margin: 0 auto;
  &::before,
  &::after {
    content: "";
    width: var(--block-title-line-width);
    height: px2rem(2);
    border-radius: px2rem(2);
    background: linear-gradient(90deg, rgba(252, 95, 255, 0) 0%, #fb43ff 100%);
    display: block;
    flex-shrink: 0;
  }
  &::after {
    transform: scaleX(-1);
  }
  .content {
    color: #fff;
    text-align: center;
    -webkit-text-stroke-width: px2rem(1);
    -webkit-text-stroke-color: #c927ff;
    font-family: Gilroy;
    font-size: var(--block-title-font-size);
    font-style: normal;
    font-weight: 900;
    line-height: 100%; /* 18px */
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &::before,
    &::after {
      content: "";
      width: var(--block-title-icon-width);
      height: var(--block-title-icon-height);
      flex-shrink: 0;
      background-size: 100% 100%;
      background-image: url("@/assets/images/activity/singer/music-group.png");
    }
    &::after {
      transform: scaleX(-1);
    }
    div {
      flex-shrink: 0;
    }
  }
}

.lang-zh_cn,
.lang-zh_tw {
  .block-title {
    .content {
      background: linear-gradient(180deg, #ffdeff 20.79%, #fb3aff 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      -webkit-text-stroke-width: unset;
      -webkit-text-stroke-color: unset;
    }
  }
}

.rtl-html {
  .block-title {
    &::before {
      transform: scaleX(-1);
    }
    &::after {
      transform: scaleX(1);
    }
  }
}
</style>
