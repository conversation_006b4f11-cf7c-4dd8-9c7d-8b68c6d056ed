<template>
  <div class="app-container page-guild-home">
    <header-bar is-scroll-change-bg close-type="back" :title="t('guild.guild_members_list')"/>
    <main ref="mainRef" @touchmove="hideKeyboard">
      <div class="gapp" :style="`display:${isMainScrolled?'block':'none'}`"></div>
      <div class="sticky-search">
        <van-search
          v-model="params.account"
          round
          :placeholder="$t('guild.guild_search_placeholder')"
          enterkeyhint="search"
          @search="onInput"
          @clear="onSearch"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @input="onInput"
        >
          <template #left-icon>
            <img src="@/assets/images/common/search.png">
          </template>
        </van-search>
        <div class="filter-time-content">
          <div class="filter-time" @click="showTimeSelect=true">
            <span>{{dateStr}}</span>
            <gap :gap="4" />
            <img class="filter-time-img" src="@/assets/images/common/downArrow.png" alt="" />
          </div>
          <gap :gap="14" />
          <div class="filter-time" @click="sortChange">
            <span>{{$t('guild.guild_income')}}</span>
            <gap :gap="2" />
            <img class="filter-sort-img" :src="params.sort===1?upSort:downSort" alt="" />
          </div>
        </div>
      </div>
      <van-popup position="bottom" v-model:show="showTimeSelect" class="pop">
        <timeSelect @confirm="checkDate" />
      </van-popup>
      <div class="nodata" v-if="isWaiting">
        <img src="@/assets/images/guild/calculate.png" alt="">
        <p>{{ $t('guild.guild_calculating') }}</p>
      </div>
      <div class="nodata" v-else-if="isEmpty" @click="hideKeyboard">
        <img src="@/assets/images/common/emptyh5.png" alt="">
        <p>{{ $t('guild.guild_search_empty') }}</p>
      </div>
      <div class="member-list" v-else @touchmove="hideKeyboard" @click="hideKeyboard">
        <van-pull-refresh v-model="isLoading" @refresh="onRefresh">
          <template #loading>
            <van-loading type="spinner" />
          </template>
          <template #loosing>
            <van-loading type="spinner" />
          </template>
          <template #pulling>
            <van-loading type="spinner" />
          </template>
          <van-list
            v-model:loading="isLoading"
            :finished="finished"
            @load="onLoad"
          >
          <template #loading>
          </template>
            <div :class="{'member-item':true,'opacity-48':item.ban_status}" v-for="item in resultList" :key="item.user_account" @click="toDetail(item.user_account)">
              <div class="member-item-top">
                <div class="member-item-top-left">
                  <div class="circular-image-container">
                    <img :src="item.avatar" class="avatar" />
                    <div class="dot" v-if="item.is_online"></div>
                  </div>
                  <gap :gap="8" />
                  <div class="member-info">
                    <div class="member-name">
                      <flag :flag="item.flag" :size="16"></flag>
                      <gap v-if="item.flag" :gap="6" />
                      <span v-if="!item.ban_status">{{item.nickname}}</span>
                      <span v-else style="color: #FF4771;">{{ $t('guild.common_banned') }}</span>
                    </div>
                    <div class="member-id">
                      <span>ID</span>
                      <span>:</span>
                      <span>{{item.user_account}}</span>
                      <gap :gap="2" />
                      <img src="@/assets/images/common/copy.png" alt="" @click="copyText(item.user_account,$event)">
                    </div>
                  </div>
                </div>
                <div class="member-item-top-right">
                  <div class="member-income">
                    <img src="@/assets/images/guild/diamond.png" alt="">
                    <gap :gap="4" />
                    <span>+{{formatNum(item.total_income).val}}{{ formatNum(item.total_income).unit }}</span>
                  </div>
                </div>
              </div>
              <div class="descript">{{ $t('guild.guild_income_chat') }}:<span class="bold">{{ item.chat_income }}</span></div>
              <div class="descript">{{ $t('guild.guild_income_party') }}:<span class="bold">{{ item.party_income }}</span></div>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick,computed,reactive,onBeforeUnmount  } from 'vue'
import guildApi from '@/api/guild.js'
import { i18n } from '@/i18n/index.js'
import downSort from '@/assets/images/common/downsort.png';
import upSort from '@/assets/images/common/upsort.png';
import {getTimeRange} from '@/utils/util.js'
import clipboard from "@/utils/clipboard";
import timeSelect from './components/timeSelect.vue'
import { numberWithUnit } from '@/utils/util';

const { proxy } = getCurrentInstance()
const t = i18n.global.t

const params = reactive({
  page: 1,
  size: 20,
  sort: 1,
  account: "",
  startTs: "",
  endTs: "",
  timetype: 8
})
const isKeyboardOpen = ref(false);
const isWaiting = ref(false)
const resultList = ref([])
const isLoading = ref(false)
const isEmpty = ref(false)
const finished = ref(false)
const isRefreshing = ref(false) // 添加刷新状态
const showTimeSelect = ref(false)
const dateStr = ref(`${t('guild.common_last_30_day')}`)
const mainRef = ref(null);
const isMainScrolled = ref(false);
const timer = ref(null);

const handleMainScroll = () => {
  if (mainRef.value) {
    isMainScrolled.value = mainRef.value.scrollTop > 0;
  }
};
const formatNum = (val) => {
  return numberWithUnit(val)
}
const getList = async () => {
  isLoading.value = true
  const response = await guildApi.member_list_new(params)
  if (response.code === 200) {
    let list = response.data.list || []

    // list = list.filter(i => resultList.value.findIndex(re => re.user_id === i.user_id))
    resultList.value = resultList.value.concat(list)
    isWaiting.value = response.data.waiting
    finished.value = list.length < params.size
    params.page += 1
    isEmpty.value = resultList.value.length === 0
  } else {
    finished.value = true
  }
  // 加载状态结束
  isLoading.value = false
}
const onLoad = async () => {
  await nextTick()
  getList()
};
const onRefresh = async () => {
  // 重置列表数据和分页参数
  resultList.value = []
  params.page = 1
  finished.value = false
  isRefreshing.value = true // 开启刷新状态

  try {
    await getList() // 重新获取数据
  } catch (error) {
    console.error('Refresh error:', error);
  } finally {
    isRefreshing.value = false // 关闭刷新状态
  }
}
const copyText = (id,event) => {
  event.stopPropagation(); 
  const msg = t('guild.common_copy_success')
  clipboard(id,event,msg)
}
const checkDate = (val,str) => {
  resultList.value = []
  params.page = 1
  params.startStr = val.startStr
  params.endStr = val.endStr
  params.timeType = val.timeType
  dateStr.value = str
  showTimeSelect.value = false
  getList()
}
const sortChange = () => {
  if(params.sort === 1) params.sort = 2
  else params.sort = 1
  params.page = 1
  resultList.value = []
  getList()
}
const onSearch = () => {
  params.page = 1
  resultList.value = []
  getList() 
}
const onInput = () => {
  if (timer.value) {
    clearTimeout(timer.value);
  }
  // 设置新的计时器
  timer.value = setTimeout(() => {
    onSearch();
  }, 500); 
}
const toDetail = (id) => {
  if(isKeyboardOpen.value) return
  proxy.$router.push({
    path: `/guild/member/${id}`,
  })
}

// 处理输入框获取焦点事件
const onInputFocus = () => {
  isKeyboardOpen.value = true;
};
// 用于收起键盘
const hideKeyboard = () => {
  const inputElement = document.activeElement;
  if (inputElement && inputElement.tagName === 'INPUT') {
    inputElement.blur();
    isKeyboardOpen.value = false;
  }
};
// 处理输入框失去焦点事件
const onInputBlur = () => {
  setTimeout(() => {
    isKeyboardOpen.value = false;
  }, 500); // 弹起键盘时  点击空白区域  需要收起键盘，而不触发页面事件
};
onMounted(() => {
  params.startTs = getTimeRange(8).startTime
  params.endTs = getTimeRange(8).endTime
  getList()
  if (mainRef.value) {
    mainRef.value.addEventListener('scroll', handleMainScroll);
  }
})

onBeforeUnmount(() => {
  if (mainRef.value) {
    mainRef.value.removeEventListener('scroll', handleMainScroll);
  }
});
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding-bottom: $gap20;
  background-color: $white;
}
::v-deep(.van-pull-refresh) {
  background-color: $white; 
}
::v-deep(.van-list) {
  background-color: $bgColorB7;
}
.gapp {
  height: px2rem(6);
  width: 100vw;
  background-color: $white;
  position: sticky;
  top: 0; 
  z-index: 999; 
}
main {
  position: relative;
  overflow: hidden;
  // height: calc(100vh - px2rem(120));
  margin: 0; 
  padding: 0; 
  display: flex;
  flex-direction: column;
  &::-webkit-scrollbar {
    display: none; /* 针对 WebKit 浏览器，如 Chrome、Safari */
  }
  .sticky-search {
    position: sticky;
    top: px2rem(6); 
    padding: 0 $gap16;
    z-index: 999; 
    background-color: $white; 
    transition: top 0.3s ease; // 添加过渡效果
    margin: 0; 
  }
  ::v-deep(.van-search) {
    position: sticky;
    padding: 0;
    margin-top: px2rem(6);
    height: px2rem(42);
    .van-search__content {
      background-color: $bgColorB6;
      height: 100%;
      .van-search__field {
        height: 100%;
      }
    }
    .van-field__control {
      font-size: $fontSize17;
    }
    .van-field__left-icon {
      width: px2rem(22);
      height: px2rem(22);
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  ::v-deep(.van-search__content) {
    border-radius: px2rem(12);
  }
  .filter-time-content {
    display: flex;
    align-items: center;
    position: sticky;
    top: px2rem(40);
    padding: $gap16 $gap16 $gap8 0;
    background-color: #fff;
    .filter-time {
      font-size: $fontSize13;
      color: $mainColor;
      display: flex;
      align-items: center;
      .filter-time-img {
        width: px2rem(7);
      }
      .filter-sort-img {
        width: px2rem(13);
      }
    }
  }
  .pop {
    padding: px2rem(16);
    border-radius: px2rem(26) px2rem(26) 0 0;
    padding-bottom: px2rem(32);
    .pop-title {
      font-size: $fontSize17;
      font-weight: 700;
      text-align: center;
      margin: px2rem(16) 0;
    }
    .tab-content {
      margin-top: px2rem(16);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .tab-item {
        width: 49%;
        font-size: px2rem(16);
        color: $fontColorB4;
        background-color: $bgColorB6;
        font-weight: normal;
        margin-bottom: px2rem(10);
        text-align: center;
        padding: px2rem(10) 0;
        border-radius: px2rem(8);
        box-sizing: border-box;
        border: 1px solid transparent;
      }
    }
    .customize {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .customize-item {
        width: px2rem(159);
        padding: px2rem(10) 0;
        background-color: $bgColorB6;
        color: $fontColorB4;
        font-size: px2rem(16);
        text-align: center;
        border-radius: px2rem(8);
        box-sizing: border-box;
      }
      .divider {
        width: px2rem(16);
        height: px2rem(1);
        background-color: #D9D9D9;
      }
    }
    .okbtn {
      width: 100%;
      margin-top: px2rem(16);
      font-weight: $fontWeightBold;
    }
  }
}
.member-list {
  height: calc(100vh - px2rem(200));
  overflow-y: scroll;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .member-item {
    background-color: $white;
    margin-bottom: px2rem(8);
    padding: px2rem(12) px2rem(16);
    .member-item-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: px2rem(10);
      .member-item-top-left {
        display: flex;
        align-items: center;
        .circular-image-container {
          width: px2rem(56);
          height: px2rem(56);
          border-radius: 50%;
          position: relative;
          .dot {
            width: px2rem(10);
            height: px2rem(10);
            background: $subColorGreen;
            border-radius: 50%;
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 1;
          }
        }
        .avatar {
          width: 100%; /* 图片宽度填充容器 */
          height: 100%; /* 图片高度填充容器 */
          border-radius: 50%;
          object-fit: cover;
        }
        .member-info {
          flex: 1;
          .member-name {
            font-size: px2rem(15);
            font-weight: 700;
            display: flex;
            align-items: center;
            img {
              width: px2rem(22);
              height: px2rem(16);
            }
          }
          .member-id {
            font-size: px2rem(13);
            color: $fontColorB3;
            margin-top: px2rem(4);
            font-weight: normal;
            display: flex;
            align-items: center;
            img{
              width: px2rem(12);
            }
          }
        }
      }
      .member-item-top-right {
        .member-income {
          font-size: px2rem(17);
          font-weight: 700; 
          display: flex;
          align-items: center;
          img {
            width: px2rem(16);
          }
        }
      }
    }
    .descript {
      font-size: $fontSize13;
      color: $fontColorB2;
      margin-top: px2rem(8);
      .bold {
        font-weight: 700;
      }
    }
  }
  .opacity-48 {
    opacity: 0.48;
    background-image: url('@/assets/images/common/forbidden.png');
    background-size: 20% auto; 
    // 修改背景图片的位置为居右
    background-position: right center; 
    // 保持原有的不重复属性
    background-repeat: no-repeat;
  }
}
::v-deep(.van-pull-refresh) {
  min-height: 100%;
}
.nodata {
  height: 55vh;
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: px2rem(120);
  }
  p {
    font-size: $fontSize15;
    font-weight: 500;
    color: $fontColorB2;
  }
}
.active {
  border: 1px solid $mainColor !important;
  color: $mainColor !important;
  background-color: #F8F7FF!important;
  box-sizing: border-box;
}
</style>
