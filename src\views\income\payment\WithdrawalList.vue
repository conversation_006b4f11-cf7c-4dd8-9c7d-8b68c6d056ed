<template>
  <div class="app-container">
    <header-bar is-scroll-change-bg />
    <main>
      <div class="tips">{{ $t('withdraw.withdrawal_select_methods_tips') }}</div>
      <div class="payment-list">
        <div class="payment-cell flex"
             v-for="(item, idx) in list" :key="idx"
             @click="chooseItem(item)"
        >
          <photo :width="100" :height="50" :radius="8" :url="item.logo" fit="contain" />
          <div class="content">
            <div class="payment-name">
              {{ item.channel_name }}
            </div>
            <div class="payment-fee">
              {{ $t('withdraw.service_fee', [formatService(item)]) }}
            </div>
          </div>
          <div class="cell-right flex">
            <img class="icon-arrow" src="@/assets/images/icon/icon-arrow-right.svg" alt="">
          </div>
          <div class="cell-status"
               :class="{selected: item.withdraw_cfg_id === lastId}"
          >
            <span v-if="item.withdraw_cfg_id === lastId">{{ $t('withdraw.current_selected') }}</span>
            <span v-else-if="item.complete">{{ $t('withdraw.completed') }}</span>
            <span v-else>{{ $t('withdraw.need_complete') }}</span>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, onActivated } from 'vue'
import { useWithdrawStore } from '@/store/index.js'
import withdrawApi from '@/api/withdraw.js'
import { i18n } from '@/i18n/index.js'
import { useRouter } from 'vue-router'

const t = i18n.global.t
const { proxy } = getCurrentInstance()
const router = useRouter()
const withdrawStore = useWithdrawStore()

const list = ref([])
const lastId = ref(0)
const getList = async () => {
  const params = {
    id: withdrawStore.$state.currentProductId,
  }
  const response = await withdrawApi.withdraw_channel(params)
  if (response.code === 200) {
    list.value = response.data.items
    lastId.value = response.data.last_id

    const find = list.value?.find(i => i.type === 2)
    if (find) {
      await fetchBankList(find)
    }
  }
}
// 获取银行列表
const fetchBankList = async (bank) => {
  const params = {
    id: bank.withdraw_cfg_id,
    product_id: withdrawStore.currentProductId,
  }
  const response = await withdrawApi.bank_list(params)
  if (response.code === 200) {
    const bankList = response.data.items || []
    const findSub = bankList.find(i => i.bank_cfg_id === bank.bid)
    if (findSub) {
      const bankIdx = list.value.findIndex(i => i.id === bank.id)
      list.value[bankIdx] = {
        ...bank,
        price: findSub.price,
        actual_amount: findSub.actual_amount,
        addition: findSub.addition,
        service_amount: findSub.service_amount,
        service_charge: findSub.service_charge,
      }
    }
  }
}

// 获取已保存表单信息
const getWithdrawInfo = async (item) => {
  const params = {
    type: item.type,
    wid: item.withdraw_cfg_id,
    bid: item.bid,
  }
  const response = await withdrawApi.get_payment_info(params)
  if (response.code === 200) {
    withdrawStore.setCurrentChannelInfo({
      ...item,
      payment: response.data.payment,
    })
  }
}

const formatService = (item) => {
  let str = ''
  if (Number(item.service_charge)) str += `${Number(item.service_charge)}%`
  if (Number(item.addition)) {
    if (str.length > 0) str += ' + '
    str += `${item.currency} ${Number(item.addition)}`
  }
  if (!str) str = '0'
  return str
}

const chooseItem = async (item) => {
  // 获取保存表单信息
  await getWithdrawInfo(item)

  // 校验表单字段是否全都填写了
  const { payment, param } = withdrawStore.currentChannelInfo
  const isFinishAllField = Object.keys(payment).length === param.length

  if (item.complete && isFinishAllField) await router.push({ name: 'WithdrawalInformation' })
  else await router.push({ name: 'WithdrawalForm' })
}


onActivated(() => {
  getList()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

main {
  padding: $gap8 $gap16 $pageBottomPadding;

  .tips {
    margin: 0 $gap12 $gap12;
    color: $fontColorB3;

    @include fontSize11;
  }
}

.payment-list {
  .payment-cell {
    position: relative;
    padding: px2rem(25) $gap8;
    background-color: $white;
    border-radius: $radius16;

    &:not(:last-child) {
      margin-bottom: $gap12;
    }

    .content {
      flex: 1;
    }

    .payment-name {
      margin-bottom: $gap4;
      font-weight: $fontWeightBold;
      color: $fontColorB0;

      @include fontSize17;
    }

    .payment-fee {
      display:table;
      padding: px2rem(3.5) $gap8;
      color: $mainColor;
      background-color: rgba($mainColor, .1);
      border-radius: px2rem(37);

      @include fontSize11;
    }

    .cell-right {
      color: $fontColorB4;

      @include fontSize13;
    }

    .icon-arrow {
      width: px2rem(24);
      height: px2rem(24);
    }

    .cell-status {
      position: absolute;
      top: 0;
      right: 0;
      padding: $gap2 $gap10;
      color: $fontColorB3;
      background-color: $bgColorB6;
      border-radius: 0 $radius16 0 $radius16;

      @include fontSize10;

      &.selected {
        color: $mainColor;
        background-color: rgba($mainColor, .12);
      }
    }
  }
}
</style>
