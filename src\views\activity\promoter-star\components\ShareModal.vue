<template>
  <van-overlay
    :show="dialogShow"
    @touchmove.prevent
    @click="handleCancel()"
    z-index="1001"
  >
    <div class="base-modal-wrapper">
      <div class="show-animation" @click.stop>
        <div class="close-btn" @click="handleCancel()">
          <svg-icon icon="close" color="#999999"></svg-icon>
        </div>
        <div class="linear-bg"></div>
        <div class="container">
          <div class="title">{{ $t(`promoter_star.share_title`) }}</div>
          <div class="wrapper-content">
            <div class="content-text">
              <div>
                {{ $t(`promoter_star.share_content`) }}
              </div>
            </div>
            <div class="content-shadow"></div>
          </div>
          <div
            class="wrapper-button active"
            @click="handleCopy"
            v-track:click
            trace-key="star_ambassador_link_copy"
          >
            {{ $t(`promoter_star.copy_btn`) }}
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
import { ref, watch } from "vue";
import clipboard from "@/utils/clipboard";
import { i18n } from "@/i18n/index.js";
const t = i18n.global.t;

const emit = defineEmits(["update:show", "confirm"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const dialogShow = ref(false);

watch(
  () => props.show,
  (val) => {
    dialogShow.value = !!val;
  }
);

const handleCopy = async (event) => {
  await clipboard(
    `${location.href}?lang=${i18n.global.locale}&p0=1`,
    event,
    t("common.copy_success")
  );
  handleCancel();
};

const handleCancel = () => {
  emit("update:show", false);
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.base-modal-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  .show-animation {
    animation: show 0.3s 1;
    width: px2rem(300);
    padding: px2rem(32) $gap16 $gap16;
    background: #ffffff;
    border-radius: px2rem(26);
  }

  .linear-bg {
    border-radius: px2rem(26);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: px2rem(188);
    background-image: url("@/assets/images/promoter-star/share-bg.png");
    background-size: 100% 100%;
  }

  .close-btn {
    z-index: 9;
    width: px2rem(24);
    height: px2rem(24);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    font-size: px2rem(12);
    position: absolute;
    right: px2rem(10);
    top: px2rem(10);
  }

  .wrapper-bg {
    position: absolute;
    left: 0;
    top: px2rem(-48);
    width: px2rem(300);
    height: px2rem(168);
  }

  .icon-close {
    position: absolute;
    top: px2rem(10);
    right: px2rem(10);
    width: px2rem(24);
    height: px2rem(24);
  }

  .container {
    position: relative;
    z-index: 1;
    flex-direction: column;

    .title {
      color: #fd42a7;
      text-align: center;

      /* T24/B */
      font-family: Gilroy;
      font-size: px2rem(24);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }

  .wrapper-content {
    position: relative;
  }

  .content-text {
    color: var(---B0-Black, #000);
    text-align: center;
    margin-top: px2rem(8);

    /* T15/B */
    font-family: Gilroy;
    font-size: px2rem(15);
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    div {
      margin-bottom: $gap16;
    }
  }

  .wrapper-button {
    color: var(---White, #fff);
    text-align: center;

    /* T17/B */
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    border-radius: px2rem(16);
    background: var(---C1, #5f56ed);

    &.active {
      height: px2rem(50);
      line-height: px2rem(50);
      color: $white;
      background-color: #5f56ed;
    }
  }
}

.rtl-html {
  .close-btn {
    right: px2rem(10);
    right: unset !important;
    left: px2rem(10);
  }
}
</style>
