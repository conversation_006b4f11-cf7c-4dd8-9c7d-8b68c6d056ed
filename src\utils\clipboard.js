import Clipboard from "clipboard";

function clipboardSuccess(msg) {
  showToast(msg ? msg : "copy success");
}

function clipboardError() {
  showToast("copy fail");
}

export default function handleClipboard(text, event, msg = "") {
  return new Promise((resolve, reject) => {
    const fakeElement = document.createElement("button");
    const clipboard = new Clipboard(fakeElement, {
      text: () => text,
    });
    clipboard.on("success", () => {
      clipboardSuccess(msg);
      clipboard.destroy();
      resolve();
    });
    clipboard.on("error", () => {
      clipboardError();
      clipboard.destroy();
      reject();
    });
    clipboard.onClick(event);
  });
}
