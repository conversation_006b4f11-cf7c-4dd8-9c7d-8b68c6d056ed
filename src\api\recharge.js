import request from "@/utils/request";

export default {
  // 客户端内三方充值跳转预下单
  app_pay_pre(query) {
    return request({
      url: "/pay/pre",
      method: "post",
      data: query,
    });
  },
  // 客户端内三方充值跳转预下单
  app_vip_pay_pre(query) {
    return request({
      url: "/pay/vip_pre",
      method: "post",
      data: query,
    });
  },
  // 获取付款信息
  get_payment_info(query) {
    return request({
      url: "/pay/get_payment_info",
      method: "get",
      params: query,
    });
  },
  // 设置付款信息
  set_payment_info(query) {
    return request({
      url: "/pay/set_payment_info",
      method: "post",
      data: query,
    });
  },
};
