<template>
  <div class="app-container flex">
    <header-bar
      isScrollChangeBg
      :title="$t('pageTitle.Information')"
      closeType="close"
    >
    </header-bar>
    <main>
      <div
        class="cell-wrap"
        :class="{ error: field.isError }"
        v-for="(field, idx) in formCell"
        :key="idx"
      >
        <div class="cell-title flex">
          <div class="icon-circle"></div>
          <span>{{ field.title || field.key }}</span>
        </div>
        <van-field
          class="cell-field"
          v-model="field.value"
          :placeholder="field.placeholder"
          :border="false"
        >
          <template #left-icon v-if="field?.preText">
            <span class="area-code">{{ field.preText }}</span>
          </template>
        </van-field>
        <div class="cell-hint" v-if="field.isError">{{ field.hint }}</div>
      </div>
    </main>
    <footer>
      <div class="lock-wrap">
        <img src="@/assets/images/recharge/lock.png" alt="" />
        {{ t("recharge.recharge_data_encryption") }}
      </div>
      <div class="encrypt-desc">
        {{ t("recharge.recharge_data_encryption_desc") }}
      </div>
      <van-button
        class="submit-button"
        type="primary"
        block
        @click="handleSubmit"
        :disabled="buttonDisabled"
      >
        {{ t("recharge.recharge_next_step") }}
      </van-button>
    </footer>
  </div>
</template>

<script setup>
import { computed, ref, getCurrentInstance, onMounted } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter, useRoute } from "vue-router";
import rechargeApi from "@/api/recharge.js";
import { validatorMap } from "@/views/income/payment/utils/validator.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

// 格式化表单数据
const formCell = ref([]);
const CLIPSPAY = {
  firstName: {
    title: t("recharge.recharge_real_name"),
    placeholder: t("recharge.recharge_placeholder_real_name"),
    hint: t("recharge.recharge_placeholder_real_name"),
    value: "",
    validator: (val) => {
      return validatorMap.minAndMaxAndLetterOrChars5(val, 1, 200);
    },
  },
  fullName: {
    title: t("recharge.recharge_surname"),
    placeholder: t("recharge.recharge_placeholder_real_surname"),
    hint: t("recharge.recharge_placeholder_real_surname"),
    value: "",
    validator: (val) => {
      return validatorMap.minAndMaxAndLetterOrChars5(val, 1, 200);
    },
  },
  email: {
    title: t("withdraw.payeeInfo_email_title"),
    placeholder: t("withdraw.payeeInfo_email_placeholder", ["@"]),
    hint: t("withdraw.payeeInfo_email_hint"),
    value: "",
    validator: (val) => {
      // 符合邮箱基本格式
      return validatorMap.email(val);
    },
  },
};

// 表单校验
const validatorForm = () => {
  let errorCount = 0;
  formCell.value.forEach((cell) => {
    if (cell.validator && !cell.validator(cell.value, formCell.value)) {
      cell.isError = true;
      errorCount += 1;
    } else {
      cell.isError = false;
    }
  });
  return errorCount === 0;
};

// 提交
const handleSubmit = async () => {
  if (!validatorForm()) return;
  const params = {
    pay_cfg_id: Number(route.query?.payWayId),
    pay_scene: route.query?.payScene,
    payment: {},
  };
  formCell.value.forEach((i) => {
    params.payment[i.key] = i.value;
  });
  const response = await rechargeApi.set_payment_info(params);
  if (response.code === 200) {
    router.go(-1);
  }
};

const formatFormCell = () => {
  formCell.value = Object.keys(CLIPSPAY).map((key) => {
    return {
      key,
      ...CLIPSPAY[key],
      value: "",
    };
  });
};

// 按钮禁用状态
const buttonDisabled = computed(() => {
  return formCell.value.filter((e) => !e.value).length > 0;
});

onMounted(() => {
  formatFormCell();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.app-container {
  flex-direction: column;
}

main {
  padding: $gap20 $gap16;
  flex: 1;
  width: 100%;
  padding-bottom: px2rem(100);

  .bank-tips {
    margin-bottom: px2rem(28);
    color: $fontColorB3;

    @include fontSize13;
  }

  .cell-wrap {
    margin-bottom: px2rem(28);

    &.error {
      .cell-hint {
        color: $subColorRed;
      }

      .cell-field {
        border: 1px solid $subColorRed;
      }
    }
  }

  .payment-methods-cell {
    margin-bottom: px2rem(28);

    .payment-name {
      color: $fontColorB1;

      @include fontSize15;
    }
  }

  .cell-title {
    margin-bottom: $gap8;
    font-weight: $fontWeightBold;
    color: $fontColorB1;

    @include fontSize13;

    .icon-circle {
      width: px2rem(4);
      height: px2rem(4);
      margin: 0 $gap8;
      // background-color: $fontColorB1;
      background-color: $white;
      border-radius: 50%;
    }
  }

  .cell-field {
    min-height: px2rem(50);
    margin-bottom: $gap8;
    background-color: $white;
    border-radius: $radius16;
    .area-code {
      color: $fontColorB4;
      background: $bgColorB7;
      display: inline-block;
      width: px2rem(33);
      height: px2rem(26);
      text-align: center;
      border-radius: px2rem(8);
    }
  }

  .cell-hint {
    padding: $gap8 0;
    color: $fontColorB3;

    @include fontSize13;
  }
}

footer {
  padding: $gap16;
  width: 100%;
  font-size: $fontSize13;
  bottom: v-bind("(proxy.$appBottomBarHeight) + 'px'");
  .lock-wrap {
    color: #5f56ed;
    img {
      width: px2rem(12);
      height: px2rem(12);
    }
  }
  .encrypt-desc {
    margin: px2rem(2) 0 px2rem(12);
  }
}
</style>
