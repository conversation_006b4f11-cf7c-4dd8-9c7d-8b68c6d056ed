<template>
  <div class="info-card">
    <div class="cell">
      <div class="cell-title">
        <template v-if="isOrderDetail">{{
          $t("withdraw.withdrawal_amount")
        }}</template>
        <template v-else>{{
          $t("withdraw.estimated_withdrawal_amount")
        }}</template>
      </div>
      <div class="total-amount-field">
        {{ currentItem.price }} {{ currentItem.currency }}
      </div>
    </div>

    <div class="cell">
      <div class="cell-title flex">
        <span>{{ $t("withdraw.withdrawal_methods") }}</span>
        <template v-if="isOrderDetail">
          <div
            class="button-change"
            :class="{
              success: currentItem.statusText === 'SUCCESS',
              fail: currentItem.statusText === 'FAIL',
            }"
          >
            {{ currentItem.statusTextI18n }}
          </div>
        </template>
        <template v-else>
          <div class="button-change flex pending" @click="handleChange">
            <span>{{ $t("common.common_change") }}</span>
            <img
              class="icon-arrow"
              src="@/assets/images/icon/icon-arrow-right-purple.svg"
              alt=""
            />
          </div>
        </template>
      </div>
      <div class="method-field flex">
        <photo
          :width="100"
          :height="50"
          :radius="8"
          :url="currentItem.logo"
          fit="contain"
        />
        <gap :gap="4" />
        <div class="payment-name">{{ currentItem.channel_name }}</div>
      </div>
    </div>

    <div class="cell">
      <div class="cell-title flex">
        <span>{{ $t("withdraw.withdrawal_information") }}</span>
        <img
          class="icon-edit"
          v-if="!isOrderDetail"
          @click="handleEdit"
          src="@/assets/images/icon/icon-edit.svg"
          alt=""
        />
      </div>
      <div class="info-field">
        <div
          class="info-field-child flex"
          v-for="(field, idx) in formCell"
          :key="idx"
        >
          <div class="info-field-key">{{ field.title }}</div>
          <gap :gap="10" />
          <div class="info-field-value">
            {{
              field?.type === "select"
                ? value2Text(field)
                : (field?.orderDetailPreFn
                    ? `${field?.orderDetailPreFn(field, formCell)} `
                    : "") + field.value
            }}
          </div>
        </div>
        <div class="info-field-child flex">
          <div class="info-field-key">
            {{ $t("withdraw.estimated_service_fee") }}
          </div>
          <div class="info-field-value">
            {{ currentItem.currency }} {{ currentItem.service_amount }}
          </div>
        </div>
        <div class="info-field-child flex">
          <div class="info-field-key">
            {{ $t("withdraw.estimated_amount_received") }}
          </div>
          <div class="info-field-value">
            {{ currentItem.currency }} {{ currentItem.actual_amount }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <template v-if="isOrderDetail">
    <div class="fail-reason" v-if="currentItem.statusText === 'FAIL'">
      <div class="fail-reason-title">{{ $t("withdraw.fail_reason") }}</div>
      <div class="fail-reason-detail">
        {{ currentItem.reason }}
      </div>
    </div>
  </template>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { useWithdrawStore } from "@/store/index.js";

const emit = defineEmits(["change", "edit"]);

const withdrawStore = useWithdrawStore();
const props = defineProps({
  currentItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
});

const value2Text = (row) => {
  const targetOption =
    row.options.find((option) => option.value === row.value) || {};
  return targetOption.text;
};

// 格式化表单数据
const formCell = ref([]);
watch(
  () => props.currentItem,
  (val) => {
    formCell.value = withdrawStore.formatFormData({
      ...val,
      param: Object.keys(val.payment),
    });
  },
  {
    immediate: true,
  }
);

const isOrderDetail = computed(() => {
  return props.currentItem.status > 0;
});

const handleChange = () => {
  emit("change");
};

const handleEdit = () => {
  emit("edit");
};
</script>

<style scoped lang="scss">
@import "../assets/payment.scss";
</style>
