<template>
  <div class="app-container flex">
    <header-bar :padding="false" closeType="close" fontColor="#fff" />
    <div class="title">
      <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+lang+'/title.png'" alt="" />
    </div>
    <div class="content">
      <div class="content-out">
        <div class="content-inner">
          <div class="s-title">
            {{ t('rules.face_real_interaction') }}
          </div>
          <div class="sub-title">
            {{ t('rules.face_greet_actively') }}
          </div>
          <div>
            {{ t('rules.face_friendly_interaction') }}
          </div>
          <div class="glass">
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/yes1.png'" alt="" />
              <img class="little-icon" :src="getImageUrl(true)" alt="" />
            </div>
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/no1.png'" alt="" />
              <img class="little-icon" :src="getImageUrl(false)" alt="" />
            </div>
          </div>
          <div class="s-title">
            {{ t('rules.face_interaction_opening') }}
          </div>
          <div class="sub-title">
            {{ t('rules.face_first_sentence') }}
          </div>
          <div>
            {{ t('rules.face_enthusiasm_high_return') }}
          </div>
          <div class="glass">
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/yes2.png'" alt="" />
              <img class="hi" src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/hi.png" alt="" />
              <img class="little-icon" :src="getImageUrl(true)" alt="" />
            </div>
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/no2.png'" alt="" />
              <img class="little-icon" :src="getImageUrl(false)" alt="" />
            </div>
          </div>
          <div class="s-title">
            {{ t('rules.face_positive') }}
          </div>
          <div class="sub-title">
            {{ t('rules.face_active_high_interaction') }}
          </div>
          <div class="glass-content" v-html="t('rules.face_data_active_interaction')">
          </div>
          <div class="glass">
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/yes3.png'" alt="" />
              <img class="little-icon" :src="getImageUrl(true)" alt="" />
            </div>
            <div class="glass-inner">
              <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/no3.png'" alt="" />
              <img class="little-icon" :src="getImageUrl(false)" alt="" />
            </div>
          </div>
          <div class="s-title">
            {{ t('rules.face_face_reveal_easy') }}
          </div>
          <div class="sub-title">
            {{ t('rules.face_beauty_filter_protection') }}
          </div>
          <div>
            {{ t('rules.face_platform_beauty_features') }}
          </div>
          <div class="glass">
            <div class="glass-inner">
              <div style="display: flex;justify-content: space-between;width: 100%;">
                <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/yes4.png'" alt="" />
                <img :src="'https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/'+anLang+'/no4.png'" alt="" />
              </div>
              <img class="little-icon" :src="getImageUrl(true)" alt="" />
            </div>
          </div>
          <div class="s-title">
            {{ t('rules.face_interaction_tips') }}
          </div>
          <div class="sub-title">
            {{ t('rules.face_popular_person') }}
          </div>
          <div class="tips">
            <div class="glass-item">
              {{ t('rules.face_greet_at_start') }}
              <img class="gou" src="@/assets/images/rules/face/right.png" alt="" />
            </div>
            <div class="glass-item">
              {{ t('rules.face_smile_while_showing_face') }}
              <img class="gou" src="@/assets/images/rules/face/right.png" alt="" />
            </div>
            <div class="glass-item">
              {{ t('rules.face_ask_questions_guidance') }}
              <img class="gou" src="@/assets/images/rules/face/right.png" alt="" />
            </div>
            <div class="glass-item">
              {{ t('rules.face_patient_polite_conversation') }}
              <img class="gou" src="@/assets/images/rules/face/right.png" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed } from "vue";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();

const lang = proxy.$languageFile
const anLang = ['pt', 'es', 'tr'].includes(lang) 
  ? 'pt' 
  : lang === 'ar' 
    ? 'ar' 
    : 'en';

function getImageUrl(val) {
  if(val) {
    return new URL(
      `../../../assets/images/rules/face/${lang}/good.png`,
      import.meta.url
    ).href;
  } else {
    return new URL(
      `../../../assets/images/chat-tips/${lang}/bad.png`,
      import.meta.url
    ).href;
  }
}
onMounted(async () => {
 
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.app-container {
  background-image: url("https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/rules/face/bg.png"),
    linear-gradient(#6691FF, #6691FF);
  background-repeat: no-repeat, no-repeat;
  background-size: 100vw auto, auto;
  background-position: top center, top center;
  color: #112558;
  font-size: px2rem(15);
  .title {
    height: px2rem(68);
    width: 100vw;
    margin-top: px2rem(64);
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.glass-content {
  :deep(.bold) {
    color: #255CE9;
    font-size: px2rem(20);
    font-weight: 700;
  }
}
.content {
  padding: 0 px2rem(12) px2rem(40) px2rem(12);
  margin-top: px2rem(12);
  .content-out {
    border-radius: px2rem(16);
    border: 1px solid var(--48, rgba(255, 255, 255, 0.48));
    background: var(--32, rgba(255, 255, 255, 0.32));
    .content-inner {
      background: linear-gradient(90deg, #FDDAF9 0%, #C0EEFD 100%);
      border-radius: px2rem(16);
      padding: px2rem(16) px2rem(12);
      .s-title {
        color: #255CE9;
        text-shadow:
        -1px -1px 0 #fff,     // 白色描边 - 上左
        1px -1px 0 #fff,     // 白色描边 - 上右
        -1px  1px 0 #fff,     // 白色描边 - 下左
        1px  1px 0 #fff,     // 白色描边 - 下右
        2px 3px 0px rgba(112, 154, 194, 0.64);  // 灰
        // text-shadow: 1px 2px 0px rgba(112, 154, 194, 0.64);
        // -webkit-text-stroke-width: 1px;
        // -webkit-text-stroke-color: #FFF;
        font-family: Gilroy;
        font-size: px2rem(20);
        font-style: italic;
        font-weight: 900;
        line-height: normal;
      }
      .sub-title {
        color: #F457FF;
        font-size: px2rem(17);
        font-weight: 700;
        margin-bottom: px2rem(4);
        margin-top: px2rem(8);
      }
      .glass{
        border-radius: px2rem(12);
        background: var(--24, rgba(255, 255, 255, 0.24));
        margin-top: px2rem(12);
        margin-bottom: px2rem(24);
        padding: px2rem(20) px2rem(12);
        display: flex;
        justify-content: space-between;
        .glass-inner{
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          position: relative;
          .little-icon {
            height: px2rem(32);
            margin-top: px2rem(4);
          } 
          img{
            height: px2rem(262);
          }
          .hi {
            position: absolute;
            left: px2rem(-6);
            top: px2rem(-20);
            width: px2rem(100);
          }
        }
      }
      .tips {
        border-radius: px2rem(12);
        background: var(--24, rgba(255, 255, 255, 0.24));
        margin-top: px2rem(12);
        padding: px2rem(20);
        .glass-item {
          border-radius: px2rem(8);
          border: 1px solid #29D40D;
          background: var(---White, #FFF);
          width: 100%;
          padding: px2rem(8);
          position: relative;
          margin-bottom: px2rem(24);
          &:last-child {
            margin-bottom: 0;
          }
          .gou {
            position: absolute;
            right: px2rem(-12);
            width: px2rem(24);
            top: px2rem(-10);
          }
        }
      }
    }
  }
}
.rtl-html {
  .content .content-out .content-inner .tips .glass-item .gou {
    left: px2rem(-12);
    right: auto;
  }
}
</style>
