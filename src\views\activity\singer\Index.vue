<template>
  <div class="singer-container" v-track:exposure trace-key="singer_expo">
    <HeaderBlock></HeaderBlock>

    <!-- 音频提交页面 -->
    <SubmitBlock
      v-if="activityInfo?.stage === 0 || activityInfo?.stage === 1"
    ></SubmitBlock>
    <!-- 活动开始页面 -->
    <div
      v-if="
        activityInfo && activityInfo?.stage !== 0 && activityInfo?.stage !== 1
      "
      class="main-block"
    >
      <div class="tabs">
        <div
          class="tab-button"
          :class="tabIndex === 0 ? 'actived' : ''"
          @click="tabIndex = 0"
        >
          {{ $t("singer.good_voice") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="tab-button"
          :class="tabIndex === 1 ? 'actived' : ''"
          @click="tabIndex = 1"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 15 })"
        >
          {{ $t("singer.popularity_ranking_title") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="tab-button"
          :class="tabIndex === 2 ? 'actived' : ''"
          @click="tabIndex = 2"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 16 })"
        >
          {{ $t("singer.support_ranking_title") }}
        </div>
      </div>
      <!-- 好声音 -->
      <GoodVoice v-if="tabIndex === 0"></GoodVoice>
      <!-- 人气榜 -->
      <PopularityRanking v-if="tabIndex === 1"></PopularityRanking>
      <!-- 应援榜 -->
      <SupportRanking v-if="tabIndex === 2"></SupportRanking>
    </div>
  </div>
</template>
<script setup>
import HeaderBlock from "./components/HeaderBlock.vue";
import SubmitBlock from "./components/SubmitBlock.vue";
import GoodVoice from "./components/GoodVoice.vue";
import PopularityRanking from "./components/PopularityRanking.vue";
import SupportRanking from "./components/SupportRanking.vue";
import singerApi from "@/api/activity.js";
import { onMounted, provide, ref } from "vue";

const tabIndex = ref(0);
const activityInfo = ref(null);

provide("activityInfo", activityInfo);

const fetchData = async () => {
  const res = await singerApi.singer_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
    // Test
    // activityInfo.value.stage = 1
  }
};
onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.singer-container {
  min-height: 100vh;
  background-image: url("@/assets/images/activity/singer/index-bg.jpg");
  background-size: 100% px2rem(385);
  background-repeat: no-repeat;
  overflow: hidden;
  background-color: #230153;
  .main-block {
    background: linear-gradient(180deg, #230153 0%, #230153 100%);
    margin-top: px2rem(32);
    position: relative;
    .tabs {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: px2rem(87);
      .tab-button {
        width: px2rem(119);
        height: px2rem(44);
        border-radius: px2rem(50);
        border: px2rem(2) solid #954dff;
        padding: 0 px2rem(12);
        background: linear-gradient(
          180deg,
          #5600d3 0%,
          #39008b 34.07%,
          #18003b 100%
        );
        display: flex;
        justify-content: center;
        align-items: center;
        color: #e3d0ff;
        text-align: center;
        text-shadow: 0px 3px 1.7px rgba(0, 0, 0, 0.3);
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 700;
        line-height: 100%; /* 13px */
        transition: all 0.2s linear;
      }
      .actived {
        color: #fff;
        border: px2rem(2) solid #fff;
        background: linear-gradient(
          180deg,
          #ffa798 0%,
          #cd1ff6 47.12%,
          #6600f5 83.17%
        );

        box-shadow: 0px -4px 4px 0px rgba(44, 2, 104, 0.64) inset,
          0px 3px 3px 0px rgba(255, 255, 255, 0.66) inset;
      }
    }
  }
}
</style>
