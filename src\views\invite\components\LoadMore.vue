<template>
  <div ref="loadMoreRef"></div>
</template>
<script setup>
import { onMounted, onUnmounted, ref } from "vue";
const emit = defineEmits(["loadMore"]);
const loadMoreRef = ref();
let observer = null;
const initObserver = () => {
  const observer = new IntersectionObserver((entries) => {
    const entry = entries[0];
    if (entry?.isIntersecting) {
      emit("loadMore");
    }
  });
  observer.observe(loadMoreRef.value);
};

onMounted(() => {
  initObserver();
});

onUnmounted(() => {
  observer?.disconnect();
  observer = null;
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
</style>
