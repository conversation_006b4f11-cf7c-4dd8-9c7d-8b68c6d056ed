<template>
  <header-bar is-scroll-change-bg
   title="عيد وطني سوري سعيد!"
    closeType="close"
   />
  <div class="content">
    <img src="@/assets/images/activity/april-15th-bg.jpg" alt="" />
  </div>
</template>

<script setup>
import { getCurrentInstance,ref,onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t
const { proxy } = getCurrentInstance()

const lang = proxy.$languageFile


</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.content {
  img {
    width: 100vw;
    display: block;
  }
}
</style>
  