<template>
  <div class="app-container flex">
    <header>
      <div class="header-title">SIYA</div>
      <div class="header-title">GOT TALENT</div>
      <div class="sub-title">THE FINAL ROUND</div>
      <div class="sub-title date">June 20,2025</div>
    </header>
    <div class="video-wrap">
      <video
        src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/baseforapp/promotional/video1.mp4"
        loop
        poster="@/assets/images/promotional/video-bg.jpg"
        preload
        controls
        id="video"
        playsinline
      ></video>
    </div>
  </div>
</template>

<script setup>
import { nextTick, onMounted, ref } from "vue";
// src="http://sui3uleq0.sabkt.gdipper.com/mgmt/siya-dev/common/174531732015823gafumfv18gb.mp4"

// 下滑展示标题处理
const isScrollChangeBg = ref(false);
const windowWidth = window.innerWidth;
if (windowWidth >= 768) {
  isScrollChangeBg.value = true;
}

const scrollChangeBgShow = (show) => {
  const targetEl = document.querySelector(".header-content");
  if (show) {
    targetEl.style.color = "#000";
  } else {
    targetEl.style.color = "#fff";
  }
};

// onMounted(() => {
//   nextTick(() => {
//     const video = document.getElementById("video");
//     video.play().catch((error) => {
//       console.error("播放失败:", error);
//     });
//     video.addEventListener("click", () => {
//       video.setAttribute("controls", "");
//     });
//   });
// });
onMounted(() => {
  document.title = "SIYA GOT TALENT";
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  position: relative;
  flex-direction: column;
  min-height: 100vh;
  background: url("@/assets/images/promotional/bg-top.png") right top no-repeat,
    url("@/assets/images/promotional/bg-bottom.png") right bottom no-repeat;
  background-size: 100% auto;
  background-color: #5f56ed;
  align-items: center;
  justify-content: center;

  header {
    padding: px2rem(26) 0 px2rem(30);
    color: $white;
    text-align: center;
    .header-title {
      font-size: px2rem(36);
      font-style: italic;
      font-family: Gilroy;
      font-weight: 900;
    }
    .sub-title {
      color: #FFF;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(16);
      font-style: italic;
      font-weight: 900;
      line-height: normal;
    }
    .date {
      margin-top: px2rem(12);
      color: #BAB6F7;
    }
    .feat-wrap {
      justify-content: space-between;
      align-items: center;
      font-size: px2rem(13);
      min-width: px2rem(250);
      max-width: 100vw;
      .feat {
        padding: 0 px2rem(10);
        height: px2rem(18);
        margin: 0 px2rem(2);
        background: linear-gradient(
          90deg,
          #ff2f90 0%,
          rgba(255, 122, 74, 0.52) 100%
        );
        border-radius: px2rem(18);
      }
    }
  }

  .video-wrap {
    width: calc(100% - px2rem(76));
    border-radius: px2rem(30);
    overflow: hidden;
    background: #000;
    margin: 0 0 px2rem(40);
    height: px2rem(542);
    border: px2rem(10) solid #000;
    display: flex;
    video {
      width: 100%;
    }
  }
}
::v-deep(.header-content .title) {
  text-align: center;
}
</style>
