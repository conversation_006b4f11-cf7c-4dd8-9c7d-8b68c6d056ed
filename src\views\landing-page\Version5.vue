<template>
  <div
    class="app-container flex"
    @click="downloadOrAdjust"
    v-track:click
    trace-key="fb_share_h5_click"
    :track-params="JSON.stringify(pointData('BR-1', 1, 'pt'))"
  >
    <marquee
      :list="marqueeList1"
      :speed="30"
      :style="{
        'margin-bottom': '10px',
      }"
    />

    <marquee :list="marqueeList2" :speed="30" />
    <swiper
      :effect="'coverflow'"
      :loop="true"
      :grabCursor="true"
      :centeredSlides="true"
      :space-between="30"
      :slidesPerView="'auto'"
      :autoplay="{
        delay: 3000,
        disableOnInteraction: false,
      }"
      :coverflowEffect="{
        rotate: 5,
        stretch: 0,
        depth: 60,
        modifier: 2,
        slideShadows: false,
      }"
      :modules="[EffectCoverflow, Autoplay]"
      class="mySwiper"
    >
      <swiper-slide v-for="(item, index) in swiperList" :key="index">
        <div class="swiper-item">
          <div class="info flex">
            <div class="text">{{ item.text }}</div>
            <img
              src="@/assets/images/landing-page/version2/location.png"
              alt=""
              class="local"
            />
            <gap :gap="2" />
            {{ item.age }}
            <gap :gap="2" />
            <img
              src="@/assets/images/common/female.png"
              alt=""
              class="gender"
            />
          </div>
          <img class="avatar" :src="item.avatar" />
          <img
            class="call-btn"
            src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/call.png"
            alt=""
          />
        </div>
      </swiper-slide>
    </swiper>
    <img
      src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/more-title.png"
      alt=""
      class="more-img"
      v-track:exposure
      trace-key="fb_share_h5_expo"
      :track-params="JSON.stringify(pointData('BR-1', 1, 'pt'))"
    />
    <main>
      <section v-for="(item, idx) in pictureList" :key="idx">
        <div class="info flex">
          <span>{{ item.distance }}</span>
          <img
            src="@/assets/images/landing-page/version2/location.png"
            alt=""
          />
          <gap :gap="2"></gap>
          <span>{{ item.age }}</span>
          <gap :gap="2"></gap>
          <img src="@/assets/images/common/female.png" alt="" class="gender" />
        </div>
        <van-swipe
          class="picture-wrap"
          :autoplay="currentIndex === idx ? 3000 : 0"
          :touchable="false"
        >
          <van-swipe-item>
            <img
              :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/avatars/${item.pictrue[0]}.jpg`"
              alt=""
              class="picture"
            />
          </van-swipe-item>
        </van-swipe>
        <img
          src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/call.png"
          alt=""
          class="free-btn"
        />
      </section>
    </main>
    <footer>
      <div class="download-btn">
        <span>descarregar</span>
        <gap :gap="4"></gap>
        <img
          src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/download.png"
          alt=""
        />
      </div>
    </footer>
  </div>
</template>

<script setup>
import { onMounted, getCurrentInstance, ref } from "vue";
import { i18n } from "@/i18n/index.js";
import { Swiper, SwiperSlide } from "swiper/vue";
import Marquee from "./components/Marquee.vue";

import "swiper/css";
import "swiper/css/effect-coverflow";
import "swiper/css/pagination";

// import required modules
import { EffectCoverflow, Autoplay } from "swiper/modules";
import { downloadOrAdjust, pointData } from "./utils.js";

const t = i18n.global.t;
const avatarPre =
  "https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/avatars/";
const { proxy } = getCurrentInstance();

const pictureList = [
  {
    pictrue: ["16"],
    distance: "2.8km",
    age: "19",
  },
  {
    pictrue: ["10"],
    distance: "3.2km",
    age: "18",
  },
  {
    pictrue: ["11"],
    distance: "850m",
    age: "19",
  },
  {
    pictrue: ["12"],
    distance: "1.2km",
    age: "23",
  },
  {
    pictrue: ["13"],
    distance: "4.1km",
    age: "22",
  },
  {
    pictrue: ["14"],
    distance: "1.1km",
    age: "19",
  },
];

const swiperList = [
  {
    avatar: avatarPre + "5.jpg",
    text: "1.1km",
    age: 23,
  },
  {
    avatar: avatarPre + "3.jpg",
    text: "1.6km",
    age: 19,
  },
  {
    avatar: avatarPre + "4.jpg",
    text: "644m",
    age: 18,
  },
  {
    avatar: avatarPre + "2.jpg",
    text: "365m",
    age: 22,
  },
  {
    avatar: avatarPre + "6.jpg",
    text: "4.1km",
    age: 26,
  },
  {
    avatar: avatarPre + "7.jpg",
    text: "5.6km",
    age: 19,
  },
  {
    avatar: avatarPre + "8.jpg",
    text: "2.1km",
    age: 18,
  },
];
const marqueeList1 = [
  {
    avatar: avatarPre + "1.jpg",
    text: "Te vejo no siya se quiser😎",
  },
  {
    avatar: avatarPre + "2.jpg",
    text: "siya-Conhecer amigos próximos",
  },
  {
    avatar: avatarPre + "3.jpg",
    text: "Seu bairro 💝🥰🥰",
  },
  {
    avatar: avatarPre + "4.jpg",
    text: "apenas para pessoas solteiras🔥🔥🔥",
  },
  {
    avatar: avatarPre + "5.jpg",
    text: "Coisas interessantes e pessoas interessantes",
  },
  {
    avatar: avatarPre + "6.jpg",
    text: "Queria encontrar-se com menina na mesma cidade?",
  },
  {
    avatar: avatarPre + "7.jpg",
    text: "🌈🌈Venha aqui para passar o tempo chato",
  },
];
const marqueeList2 = [
  {
    avatar: avatarPre + "8.jpg",
    text: "Aquí está todo👅",
  },
  {
    avatar: avatarPre + "9.jpg",
    text: "Tu hotwheel ♿️ favorito 💕",
  },
  {
    avatar: avatarPre + "10.jpg",
    text: "👇🏻Lo que buscas está aquí👇🏻",
  },
  {
    avatar: avatarPre + "11.jpg",
    text: "Aquí también la subo jeje ₍˄·͈༝·͈˄₎👅⬇️",
  },
  {
    avatar: avatarPre + "12.jpg",
    text: "Quer algo mais quente hoje? 😏",
  },
  {
    avatar: avatarPre + "13.jpg",
    text: "Seus segredos estão seguros comigo… ou não? 🤫🔥",
  },
  {
    avatar: avatarPre + "14.jpg",
    text: "Vamos conversar onde ninguém nos vê? 👀🔒",
  },
];

onMounted(() => {});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  position: relative;
  width: 100vw;
  background: url("https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/bg.jpg")
    no-repeat;
  background-color: #c28bf5;
  background-size: 100% auto;
  padding: px2rem(180) 0 px2rem(70) 0;
  .swiper {
    width: 100%;
    padding: px2rem(20) 0 px2rem(40);
    width: 100%;
  }
  .swiper-slide {
    width: px2rem(276);
    height: px2rem(374);
    border: 2px solid #fff;
    border-radius: px2rem(16);
    .swiper-item {
      height: 100%;
      width: 100%;
      position: relative;
      .info {
        position: absolute;
        font-size: px2rem(22);
        top: px2rem(20);
        right: px2rem(22);
        color: #fff;
        justify-content: flex-end;
        width: 100%;
        img {
          width: px2rem(20);
          height: px2rem(20);
        }
      }
      .avatar {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: px2rem(16);
        object-fit: cover;
      }
      .call-btn {
        position: absolute;
        width: px2rem(184);
        height: px2rem(47);
        left: 50%;
        bottom: 0;
        transform: translate(-50%, 50%);
      }
    }
  }
  .more-img {
    width: px2rem(306);
    height: px2rem(60);
    margin: px2rem(10) auto px2rem(12);
  }
  main {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 px2rem(22) px2rem(34);
    section {
      position: relative;
      margin-bottom: px2rem(40);
      .info {
        position: absolute;
        top: px2rem(12);
        right: px2rem(8);
        width: 100%;
        z-index: 100;
        justify-content: flex-end;
        padding: px2rem(8);
        font-size: $fontSize15;
        color: $white;
        height: px2rem(21);
        @include centerL;
        padding: 0 px2rem(4);
        img {
          width: px2rem(12);
          height: px2rem(12);
        }
      }
      .picture-wrap {
        width: px2rem(164);
        height: px2rem(208);
        border-radius: px2rem(16);
        overflow: hidden;
        transform: scaleX(-1);
        .picture {
          width: 100%;
          object-fit: fill;
        }
      }
      .free-btn {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 50%);
        width: px2rem(120);
        height: px2rem(42);
      }
    }
  }
  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 101;
    padding: px2rem(60) px2rem(16) px2rem(30);
    background: linear-gradient(
      0deg,
      rgba(211, 81, 255, 0.64) 0%,
      rgba(255, 90, 219, 0.32) 50%,
      rgba(255, 90, 214, 0) 100%
    );

    .download-btn {
      width: 100%;
      height: px2rem(72);
      line-height: px2rem(72);
      border-radius: px2rem(72);
      font-weight: $fontWeightBold;
      text-align: center;
      background: linear-gradient(90deg, #9091fe 0%, #fc98fe 100%);
      font-size: px2rem(28);
      color: #fff;
      animation: spin 0.8s ease-in-out infinite;
      box-shadow: 0px 0px 11px 2px #ffffff inset;
      @include center;
      img {
        width: px2rem(24);
        height: px2rem(24);
      }
    }
  }
}
@keyframes spin {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
</style>
