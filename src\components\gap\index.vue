<template>
  <div class="gap" :style="{width: `${gapWidth}px`}"></div>
</template>

<script setup>
import { ref, watch, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  gap: {
    type: Number,
    default: 0,
  }
})

const gapWidth = ref(0)

watch(() => props.gap, (val) => {
  gapWidth.value = proxy.$pxToRemPx(val)
}, {
  immediate: true,
})

</script>

<style scoped lang="scss">
.gap {
  height: 1px;
  flex: none;
}
</style>
