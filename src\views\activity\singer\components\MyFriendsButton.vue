<template>
  <div
    class="right-button"
    :style="{
      top: `${$pxToRemPx(top)}px`,
    }"
    @click="handleShow"
    v-track:click
    trace-key="singer_button_click"
    :track-params="
      JSON.stringify({
        singer_button_name: 9,
      })
    "
  >
    <div class="right-button-text">
      {{ $t(`singer.my_friends`) }}
    </div>
  </div>
  <van-overlay :show="showDialog" :lock-scroll="false" z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <div class="inner">
        <div class="top-bg"></div>
        <div class="box">
          <BorderBox>
            <div class="box-content">
              <div class="box-title">
                <BlockTitle
                  :show-line="true"
                  :font-size="18"
                  :lineWidth="21"
                  :iconGap="8"
                  :lineGap="2"
                  icon-size="small"
                  :title="$t(`singer.my_friends`)"
                ></BlockTitle>
              </div>
              <div
                ref="rankListRef"
                class="ranking-list"
                v-if="friends.length > 0"
              >
                <div
                  class="rank-item"
                  v-for="(item, index) in friends"
                  :key="index"
                >
                  <div class="index">{{ item.rank || "-" }}</div>
                  <gap :gap="8"></gap>
                  <div class="avatar">
                    <frame-avatar
                      :avatar="item?.avatar"
                      :frame="item.frame"
                      :size="36"
                    ></frame-avatar>
                  </div>
                  <gap :gap="8"></gap>
                  <div class="nickname">{{ item.nickname }}</div>
                  <gap :gap="8"></gap>
                  <div class="action">
                    <TicketNumber :number="item.count"></TicketNumber>
                    <div>
                      <div v-if="showAudioBtn">
                        <AudioPlayer
                          v-if="showDialog"
                          :url="item.media_url"
                        ></AudioPlayer>
                      </div>
                      <gap :gap="4" v-if="showVoteBtn"></gap>
                      <div>
                        <VoteButton
                          v-if="showVoteBtn"
                          :user-item="item"
                          :source="6"
                          @vote="onVote"
                        ></VoteButton>
                      </div>
                    </div>
                  </div>
                </div>
                <load-more :root="rankListRef" @loadMore="load()"></load-more>
              </div>
              <empty v-else :tips="$t('common.common_content_yet')"></empty>
              <div class="box-bottom-cover"></div>
            </div>
          </BorderBox>
        </div>
        <div class="close-btn" @click="showDialog = false"></div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { onMounted, ref } from "vue";
import BlockTitle from "./BlockTitle.vue";
import BorderBox from "./BorderBox.vue";
import AudioPlayer from "./AudioPlayer.vue";
import TicketNumber from "./TicketNumber.vue";
import VoteButton from "./VoteButton.vue";
import LoadMore from "@/components/load-more/index.vue";
import singerApi from "@/api/activity.js";
const props = defineProps({
  top: {
    type: Number,
    default: -4,
  },
  showVoteBtn: {
    type: Boolean,
    default: true,
  },
  showAudioBtn: {
    type: Boolean,
    default: true,
  },
});
const rankListRef = ref();
const showDialog = ref(false);
const friends = ref([]);

const handleShow = () => {
  load(true);
  showDialog.value = true;
};
const onVote = (item) => {
  const index = friends.value.findIndex((i) => i.account === item.account);
  if (index > -1) {
    friends.value[index].is_vote = true;
    friends.value[index].count++;
  }
};

const STATE_MORE = "more",
  STATE_LOADING = "loading",
  STATE_NOMORE = "nomore";
const pageIndex = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loadState = ref(STATE_MORE); // more loading nomore
const load = async (refreh) => {
  console.log("load");

  if (loadState.value === STATE_LOADING) {
    return;
  }
  if (refreh === true) {
    pageIndex.value = 1;
    friends.value = [];
    loadState.value = STATE_MORE;
  }
  if (loadState.value === STATE_NOMORE) {
    return;
  }
  loadState.value = STATE_LOADING;
  try {
    const res = await singerApi.singer_friends({
      page: pageIndex.value,
      size: pageSize.value,
      source: 1,
    });
    if (res.code === 200) {
      const data = res.data?.list || [];
      const count = res.data?.count || 0;
      friends.value = [...friends.value, ...data];
      pageIndex.value++;
      if (friends.value.length >= count) {
        loadState.value = STATE_NOMORE;
      } else {
        loadState.value = STATE_MORE;
      }
    } else {
      loadState.value = STATE_NOMORE;
    }
  } catch (e) {
    loadState.value = STATE_NOMORE;
  }
};

onMounted(() => {
  load(true);
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.right-button {
  height: px2rem(30);
  width: fit-content;
  position: absolute;
  z-index: 9;
  right: px2rem(-22);
  border-radius: px2rem(30) 0 0 px2rem(30);
  border: 1px solid #fff;
  background: #791ec9;
  box-shadow: 0px -2.454px 2.454px 0px rgba(44, 2, 104, 0.64) inset,
    0px 1.84px 1.84px 0px rgba(255, 255, 255, 0.66) inset;
  display: flex;
  align-items: center;
  padding: 0 px2rem(4) 0 px2rem(12);
  .right-button-text {
    color: #fff;
    text-align: center;
    text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: 124%; /* 14.88px */
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: 0;
    .right-button-text {
      padding: 0 px2rem(12) 0 px2rem(4);
    }
  }
}

.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: show 0.3s 1;
    .top-bg {
      background-image: url("@/assets/images/activity/singer/dialog-top.png");
      background-size: 100% 100%;
      width: px2rem(388);
      height: px2rem(98);
    }
    .box {
      margin-top: px2rem(-6);
      width: px2rem(344);
      .box-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .box-title {
          width: px2rem(277);
          padding-top: px2rem(24);
        }
        .desc {
          width: px2rem(226);
          margin-top: px2rem(17);
          color: #ff00f1;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 700;
          line-height: 100%; /* 12px */
        }
        .table {
          width: px2rem(270);
          margin-top: px2rem(23);
          .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: px2rem(27);
            .table-header-item {
              display: flex;
              padding: px2rem(5) px2rem(8);
              width: px2rem(82);
              height: px2rem(34);
              justify-content: center;
              align-items: center;
              border-radius: px2rem(4);
              border: px2rem(1) solid #fff;
              background: linear-gradient(
                180deg,
                rgba(255, 255, 255, 0.34) 0%,
                rgba(154, 70, 255, 0.34) 100%
              );
              text-align: center;
              font-family: Gilroy;
              font-size: px2rem(12);
              font-style: normal;
              font-weight: 700;
              line-height: 100%; /* 12px */
              background: linear-gradient(180deg, #fff 13.29%, #d247ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .table-body {
            max-height: px2rem(160);
            overflow-y: auto;
            padding-bottom: px2rem(53);
            .table-cell {
              display: flex;
              justify-content: center;
              margin-bottom: px2rem(12);
              .time {
                color: #d8bdff;
                font-family: Gilroy;
                font-size: px2rem(10);
                font-style: normal;
                font-weight: 500;
                text-align: center;
                line-height: 124%; /* 12.4px */
                width: 33.33%;
                flex-grow: 1;
              }
              .audio {
                width: 33.33%;
                flex-grow: 1;
                display: flex;
                justify-content: center;
                .audio-play {
                  width: px2rem(28);
                  height: px2rem(28);
                  flex-shrink: 0;
                }
              }
              .state {
                width: 33.33%;
                flex-grow: 1;
                display: flex;
                justify-content: center;
                .state-tag {
                  width: px2rem(72);
                  height: px2rem(26);
                  padding: 0 px2rem(12);
                  flex-shrink: 0;
                  border-radius: px2rem(50);
                  text-align: center;
                  font-family: Gilroy;
                  font-size: px2rem(9);
                  font-style: normal;
                  font-weight: 500;
                  line-height: 100%; /* 9px */
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  border: 1.5px solid #f4a9ff;
                  background: linear-gradient(
                    180deg,
                    rgba(255, 97, 240, 0.5) 13.41%,
                    rgba(148, 48, 255, 0.5) 77.63%
                  );
                  span {
                    background: linear-gradient(180deg, #fff 0%, #d1a3ff 100%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
              }
            }
          }
        }
        .box-bottom-cover {
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: px2rem(53);
          border-radius: 0px 0px px2rem(7) px2rem(7);
          background: linear-gradient(
            180deg,
            rgba(32, 2, 71, 0) -24%,
            #24044c 44.02%
          );
        }
      }
    }
    .close-btn {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(26);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/singer/icon-close.svg");
      background-size: 100% 100%;
    }
  }
}

.ranking-list {
  padding: 0 px2rem(15);
  padding-bottom: px2rem(46);
  margin-top: px2rem(18);
  height: px2rem(282);
  overflow-y: auto;
  width: 100%;
  .rank-item {
    display: flex;
    height: px2rem(46);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    margin-bottom: px2rem(13);
    padding-right: px2rem(7);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .nickname {
      color: var(---White, #fff);

      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
  }
}
.action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  & > div {
    display: flex;
    align-items: center;
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: px2rem(-22);
    border-radius: 0 px2rem(30) px2rem(30) 0;
  }
}
</style>
