<template>
  <div class="app-container">
    <header-bar is-scroll-change-bg @emit:before-close="onBeforeBack" />

    <main>
      <div class="payment-methods-cell">
        <div class="cell-title">{{ $t("withdraw.withdrawal_methods") }}</div>
        <div class="cell-field flex">
          <photo
            :width="100"
            :height="50"
            :radius="8"
            :url="currentChannel.logo"
          />
          <gap :gap="4" />
          <div class="payment-name">{{ currentChannel.channel_name }}</div>
        </div>
      </div>

      <template v-if="isBank && !isHideSelect">
        <div class="bank-tips">
          {{ $t("withdraw.BANK_TRANSFER_fill_info_tips") }}
        </div>
        <div class="cell-wrap">
          <div class="cell-title flex">
            <div class="icon-circle"></div>
            <span>{{ $t("withdraw.BANK_TRANSFER_receive_bank") }}</span>
          </div>
          <van-field
            class="cell-field"
            v-model="currentBankInfo.bank_name"
            :placeholder="$t('withdraw.BANK_TRANSFER_receive_bank')"
            :border="false"
            :right-icon="iconArrowDown"
            readonly
            @click="toggleActionVisible(true)"
          />
        </div>
      </template>

      <div
        class="cell-wrap"
        :class="{ error: field.isError }"
        v-for="(field, idx) in formCell"
        :key="idx"
      >
        <div class="cell-title flex">
          <div class="icon-circle"></div>
          <span>{{ field.title || field.key }}</span>
        </div>
        <template v-if="field?.type === 'select'">
          <van-field
            v-model="field.textValue"
            :model-value="value2Text(field)"
            readonly
            :border="false"
            :placeholder="field.placeholder"
            :hook:mounted="field.mounted && field.mounted(field, formCell)"
            @click="field.showPicker = true"
          >
          </van-field>
          <van-popup
            v-model:show="field.showPicker"
            destroy-on-close
            position="bottom"
          >
            <van-picker
              :columns="field?.options || []"
              :model-value="[field.value]"
              @confirm="(v) => field?.selectConfirm(v, field, formCell)"
              @cancel="field.showPicker = false"
            />
          </van-popup>
        </template>

        <van-field
          class="cell-field"
          v-else
          v-model="field.value"
          :placeholder="field.placeholder"
          :border="false"
          :hook:mounted="field.mounted && field.mounted(field, formCell)"
        >
          <template #left-icon v-if="field?.preText">
            <span class="area-code">{{ field.preText }}</span>
          </template>
        </van-field>
        <div class="cell-hint" v-if="field?.hint">{{ field.hint }}</div>
      </div>

      <van-button
        class="submit-button"
        type="primary"
        block
        :disabled="buttonDisabled"
        @click="handleSubmit"
      >
        {{ $t("common.common_submit") }}
      </van-button>
    </main>

    <van-popup v-model:show="actionVisible" destroy-on-close position="bottom">
      <van-picker
        class-name="action-sheet"
        :columns="bankList"
        :columns-field-names="{ text: 'bank_name', value: 'bank_cfg_id' }"
        :model-value="currentBank"
        @confirm="onSelectAction"
        @cancel="toggleActionVisible(false)"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";
import withdrawApi from "@/api/withdraw.js";
import { useWithdrawStore } from "@/store/index.js";
import iconArrowDown from "@/assets/images/icon/icon-arrow-down-black.svg";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const router = useRouter();
const withdrawStore = useWithdrawStore();

const { currentChannelInfo: currentChannel } = storeToRefs(withdrawStore);
// 格式化表单数据
const formCell = ref([]);
const formatFormCell = () => {
  formCell.value = withdrawStore.formatFormData(currentChannel.value);
};

// 获取银行列表
const bankList = ref([]);
const fetchBankList = async () => {
  const params = {
    id: currentChannel.value.withdraw_cfg_id,
    product_id: withdrawStore.currentProductId,
  };
  const response = await withdrawApi.bank_list(params);
  if (response.code === 200) {
    bankList.value = response.data.items || [];
    if (isHideSelect.value) {
      const onlyBank = bankList.value[0];
      currentBank.value = [onlyBank.bank_cfg_id];
      currentBankInfo.value = onlyBank;
    }
  }
};

const value2Text = (row) => {
  const targetOption =
    row.options.find((option) => option.value === row.value) || {};
  return targetOption.text;
};

// 选择银行
const isBank = computed(() => {
  return currentChannel.value.type === 2;
});
// 仅有一家银行且无bank_name时，不展示下拉选项
const isHideSelect = computed(() => {
  return bankList.value.length === 1 && !bankList?.value[0]?.bank_name;
});
const currentBank = ref([]);
const currentBankInfo = ref({});
const actionVisible = ref(false);
const toggleActionVisible = (bool) => {
  actionVisible.value = bool;
};
const onSelectAction = ({ selectedValues, selectedOptions }) => {
  currentBank.value = selectedValues;
  currentChannel.value.bid = selectedOptions[0]?.bank_cfg_id;
  currentBankInfo.value = selectedOptions[0];
  toggleActionVisible(false);
  getWithdrawInfo();
};

// 重新获取银行表单数据
const getWithdrawInfo = async () => {
  const params = {
    type: currentChannel.value.type,
    wid: currentChannel.value.withdraw_cfg_id,
    bid: currentChannel.value.bid,
  };
  const response = await withdrawApi.get_payment_info(params);
  if (response.code === 200) {
    currentChannel.value.payment = response.data.payment;
    formatFormCell();
  }
};

// 按钮禁用状态
const buttonDisabled = computed(() => {
  const isCheckForm = formCell.value.filter((e) => !e.value).length > 0;
  const isCheckBank = currentBank.value.length <= 0 && !isHideSelect.value;
  if (isBank.value) return isCheckBank || isCheckForm;
  return isCheckForm;
});

// 表单校验
const validatorForm = () => {
  let errorCount = 0;
  formCell.value.forEach((cell) => {
    if (cell.validator && !cell.validator(cell.value, formCell.value)) {
      cell.isError = true;
      errorCount += 1;
    } else {
      cell.isError = false;
    }
  });
  return errorCount === 0;
};

// 提交
const handleSubmit = async () => {
  if (!validatorForm()) return;

  const params = {
    type: currentChannel.value.type,
    wid: currentChannel.value.withdraw_cfg_id,
    bid: currentBankInfo.value.bank_cfg_id,
    payment: {},
  };
  formCell.value.forEach((i) => {
    params.payment[i.key] = i.value;
  });
  // 存储表单
  withdrawStore.setCurrentChannelInfo({
    ...currentChannel.value,
    ...currentBankInfo.value,
    payment: params.payment,
    bid: params.bid,
    id: currentChannel.value.id,
  });
  const response = await withdrawApi.set_payment_info(params);
  if (response.code === 200) {
    if (router.options.history.state.back.includes("confirm")) {
      router.go(-1);
    } else {
      await router.replace({ name: "WithdrawalInformation" });
    }
  }
};

// 返回上一页 如果是银行，重置payment信息
let lastPaymentInfo = {};
const onBeforeBack = () => {
  if (isBank.value) {
    withdrawStore.setCurrentChannelInfo({
      ...lastPaymentInfo,
    });
  }
};

onMounted(async () => {
  formatFormCell();

  // 银行转账需要获取银行列表
  if (isBank.value) {
    await fetchBankList();
    const backId = currentChannel.value.bid;
    if (backId) {
      currentBank.value = [backId];
      currentBankInfo.value =
        bankList.value.find((i) => i.bank_cfg_id === backId) || {};
    }
    lastPaymentInfo = {
      ...withdrawStore.currentChannelInfo,
    };
  }
});
</script>

<style scoped lang="scss">
@import "./assets/payment.scss";

main {
  padding: $gap20 $gap16 $pageBottomPadding;

  .bank-tips {
    margin-bottom: px2rem(28);
    color: $fontColorB3;

    @include fontSize13;
  }

  .cell-wrap {
    margin-bottom: px2rem(28);

    &.error {
      .cell-hint {
        color: $subColorRed;
      }

      .cell-field {
        border: 1px solid $subColorRed;
      }
    }
  }

  .payment-methods-cell {
    margin-bottom: px2rem(28);

    .payment-name {
      color: $fontColorB1;

      @include fontSize15;
    }
  }

  .cell-title {
    margin-bottom: $gap8;
    font-weight: $fontWeightBold;
    color: $fontColorB1;

    @include fontSize13;

    .icon-circle {
      width: px2rem(4);
      height: px2rem(4);
      margin: 0 $gap8;
      background-color: $fontColorB1;
      border-radius: 50%;
    }
  }

  .cell-field {
    min-height: px2rem(50);
    margin-bottom: $gap8;
    background-color: $white;
    border-radius: $radius16;
    .area-code {
      color: $fontColorB4;
      background: $bgColorB7;
      display: inline-block;
      width: px2rem(33);
      height: px2rem(26);
      text-align: center;
      border-radius: px2rem(8);
    }
  }

  .cell-hint {
    padding: $gap8 0;
    color: $fontColorB3;

    @include fontSize13;
  }
}
</style>
