<template>
  <div class="app-container coiner">
    <header>
      <header-bar closeType="close">
        <div class="header-bar-right">
          <span @click="jumpRecord">
            {{ $t("dealer_coin.coin_dealer_coin_details") }}
          </span>
        </div>
        <div class="header-section">
          <div class="userinfo-wrap">
            <img :src="walletInfo?.user_info?.avatar" alt="" class="avater" />
            <gap :gap="16" />
            <div class="flex flex-col">
              <div class="userinfo-name ellipsis">
                {{ walletInfo?.user_info?.nickname }}
              </div>
              <div class="userinfo-ID flex">
                <img
                  src="@/assets/images/dealer-coin/ID.png"
                  alt=""
                  class="ID-icon"
                />
                <gap :gap="2"></gap>
                {{ walletInfo?.user_info?.account }}
              </div>
            </div>
          </div>
          <van-row class="account-wrap">
            <van-col span="12" class="item-wrap">
              <div class="label-warp flex" @click="jumpRecharge">
                <span class="label-text">{{
                  $t("dealer_coin.coin_dealer_usable_coin")
                }}</span>
                <img
                  src="@/assets/images/dealer-coin/arrow-right.png"
                  alt=""
                  class="label-icon"
                />
              </div>
              <div class="coin-wrap flex">
                <img
                  src="@/assets/images/common/coin.png"
                  alt=""
                  class="coin-img"
                />
                <gap :gap="2" />
                <coin-number
                  class="coin-num"
                  :num="walletInfo?.coin"
                  :unit-size="20"
                />
              </div>
            </van-col>
            <van-col span="12" class="item-wrap">
              <div class="label-warp flex">
                <span class="label-text">{{
                  $t("dealer_coin.coin_dealer_frozen_deposit")
                }}</span>
                <img
                  src="@/assets/images/dealer-coin/question-mark.png"
                  alt=""
                  class="label-icon"
                  @click="handleShowDialog(1)"
                />
              </div>
              <div class="coin-wrap flex">
                <img
                  src="@/assets/images/common/coin.png"
                  alt=""
                  class="coin-img"
                />
                <gap :gap="2" />
                <coin-number
                  class="coin-num"
                  :num="walletInfo?.freeze_coin"
                  :unit-size="20"
                />
              </div>
            </van-col>
            <van-col span="12" class="item-wrap">
              <div class="label-warp flex">
                <span class="label-text">{{
                  $t("dealer_coin.coin_dealer_total_recd_coins")
                }}</span>
              </div>
              <div class="coin-wrap flex">
                <img
                  src="@/assets/images/common/coin.png"
                  alt=""
                  class="coin-img"
                />
                <gap :gap="2" />
                <coin-number
                  class="coin-num"
                  :num="walletInfo?.sum_recharge_coins"
                  :unit-size="20"
                />
              </div>
            </van-col>
            <van-col span="12" class="item-wrap">
              <div class="label-warp flex">
                <span class="label-text">{{
                  $t("dealer_coin.coin_dealer_cumul_sold_coins")
                }}</span>
              </div>
              <div class="coin-wrap flex">
                <img
                  src="@/assets/images/common/coin.png"
                  alt=""
                  class="coin-img"
                />
                <gap :gap="2" />
                <coin-number
                  class="coin-num"
                  :num="walletInfo?.sum_sold_coins"
                  :unit-size="20"
                />
              </div>
            </van-col>
          </van-row>
        </div>
      </header-bar>
    </header>
    <main>
      <div class="title-wrap flex">
        <span class="title-text">{{
          $t("dealer_coin.coin_dealer_input_transfer_user_id")
        }}</span>
        <img
          src="@/assets/images/dealer-coin/question-mark.png"
          alt=""
          class="coin"
          @click="handleShowDialog(2)"
        />
      </div>
      <!-- <div class="input-wrap flex" @click="jumpSearch">
        <img
          src="@/assets/images/dealer-coin/search.png"
          alt=""
          class="search-icon"
        />
        <gap :gap="4"></gap>
        <div class="input-placeholder">
          {{ $t("dealer_coin.coin_dealer_input_transfer_user_id") }}
        </div>
      </div> -->
      <van-search
        v-model="currentUser.account"
        readonly
        :placeholder="$t('dealer_coin.coin_dealer_input_transfer_user_id')"
        @click="jumpSearch"
      >
      </van-search>
      <div class="tab-wrap flex">
        <div
          class="tab"
          :class="{ active: currentTab === 0 }"
          @click="onChangeTab(0)"
        >
          {{ $t("dealer_coin.coin_dealer_recent_trades") }}
        </div>
        <gap :gap="16"></gap>
        <div
          class="tab"
          :class="{ active: currentTab === 1 }"
          @click="onChangeTab(1)"
        >
          {{ $t("dealer_coin.coin_dealer_contacts") }}
        </div>
      </div>
      <swiper
        class="list-wrap"
        :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
        :modules="modules"
        slides-per-view="auto"
        :space-between="0"
        @swiper="setControlledSwiper"
        @slideChange="onSlideChange"
      >
        <swiper-slide class="swipe-item" :key="1">
          <van-list
            v-model:loading="isLoading"
            :finished="finished"
            finished-text=""
            loading-text="loading..."
            :immediate-check="false"
            @load="fetchList"
          >
            <template v-if="list.length > 0">
              <user-card
                v-for="item in list"
                :key="item.id"
                @click="handleTransfer(item.user_info)"
                :data="{ ...item, ...item.user_info }"
                :border="true"
              ></user-card
            ></template>
            <Empty
              v-else
              class="empty"
              :tips="$t('common.common_content_yet')"
            ></Empty>
          </van-list>
        </swiper-slide>
        <swiper-slide class="swipe-item" :key="2">
          <van-list
            v-model:loading="isLoading"
            :finished="finished"
            finished-text=""
            loading-text="loading..."
            :immediate-check="false"
            @load="fetchList"
          >
            <template v-if="list.length > 0">
              <user-card
                v-for="item in list"
                :key="item.id"
                @click="handleTransfer(item.user_info)"
                :data="{ ...item, ...item.user_info }"
                :border="true"
              ></user-card
            ></template>
            <Empty
              v-else
              class="empty"
              :tips="$t('common.common_content_yet')"
            ></Empty>
          </van-list>
        </swiper-slide>
      </swiper>

      <!-- <van-tabs
        class="list-wrap"
        v-model:active="currentTab"
        swipeable
        shrink
        title-active-color="#444"
        :line-width="proxy.$pxToRemPx(22)"
        @change="onChangeTab"
      >
        <van-tab :title="$t('dealer_coin.coin_dealer_recent_trades')" :name="1">
          <van-list
            v-model:loading="isLoading"
            :finished="finished"
            finished-text=""
            loading-text="loading..."
            :immediate-check="false"
            @load="fetchList"
          >
            <template v-if="list.length > 0">
              <user-card
                v-for="item in list"
                :key="item.id"
                @click="handleTransfer(item.user_info)"
                :data="{ ...item, ...item.user_info }"
                :border="true"
              ></user-card
            ></template>
            <Empty
              v-else
              class="empty"
              :tips="$t('common.common_content_yet')"
            ></Empty>
          </van-list>
        </van-tab>
        <van-tab :title="$t('dealer_coin.coin_dealer_contacts')" :name="2">
          <van-list
            v-model:loading="isLoading"
            :finished="finished"
            finished-text=""
            loading-text="loading..."
            :immediate-check="false"
            @load="fetchList"
          >
            <template v-if="list.length > 0">
              <user-card
                v-for="item in list"
                :key="item.id"
                @click="handleTransfer(item.user_info)"
                :data="{ ...item, ...item.user_info }"
                :border="true"
              ></user-card
            ></template>
            <Empty
              v-else
              class="empty"
              :tips="$t('common.common_content_yet')"
            ></Empty>
          </van-list>
        </van-tab>
      </van-tabs> -->
      <div class="btn-wrap">
        <van-button
          type="primary"
          block
          @click="handleTransfer(currentUser)"
          :disabled="!currentUser.account"
        >
          {{ $t("dealer_coin.coin_dealer_transfer") }}
        </van-button>
      </div>
    </main>
  </div>
  <base-modal
    :show="isShowDialog"
    :conform-text="$t('common.common_confirm')"
    @confirm="isShowDialog = false"
  >
    <template #title>
      <span v-if="currentDialog === 1">
        {{ $t("dealer_coin.coin_dealer_agent_mortg_coin") }}
      </span>
      <span v-else>
        {{ $t("dealer_coin.coin_dealer_agent_recharg_whitelist") }}
      </span>
    </template>

    <template #content v-if="currentDialog === 1">
      <div
        style="text-align: left"
        class="coiner explain-text"
        v-for="(cell, idx) in $tm(
          'dealer_coin.coin_dealer_agent_amount_explain'
        )"
        :key="idx"
      >
        {{ cell }}
      </div>
    </template>
  </base-modal>
  <base-modal
    :show="dialogTransferData.show"
    :conform-text="$t('common.common_confirm')"
    @confirm="dialogTransferData.show = false"
  >
    <template #title>
      {{ dialogTransferData.msg }}
    </template>
  </base-modal>
  <TransferPopup
    v-model:show="transferPopupShow"
    :userinfo="transferUser"
    :wallet="walletInfo"
    @refresh="init"
    v-if="transferPopupShow"
  />
</template>

<script setup>
import { ref, getCurrentInstance, onMounted, unref, onActivated } from "vue";
import UserCard from "./components/UserCard.vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Controller } from "swiper/modules";
import { i18n } from "@/i18n/index.js";
import TransferPopup from "./components/TransferPopup.vue";
import dealerApi from "@/api/dealer.js";
import { useRouter, useRoute } from "vue-router";

const { proxy } = getCurrentInstance();
const modules = [Controller];
const controlledSwiper = ref(null);
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};
const t = i18n.global.t;
const router = useRouter();
const route = useRoute();
const currentTab = ref(0);
const currentUser = ref({});
const transferUser = ref({});
const currentDialog = ref(1);
const isShowDialog = ref(false);
const dialogTransferData = ref({});
const transferPopupShow = ref(false);
const walletInfo = ref({});
const page = ref(1);
const size = ref(10);
const isLoading = ref(false);
const finished = ref(false);
const list = ref([]);

const handleTransfer = async (user) => {
  const resResult = await dealerApi.transfer_check({ account: user.account });
  if (resResult.code === 200) {
    if (resResult.data.is_allow_transfer) {
      transferUser.value = user;
      transferPopupShow.value = true;
    } else {
      dialogTransferData.value.show = true;
      dialogTransferData.value.msg = resResult.data.transfer_msg;
    }
  }
};

const fetchAgentWallet = () => {
  dealerApi.agent_wallet().then((res) => {
    walletInfo.value = res.data;
  });
};
const fetchList = (refresh = false) => {
  isLoading.value = true;
  const apiName = currentTab.value === 0 ? "recent_traders" : "recent_contacts";
  dealerApi[apiName]({ page: unref(page), size: unref(size) })
    .then(({ data }) => {
      list.value = refresh ? data.list : unref(list).concat(data.list);
      page.value += 1;
      if (data.list.length < unref(size)) finished.value = true;
    })
    .catch(() => {
      finished.value = true;
    })
    .finally(() => {
      isLoading.value = false;
      // 按原型暂只需请求一次
      finished.value = true;
    });
};

const onChangeTab = (i) => {
  currentTab.value = i;
  page.value = 1;
  finished.value = false;
  fetchList(true);
};

const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === currentTab.value) return;
  onChangeTab(idx);
};

const handleShowDialog = (type) => {
  currentDialog.value = type;
  isShowDialog.value = true;
};

const onRouteQuery = () => {
  if (route.query?.account) {
    currentUser.value = route.query;
  }
  if (localStorage.getItem("isCheck") == 1) {
    localStorage.setItem("isCheck", 0);
    handleTransfer(route.query);
  }
};

const jumpRecharge = async () => {
  const resResult = await proxy.$siyaApp("openSiyaUrl", {
    url: "siya://siya.com/app?method=goRecharge",
  });
  if (resResult.data?.scene === "recharge") fetchAgentWallet();
};

const jumpSearch = () => {
  router.push({
    name: "DealerCoinSearch",
    query: unref(currentUser),
  });
};

const jumpRecord = () => {
  router.push({
    name: "DealerCoinRecord",
  });
};

const init = () => {
  page.value = 1;
  finished.value = false;
  fetchAgentWallet();
  fetchList(true);
};

onMounted(() => {
  init();
});

onActivated(() => {
  onRouteQuery();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.app-container {
  display: flex;
  height: 100vh;
  flex-direction: column;
}
header {
  width: 100vw;
  height: px2rem(336);
  background: url("@/assets/images/dealer-coin/header-bg.png") no-repeat;
  background-size: cover;
  .header-bar-right {
    position: absolute;
    width: 100%;
    justify-content: flex-end;
    top: 0;
    bottom: 0;
    left: 0;
    font-weight: 500;
    font-size: px2rem(17);
    padding: 0 px2rem(16);
    @include fontSize17;
    @include centerL;
  }
  .header-section {
    position: absolute;
    z-index: -1;
    top: px2rem(46);
    left: 0;
    width: 100vw;
    margin-bottom: px2rem(8);
    .userinfo-wrap {
      @include centerL;
      color: $fontColorB1;
      padding: px2rem(24) px2rem(24) px2rem(11);
      .avater {
        width: px2rem(40);
        height: px2rem(40);
        border-radius: 50%;
      }
      .userinfo-name {
        font-size: px2rem(17);
        font-weight: 700;
        margin-bottom: px2rem(4);
        align-self: start;
        max-width: calc(100vw - px2rem(100));
      }
      .userinfo-ID {
        font-size: px2rem(13);
        color: $fontColorB3;
        align-self: start;
        .ID-icon {
          width: px2rem(12);
          height: px2rem(12);
        }
      }
    }
    .account-wrap {
      padding-top: px2rem(24);
      .item-wrap {
        padding: 0 px2rem(24) px2rem(24);
        .label-warp {
          @include fontSize13;
          color: $fontColorB3;
          .label-icon {
            width: px2rem(14);
            height: px2rem(14);
            margin-top: px2rem(-4);
          }
          .label-text {
            margin-bottom: $gap2;
          }
        }
        .coin-wrap {
          font-size: px2rem(20);
          margin-right: px2rem(2);
          .coin-img {
            width: px2rem(20);
            height: px2rem(20);
          }
          .coin-num {
            color: $fontColorB1;
            font-weight: 700;
          }
        }
      }
    }
  }
}
main {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  background: #fff;
  width: 100%;
  border-top-right-radius: $radius16;
  border-top-left-radius: $radius16;
  padding: $gap20 $gap24 0 $gap20;
  padding-bottom: v-bind("proxy.$appBottomBarHeight + 'px'");
  color: $fontColorB1;
  .title-wrap {
    @include fontSize17;
    font-weight: 700;
    margin-bottom: px2rem(12);
    .title-text {
      font-size: $fontSize15;
    }
    .coin {
      width: px2rem(16);
      height: px2rem(16);
    }
  }
  .input-wrap {
    height: px2rem(42);
    background: #f2f2f2;
    border-radius: $radius12;
    padding: 0 $gap10;
    .search-icon {
      width: px2rem(24);
      height: px2rem(24);
    }
    .input-placeholder {
      @include fontSize17;
      color: $fontColorB4;
    }
  }
  .list-wrap {
    flex: 1;
    width: 100%;
    min-height: 0;
    display: flex;
    flex-direction: column;
    .swipe-item {
      min-height: 0;
      overflow: auto;
    }
  }
  .btn-wrap {
    background: $white;
    padding: px2rem(8) 0;
  }
}

.tab-wrap {
  border-bottom: 1px solid #ffffff1f;
  align-items: center;
  border-bottom: 0.5px solid #f2f2f2;
  .tab {
    height: px2rem(40);
    @include fontSize15;
    @include centerV;
    position: relative;

    &.active {
      font-weight: 700;
      @include fontSize15;

      &::after {
        content: "";
        position: absolute;
        bottom: 0;
        height: px2rem(3);
        width: px2rem(22);
        background: #5f56ed;
      }
    }
  }
}

// ::v-deep(.van-tabs__nav) {
//   padding-right: 0 !important;
//   padding-left: 0 !important;
//   .van-tab {
//     padding: 0;
//     margin: 0 px2rem(16);
//     &:first-child {
//       margin: 0;
//     }
//   }
// }
::v-deep(.van-swipe-item) {
  overflow-y: auto;
}
// ::v-deep(.van-tabs__content) {
//   flex: 1;
//   min-height: 0;
//   overflow: auto;
//   border-top: 0.5px solid #f2f2f2;
// }
::v-deep(.header-bar-fixed) {
  z-index: 0 !important;
}
::v-deep(.van-search) {
  padding: 0 !important;
}
::v-deep(.van-search__action) {
  padding: 0 px2rem(16) 0 px2rem(8);
}
::v-deep(.van-search__content) {
  border-radius: px2rem(12);
  background: $bgColorB6;
  .van-search__field {
    height: px2rem(44);
  }
}
</style>
