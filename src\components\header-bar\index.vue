<template>
  <div class="header-bar">
    <div
      class="check"
      ref="checkRef"
      :style="{ 'padding-top': `${statusBarH}px` }"
    ></div>
    <div
      v-if="padding"
      class="header-padding"
      :style="{
        'padding-top': `${statusBarH}px`,
        'background-color': barBgColor,
      }"
    >
      <div class="content-padding" v-if="contentPadding"></div>
    </div>

    <div
      class="header-bar-fixed"
      :class="{ 'on-page': !headerFixed }"
      :style="{
        'padding-top': `${statusBarH}px`,
      }"
      v-if="backIconShow"
    >
      <div class="header-content">
        <div
          class="icon-back icon-back-img rotate-icon"
          @click="closeWebview"
          v-show="!hideCloseIcon"
        >
          <img :src="backIcon" alt="" />
        </div>
        <div
          v-if="alwaysShowRightIcon && (slots.right || rightIcon)"
          class="right-icon"
          @click="handleClickIcon"
        >
          <slot name="right" v-if="slots.right"></slot>
          <img :src="rightIcon" alt="" v-else v-show="rightIcon" />
        </div>
      </div>
    </div>

    <div
      class="header-bar-fixed"
      :class="{ 'on-page': !headerFixed }"
      :style="{
        'padding-top': `${statusBarH}px`,
        color: fontColor,
        opacity: backIconShow ? fixedBgOpacityV : 1,
      }"
      v-if="showTitle"
    >
      <div
        class="header-bar-bg"
        :style="{
          'background-color': `${fixedBgColor || fixedBgColorV}`,
          opacity: fixedBgOpacityV,
        }"
      ></div>
      <div class="header-content">
        <van-icon
          name="arrow-left"
          class="icon-back rotate-icon"
          v-show="!hideCloseIcon"
          @click.native="closeWebview"
        />
        <template v-if="$slots.title">
          <slot name="title"></slot>
        </template>
        <div class="title" v-else-if="showTitle && pageTitle">
          {{ pageTitle }}
        </div>
        <div class="right-icon" @click="handleClickIcon">
          <slot name="right" v-if="slots.right"></slot>
          <img :src="rightIcon" alt="" v-else v-show="rightIcon" />
        </div>
        <slot></slot>
      </div>
      <slot name="bottom"></slot>
    </div>
  </div>
</template>

<script setup>
import {
  getCurrentInstance,
  ref,
  onMounted,
  watch,
  computed,
  useSlots,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import { i18n } from "@/i18n/index.js";
import iconBack from "@/assets/images/icon/icon-back-circle.svg";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const slots = useSlots();

const emit = defineEmits([
  "emit:rightIcon",
  "emit:scrollChangeBgShow",
  "emit:beforeClose",
]);

const props = defineProps({
  // 标题文案
  title: {
    type: String,
    default: "",
  },
  // 是否显示标题文案
  showTitle: {
    type: Boolean,
    default: true,
  },
  // 是否需要导航栏占位
  padding: {
    type: Boolean,
    default: true,
  },
  // 是否需要标题高度占位
  contentPadding: {
    type: Boolean,
    default: true,
  },
  // 标题文案是否固定在顶部
  headerFixed: {
    type: Boolean,
    default: true,
  },
  // 是否需要下滑页面后添加标题背景颜色，默认白色
  isScrollChangeBg: {
    type: Boolean,
    default: false,
  },
  // 设置状态栏高度
  statusBarPadding: {
    type: Number,
    default: 0,
  },
  // 左侧按钮图片地址
  backIcon: {
    type: String,
    default: iconBack,
  },
  // 左侧按钮是否展示
  backIconShow: {
    type: Boolean,
    default: false,
  },
  // 左侧按钮点击操作
  closeType: {
    type: String,
    default: "back",
  },
  // 右侧按钮图片地址
  rightIcon: {
    type: String,
    default: "",
  },
  // 右侧按钮总是显示
  alwaysShowRightIcon: {
    type: Boolean,
    default: false,
  },
  // 占位背景颜色
  barBgColor: {
    type: String,
    default: "",
  },
  // 导航条背景颜色
  fixedBgColor: {
    type: String,
    default: "",
  },
  // 文字颜色
  fontColor: {
    type: String,
    default: "#000",
  },
  // 透明度倍率
  opacityRate: {
    type: Number,
    default: 1,
  },
  // rootMargin配置
  observerRootMargin: {
    type: String,
    default: "20px",
  },
});

const appNavBarHeight = ref(0);

const hideCloseIcon = computed(() => {
  return proxy.$isHalf && props.closeType === "close";
});

const statusBarH = computed(() => {
  if (props.statusBarPadding) return props.statusBarPadding;
  return proxy.$isHalf ? 0 : appNavBarHeight.value;
});

const pageTitle = ref("");
// 默认取页面标题
const i18nTitle = t(`pageTitle.${route.name}`);
if (!i18nTitle.includes("pageTitle")) pageTitle.value = i18nTitle;
watch(
  () => props.title,
  (val) => {
    if (val) {
      pageTitle.value = val;
    }
  },
  {
    immediate: true,
  }
);

const handleClickIcon = () => {
  emit("emit:rightIcon");
};

const closeWebview = () => {
  emit("emit:beforeClose");
  if (props.closeType === "close") proxy.$siyaApp("closeWebview");
  else if (props.closeType === "back") router.go(-1);
};

const fixedBgColorV = ref();
const fixedBgOpacityV = ref();
const checkRef = ref();
const checkHeaderBarBgColor = () => {
  let options = {
    rootMargin: props.observerRootMargin,
    threshold: [0, 0.2, 0.4, 0.6, 0.8, 1],
  };

  let observer = new IntersectionObserver((entries, observer) => {
    const el = entries[0];
    if (el.intersectionRatio < 1 && !fixedBgColorV.value) {
      fixedBgColorV.value = "#fff";
      emit("emit:scrollChangeBgShow", true);
    } else if (el.intersectionRatio === 1) {
      fixedBgColorV.value = "";
      emit("emit:scrollChangeBgShow", false);
    }
    fixedBgOpacityV.value = (1 - el.intersectionRatio) * props.opacityRate;
  }, options);
  observer.observe(checkRef.value);
};

onMounted(() => {
  appNavBarHeight.value = Math.max(proxy.$appNavBarHeight, 44);

  if (props.isScrollChangeBg) checkHeaderBarBgColor();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

// $headerHeight: px2rem(46);
$headerHeight: v-bind(
  "proxy.$isHalf?proxy.$pxToRemPx(54)+'px':proxy.$pxToRemPx(46)+'px'"
);

.check {
  box-sizing: content-box;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: $headerHeight;
  pointer-events: none;
}

.header-padding {
  box-sizing: content-box;
  width: 100%;

  .content-padding {
    height: $headerHeight;
  }
}

.header-bar-fixed {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $ZIndexHeader;
  box-sizing: content-box;
  height: $headerHeight;
  animation: opacify 0.3s linear;

  &.on-page {
    position: relative;
  }

  .header-bar-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .header-content {
    position: relative;
    height: $headerHeight;
    padding: 0 px2rem(50);
  }

  .icon-back {
    position: absolute;
    top: 50%;
    left: $gap16;
    z-index: 10;
    width: px2rem(24);
    height: px2rem(24);
    margin-top: px2rem(-12);
    font-size: px2rem(24);
    //transform: translateY(-50%);

    // &.icon-back-img {
    //   top: $gap2;
    //   margin-top: 0;
    // }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .title {
    height: $headerHeight;
    font-size: px2rem(20);
    font-weight: $fontWeightBold;
    line-height: $headerHeight;
    text-align: left;
    white-space: nowrap;
  }

  .right-icon {
    position: absolute;
    top: 50%;
    right: $gap16;
    height: px2rem(28);
    margin-top: px2rem(-12);
    font-size: 0;

    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
