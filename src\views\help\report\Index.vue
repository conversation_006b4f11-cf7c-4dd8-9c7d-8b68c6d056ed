<template>
<div class="app-container flex">
  <div class="list">
    <div class="cell flex"
         v-for="(cell, idx) in list" :key="idx"
         @click="goDetail(cell)"
    >
      <div class="cell-title">{{cell.name}}</div>
      <img class="icon-arrow rotate-icon" src="@/assets/images/icon/icon-arrow-right.svg" alt="">
    </div>
  </div>

  <div class="tips">{{ $t('help.report_tips') }}</div>
</div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router';
import helpApi from '@/api/help.js'

const route = useRoute()
const router = useRouter()

const { type, userId, reportId, channel } = route.query

const list = ref([])
const getList = async () => {
  const response = await helpApi.report_cfg({
    // 1:举报动态 2:举报评论 3:举报用户 4:举报私信 5:房间举报 6:家族举报
    type: Number(type),
  })
  if (response.code === 200) {
    list.value = response.data
  }
}

const goDetail = (cell) => {
  router.push({
    name: 'ReportDetail',
    query: {
      reason: JSON.stringify({
        ...cell,
        type: Number(type),
        userId: Number(userId),
        reportId: Number(reportId),
        channel: Number(channel),
      }),
    }
  })
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding: px2rem(16);
  flex-direction: column;
  flex-wrap: nowrap;
}

.list {
  width: 100%;
  padding: px2rem(6);
  margin-bottom: $gap16;
  background-color: $white;
  border-radius: $radius16;

  .cell {
    height: px2rem(50);
    padding-left: px2rem(4);
  }

  .cell-title {
    flex: 1;
    font-size: px2rem(15);
    line-height: px2rem(10);
    color: $fontColorB0;
  }

  .icon-arrow {
    width: px2rem(24);
    height: px2rem(24);
  }
}

.tips {
  margin-top: auto;
  padding: px2rem(10) px2rem(16);
  font-size: px2rem(11);
  text-align: center;
  color: $fontColorB3;
  background-color: $bgColorB7;
}
</style>
