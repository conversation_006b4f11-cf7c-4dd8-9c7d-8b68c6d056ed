<template>
  <div class="block-title" :style="style">
    <div class="content">
      <gap :gap="iconGap"></gap>
      <slot v-if="$slots.default"></slot>
      <span v-else>{{ title }}</span>
      <gap :gap="iconGap"></gap>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();
const props = defineProps({
  title: {
    type: String,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  iconSize: {
    type: String,
    default: "large",
  },
  fontSize: {
    type: Number,
    default: 22,
  },
  iconGap: {
    type: Number,
    default: 10,
  },
});
const iconSizeMap = {
  large: [54, 35],
  small: [26, 22],
};
const style = computed(() => {
  const [w, h] = iconSizeMap[props.iconSize];
  const iconWidth = props.showIcon ? proxy.$pxToRemPx(w || 36) : 0;
  const iconHeight = proxy.$pxToRemPx(h || 30);
  const lineWidth = props.showLine ? proxy.$pxToRemPx(props.lineWidth) : 0;
  const fontSize = proxy.$pxToRemPx(props.fontSize);

  return {
    "--block-title-font-size": `${fontSize}px`,
    "--block-title-line-width": `${lineWidth}px`,
    "--block-title-icon-width": `${iconWidth}px`,
    "--block-title-icon-height": `${iconHeight}px`,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-title {
  display: flex;
  align-items: center;
  width: fit-content;
  margin: 0 auto;
  .content {
    flex-grow: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > span {
      text-align: center;
      text-shadow: 0px 0px px2rem(18) rgba(255, 234, 0, 0.4),
        0px 4px 4px rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(22);
      font-style: italic;
      font-weight: 900;
      line-height: normal;
      background: linear-gradient(180deg, #fff 0%, #ffba24 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      padding: 0 px2rem(3);
    }
    &::before,
    &::after {
      content: "";
      width: var(--block-title-icon-width);
      height: var(--block-title-icon-height);
      flex-shrink: 0;
      background-size: 100% 100%;
      background-image: url("@/assets/images/activity/egypt-national-day/shap.png");
    }
    &::after {
      transform: scaleX(-1);
    }
    div {
      flex-shrink: 0;
    }
  }
}

.rtl-html {
  .block-title {
    .content {
      &::before {
        transform: scaleX(-1);
      }
      &::after {
        transform: scaleX(1);
      }
    }
  }
}
</style>
