<template>
  <div class="app-container flex">
    <header>
      <header-bar font-color="#fff" close-type="close"/>
      <div class="tab-wrap flex">
        <div class="tab"
             :class="{active: currentTabIdx === idx}"
             v-for="(tab, idx) in tabList" :key="idx"
             @click="onChangeTab(idx)"
        >
          {{ tab }}
        </div>
      </div>
    </header>

    <swiper class="main"
            :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
            :modules="modules"
            slides-per-view="auto"
            :space-between="0"
            @swiper="setControlledSwiper"
            @slideChange="onSlideChange"
    >
      <swiper-slide class="swipe-item" v-for="(n, idx) in tabList" :key="idx">
        <div class="rule-cell cell">
          <div class="rule-title flex">
            <img class="front-dot" src="@/assets/images/daily-ranking/dot-title-left.svg" alt="">
            <gap :gap="16"></gap>
            <span>{{ $t('base_total.daily_ranking_rule_title') }}</span>
            <gap :gap="16"></gap>
            <img class="back-dot" src="@/assets/images/daily-ranking/dot-title-right.svg" alt="">
          </div>
          <div class="rule-desc">
            <div class="rule-desc-text flex"
                 v-for="(text, idx) in ruleList[idx]" :key="idx"
            >
              <div class="tips-step"></div>
              <div>{{text}}</div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>

</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { i18n } from '@/i18n/index.js'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Controller } from 'swiper/modules'
import baseForAppApi from '@/api/baseforapp.js'

const t = i18n.global.t
const route = useRoute()

const modules = [Controller]
const controlledSwiper = ref(null);
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};

const tabList = ref([
  t('base_total.daily_ranking_charm_ranking'),
  t('base_total.daily_ranking_wealth_ranking'),
  t('base_total.daily_ranking_room_ranking'),
  t('base_total.daily_ranking_cp_ranking'),
])

const currentTabIdx = ref(0)
const onChangeTab = (idx) => {
  controlledSwiper.value.slideTo(idx)
}
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex
  if (idx === currentTabIdx.value) return
  onChangeSwipe(idx)
};
const onChangeSwipe = (n) => {
  currentTabIdx.value = n
}

const UTCOffset = ref('+0')
const defaultRuleList = () => {
  return [
    [
      t('base_total.daily_ranking_charm_ranking_rule_1'),
      t('base_total.daily_ranking_charm_ranking_rule_2', [UTCOffset.value]),
      t('base_total.daily_ranking_charm_ranking_rule_3'),
    ],
    [
      t('base_total.daily_ranking_wealth_ranking_rule_1'),
      t('base_total.daily_ranking_charm_ranking_rule_2', [UTCOffset.value]),
      t('base_total.daily_ranking_wealth_ranking_rule_2'),
    ],
    [
      t('base_total.daily_ranking_room_ranking_rule_1'),
      t('base_total.daily_ranking_charm_ranking_rule_2', [UTCOffset.value]),
      t('base_total.daily_ranking_room_ranking_rule_2'),
    ],
    [
      t('base_total.daily_ranking_cp_ranking_rule_1'),
      t('base_total.daily_ranking_charm_ranking_rule_2', [UTCOffset.value]),
      t('base_total.daily_ranking_cp_ranking_rule_2'),
    ],
  ]
}
const ruleList = ref([])

const getRankInfo = async () => {
  const response = await baseForAppApi.rank_info()
  if (response.code === 200) {
    UTCOffset.value = response.data.offset
    ruleList.value = defaultRuleList()
  }
}

onMounted(() => {
  // tab:  1 魅力; 2 财富; 3 房间; 4 cp
  const tab = Number(route.query.tab)
  if (tab) onChangeTab(tab - 1)

  getRankInfo()
})

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

$pageBgColor: #190F2C;

.app-container {
  flex-direction: column;
  height: 100vh;
  background-color: $pageBgColor;
}

header {
  width: 100%;
}

.tab-wrap {
  padding: $gap6 $gap16;
  border-bottom: 1px solid #FFFFFF1F;

  .tab {
    flex: 1;
    height: px2rem(24);
    color: rgba($white, .7);

    @include fontSize15;
    @include centerV;

    &.active {
      font-weight: $fontWeightBold;
      color: $white;

      @include fontSize17;
    }
  }
}

.main {
  flex: 1;
  width: 100vw;
  overflow: hidden;
}

.swipe-item {
  overflow-y: scroll;
  color: $white;
  padding-bottom: $pageBottomPadding;
}

.cell {
  padding: $gap32 $gap16;

  &.rule-cell {
    position: sticky;
    top: 0;
    background-color: $pageBgColor;
  }

  .rule-title {
    justify-content: center;
    margin-bottom: $gap4;
    font-weight: $fontWeightBold;
    text-align: center;

    @include fontSize17;

    .front-dot,
    .back-dot {
      width: px2rem(34);
      height: px2rem(8);
    }
  }

  .rule-desc-text {
    flex-wrap: nowrap;
    align-items: stretch;
    margin-bottom: $gap14;

    @include fontSize13;

    .tips-step {
      flex: none;
      width: px2rem(4);
      height: px2rem(4);
      margin: $gap6 $gap8;
      background-color: $white;
      border-radius: 50%;
    }
  }
}
</style>
