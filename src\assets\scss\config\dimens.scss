@use "sass:math";

// 定义预计根元素 fontSize
$rootFontSize: math.div(390, 10);
// 定义像素转化为 rem 函数
@function px2rem ($px) {
  @return calc(math.div(math.div($px, 1), $rootFontSize)) + rem;
}

$navBarHeight: px2rem(44);
// 手机中 stateBar 高度
$stateBarHeight: px2rem(22);
// IphoneX 底部操作位置高度
$iphoneXBottomHeight: px2rem(34);


// border
$radius4: px2rem(4);
$radius8: px2rem(8);
$radius10: px2rem(10);
$radius12: px2rem(12);
$radius16: px2rem(16);
$radius24: px2rem(24);

// margin gap
$gap1: px2rem(1);
$gap2: px2rem(2);
$gap4: px2rem(4);
$gap6: px2rem(6);
$gap8: px2rem(8);
$gap10: px2rem(10);
$gap12: px2rem(12);
$gap14: px2rem(14);
$gap16: px2rem(16);
$gap18: px2rem(18);
$gap20: px2rem(20);
$gap24: px2rem(24);
$gap32: px2rem(32);

$pageBottomPadding: px2rem(100);

// z-index
$ZIndexHeader: 1000;

// 字重
$fontWeightNormal: normal; // 500
$fontWeightBold: 700; // 700
$fontWeightHeavy: 900;

// 字号
$fontSize10: px2rem(10);
$fontSize11: px2rem(11);
$fontSize13: px2rem(13);
$fontSize15: px2rem(15);
$fontSize17: px2rem(17);


@mixin fontSize10() {
  font-size: $fontSize10;
  line-height: 1.2em;
}

@mixin fontSize11() {
  font-size: $fontSize11;
  line-height: 1.2em;
}

@mixin fontSize13() {
  font-size: $fontSize13;
  line-height: 1.24em;
}

@mixin fontSize15() {
  font-size: $fontSize15;
  line-height: 1.28em;
}

@mixin fontSize17() {
  font-size: $fontSize17;
  line-height: 1.32em;
}
