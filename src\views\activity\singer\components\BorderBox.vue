<template>
  <div class="border-box">
    <div class="box-inner">
      <slot></slot>
    </div>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.border-box {
  border-radius: px2rem(12);
  box-shadow: -0.9px 0px 1.6px 0px rgba(255, 255, 255, 0.84),
    1px 0px 0.5px 0px rgba(255, 255, 255, 0.97),
    0px -0.7px 0.2px 0px rgba(255, 255, 255, 0.69), 0px 1px 1px 0px #fff;
  border-radius: 12px;
  background: linear-gradient(
    180deg,
    #eb43ff 0%,
    #00c1ff 33%,
    #0092ed 66%,
    #7363ff 100%
  );
  width: 100%;
  margin: 0 auto;
  padding: px2rem(5);
  filter: drop-shadow(0px 0px 27.7px rgba(161, 0, 255, 0.5))
    drop-shadow(0px 4px 4px rgba(0, 0, 0, 0.25));
  .box-inner {
    border-radius: px2rem(8);
    background: linear-gradient(180deg, #5e1baf 0%, #1a003b 100%);
    box-shadow: -1px 0px 1.6px 0px rgba(255, 255, 255, 0.84),
      1px 0px 0.5px 0px rgba(255, 255, 255, 0.97),
      0px -0.7px 0.2px 0px rgba(255, 255, 255, 0.69), 0px 1px 1px 0px #fff;
  }
}
</style>
