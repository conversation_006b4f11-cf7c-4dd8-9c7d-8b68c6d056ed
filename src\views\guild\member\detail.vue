<template>
  <div class="app-container page-member-detail">
    <header-bar is-scroll-change-bg barBgColor="#fff" close-type="back" :title="t('guild.guild_member_detail')"/>
    <main>
      <div :class="{'member-detail-top':true,'opacity-48':userInfo.ban_status}">
        <div class="avatar">
          <img :src="userInfo.avatar" />
          <div class="dot" v-if="userInfo.online"></div>
        </div>
        <gap :gap="8" />
        <div class="info">
          <div class="name">
            <flag :flag="userInfo.flag" :size="16"></flag>
            <gap v-if="userInfo.flag" :gap="6" />
            <span v-if="!userInfo.ban_status">{{userInfo.nickname}}</span>
            <span v-else style="color: #FF4771;">{{ $t('guild.common_banned') }}</span>
          </div>
          <div class="id">
            <span>ID</span>
            <span>:</span>
            <span>{{userInfo.user_account}}</span>
            <gap :gap="2" />
            <img src="@/assets/images/common/copy.png" alt="" @click="copyText(userInfo.user_account,$event)">
          </div>
          <div class="time">
            <span>{{t('guild.guild_join_guild_time')}}:</span>
            <span>{{userInfo.join_guild}}</span>
          </div>
        </div>
      </div>
      <div class="filter-time">
        <div class="filter-time-content" @click="showTimeSelect=true">
          <span>{{dateStr}}</span>
          <gap :gap="4" />
          <img class="filter-time-img" src="@/assets/images/common/downArrow.png" alt="" />
        </div>     
      </div>
      <div class="member-detail">
        <div class="total-income">
          <span>{{t('guild.guild_member_income_total')}}:</span>
          <img src="@/assets/images/guild/diamond.png" alt="" />
          <span class="bold">+{{userInfo.income}}</span>
        </div>
        <div class="income-detail" v-for="item in incomeList" :key="item.value">
          <span class="income-title">{{item.text}}:</span>
          <span class="income-num">{{userInfo[item.value]}}</span>
        </div>
      </div>
      <van-popup position="bottom" v-model:show="showTimeSelect" class="pop">
        <timeSelect @confirm="checkDate" />
      </van-popup>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick,computed,reactive } from 'vue'
import guildApi from '@/api/guild.js'
import { i18n } from '@/i18n/index.js'
import {getTimeRange} from '@/utils/util.js'
import clipboard from "@/utils/clipboard";
import { useRoute } from 'vue-router';
import timeSelect from './components/timeSelect.vue'

const route = useRoute();
const { proxy } = getCurrentInstance()
const t = i18n.global.t
const showTimeSelect = ref(false)
const dateStr = ref(`${t('guild.common_last_30_day')}`)
const incomeList = ref([
  {text: t('guild.guild_member_income_chat'),value: 'chat_income'},
  {text: t('guild.guild_member_income_chat_gift'),value: 'chat_gift_income'},
  {text: t('guild.guild_member_income_video'),value: 'vedio_income'},
  {text: t('guild.guild_member_income_voice'),value: 'voice_income'},
  {text: t('guild.guild_member_income_party_gift'),value: 'party_gift_income'},
  {text: t('guild.guild_member_income_bonus'),value: 'platform_bonus'},
])

const params = reactive({
  account: route.params.id,
  startTs: "",
  endTs: "",
  timeType: 8
})
const userInfo = ref({})

const getData = () => {
  guildApi.member_info(params).then(res => {
    if (res.code == 200) {
      userInfo.value = res.data
    }
  })
}
const checkDate = (val,str) => {
  params.startStr = val.startStr
  params.endStr = val.endStr
  params.timeType = val.timeType
  dateStr.value = str
  showTimeSelect.value = false
  getData()
}
const copyText = (id,event) => {
  const msg = t('guild.common_copy_success')
  clipboard(id,event,msg)
}
onMounted(() => {
  params.startTs = getTimeRange(8).startTime
  params.endTs = getTimeRange(8).endTime
  getData()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.page-member-detail {
  height: 100vh;
  overflow: hidden;
}
.member-detail-top {
  display: flex;
  background-color: $white;
  padding: px2rem(8) px2rem(16);
  .avatar {
    position: relative;
    height: px2rem(56);
    width: px2rem(56);
    border-radius: 50%;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 50%;
    }
    .dot {
      width: px2rem(10);
      height: px2rem(10);
      background: $subColorGreen;
      border-radius: 50%;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
  .info {
    .name {
      font-size: $fontSize15;
      font-weight: 700;
      display: flex;
      align-items: center;
      img {
        width: px2rem(22);
        height: px2rem(16);
        background: pink;
      }
    }
    .id {
      font-size: $fontSize13;
      color: $fontColorB3;
      margin-top: px2rem(4);
      display: flex;
      align-items: center;
      img {
        width: px2rem(12);
        height: px2rem(12);
      }
    }
    .time {
      margin-top: px2rem(4);
      font-size: $fontSize11;
      color: $fontColorB3;
    }
  }
}
.opacity-48 {
  opacity: 0.52;
  background-image: url('@/assets/images/common/forbidden.png');
  background-size: 20% auto; 
  // 修改背景图片的位置为居右
  background-position: right center; 
  // 保持原有的不重复属性
  background-repeat: no-repeat;
}
.filter-time {
  background-color: $white;
  .filter-time-content {
    padding: px2rem(10) px2rem(16);
    font-size: $fontSize13;
    color: $mainColor;
    display: flex;
    align-items: center;
    width: fit-content;
  }
  .filter-time-img {
    width: px2rem(7);
  }
  .filter-sort-img {
    width: px2rem(13);
  }
}
.member-detail {
  padding: px2rem(16);
  background-color: $white;
  margin-top: px2rem(8);
  height: calc(100vh - px2rem(100));
  .total-income {
    background-color: #ECEBFF;
    border-radius: px2rem(8);
    font-size: $fontSize13;
    color: $fontColorB2;
    padding: px2rem(12) px2rem(8);
    display: flex;
    align-items: center;
    margin-bottom: px2rem(4);
    img {
      width: px2rem(18);
      height: px2rem(18);
    }
    .bold {
      font-weight: 700;
      font-size: $fontSize17;
      color: $fontColorB1;
    }
  }
  .income-detail {
    font-size: $fontSize13;
    color: $fontColorB2;
    padding: px2rem(12) 0;
    border-bottom: 1px solid $bgColorB6;
    .income-num {
      font-weight: 700;
    }
  }
}
.pop {
    padding: px2rem(16);
    border-radius: px2rem(26) px2rem(26) 0 0;
    padding-bottom: px2rem(32);
    .pop-title {
      font-size: $fontSize17;
      font-weight: 700;
      text-align: center;
      margin: px2rem(16) 0;
    }
    .tab-content {
      margin-top: px2rem(16);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .tab-item {
        width: 49%;
        font-size: px2rem(16);
        color: $fontColorB4;
        background-color: $bgColorB6;
        font-weight: normal;
        margin-bottom: px2rem(10);
        text-align: center;
        padding: px2rem(10) 0;
        border-radius: px2rem(8);
        box-sizing: border-box;
        border: 1px solid transparent;
      }
    }
    .customize {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .customize-item {
        width: px2rem(159);
        padding: px2rem(10) 0;
        background-color: $bgColorB6;
        color: $fontColorB4;
        font-size: px2rem(16);
        text-align: center;
        border-radius: px2rem(8);
        box-sizing: border-box;
      }
      .divider {
        width: px2rem(16);
        height: px2rem(1);
        background-color: #D9D9D9;
      }
    }
    .okbtn {
      width: 100%;
      margin-top: px2rem(16);
      font-weight: $fontWeightBold;
    }
  }
</style>
