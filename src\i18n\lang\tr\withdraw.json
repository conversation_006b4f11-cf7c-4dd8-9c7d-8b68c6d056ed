{"withdrawal_rule_title": "Para Çekme Talimatları", "withdrawal_rule_detail": ["1.Başarılı bir para çekme talebinden sonra paranın 1-7 iş günü içinde ulaşması beklenir. Para tahmini süre içinde ulaşmadıysa, lütfen aldığınız hesabınızın doğru olduğunu onaylayın ve sabırla bekleyin veya müşteri hizmetleriyle iletişime geçin.", "2.<PERSON><PERSON><PERSON>n miktara ba<PERSON><PERSON><PERSON>, beli<PERSON>i miktar fiili çekim ücretine tabi olmak üzere farklı para çekme ücretleri gereklidir. alınan tutar.", "3.Hesabınızı iptal etmeniz veya para çekme süresindeki ihlaller nedeniyle yasaklanmanız durumunda, para çekme talimatınızı donduracağız."], "withdrawal_select_methods_tips": "Para çekme, ödeme yönteminizin bağlanmasını gerektirir. Lütfen ödeme yönteminizi seçin ve ödeme bilgilerinizin doğru ve geçerli olduğundan emin olun.", "service_fee": "<PERSON><PERSON><PERSON>ti: {0}", "withdrawal_methods": "<PERSON><PERSON><PERSON>", "withdrawal_information": "Çekilme Bilgileri", "withdrawal_info_check": "Lütfen para çekme bilgilerinizi onaylayın.", "estimated_withdrawal_amount": "<PERSON><PERSON><PERSON> para çekme tutarı", "estimated_amount_received": "Alınması gereken tahmini tutar", "estimated_service_fee": "<PERSON><PERSON><PERSON>", "current_selected": "<PERSON><PERSON><PERSON>", "completed": "Zaten dold<PERSON>lm<PERSON>ş", "need_complete": "Tamamlanacak", "withdrawal_amount": "Çekme tutarı", "total_withdrawal_amount": "Kümülatif para çekme tutarı", "withdrawal_pending": "geri <PERSON><PERSON><PERSON><PERSON> devam ediyor", "withdrawal_success": "Para çekme başarılı", "withdrawal_fail": "Para çekme işlemi başarısız oldu", "fail_reason": "Para çekme başarısızlığının nedeni", "empty": "Para çekme kaydı yok", "BANK_TRANSFER_receive_bank": "Alacaklı banka", "BANK_TRANSFER_receive_bank_placeholder": "Alacaklı bankayı seçin", "BANK_TRANSFER_fill_info_tips": "Lütfen ödeme bilgilerinizi giriniz", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "ör.***********", "payeeInfo_documentId_hint": "Lütfen CPF/CNPJ (Bireysel/Kurumsal Vergi Numarası) giriniz", "payeeInfo_address_title": "<PERSON><PERSON>", "payeeInfo_email_title": "E-posta", "payeeInfo_email_placeholder": "ör.1234566{0}gmail.com", "payeeInfo_email_hint": "Lütfen doğru e-posta formatını girin", "payeeInfo_phone_title": "Telefon numarası", "WALLET_accountNo_title": "{0}hesap", "WALLET_accountNo_placeholder_SA": "mesal.************", "WALLET_accountNo_placeholder_EG": "mesal.0**********", "WALLET_accountNo_placeholder_AE": "Örneğin +971-*********", "WALLET_accountNo_placeholder_TR": "mesal.**********", "WALLET_accountNo_placeholder_PH": "mesal.***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "mesal.mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "mesal.***********", "WALLET_accountNo_placeholder_BR_PagBank": "mesal.************{0}123.com", "WALLET_accountNo_placeholder_ID": "örn.***********", "WALLET_accountNo_placeholder_MY": "örn.**********", "WALLET_accountNo_placeholder_TH": "örn.**********", "WALLET_accountNo_placeholder_VN": "örn.***********", "WALLET_accountNo_hint_SA": "Lütfen cüzdanınıza bağlı telefon numarasını 9665 ile başlayan 12 haneli olarak giriniz.", "WALLET_accountNo_hint_EG": "Lütfen cüzdanınıza bağlı telefon numarasını 01 ile başlayan 11 haneli giriniz.", "WALLET_accountNo_hint_EG_2": "Lütfen cüzdanınıza bağlı telefon numarasını girin, 01 ile başlayan 12 hane veya ülke kodu artı 10 hane", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "Ödeyenin telefon numarası-Gerekli-Cüzdana bağlı telefon numarası, 01 veya 201 ile başlayan 11 hane", "WALLET_accountNo_hint_AE": "Lütfen cüzdanınıza bağlı cep telefonu numarasını giriniz. Format + ülke kodu - cep telefonu numarası olmalıdır.", "WALLET_accountNo_hint_TR": "Lütfen cüzdanınıza bağlı telefon numarasını 10 haneli veya PL artı 10 haneli olarak girin", "WALLET_accountNo_hint_PH": "Lütfen cüzdanınıza bağlı telefon numaranızı 09 ile başlayan 11 haneli giriniz.", "WALLET_accountNo_hint_PH_LAZADAPH": "Lütfen cüzdanınıza bağlı telefon numarasını, 09 ile başlayan 11 haneli veya e-posta adresini giriniz.", "WALLET_accountNo_hint_BR_MERCADOPAGO": "Lütfen cüzdana bağlı kullanıcı hesabını (e-posta veya kimlik) girin", "WALLET_accountNo_hint_BR_PIX": "Lütfen cüzdana bağlı ilgili hesabı girin", "WALLET_accountNo_hint_BR_PagBank": "Lütfen cüzdanınıza bağlı e-posta adresini en fazla 60 karakter olacak şekilde girin", "WALLET_accountNo_hint_ID": "Lütfen cüzdan bağlantılı hesap numaranızı girin:08 ile başlayan 9 ila 14 hane", "WALLET_accountNo_hint_MY": "Lütfen cüzdan bağlantılı hesap numaranızı girin:0 ile başlayan 10 veya 11 hane", "WALLET_accountNo_hint_TH": "Lütfen cüzdan bağlantılı hesap numaranızı girin: 0 ile başlayan 10 haneli", "WALLET_accountNo_hint_VN": "Lütfen cüzdan bağlantılı hesap numaranızı girin:84 ile başlayan 11 hane", "WALLET_fullName_title": "Alacaklının Adı", "WALLET_fullName_placeholder": "<PERSON><PERSON>, Maldonado, <PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "mesal .<PERSON>", "WALLET_fullName_placeholder_PH": "<PERSON><PERSON>, Maldonado, <PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "mesal <PERSON> Evan<PERSON>", "WALLET_fullName_hint": "Lütfen tam İngilizce adınızı girin", "WALLET_lastName_title": "Ödeme yapanın soyadı", "WALLET_lastName_placeholder_EG": "<PERSON><PERSON><PERSON>", "WALLET_lastName_hint": "Lütfen soyadınızı girin", "WALLET_accountType_title": "{0}<PERSON><PERSON><PERSON> b<PERSON>", "WALLET_accountType_placeholder_BR_PIX": "Lütfen bağlayıcı hesap türünü seçin", "WALLET_accountType_option_E_PIX": "E-POSTA", "WALLET_accountType_option_P_PIX": "Telefon ", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (Kişisel/Kurumsal Vergi Numarası)", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "örneğin 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "SWIFT hesabı", "SWIFT_accountNo_placeholder_SA": "mesal.************************", "SWIFT_accountNo_placeholder_AE": "mesal.***********************", "SWIFT_accountNo_placeholder_KW": "mesal.******************************", "SWIFT_accountNo_placeholder_QA": "mesal.*****************************", "SWIFT_accountNo_placeholder_TR": "mesal.TR32001000999990**********", "SWIFT_accountNo_placeholder_BH": "mesal.**********************", "SWIFT_accountNo_placeholder_YE": "mesal.**********", "SWIFT_accountNo_placeholder_IQ": "mesal.***********************", "SWIFT_accountNo_placeholder_IL": "mesal.***********************", "SWIFT_accountNo_placeholder_PS": "mesal.**********", "SWIFT_accountNo_placeholder_AZ": "mesal.****************************", "SWIFT_accountNo_placeholder_LB": "mesal.****************************", "SWIFT_accountNo_placeholder_MA": "mesal.**********", "SWIFT_accountNo_placeholder_PH": "mesal.**********", "SWIFT_accountNo_placeholder_BR": "mesal.*****************************", "SWIFT_accountNo_placeholder_EG": "mesal.*****************************", "SWIFT_accountNo_placeholder_JO": "ör.******************************", "SWIFT_accountNo_hint_SA": "Lütfen SWIFT hesabını giriniz,", "SWIFT_accountNo_hint_AE": "Lütfen SWIFT hesabınızı AE ile başlayan 23 karakterden girin", "SWIFT_accountNo_hint_KW": "Lütfen SWIFT hesabınızı KW ile başlayan 30 karakter girin", "SWIFT_accountNo_hint_QA": "Lütfen SWIFT hesabın<PERSON><PERSON><PERSON> girin, QA ile başlayan 29 karakter", "SWIFT_accountNo_hint_TR": "Lütfen TR ile başlayan 26 karakterden oluşan SWIFT hesabınızı giriniz.", "SWIFT_accountNo_hint_BH": "Lütfen SWIFT hesabınızı BH ile başlayan 22 karakterle girin", "SWIFT_accountNo_hint_YE": "Lütfen SWIFT hesabın<PERSON><PERSON><PERSON> girin, 34 rakam ve harf veya daha az", "SWIFT_accountNo_hint_IQ": "Lütfen SWIFT hesabınızı IQ ile başlayan 23 karakterle girin", "SWIFT_accountNo_hint_IL": "Lütfen SWIFT hesabınızı IL ile başlayan 23 karakterle girin", "SWIFT_accountNo_hint_PS": "Lütfen SWIFT hesabın<PERSON><PERSON><PERSON> girin, 34 rakam ve harf veya daha az", "SWIFT_accountNo_hint_AZ": "Lütfen SWIFT hesabınızı A'dan Z'ye kadar 28 karakterle girin", "SWIFT_accountNo_hint_LB": "Lütfen SWIFT hesabınızı LB ile başlayan 28 karakterle girin", "SWIFT_accountNo_hint_MA": "Lütfen SWIFT hesabın<PERSON><PERSON><PERSON> girin, 34 rakam ve harf veya daha az", "SWIFT_accountNo_hint_PH": "Lütfen SWIFT hesabın<PERSON><PERSON><PERSON> girin, 34 rakam ve harf veya daha az", "SWIFT_accountNo_hint_BR": "Lütfen BR ile başlayan 29 karakterden oluşan SWIFT hesabınızı girin", "SWIFT_accountNo_hint_EG": "Lütfen SWIFT hesabınızı EG ile başlayan 29 karakterden girin", "SWIFT_accountNo_hint_JO": "Lütfen Jo ile başlayan 30 karakterden oluşan birinin Swift hesabını girin", "SWIFT_fullName_title": "Alacaklının Adı", "SWIFT_fullName_placeholder": "mesal .<PERSON>", "SWIFT_fullName_hint": "Lütfen tam İngilizce adınızı girin", "SWIFT_bankCode_title": "Banka Numarası (SWIFT Kodu)", "SWIFT_bankCode_placeholder": "ör.SCBLHKHH", "SWIFT_bankCode_hint": "Lütfen banka kodunuzu giriniz, 8 veya 11 haneli, rakam ve büyük harf kullanımına izin verilmektedir", "CARD_accountNo_title": "Banka hesabı", "CARD_accountNo_placeholder_EG": "mesal. ***********", "CARD_accountNo_placeholder_TR": "mesal.****************", "CARD_accountNo_hint_EG": "Lütfen banka hesabını girin, uzunluk>=8", "CARD_accountNo_hint_TR": "Lütfen 16 haneli banka kartı numarasını girin, yalnızca banka kartları desteklenmektedir", "CARD_fullName_title": "Alacaklının Adı", "CARD_fullName_placeholder": "mesal <PERSON> Evan<PERSON>", "CARD_fullName_hint": "Lütfen tam İngilizce adınızı girin", "CARRIER_BILLING_accountNo_title": "Telefon numarası", "CARRIER_BILLING_accountNo_placeholder": "mesal.63**********", "CARRIER_BILLING_accountNo_hint": "Lütfen telefon numaranızı giriniz", "CASH_accountNo_title": "1%s hesap", "CASH_accountNo_placeholder": "mesal.***********", "CASH_accountNo_hint": "Lütfen hesap girin 1%s , 0 ile başlayan 11 hane", "BANK_TRANSFER_accountNo_title_SA": "Banka Hesabı (IBAN)", "BANK_TRANSFER_accountNo_title_AE": "Banka Hesabı (IBAN)", "BANK_TRANSFER_accountNo_title_TR": "Banka Hesabı (IBAN)", "BANK_TRANSFER_accountNo_title": "Banka Hesabı", "BANK_TRANSFER_accountNo_title_EG": "Banka Hesabı", "BANK_TRANSFER_accountNo_title_KW": "Banka Hesabı", "BANK_TRANSFER_accountNo_title_QA": "Banka Hesabı", "BANK_TRANSFER_accountNo_title_MA": "Banka hesabı (RIB)", "BANK_TRANSFER_accountNo_title_PH": "Banka Hesabı", "BANK_TRANSFER_accountNo_title_BR": "Banka Hesabı", "BANK_TRANSFER_accountNo_placeholder_SA": "mesal.************************", "BANK_TRANSFER_accountNo_placeholder_EG": "mesal.************", "BANK_TRANSFER_accountNo_placeholder_AE": "mesal.***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "mesal.******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "mesal.*****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "mesal.**************************", "BANK_TRANSFER_accountNo_placeholder_MA": "ör. 123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "ör.************", "BANK_TRANSFER_accountNo_placeholder_BR": "ör.***********", "BANK_TRANSFER_accountNo_placeholder_JO": "ör.******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "örn.**********", "BANK_TRANSFER_accountNo_placeholder_TH": "örn.*********", "BANK_TRANSFER_accountNo_placeholder_ID": "örn.***********", "BANK_TRANSFER_accountNo_hint_SA": "Lütfen SA+22 harf ve raka<PERSON>lardan o<PERSON>an bir kombinasyon girin", "BANK_TRANSFER_accountNo_hint_EG": "Lütfen SA+22 harf ve raka<PERSON>lardan o<PERSON>an bir kombinasyon girin", "BANK_TRANSFER_accountNo_hint_AE": "Lütfen AE+21 r<PERSON><PERSON><PERSON><PERSON><PERSON> girin, AE büyük harfle yazılmalıdır", "BANK_TRANSFER_accountNo_hint_KW": "Lütfen IBAN formatında 50 karakterden kısa bir uzunluk girin", "BANK_TRANSFER_accountNo_hint_QA": "Lütfen IBAN formatında 50 karakterden kısa bir uzunluk girin", "BANK_TRANSFER_accountNo_hint_TR": "Lütfen TR ile başlayan ve 24 haneli IBAN formatında bir sayı girin", "BANK_TRANSFER_accountNo_hint_MA": "lütfen 24 haneli rib hesap kodunu giriniz.", "BANK_TRANSFER_accountNo_hint_PH": "Lütfen 6 ila 128 haneli bir sayı girin", "BANK_TRANSFER_accountNo_hint_BR": "Lütfen 4 ila 15 haneli bir sayı giriniz", "BANK_TRANSFER_accountNo_hint_JO": "Lütfen JO ile başlayan 30 haneli harf ve rakam kombinasyonunu giriniz", "BANK_TRANSFER_accountNo_hint_MY": "Maksimum 35 karakterden oluşan bir hesap numarası girin.", "BANK_TRANSFER_accountNo_hint_TH": "Lütfen 10-18 haneli bir hesap numarası girin", "BANK_TRANSFER_accountNo_hint_ID": "<PERSON><PERSON><PERSON> bir hesap numa<PERSON>ı girin.", "BANK_TRANSFER_bankBranch_title": "Banka Şube Numarası", "BANK_TRANSFER_bankBranch_placeholder_BR": "Ör. 1234", "BANK_TRANSFER_bankBranch_hint_BR": "Lütfen banka şube numaranızı 4 ila 6 haneli olarak giriniz", "BANK_TRANSFER_address_placeholder_SA": "Çok üzgünüm aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "Ör. ALICI CADDESİ KADIKÖY İSTANBUL", "BANK_TRANSFER_address_placeholder_KW": "<PERSON><PERSON><PERSON> xx caddesi", "BANK_TRANSFER_address_hint_SA": "Lütfen adresi giriniz, uzunluk 10~200 karakterle sınırlıdır, ya<PERSON><PERSON><PERSON><PERSON> sayı<PERSON>, noktalar, büyük ve küçük harfler ve boşluklara izin verilir", "BANK_TRANSFER_address_hint_AE": "Lütfen adresi giriniz, uzunluk sınırı 10~200 karakterdir, 1~200 karakter, banka risk kontrol doğrulaması yapacaktır", "BANK_TRANSFER_address_hint_KW": "Lütfen adresi giri<PERSON>z, uzunluk <= 255", "BANK_TRANSFER_fullName_placeholder_TR": "<PERSON><PERSON>, <PERSON>", "BANK_TRANSFER_fullName_placeholder_PH": "<PERSON><PERSON>, <PERSON>", "BANK_TRANSFER_fullName_placeholder_BR": "<PERSON><PERSON>, <PERSON>", "BANK_TRANSFER_fullName_placeholder_MA": "<PERSON><PERSON>, <PERSON>", "BANK_TRANSFER_fullName_placeholder_KW": "<PERSON><PERSON><PERSON>", "BANK_TRANSFER_phone_placeholder_PH": "Ör.***********", "BANK_TRANSFER_phone_placeholder_BR": "Ör. *************", "BANK_TRANSFER_phone_hint_PH": "Lütfen 09 ile başlayan 11 haneli cep telefonu numaranızı giriniz (0 eklenmelidir)", "BANK_TRANSFER_phone_hint_BR": "Lütfen 55 ile başlayan 12-13 haneli cep telefonu numaranızı giriniz", "BANK_TRANSFER_bankCode_title_MY": "Banka <PERSON>du (SWIFT Kodu) ", "BANK_TRANSFER_bankCode_placeholder_MY": "örn.CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "<PERSON><PERSON> <PERSON> r<PERSON><PERSON><PERSON> 8-11 karakterlik bir kombinasyon girin.", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT kodu", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "Lütfen 8 veya 11 büyük harf ve rakamdan olu<PERSON>an bir kombinasyon girin", "BANK_TRANSFER_city_title_SA": "Şehir", "BANK_TRANSFER_city_placeholder_SA": "Örneğin, New York", "BANK_TRANSFER_city_hint_SA": "Lütfen 35 karaktere kadar şehir adını girin", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "Örneğin: 00112321111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "Lütfen 24 haneli bir kombinasyon girin", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "<PERSON><PERSON><PERSON> tarihi", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "örneğin ********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "lütfen doğum tarihini girin", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "be<PERSON> tü<PERSON>", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "Lütfen belge türü<PERSON><PERSON> se<PERSON>in", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "<PERSON><PERSON>", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "Örneğin, NNN-NNNN-NNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "<PERSON><PERSON><PERSON><PERSON>, ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "Lütfen belge kimliğini girin", "AGENT_accountNo_Whatsapp_title_ALL": "Whatsapp numarası", "AGENT_accountNo_Whatsapp_placeholder_ALL": "<PERSON><PERSON><PERSON><PERSON> +90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "Lütfen Whatsapp hesabınıza giriniz", "AGENT_accountNo_vodafone_title_EG": "Vodafone hesabı", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "Blockchain adı", "USDT_address_title_ALL": "Jeton'u veren adres", "USDT_address_placeholder_ALL": "Örneğin 1111111111111111111111111", "USDT_address_hint_ALL": "Lütfen 1, 3 veya bc ile başlayan bir adres girin", "USDT_address_hint_common": "Lütfen USDT adresinizi girin"}