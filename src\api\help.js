import request from '@/utils/request'

export default {
  feedback_cfg(query) {
    return request({
      url: '/client_web/feedback/cfg',
      method: 'get',
      params: query,
    })
  },
  feedback_submit(query) {
    return request({
      url: '/client_web/feedback/submit',
      method: 'post',
      data: query,
    })
  },

  report_cfg(query) {
    return request({
      url: '/client_web/report/cfg',
      method: 'get',
      params: query,
    })
  },
  report_submit(query) {
    return request({
      url: '/client_web/report/submit',
      method: 'post',
      data: query,
    })
  },
}
