<template>
  <div class="random-ranking">
    <div
      class="change-button"
      @click="fetchData"
      v-track:click
      trace-key="singer_button_click"
      :track-params="JSON.stringify({ singer_button_name: 12 })"
    >
      {{ $t(`singer.change_one`) }}
    </div>
    <div class="ranking-list">
      <div class="rank-item" v-for="(item, index) in rankList" :key="index">
        <div class="avatar">
          <frame-avatar
            :avatar="item?.avatar"
            :frame="item.frame"
            :size="36"
          ></frame-avatar>
        </div>
        <gap :gap="8"></gap>
        <div class="nickname">{{ item.nickname }}</div>
        <gap :gap="8"></gap>
        <div class="action">
          <TicketNumber :number="item?.count"></TicketNumber>
          <div>
            <div v-if="showAudioBtn">
              <AudioPlayer :url="item.media_url"></AudioPlayer>
            </div>
            <gap :gap="4" v-if="showVoteBtn"></gap>
            <div>
              <VoteButton
                v-if="showVoteBtn"
                :user-item="item"
                :source="5"
                @vote="onVote"
              ></VoteButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import AudioPlayer from "./AudioPlayer.vue";
import TicketNumber from "./TicketNumber.vue";
import VoteButton from "./VoteButton.vue";
import { onMounted, ref } from "vue";
import singerApi from "@/api/activity.js";
const props = defineProps({
  showVoteBtn: {
    type: Boolean,
    default: true,
  },
  showAudioBtn: {
    type: Boolean,
    default: true,
  },
});
const rankList = ref([]);
const fetchData = async () => {
  const res = await singerApi.singer_friends({
    page: 1,
    size: 5,
    source: 2,
  });
  if (res.code === 200) {
    rankList.value = res.data?.list || [];
  }
};

const onVote = (item) => {
  const index = rankList.value.findIndex((i) => i.account === item.account);
  if (index > -1) {
    rankList.value[index].is_vote = true;
    rankList.value[index].count++;
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.random-ranking {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: px2rem(29);
  padding-bottom: px2rem(45);
  .change-button {
    display: inline-flex;
    padding: px2rem(10);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(7);
    border: px2rem(1) solid #b17bff;
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.17) 0%,
      rgba(112, 23, 190, 0.17) 100%
    );
    color: #d5b8ff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 14px */
  }
}

.ranking-list {
  padding: 0 px2rem(15);
  margin-top: px2rem(33);
  padding-bottom: px2rem(20);
  min-height: px2rem(300);
  width: 100%;
  .rank-item {
    display: flex;
    height: px2rem(60);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    background: #380171;
    margin-bottom: px2rem(8);
    padding: px2rem(8) px2rem(11);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .nickname {
      color: var(---White, #fff);

      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
  }
}
.action {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  & > div {
    display: flex;
    align-items: center;
  }
}
</style>
