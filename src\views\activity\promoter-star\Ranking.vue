<template>
  <div class="container">
    <header-bar
      back-icon-show
      :show-title="true"
      :title="$t(`promoter_star.siya_star_promoter`)"
      is-scroll-change-bg
      :content-padding="false"
      :right-icon="rightIcon"
      :always-show-right-icon="true"
      :statusBarPadding="!isApp ? 10 : undefined"
      close-type="close"
      @emit:right-icon="goFeedback()"
    >
    </header-bar>
    <div v-if="true" class="ended">{{ $t(`promoter_star.event_ended`) }}</div>
    <template v-else>
      <div class="banner-title">
        <div class="title">{{ $t(`promoter_star.siya_star_promoter`) }}</div>
        <div class="sub-title">
          {{ $t(`promoter_star.results_announcement`) }}
        </div>
      </div>

      <div class="video-show">
        <div class="inner">
          <div class="win-bg"></div>
          <div class="video-show-title">
            <sub-title
              :title="$t(`promoter_star.excellent_works_display`)"
            ></sub-title>
          </div>
          <div class="video-list">
            <div>
              <video-player
                url="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102832.mp4"
                cover="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102832.png"
                :icon-size="24"
              ></video-player>
            </div>
            <div>
              <video-player
                url="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102810.mp4"
                cover="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102810.png"
                :icon-size="24"
              ></video-player>
            </div>
            <div>
              <video-player
                url="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102840.mp4"
                cover="https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/video-show/20250626-102840.png"
                :icon-size="24"
              ></video-player>
            </div>
          </div>
        </div>
      </div>

      <div class="ranking-block">
        <div class="inner">
          <div class="ranking-title">
            <sub-title :title="$t(`promoter_star.list_of_winners`)"></sub-title>
          </div>
          <div class="top-box">
            <div class="inner">
              <div class="top-2">
                <div class="top-avatar">
                  <div></div>
                  <img :src="top2?.avatar" />
                </div>
                <div class="top-nickname">{{ top2?.username }}</div>
                <div class="play-number">
                  <div class="icon-play"></div>
                  <gap :gap="2"></gap>
                  <span>{{ $t(`promoter_star.play`) }}</span
                  ><gap :gap="2"></gap
                  ><span>{{ formatCount(top2?.play_count) }}</span>
                </div>
                <div class="prize-money">
                  <div class="icon-money"></div>
                  <span>$</span><span>{{ top2?.bonus }}</span>
                </div>
              </div>
              <gap :gap="12"></gap>
              <div class="top-1">
                <div class="top-avatar">
                  <div></div>
                  <img :src="top1?.avatar" />
                </div>
                <div class="top-nickname">
                  {{ top1?.username }}
                </div>
                <div class="play-number">
                  <div class="icon-play"></div>
                  <gap :gap="2"></gap>
                  <span>{{ $t(`promoter_star.play`) }}</span
                  ><gap :gap="2"></gap
                  ><span>{{ formatCount(top1?.play_count) }}</span>
                </div>
                <div class="prize-money">
                  <div class="icon-money"></div>
                  <span>$</span><span>{{ top1?.bonus }}</span>
                </div>
              </div>
              <gap :gap="12"></gap>
              <div class="top-3">
                <div class="top-avatar">
                  <div></div>
                  <img :src="top3?.avatar" />
                </div>
                <div class="top-nickname">{{ top3?.username }}</div>
                <div class="play-number">
                  <div class="icon-play"></div>
                  <gap :gap="2"></gap>
                  <span>{{ $t(`promoter_star.play`) }}</span
                  ><gap :gap="2"></gap
                  ><span>{{ formatCount(top3?.play_count) }}</span>
                </div>
                <div class="prize-money">
                  <div class="icon-money"></div>
                  <gap :gap="2"></gap>
                  <span>$</span><span>{{ top3?.bonus }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="list">
            <div class="item" v-for="item in topOther">
              <div class="index">{{ item.index }}</div>
              <gap :gap="12"></gap>
              <div class="avatar">
                <img :src="item.avatar" />
              </div>
              <gap :gap="12"></gap>
              <div class="nickname">{{ item.username }}</div>
              <gap :gap="12"></gap>
              <div class="number">
                <div>{{ formatCount(item.play_count) }}</div>
                <div>
                  <span>$</span><span>{{ item.bonus }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, ref } from "vue";
import { i18n } from "@/i18n/index.js";
import VideoPlayer from "./components/VideoPlayer.vue";
import SubTitle from "./components/SubTitle.vue";
import activityApi from "@/api/activity.js";
import { useRouter } from "vue-router";
import rightIcon from "@/assets/images/promoter-star/icon-contact-black.svg";

const { proxy, appContext } = getCurrentInstance();
const router = useRouter();

const lang = proxy.$languageFile;

// 是否在APP内
const isApp = computed(() => {
  return appContext?.config.globalProperties.$isApp;
});
const dataList = ref([]);

const top1 = computed(() => dataList.value.find((i) => i.index === 1));
const top2 = computed(() => dataList.value.find((i) => i.index === 2));
const top3 = computed(() => dataList.value.find((i) => i.index === 3));
const topOther = computed(() => dataList.value.slice(3));

const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};

const fetchData = async () => {
  const { data, code } = await activityApi.star_ranking();
  if (code === 200) {
    dataList.value = data || [];
  }
};

const formatCount = (number = 0) => {
  if (number < 1000) {
    return number;
  }
  if (number < 1000000) {
    return lang === "ar"
      ? `K${Math.floor(number / 100) / 10}`
      : `${Math.floor(number / 100) / 10}K`;
  }
  return lang === "ar"
    ? `M${Math.floor(number / 100000) / 10}`
    : `${Math.floor(number / 100000) / 10}M`;
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.container {
  background: #bfeff5;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-bottom: px2rem(60);
  overflow: hidden;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    height: px2rem(376);
    width: 100%;
    background-image: url("@/assets/images/promoter-star/ranking-top-picture.png");
    background-size: 100% px2rem(376);
  }
  .ended {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: px2rem(18);
    color: #062226;
    font-family: Gilroy;
    font-weight: 900;
    font-style: italic;
  }
  .banner-title {
    position: relative;
    z-index: 1;
    margin-top: px2rem(20);
    .title,
    .sub-title {
      text-align: center;
      font-family: Gilroy;
      font-weight: 900;
      font-style: italic;
    }
    .title {
      color: #062226;
      font-size: px2rem(28);
      height: px2rem(36);
      line-height: px2rem(36);
    }
    .sub-title {
      color: #fd42a7;
      font-size: px2rem(32);
      height: px2rem(41);
      line-height: px2rem(41);
      margin-top: px2rem(4);
    }
  }

  .video-show {
    border-radius: px2rem(16);
    padding: px2rem(4);
    border: 1px solid rgba(255, 255, 255, 0.64);
    background: rgba(255, 255, 255, 0.32);
    margin: 0 px2rem(12);
    margin-top: px2rem(22);
    .inner {
      padding: px2rem(16) px2rem(8);
      padding-top: px2rem(20);
      border-radius: px2rem(12);
      background: linear-gradient(0deg, #ffb2df 0.29%, #fff1f9 99.76%);
      box-shadow: 0px 1px 0px 0px #fff inset;
      position: relative;
      .video-show-title {
        width: px2rem(250);
      }
      .win-bg {
        z-index: 1;
        width: px2rem(163);
        height: px2rem(163);
        background-image: url("@/assets/images/promoter-star/win.png");
        background-size: 100% 100%;
        position: absolute;
        top: px2rem(-38);
        right: px2rem(-21);
        opacity: 0.32;
      }

      .video-list {
        position: relative;
        z-index: 2;
        margin-top: px2rem(20);
        height: px2rem(144);
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: px2rem(8);
        div {
          background-color: #fff;
          border-radius: px2rem(4);
        }
      }
    }
  }
  .ranking-block {
    border-radius: px2rem(16);
    padding: px2rem(4);
    border: 1px solid rgba(255, 255, 255, 0.64);
    background: rgba(255, 255, 255, 0.32);
    margin: 0 px2rem(12);
    margin-top: px2rem(22);
    position: relative;
    z-index: 2;
    & > .inner {
      padding-top: px2rem(20);
      border-radius: px2rem(12);
      overflow: hidden;
      background: linear-gradient(0deg, #84e2fe 0.29%, #aaf2ff 99.76%);
      .ranking-title {
        margin: 0 px2rem(12);
      }
      .top-box {
        width: 100%;
        height: px2rem(397);
        background-image: url("@/assets/images/promoter-star/ranking-bg.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .inner {
          width: 100%;
          height: px2rem(397);
          background-image: url("@/assets/images/promoter-star/ranking-top3-bg.png");
          background-size: px2rem(340) px2rem(300);
          background-position: bottom;
          background-repeat: no-repeat;
          margin: 0 auto;
          display: flex;
          justify-content: center;
          .top-2,
          .top-1,
          .top-3 {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: px2rem(100);
            .top-avatar {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: flex-end;
              flex-shrink: 0;
              div {
                background-size: 100% 100%;
                position: absolute;
              }
              img {
                margin: 0 auto;
                background-color: #083c44;
                object-fit: cover;
              }
            }
            .top-nickname {
              color: #083c44;
              text-align: center;

              /* T15/B */
              font-family: Gilroy;
              font-size: px2rem(15);
              font-style: normal;
              font-weight: 700;
              line-height: 125%;
              margin-top: px2rem(24);
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .play-number {
              color: #083c44;
              display: flex;

              /* T13/R */
              font-family: Gilroy;
              font-size: px2rem(13);
              font-style: normal;
              font-weight: 500;
              line-height: 124%; /* 16.12px */
              margin-top: px2rem(4);
              .icon-play {
                width: px2rem(16);
                height: px2rem(16);
                background-image: url("@/assets/images/promoter-star/icon-play.svg");
                background-size: px2rem(16) px2rem(16);
              }
            }
            .prize-money {
              color: #ffed63;
              text-align: center;
              display: flex;
              align-items: center;

              /* T20/B */
              font-family: Gilroy;
              font-size: px2rem(20);
              font-style: normal;
              font-weight: 700;
              line-height: normal;
              margin-top: px2rem(4);
              .icon-money {
                width: px2rem(24);
                height: px2rem(24);
                background-image: url("@/assets/images/promoter-star/money.png");
                background-size: px2rem(24) px2rem(24);
              }
            }
          }
          .top-2 {
            padding-top: px2rem(55);
            .top-avatar {
              width: px2rem(70);
              height: px2rem(82);
              div {
                width: px2rem(70);
                height: px2rem(82);
                background-image: url("@/assets/images/promoter-star/avatar-cover-2.svg");
              }
              img {
                width: px2rem(66);
                height: px2rem(66);
                border-radius: px2rem(66);
                margin-bottom: px2rem(4);
              }
            }
          }
          .top-1 {
            .top-avatar {
              width: px2rem(90);
              height: px2rem(108);
              div {
                width: px2rem(90);
                height: px2rem(108);
                background-image: url("@/assets/images/promoter-star/avatar-cover-1.svg");
              }
              img {
                width: px2rem(86);
                height: px2rem(86);
                border-radius: px2rem(86);
                margin-bottom: px2rem(3);
              }
            }
            .prize-money {
              font-size: px2rem(24);
            }
          }
          .top-3 {
            padding-top: px2rem(80);
            .top-avatar {
              width: px2rem(64);
              height: px2rem(74);
              div {
                width: px2rem(64);
                height: px2rem(74);
                background-image: url("@/assets/images/promoter-star/avatar-cover-3.svg");
              }
              img {
                width: px2rem(60);
                height: px2rem(60);
                border-radius: px2rem(60);
                margin-bottom: px2rem(3);
              }
            }
          }
        }
      }

      .list {
        border-radius: px2rem(8);
        margin: px2rem(8);
        margin-top: px2rem(-96);
        padding: px2rem(16);
        background: #f4feff;
        .item {
          display: flex;
          padding-bottom: px2rem(16);
          margin-bottom: px2rem(16);
          align-items: center;
          border-bottom: 0.5px solid var(---B5, #e6e6e6);
          &:last-child {
            border-bottom: none;
            margin-bottom: px2rem(0);
          }
          .index {
            color: var(---B4, #ccc);
            text-align: right;
            font-family: Gilroy;
            font-size: px2rem(24);
            font-style: italic;
            font-weight: 900;
            line-height: normal;
          }
          .avatar {
            display: flex;
            width: px2rem(48);
            height: px2rem(48);
            justify-content: center;
            align-items: center;
            border-radius: px2rem(48);
            overflow: hidden;
            background-color: #999;
            img {
              width: px2rem(48);
              height: px2rem(48);
              object-fit: cover;
            }
          }
          .nickname {
            flex-grow: 1;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            flex: 1 0 0;
            overflow: hidden;
            color: #083c44;
            text-overflow: ellipsis;

            /* T17/R */
            font-family: Gilroy;
            font-size: px2rem(17);
            font-style: normal;
            font-weight: 500;
            line-height: 132%; /* 22.44px */
          }
          .number {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            & > :first-child {
              color: var(---B3, #999);

              /* T13/R */
              font-family: Gilroy;
              font-size: px2rem(13);
              font-style: normal;
              font-weight: 500;
              line-height: 124%; /* 16.12px */

              padding-left: px2rem(20);

              background-image: url("@/assets/images/promoter-star/icon-view.svg");
              background-size: px2rem(16) px2rem(16);
              background-repeat: no-repeat;
            }

            & > :last-child {
              color: #083c44;
              text-align: center;
              display: flex;
              align-items: flex-end;

              /* T15/B */
              font-family: Gilroy;
              font-size: px2rem(15);
              font-style: normal;
              font-weight: 700;
              line-height: normal;
              margin-top: px2rem(4);
            }
          }
        }
      }
    }
  }
}
.rtl-html {
  .container {
    &::before {
      transform: scaleX(-1);
    }
  }
  .win-bg {
    right: unset !important;
    left: px2rem(-21) !important;
    transform: scaleX(-1);
  }
  .top-box {
    .inner {
      flex-direction: row-reverse;
    }
  }
}
</style>
