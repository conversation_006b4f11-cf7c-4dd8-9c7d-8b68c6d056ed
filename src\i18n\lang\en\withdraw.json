{"withdrawal_rule_title": "With<PERSON>wal Instructions", "withdrawal_rule_detail": ["After a successful withdrawal request, the funds are expected to arrive within 1-7 business days. If the funds have not arrived within the estimated time, please confirm that your receiving account is correct and wait patiently or contact customer service.", "Different withdrawal fees are required based on the amount of withdrawal, with the specific amount being subject to the actual amount received.", "If you cancel your account or are banned due to violations during the withdrawal period, we will freeze your withdrawal order."], "withdrawal_select_methods_tips": "With<PERSON><PERSON> requires binding your payment method. Please select your payment method and ensure that your payment information is true and valid.", "service_fee": "handling charge: {0}", "withdrawal_methods": "Payment Method", "withdrawal_information": "Withdrawal Information", "withdrawal_info_check": "Please confirm your withdrawal information.", "estimated_withdrawal_amount": "Estimated withdrawal amount", "estimated_amount_received": "Estimated amount to be received", "estimated_service_fee": "Estimated handling charge", "current_selected": "Current Selection", "completed": "Already filled", "need_complete": "To be completed", "withdrawal_amount": "Withdrawal amount", "total_withdrawal_amount": "Cumulative withdrawal amount", "withdrawal_pending": "Withdrawal in progress", "withdrawal_success": "<PERSON><PERSON><PERSON> successful", "withdrawal_fail": "<PERSON><PERSON><PERSON> failed", "fail_reason": "Reason for withdrawal failure", "empty": "No withdrawal record", "BANK_TRANSFER_receive_bank": "Payee bank", "BANK_TRANSFER_receive_bank_placeholder": "Select the payee bank", "BANK_TRANSFER_fill_info_tips": "Please fill in your payment information", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "e.g.***********", "payeeInfo_documentId_hint": "Please enter CPF/CNPJ (Individual/Corporate Tax Number)", "payeeInfo_address_title": "<PERSON>ress", "payeeInfo_email_title": "Email", "payeeInfo_email_placeholder": "e.g.1234566{0}gmail.com", "payeeInfo_email_hint": "Please enter the correct email format", "payeeInfo_phone_title": "Phone number", "WALLET_accountNo_title": "{0} account", "WALLET_accountNo_placeholder_SA": "e.g.************", "WALLET_accountNo_placeholder_EG": "e.g.0**********", "WALLET_accountNo_placeholder_AE": "e.g.+971-*********", "WALLET_accountNo_placeholder_TR": "e.g.**********", "WALLET_accountNo_placeholder_PH": "e.g.***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "e.g.mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "e.g.***********", "WALLET_accountNo_placeholder_BR_PagBank": "e.g.************{0}123.com", "WALLET_accountNo_placeholder_ID": "e.g.***********", "WALLET_accountNo_placeholder_MY": "e.g.**********", "WALLET_accountNo_placeholder_TH": "e.g.**********", "WALLET_accountNo_placeholder_VN": "e.g.***********", "WALLET_accountNo_hint_SA": "Please enter the phone number bound to your wallet, 12 digits starting with 9665", "WALLET_accountNo_hint_EG": "Please enter the phone number bound to your wallet, 11 digits starting with 01", "WALLET_accountNo_hint_EG_2": "Please enter the phone number bound to your wallet, 12 digits starting with 01 or country code plus 10 digits", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "Payer's phone number-Required-The phone number bound to the wallet，11 digits starting with 01 or 201", "WALLET_accountNo_hint_AE": "Please enter the phone number bound to your wallet. The format must be: +country code - phone number.", "WALLET_accountNo_hint_TR": "Please enter the phone number bound to your wallet, a 10 digits or PL plus 10 digits", "WALLET_accountNo_hint_PH": "Please enter the phone number bound to your wallet,11 digits starting with 09", "WALLET_accountNo_hint_PH_LAZADAPH": "Please enter the phone number bound to your wallet,11 digits starting with 09 or email", "WALLET_accountNo_hint_BR_MERCADOPAGO": "Please enter the user account (email or ID) bound to the wallet", "WALLET_accountNo_hint_BR_PIX": "Please enter the corresponding account bound to the wallet", "WALLET_accountNo_hint_BR_PagBank": "Please enter the email address bound to your wallet, up to 60 characters", "WALLET_accountNo_hint_ID": "Please enter your wallet-linked account number:9 to 14 digits starting with 08", "WALLET_accountNo_hint_MY": "Please enter your wallet-linked account number:10 or 11 digits starting with 0", "WALLET_accountNo_hint_TH": "Please enter your wallet-linked account number:10 digits starting with 0", "WALLET_accountNo_hint_VN": "Please enter your wallet-linked account number:11 digits starting with 84", "WALLET_fullName_title": "Payee's Name", "WALLET_fullName_placeholder": "e.g.<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "e.g.<PERSON>", "WALLET_fullName_placeholder_PH": "e.g.<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "e.g.<PERSON>", "WALLET_fullName_hint": "Please enter your full english name", "WALLET_lastName_title": "Payee's last name", "WALLET_lastName_placeholder_EG": "eg.<PERSON><PERSON><PERSON>", "WALLET_lastName_hint": "Please enter your last name", "WALLET_accountType_title": "{0} Binding account type", "WALLET_accountType_placeholder_BR_PIX": "Please select the binding account type", "WALLET_accountType_option_E_PIX": "Email", "WALLET_accountType_option_P_PIX": "Phone ", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (personal/corporate tax number)", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "eg. 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "SWIFT account", "SWIFT_accountNo_placeholder_SA": "e.g.************************", "SWIFT_accountNo_placeholder_AE": "e.g.***********************", "SWIFT_accountNo_placeholder_KW": "e.g.******************************", "SWIFT_accountNo_placeholder_QA": "e.g.*****************************", "SWIFT_accountNo_placeholder_TR": "e.g.**************************", "SWIFT_accountNo_placeholder_BH": "e.g.**********************", "SWIFT_accountNo_placeholder_YE": "e.g.**********", "SWIFT_accountNo_placeholder_IQ": "e.g.***********************", "SWIFT_accountNo_placeholder_IL": "e.g.***********************", "SWIFT_accountNo_placeholder_PS": "e.g.**********", "SWIFT_accountNo_placeholder_AZ": "e.g.AZ77VTBA0000000000**********", "SWIFT_accountNo_placeholder_LB": "e.g.****************************", "SWIFT_accountNo_placeholder_MA": "e.g.**********", "SWIFT_accountNo_placeholder_PH": "e.g.**********", "SWIFT_accountNo_placeholder_BR": "e.g.*****************************", "SWIFT_accountNo_placeholder_EG": "e.g.*****************************", "SWIFT_accountNo_placeholder_JO": "例如JO71CBJO000000000000**********", "SWIFT_accountNo_hint_SA": "Please enter your SWIFT account,24characters starting with SA", "SWIFT_accountNo_hint_AE": "Please enter your SWIFT account, 23 characters starting with AE", "SWIFT_accountNo_hint_KW": "Please enter your SWIFT account,30 characters starting with KW", "SWIFT_accountNo_hint_QA": "Please enter your SWIFT account,29 characters starting with QA", "SWIFT_accountNo_hint_TR": "Please enter your SWIFT account,26 characters starting with TR", "SWIFT_accountNo_hint_BH": "Please enter your SWIFT account,22 characters starting with BH", "SWIFT_accountNo_hint_YE": "Please enter your SWIFT account,34 digits and letters or less", "SWIFT_accountNo_hint_IQ": "Please enter your SWIFT account,23 characters starting with IQ", "SWIFT_accountNo_hint_IL": "Please enter your SWIFT account,23 characters starting with IL", "SWIFT_accountNo_hint_PS": "Please enter your SWIFT account,34 digits and letters or less", "SWIFT_accountNo_hint_AZ": "Please enter your SWIFT account,28 characters starting with AZ", "SWIFT_accountNo_hint_LB": "Please enter your SWIFT account,28 characters starting with LB", "SWIFT_accountNo_hint_MA": "Please enter your SWIFT account,34 digits and letters or less", "SWIFT_accountNo_hint_PH": "Please enter your SWIFT account, 34 digits and letters or less", "SWIFT_accountNo_hint_BR": "Please enter your SWIFT account, 29 characters starting with BR", "SWIFT_accountNo_hint_EG": "Please enter your SWIFT account, 29 characters starting with EG", "SWIFT_accountNo_hint_JO": "Please enter your SWIFT account,30 characters starting with <PERSON><PERSON>", "SWIFT_fullName_title": "Payee's Name", "SWIFT_fullName_placeholder": "e.g.<PERSON>", "SWIFT_fullName_hint": "Please enter your full english name", "SWIFT_bankCode_title": "Bank Number (SWIFT Code)", "SWIFT_bankCode_placeholder": "e.g.SCBLHKHH", "SWIFT_bankCode_hint": "Please enter the bank code, 8 or 11 digits, numbers and uppercase letters are allowed", "CARD_accountNo_title": "Bank account ", "CARD_accountNo_placeholder_EG": "e.g. ***********", "CARD_accountNo_placeholder_TR": "e.g. ****************", "CARD_accountNo_hint_EG": "Please enter the bank account, length>=8", "CARD_accountNo_hint_TR": "Please enter the 16-digit bank card number, only debit cards are supported", "CARD_fullName_title": "Payee's Name", "CARD_fullName_placeholder": "e.g.<PERSON>", "CARD_fullName_hint": "Please enter your full english name", "CARRIER_BILLING_accountNo_title": "Phone number", "CARRIER_BILLING_accountNo_placeholder": "e.g.63**********", "CARRIER_BILLING_accountNo_hint": "Please enter phone number.", "CASH_accountNo_title": "{0}account", "CASH_accountNo_placeholder": "e.g.***********", "CASH_accountNo_hint": "Please enter{0}account,11 digits starting with 0", "BANK_TRANSFER_accountNo_title_SA": "Bank Account （IBAN）", "BANK_TRANSFER_accountNo_title_AE": "Bank Account （IBAN）", "BANK_TRANSFER_accountNo_title_TR": "Bank Account （IBAN）", "BANK_TRANSFER_accountNo_title": "Bank Account", "BANK_TRANSFER_accountNo_title_EG": "Bank Account", "BANK_TRANSFER_accountNo_title_KW": "Bank Account", "BANK_TRANSFER_accountNo_title_QA": "Bank Account", "BANK_TRANSFER_accountNo_title_MA": "Bank account（RIB）", "BANK_TRANSFER_accountNo_title_PH": "Bank Account", "BANK_TRANSFER_accountNo_title_BR": "Bank Account", "BANK_TRANSFER_accountNo_placeholder_SA": "e.g.************************", "BANK_TRANSFER_accountNo_placeholder_EG": "e.g.************", "BANK_TRANSFER_accountNo_placeholder_AE": "e.g.***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "e.g.******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "e.g.*****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "e.g.**************************", "BANK_TRANSFER_accountNo_placeholder_MA": "e.g.123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "e.g.************", "BANK_TRANSFER_accountNo_placeholder_BR": "e.g.***********", "BANK_TRANSFER_accountNo_placeholder_JO": "e.g.******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "e.g.**********", "BANK_TRANSFER_accountNo_placeholder_TH": "e.g.*********", "BANK_TRANSFER_accountNo_placeholder_ID": "e.g.***********", "BANK_TRANSFER_accountNo_hint_SA": "Please enter a combination of SA+22 letters and numbers", "BANK_TRANSFER_accountNo_hint_EG": "Please enter IBAN and local bank account , IBAN; 29 characters (EG+27 digits)", "BANK_TRANSFER_accountNo_hint_AE": "Please enter AE+21 digits, AE must be capitalized", "BANK_TRANSFER_accountNo_hint_KW": "Please enter a length of less than 50 characters, in IBAN format", "BANK_TRANSFER_accountNo_hint_QA": "Please enter a length of less than 50 characters, in IBAN format", "BANK_TRANSFER_accountNo_hint_TR": "Please enter a number starting with TR plus 24 digits, in IBAN format", "BANK_TRANSFER_accountNo_hint_MA": "Please enter the 24-digit RIB account code.", "BANK_TRANSFER_accountNo_hint_PH": "Please enter 6 to 128 digits", "BANK_TRANSFER_accountNo_hint_BR": "Please enter 4 to 15 digits", "BANK_TRANSFER_accountNo_hint_JO": "Please enter a 30-character combination of numbers and letters starting with JO", "BANK_TRANSFER_accountNo_hint_MY": "Enter an account number with a maximum of 35 characters.", "BANK_TRANSFER_accountNo_hint_TH": "Please enter a 10-18 digit account number", "BANK_TRANSFER_accountNo_hint_ID": "Enter a numeric account number.", "BANK_TRANSFER_bankBranch_title": "Bank Branch Number", "BANK_TRANSFER_bankBranch_placeholder_BR": "e.g.1234", "BANK_TRANSFER_bankBranch_hint_BR": "Please input the bank branch number, 4~6 digits", "BANK_TRANSFER_address_placeholder_SA": "e.g.aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "e.g.ALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "e.g.kuwait xx street", "BANK_TRANSFER_address_hint_SA": "Please enter the address, length limit of 10 to 200 characters, allowing only numbers, periods, uppercase and lowercase letters, and spaces.", "BANK_TRANSFER_address_hint_AE": "Please enter the address, length limit of 1 to 200 characters, the bank will conduct risk control checks.", "BANK_TRANSFER_address_hint_KW": "Please enter your address, length<=255", "BANK_TRANSFER_fullName_placeholder_TR": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_PH": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_BR": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_MA": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_KW": "e.g.<PERSON>", "BANK_TRANSFER_phone_placeholder_PH": "e.g.***********", "BANK_TRANSFER_phone_placeholder_BR": "e.g.*************", "BANK_TRANSFER_phone_hint_PH": "Please enter your mobile phone number, 0-9 digits starting with 11(0 must be added) ", "BANK_TRANSFER_phone_hint_BR": "Please enter your mobile phone number, 12-13 digits starting with 55", "BANK_TRANSFER_bankCode_title_MY": "Bank Code (SWIFT Code)", "BANK_TRANSFER_bankCode_placeholder_MY": "e.g.CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "Enter an 8-11 character combination of letters and numbers.", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT code", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "Please enter a combination of 8 or 11 uppercase letters and numbers", "BANK_TRANSFER_city_title_SA": "City", "BANK_TRANSFER_city_placeholder_SA": "For example: New York", "BANK_TRANSFER_city_hint_SA": "Please enter the city name, up to 35 characters", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "For example: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "Please enter a combination of 24 digits", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "Date of birth", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "例如********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "For example: ********", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "Document type", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "Please select the type of identification", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "Identification number", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "For example: NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "For example: ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "Please enter your identification number", "AGENT_accountNo_Whatsapp_title_ALL": "Whatsapp number", "AGENT_accountNo_Whatsapp_placeholder_ALL": "For example +90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "Please enter your Whatsapp account", "AGENT_accountNo_vodafone_title_EG": "Vodafone account", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "Blockchain name", "USDT_address_title_ALL": "Coin issuing address", "USDT_address_placeholder_ALL": "For example 1111111111111111111111111", "USDT_address_hint_ALL": "Please enter an address starting with 1, 3, or bc", "USDT_address_hint_common": "Please enter your USDT address"}