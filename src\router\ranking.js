const routes = [
  // 幸运礼物榜单
  {
    path: "/lucky-gift-ranking",
    name: "LuckyGiftRanking",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "LuckyGiftRanking"*/ "../views/ranking/lucky-gift/Index.vue"
      ),
  },
  // 幸运礼物榜单
  {
    path: "/lucky-gift-ranking/jackpotRule",
    name: "LuckyGiftJackpotRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "LuckyGiftJackpotRule"*/ "../views/ranking/lucky-gift/JackpotRule.vue"
      ),
  },
  // 幸运礼物榜单
  {
    path: "/lucky-gift-ranking/rankingRule",
    name: "LuckyGiftRankingRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "LuckyGiftRankingRule"*/ "../views/ranking/lucky-gift/RankingRule.vue"
      ),
  },

  // 周星榜单
  {
    path: "/ranking/weekly-star",
    name: "RankingWeeklyStar",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "RankingWeeklyStar"*/ "../views/ranking/weekly-star/Index.vue"
      ),
  },
  // 周星里程碑
  {
    path: "/ranking/weekly-star/milestone",
    name: "RankingWeeklyStarMilestone",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "RankingWeeklyStarMilestone"*/ "../views/ranking/weekly-star/Milestone.vue"
      ),
  },
  // 周星里名人堂
  {
    path: "/ranking/weekly-star/FameHall",
    name: "RankingWeeklyStarFameHall",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "RankingWeeklyStarFameHall"*/ "../views/ranking/weekly-star/FameHall.vue"
      ),
  },
];

export default routes;
