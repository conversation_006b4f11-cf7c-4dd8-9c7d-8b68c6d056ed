<template>
  <div class="submit-block">
    <div class="box-1" v-html="$t(`singer.description`)"></div>
    <div class="box-2">
      <BorderBox>
        <div class="box-content">
          <div class="box-title">
            <BlockTitle
              :show-line="false"
              :font-size="18"
              icon-size="large"
              :title="$t(`singer.registration_requirements_title`)"
            ></BlockTitle>
          </div>
          <div class="box-text">
            <div class="text-item">
              <span class="index">1</span>
              <p class="text-content">
                {{
                  $t(`singer.registration_requirements_items_0`, [
                    activityInfo?.stage1_time.start_time,
                    activityInfo?.stage1_time.end_time,
                  ])
                }}
              </p>
            </div>
            <div class="text-item">
              <span class="index">2</span>
              <p class="text-content">
                {{ $t(`singer.registration_requirements_items_1`) }}
              </p>
            </div>
            <div class="text-item">
              <span class="index">3</span>
              <p class="text-content">
                {{ $t(`singer.registration_requirements_items_2`) }}
              </p>
            </div>
            <div class="text-item">
              <span class="index">4</span>
              <p class="text-content">
                {{ $t(`singer.registration_requirements_items_3`) }}
              </p>
            </div>
          </div>
        </div>
      </BorderBox>
    </div>
  </div>
  <div class="footer">
    <div class="footer-inner">
      <div
        class="footer-button"
        @click="beforeRead"
        v-track:click
        trace-key="singer_button_click"
        :track-params="JSON.stringify({ singer_button_name: 4 })"
      >
        {{
          activityInfo?.stage === 1
            ? $t(`singer.under_review`)
            : activityInfo?.count === 0
            ? $t(`singer.submit_audio`)
            : $t(`singer.update_audio`)
        }}
        <van-uploader
          v-if="!uploading && !isMaxCount && activityInfo?.stage === 0"
          class="uploader"
          :multiple="false"
          :after-read="afterRead"
          accept="*"
          result-type="file"
        >
          <div class="uploader-click"></div>
        </van-uploader>
      </div>
      <div class="ends-time">
        {{
          $t(`singer.registration_deadline`, [
            activityInfo?.stage1_time.end_time,
          ])
        }}
      </div>
    </div>
  </div>
</template>
<script setup>
import BorderBox from "./BorderBox.vue";
import BlockTitle from "./BlockTitle.vue";
import singerApi from "@/api/activity.js";
import { computed, onMounted, ref } from "vue";
import { uploadFile } from "@/utils/util.js";
import { t } from "@/i18n/index.js";

const activityInfo = ref(null);
const isMaxCount = computed(() => activityInfo.value?.count >= 10);

const fetchData = async () => {
  const res = await singerApi.singer_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
  }
};

// 校验音频类型
function checkAudioType(f) {
  const allowedTypes = [
    "audio/mp3",
    "audio/mpeg", // mp3 常用类型
    "audio/aac",
    "audio/wav",
    "audio/m4a",
    ".mp3",
    ".aac",
    ".wav",
    ".m4a",
  ];
  const ext = f.name ? f.name.split(".").pop().toLowerCase() : "";
  return (
    allowedTypes.includes(f.type) || ["mp3", "aac", "wav", "m4a"].includes(ext)
  );
}

// 校验音频时长，返回Promise
function checkAudioDuration(file) {
  return new Promise((resolve, reject) => {
    const url = URL.createObjectURL(file);
    const audio = new Audio(url);
    audio.addEventListener("loadedmetadata", () => {
      const duration = audio.duration;
      console.log(duration);
      URL.revokeObjectURL(url);
      if (duration < 30 || duration > 120) {
        reject(t("singer.registration_requirements_items_1"));
      } else {
        resolve();
      }
    });
    audio.addEventListener("error", () => {
      URL.revokeObjectURL(url);
      reject(t("singer.registration_requirements_items_1"));
    });
  });
}
const beforeRead = () => {
  if (activityInfo.value?.stage !== 0) {
    return;
  }
  if (isMaxCount.value) {
    showToast(t("singer.submission_limit_reached"));
  }
};
const uploading = ref(false);
const afterRead = ({ file }) => {
  try {
    console.log(file.name);
    uploading.value = true;
    setTimeout(() => {
      showLoadingToast({
        duration: 0,
        forbidClick: true,
        message: t("singer.loading"),
      });
    }, 100);
    if (!checkAudioType(file)) {
      setTimeout(() => {
        closeToast();
        showToast(t("singer.registration_requirements_items_1"));
        uploading.value = false;
      }, 100);
      return;
    }
    checkAudioDuration(file)
      .then(async () => {
        // 校验通过，后续处理
        console.log("音频校验通过", file);
        // 这里可继续上传或其他逻辑
        const url = await uploadFile({ file });
        console.log(url);
        register(url);
      })
      .catch((msg) => {
        setTimeout(() => {
          uploading.value = false;
          closeToast();
          console.log("音频校验失败", file);
          showToast(msg);
        }, 110);
      });
  } catch (e) {
    setTimeout(() => {
      uploading.value = false;
      closeToast();
      console.log("失败", e);
    }, 110);
  }
};

const register = async (media_url) => {
  const res = await singerApi.singer_register({ media_url });
  uploading.value = false;
  closeToast();
  switch (res.code) {
    case 200:
      showToast(t("singer.submit_success"));
      break;
    case 20001:
      showToast("报名未开始");
      break;
    case 20002:
      showToast("报名已结束");
      break;
    case 20017:
      showToast(t("singer.submission_limit_reached"));
      break;
    default:
      showToast("报名失败");
  }
  fetchData();
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.submit-block {
  background: linear-gradient(180deg, #230253 4.56%, #350083 99.98%);
  margin-top: px2rem(32);
  position: relative;
  padding-bottom: px2rem(141);
  .box-1 {
    position: relative;
    border-radius: px2rem(8);
    border: px2rem(1) solid #ba82ff;
    background: rgba(196, 148, 255, 0.11);
    width: px2rem(322);
    color: #d8bdff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin: 0 auto;
    margin-top: px2rem(77);
    padding: px2rem(10);
    :deep(.color-p) {
      color: #ff66b5;
    }
  }
  .box-2 {
    width: px2rem(350);
    margin: 0 auto;
    margin-top: px2rem(27);
    .box-content {
      padding-top: px2rem(22);
      display: flex;
      flex-direction: column;
      align-items: center;
      .box-title {
        padding: 0 px2rem(10);
      }
      .box-text {
        padding: px2rem(13);
        .text-item {
          margin-bottom: px2rem(14);
          padding: px2rem(10);
          border-radius: px2rem(8);
          border: px2rem(1) solid #a772ea;
          background: linear-gradient(
            180deg,
            rgba(202, 158, 255, 0.22) 0%,
            rgba(136, 57, 237, 0.22) 72.96%
          );
          .index {
            width: px2rem(16);
            height: px2rem(16);
            line-height: px2rem(16);
            border-radius: px2rem(16);
            display: block;
            text-align: center;
            flex-shrink: 0;
            background: linear-gradient(
              180deg,
              #ff9ebb 0%,
              #d433e8 50.2%,
              #6100e8 100%
            );
            color: #fff;
            font-family: Gilroy;
            font-size: px2rem(14);
            font-style: normal;
            font-weight: 700;
            float: left;
            margin-top: px2rem(-3);
            margin-right: px2rem(8);
          }
          .text-content {
            color: #d8bdff;
            font-family: Gilroy;
            font-size: px2rem(14);
            font-style: normal;
            font-weight: 500;
            line-height: 130%;
          }
        }
      }
    }
  }
}
.footer {
  position: fixed;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: px2rem(121);
  border-radius: px2rem(14) px2rem(14) 0px 0px;
  padding: px2rem(2);
  padding-top: px2rem(3);
  &::before {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
      45deg,
      #1f1f1f,
      #ffffff,
      #1f1f1f,
      #ffffff,
      #1f1f1f,
      #ffffff,
      #1f1f1f
    );
    border-radius: px2rem(14) px2rem(14) 0px 0px;
    z-index: -1;
  }
  .footer-inner {
    overflow: hidden;
    position: relative;
    border-radius: px2rem(14) px2rem(14) 0px 0px;
    background: linear-gradient(180deg, #6b00d3 0%, #1d005b 87.46%);
    z-index: 1;
    width: 100%;
    height: 100%;

    .footer-button {
      width: px2rem(244);
      height: px2rem(50);
      flex-shrink: 0;
      border-radius: px2rem(50);
      border-image: linear-gradient(to right, #ff0000, #00ff00) 1;
      border: 1.5px solid #fff;
      background: linear-gradient(
        180deg,
        #ffa798 0%,
        #cd1ff6 47.12%,
        #6600f5 83.17%
      );
      box-shadow: 0px -4px 4px 0px rgba(44, 2, 104, 0.64) inset,
        0px 3px 3px 0px rgba(255, 255, 255, 0.66) inset;
      margin: 0 auto;
      margin-top: px2rem(24);
      color: #fff;
      text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3);
      font-family: Gilroy;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: 124%; /* 22.32px */
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .uploader {
        position: absolute;
        inset: 0;
        z-index: 99;
        background-color: #1d005b;
        display: block;
        opacity: 0;
        :deep(.van-uploader__wrapper) {
          width: 100%;
          height: 100%;
          display: block;
          .van-uploader__input-wrapper {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
        .uploader-click {
          width: 100%;
          height: 100%;
          background-color: #6600f5;
          display: block;
        }
      }
    }
    .ends-time {
      color: #d8bdff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 400;
      line-height: 124%; /* 14.88px */
      margin-top: px2rem(7);
    }
  }
}

.rtl-html {
  .text-item {
    .index {
      float: right !important;
      margin-right: unset !important;
      margin-left: px2rem(8) !important;
    }
  }
}
</style>
