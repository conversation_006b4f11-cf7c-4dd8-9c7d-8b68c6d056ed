<template>
  <van-overlay :show="dialogShow" @touchmove.prevent z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <div class="title">
        {{ $t("ranking.ranking_weekly_star_badge_rewards") }}
      </div>
      <div class="content flex">
        {{ $t("ranking.ranking_weekly_star_extra_desc") }}
      </div>
      <img
        src="@/assets/images/common/close.png"
        alt=""
        @click="handleCancel"
        class="close"
      />
    </div>
  </van-overlay>
</template>

<script setup>
import { ref, watch } from "vue";

const emit = defineEmits(["update:show", "confirm"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const dialogShow = ref(false);

watch(
  () => props.show,
  (val) => {
    dialogShow.value = !!val;
  },
  {
    immediate: true,
  }
);

const handleCancel = () => {
  emit("update:show", false);
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.base-modal-wrapper {
  background: url("@/assets/images/ranking/weekly-star/rule-border.png") top
    left no-repeat;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-size: 100% 100%;
  height: px2rem(180);
  width: px2rem(350);
  color: $white;
  font-size: $fontSize13;
  text-align: center;
  .title {
    font-weight: $fontWeightBold;
    padding: px2rem(10) 0;
    text-align: center;
  }
  .content {
    padding: px2rem(10) px2rem(28);
    height: px2rem(135);
  }
  .close {
    height: px2rem(52);
    width: px2rem(52);
    margin-top: px2rem(22);
  }
}
</style>
