const routes = [
  {
    path: "/dealer/coin",
    name: "DealerCoin",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(/*webChunkName: "AnchorWeek"*/ "../views/dealer/coin/Index.vue"),
  },
  {
    path: "/dealer/coin/search",
    name: "DealerCoinSearch",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "AnchorWeek"*/ "../views/dealer/coin/Search.vue"),
  },
  {
    path: "/dealer/coin/record",
    name: "DealerCoinRecord",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "AnchorWeek"*/ "../views/dealer/coin/Record.vue"),
  },

  /* ===== 币商列表===== */
  {
    path: "/coiner/list",
    name: "CoinerList",
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () =>
      import(
        /*webChunkName: "CoinerList"*/ "../views/dealer/coiner-list/Index.vue"
      ),
  },
  /* ===== 币商规则说明===== */
  {
    path: "/coiner/rule",
    name: "CoinerRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "CoinerRule"*/ "../views/dealer/coiner-list/Rule.vue"
      ),
  },
];

export default routes;
