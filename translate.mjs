import xlsx from 'node-xlsx';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { keyMap } from './keyMap.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取 Excel 文件
const excelFile = xlsx.parse(path.join(__dirname, 'i18n.xlsx'));

// 初始化语言对象
const langData = {};
Object.keys(keyMap).forEach((lang) => {
  if (lang !== 'key') {
    langData[lang] = {};
  }
});

// 遍历所有 sheet
excelFile.forEach((sheet) => {
  const sheetData = sheet.data;
  // 解析每个 sheet 的数据
  sheetData.forEach((row) => {
    const key = row[keyMap.key];
    if (key) {
      Object.keys(keyMap).forEach((lang) => {
        if (lang !== 'key') {
          langData[lang][key] = row[keyMap[lang]];
        }
      });
    }
  });
});

// 保存语言文件
const langDir = path.join(__dirname, 'src', 'i18n', 'lang');
if (!fs.existsSync(langDir)) {
  fs.mkdirSync(langDir, { recursive: true });
}

Object.keys(langData).forEach((lang) => {
  const langSubDir = path.join(langDir, lang);
  if (!fs.existsSync(langSubDir)) {
    fs.mkdirSync(langSubDir);
  }
  const langFilePath = path.join(langSubDir, 'total.json');

  // 1. 读取原始文件内容（作为字符串完整保留）
  let originalContent = '{}';
  let originalData = {};
  if (fs.existsSync(langFilePath)) {
    originalContent = fs.readFileSync(langFilePath, 'utf-8');
    try {
      originalData = JSON.parse(originalContent);
    } catch (e) {
      console.warn(`解析 ${langFilePath} 失败，将创建新文件`);
    }
  }

  // 2. 创建新内容对象（只添加原文件没有的新key）
  const newData = {};
  Object.keys(langData[lang]).forEach(key => {
    if (!originalData.hasOwnProperty(key)) {
      newData[key] = langData[lang][key];
    }
  });

  // 3. 如果没有新内容，则完全保留原文件不变
  if (Object.keys(newData).length === 0) {
    return; // 跳过写入
  }

  // 4. 将新内容追加到原文件末尾（不修改原格式）
  if (originalContent.trim() === '{}') {
    // 如果是空文件，直接写入新内容
    fs.writeFileSync(langFilePath, JSON.stringify(newData, null, 2));
  } else {
    // 否则在原内容末尾追加
    const lastBracePos = originalContent.lastIndexOf('}');
    const newContent = originalContent.slice(0, lastBracePos) + 
                      (originalContent.slice(lastBracePos-1, lastBracePos) === '\n' ? '' : '\n') +
                      ',\n' + 
                      JSON.stringify(newData, null, 2).replace(/^{\n\s*|\n\s*}$/g, '') + 
                      '\n}';
    fs.writeFileSync(langFilePath, newContent);
  }
});

console.log('翻译文件已生成到 src/i18n/lang 目录下的各语言子目录中');