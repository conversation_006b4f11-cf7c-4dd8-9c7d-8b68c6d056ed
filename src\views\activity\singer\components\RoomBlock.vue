<template>
  <div class="room-box">
    <div class="room-title">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :lineWidth="30"
        :iconGap="8"
        :lineGap="3"
        icon-size="large"
        :title="$t(`singer.qualifying_round_competition_room_title`)"
      ></BlockTitle>
    </div>
    <div class="room-block">
      <div
        class="room-cover"
        v-track:click
        trace-key="singer_button_click"
        :track-params="JSON.stringify({ singer_button_name: 14 })"
      >
        <img :src="roomInfo?.cover" />
        <div v-if="isRuning" class="is-runing"></div>
      </div>
      <gap :gap="7"></gap>
      <div class="room-info">
        <div class="room-info-cell">
          <div>
            {{ $t(`singer.qualifying_round_competition_room_room_name`) }}
          </div>
          <gap :gap="4"></gap>
          <div>{{ roomInfo?.room_name }}</div>
        </div>
        <div class="room-info-cell">
          <div>
            {{ $t(`singer.qualifying_round_competition_room_room_id`) }}
          </div>
          <gap :gap="4"></gap>
          <div>{{ roomInfo?.room_no }}</div>
        </div>
        <div class="room-info-cell">
          <div>
            {{ $t(`singer.time`) }}
          </div>
          <gap :gap="4"></gap>
          <div>20:00-22:00({{ timezone || "UTC+3" }})</div>
        </div>
      </div>
      <div
        class="room-button"
        :class="btnClass"
        @click="handleSubscribe"
        v-track:click
        trace-key="singer_button_click"
        :track-params="JSON.stringify({ singer_button_name: 13 })"
      >
        {{ buttonText }}
      </div>
    </div>
  </div>
</template>
<script setup>
import BlockTitle from "./BlockTitle.vue";
import singerApi from "@/api/activity.js";
import { t } from "@/i18n";
import { computed, getCurrentInstance } from "vue";
const emit = defineEmits(["subscribe"]);
const props = defineProps({
  // 1.晋级赛 2.决赛
  stage: {
    type: Number,
    default: 1,
  },
  // 0 报名 1.审核 2.海选 3.海选结束， 4.晋级赛当天零点-晋级赛开始时间 5晋级赛 6晋级赛结束 7.决赛当天零点-决赛开始时间 8决赛 9决赛结束 10 活动结束
  step: {
    type: Number,
    default: 1,
  },
  roomInfo: {
    type: Object,
    default: () => null,
  },
  time: {
    type: String,
    default: "",
  },
  timezone: {
    type: String,
    default: "UTC+3",
  },
  isSubscribe: {
    type: Boolean,
    default: false,
  },
});
const { proxy } = getCurrentInstance();
const isRuning = computed(() => {
  return (
    (props.step === 5 && props.stage === 1) ||
    (props.step === 8 && props.stage === 2)
  );
});
const isEnded = computed(() => {
  return (
    (props.step >= 6 && props.stage === 1) ||
    (props.step >= 9 && props.stage === 2)
  );
});
const buttonText = computed(() => {
  switch (props.step) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return t(`singer.qualifying_round_competition_room_remind_me`);
    case 5:
      if (props.stage === 1) {
        return t(`singer.qualifying_round_competition_room_go`);
      } else {
        return t(`singer.qualifying_round_competition_room_remind_me`);
      }
    case 6:
    case 7:
      if (props.stage === 1) {
        return t(`singer.qualifying_round_competition_room_ended`);
      } else {
        return t(`singer.qualifying_round_competition_room_remind_me`);
      }
    case 8:
      if (props.stage === 1) {
        return t(`singer.qualifying_round_competition_room_ended`);
      } else {
        return t(`singer.qualifying_round_competition_room_go`);
      }
    case 9:
    case 10:
      return t(`singer.qualifying_round_competition_room_ended`);
  }
});
const btnClass = computed(() => {
  if (isRuning.value) {
    return "actived";
  } else {
    return props.isSubscribe || isEnded.value ? "" : "actived";
  }
});
const handleSubscribe = async () => {
  if (isEnded.value) {
    return;
  } else if (isRuning.value) {
    proxy.$siyaApp("openSiyaUrl", {
      url: `siya://siya.com/app?method=goVoiceRoom&roomId=${props.roomInfo?.room_id}`,
    });
  } else if (!props.isSubscribe) {
    const res = await singerApi.singer_remind({
      stage: props.stage,
      status: 1,
    });
    if (res.code === 200) {
      showToast(t("singer.qualifying_round_competition_room_notification"));
      emit("subscribe");
    }
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.room-box {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .room-title {
    margin-top: px2rem(45);
    width: px2rem(351);
  }
  .room-block {
    margin-top: px2rem(28);
    width: px2rem(346);
    display: flex;
    padding: px2rem(10) px2rem(12);
    align-items: center;
    border-radius: px2rem(8);
    border: px2rem(1) solid #bf25f5;
    background: rgba(255, 255, 255, 0.09);
    box-shadow: 0 0 px2rem(27) 0 rgba(161, 0, 255, 0.5),
      0 px2rem(4) px2rem(4) 0 rgba(0, 0, 0, 0.25);

    .room-cover {
      width: px2rem(64);
      height: px2rem(64);
      border-radius: px2rem(10);
      border: px2rem(3) solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 0 px2rem(12) 0 rgba(29, 26, 71, 0.24);
      flex-shrink: 0;
      position: relative;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .is-runing {
        width: 100%;
        height: px2rem(25);
        background: linear-gradient(
          180deg,
          rgba(95, 86, 237, 0) 0%,
          rgba(95, 86, 237, 0.9) 100%
        );
        flex-shrink: 0;
        position: absolute;
        left: 0;
        bottom: 0;
        &::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: px2rem(25);
          background-image: url("@/assets/images/activity/singer/voice.png");
          background-size: px2rem(41) px2rem(19);
          background-position: center center;
          background-repeat: no-repeat;
        }
      }
    }

    .room-info {
      flex-grow: 1;
      display: flex;
      flex-direction: column;

      .room-info-cell {
        display: flex;
        align-items: center;
        color: #ca87ff;
        font-family: Gilroy;
        font-size: px2rem(10);
        font-weight: 500;
        line-height: 1;
        margin-bottom: px2rem(4);

        & > div:first-child {
          display: flex;
          padding: px2rem(2) px2rem(4);
          justify-content: center;
          align-items: center;
          gap: px2rem(10);
          border-radius: px2rem(4);
          background: rgba(255, 255, 255, 0.14);
          font-weight: 700;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .room-button {
      width: px2rem(84);
      height: px2rem(30);
      border-radius: px2rem(8);
      border: 1px solid #954dff;
      background: linear-gradient(180deg, #5000c5 0%, #1d0145 100%);
      box-shadow: 0px -2.454px 2.454px 0px #1c0141 inset,
        0px 2px 4.6px 0px rgba(188, 173, 255, 0.57) inset;
      color: #523e70;
      text-shadow: 0 px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(12);
      font-weight: 700;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }

    .actived {
      border: px2rem(1) solid #feb2ff;
      background: linear-gradient(
        180deg,
        #ffa798 0%,
        #cd1ff6 47.12%,
        #6600f5 83.17%
      );
      box-shadow: 0 px2rem(-2.454) px2rem(2.454) 0 rgba(44, 2, 104, 0.64) inset,
        0 px2rem(1.84) px2rem(1.84) 0 rgba(255, 255, 255, 0.66) inset;
      color: #fff;
    }
  }
}
</style>
