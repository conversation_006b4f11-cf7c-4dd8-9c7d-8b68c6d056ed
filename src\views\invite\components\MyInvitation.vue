<template>
  <div class="tab-panel">
    <div class="my-invitation" v-if="initialTab === 0">
      <div class="my-invitation-inner">
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{
              formatCount(revenueStats?.daily_total_invites ?? 0)
            }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <svg-icon icon="people"></svg-icon>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.invited_today") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{
              formatCount(revenueStats?.monthly_total_invites ?? 0)
            }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <svg-icon icon="people"></svg-icon>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.invited_month") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{ formatCount(revenueStats?.total_invites ?? 0) }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <svg-icon icon="people" color="#DBCDAF"></svg-icon>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.invited_total") }}</div>
        </div>
      </div>
    </div>
    <div class="my-invitation" v-else>
      <div class="my-invitation-inner">
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{ formatCount(revenueStats?.daily_total_bonus ?? 0) }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <Coin :gender="gender"></Coin>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.reward_today") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{
              formatCount(revenueStats?.monthly_total_bonus ?? 0)
            }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <Coin :gender="gender"></Coin>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.reward_month") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-invitation-item">
          <div class="my-invitation-num">
            <span>{{ formatCount(revenueStats?.total_bonus ?? 0) }}</span>
            <gap :gap="2"></gap>
            <span class="icon-coin">
              <Coin :gender="gender"></Coin>
            </span>
          </div>
          <div class="my-invitation-desc">{{ $t("invite.reward_total") }}</div>
        </div>
      </div>
    </div>
    <div class="tabs">
      <page-tab
        :tab-items="tabItems"
        v-model="initialTab"
        :colunm-gap="20"
        width="fit-content"
        :font-size="15"
        :show-active-line="false"
      ></page-tab>
    </div>
    <div>
      <date-select
        v-model="selectDate"
        @confirm="onDateSelectConfirm"
      ></date-select>
    </div>
    <user-register-list
      v-show="initialTab === 0"
      ref="userRegisterListRef"
      :start-date="startDate"
      @load="handleRegisterListLoad"
    ></user-register-list>
    <user-reward-list
      v-show="initialTab === 1"
      ref="userRewardListRef"
      :start-date="startDate"
      @load="handleRewardListLoad"
    ></user-reward-list>
  </div>
</template>
<script setup>
import { computed, inject, nextTick, onMounted, ref, watch } from "vue";
import PageTab from "./PageTab.vue";
import inviteApi from "@/api/invite.js";
import DateSelect from "./DateSelect.vue";
import UserRegisterList from "./UserRegisterList.vue";
import UserRewardList from "./UserRewardList.vue";
import { padStart } from "lodash";
import Coin from "./Coin.vue";
import { formatCount } from "@/utils/util";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const gender = inject("userGender");
const userRegisterListRef = ref();
const userRewardListRef = ref();
const initialTab = ref(0);
const tabItems = ref([
  {
    title: t("invite.invite_user_detail"),
  },
  {
    title: t("invite.invite_user_reward_detail"),
  },
]);

const revenueStats = ref(null);

const selectDate = ref([new Date().getFullYear(), new Date().getMonth() + 1]);
const startDate = computed(() => {
  if (selectDate.value?.length === 2) {
    const [year, month] = selectDate.value;
    return `${year}-${padStart(month, 2, "0")}-01 00:00:00`;
  } else {
    return "";
  }
});
const dateCache = [null, null];
const onDateSelectConfirm = () => {
  fetchData();
  console.log("onDateSelectConfirm");
  fetchList(initialTab.value);
};

const getRevenueStats = async () => {
  const res = await inviteApi.revenue_stats();
  if (res.code === 200) {
    revenueStats.value = res.data;
  }
};

const fetchData = async () => {
  await getRevenueStats();
};

const fetchList = (index = 0) => {
  nextTick(() => {
    switch (index) {
      case 0:
        userRegisterListRef.value?.load(true);
        dateCache[0] = startDate.value;
        break;
      case 1:
        userRewardListRef.value?.load(true);
        dateCache[1] = startDate.value;
        break;
    }
  });
};

const handleRegisterListLoad = (date) => {
  dateCache[0] = date;
};

const handleRewardListLoad = (date) => {
  dateCache[1] = date;
};

watch(
  () => initialTab.value,
  (index) => {
    if (dateCache[index] !== startDate.value) {
      fetchData();
      fetchList(index);
    }
  }
);

onMounted(() => {
  nextTick(() => {
    fetchData();
    fetchList(initialTab.value);
  });
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.tab-panel {
  position: relative;
  z-index: 3;
  padding: 0 px2rem(12);
  padding-top: px2rem(16);
  padding-bottom: px2rem(20);
  min-height: px2rem(560);
  overflow: hidden;
  .my-invitation {
    padding: px2rem(6);
    border-radius: px2rem(12);
    background: #f5ebd7;
    .my-invitation-inner {
      display: flex;
      padding: px2rem(16) px2rem(8);
      justify-content: center;
      align-items: center;
      gap: px2rem(6);
      align-self: stretch;
      border-radius: px2rem(8);
      background: #fff8ea;
      box-shadow: 0px px2rem(4) px2rem(8) 0px rgba(107, 77, 37, 0.08);
      .line {
        width: px2rem(1);
        height: px2rem(32);
        background: #f0d8d8;
      }
      .my-invitation-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: px2rem(100);
        .my-invitation-num {
          color: var(---B0-Black, #000);

          /* T17/B */
          font-family: Gilroy;
          font-size: px2rem(17);
          font-style: normal;
          font-weight: 700;
          height: px2rem(21);
          line-height: px2rem(21);
          display: flex;
          justify-content: center;
          align-items: center;

          .icon-coin {
            width: px2rem(20);
            height: px2rem(20);
            display: flex;
            align-items: center;
            color: #dbcdaf;
            font-size: px2rem(16);
          }
        }
        .my-invitation-desc {
          color: #7e4141;
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;

          /* T13/R */
          font-family: Gilroy;
          font-size: px2rem(13);
          min-height: px2rem(32);
          line-height: px2rem(16);
          font-style: normal;
          font-weight: 500;
          line-height: 124%; /* 16.12px */
        }
      }
    }
  }

  .tabs {
    padding-top: px2rem(16);
  }

  .list {
    padding-top: px2rem(16);
    .item {
      margin-bottom: px2rem(20);
    }
  }
}
</style>
