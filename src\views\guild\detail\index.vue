<template>
  <div class="app-container page-guild-home">
    <div class="page-bg">
    </div>
    <header-bar is-scroll-change-bg close-type="back" :title="t('guild.guild_detail')" />
    <main>
      <div class="tab-header">
        <div class="tab-left">
          <span :class="{'tab-name':true,active:active==index}" v-for="(item,index) in tabList" :key="index" @click="onChangeTab(index)">{{ item }}</span>
        </div>
        <van-button class="choose-time-range" @click="showTimeSelect=true">{{timeRange.text}}
          <img class="icon-arrow" src="@/assets/images/guild/arrow-down.png" alt="">
        </van-button>
      </div>
      <swiper class="tabbar"
        :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
        :modules="modules"
        slides-per-view="auto"
        :space-between="0"
        @swiper="setControlledSwiper"
        @slideChange="onSlideChange"
      >
        <swiper-slide class="swipe-item">
          <template v-if="!incomeObj.waiting">
            <div class="earning">
              <div class="boxs" v-show="active==0">
                <div 
                  v-for="(item,index) in incomeObj.top_data"
                  :key="item.d_type"
                  :class="{boxItem: true, active: incomeType==index+1}" 
                  @click="chooseIncomeType(index+1)"
                >
                  <div class="box-item-num">{{formatNum(incomeObj.top_data[index].num).val}}{{ formatNum(incomeObj.top_data[index].num).unit }}</div>
                  <div class="box-item-text">{{ incomeObj.top_data[index].name }}</div>
                  <img v-show="incomeType==index+1" class="active-gou" src="@/assets/images/guild/active.png" alt="">
                </div>
              </div>
            </div>
            <div v-show="active==0" ref="chart1" class="charts-content"></div>
            <div class="progress-list" v-if="active==0">
              <div class="progress-title">{{ incomeTabTitle }}</div>
              <div v-for="(item,index) in incomeObj.member_income_compose" :key="index">
                <div class="progress-detail" v-if="incomeObj.member_income_compose">
                  <span>{{ item.name }}:{{formatNum(incomeObj.member_income_compose[index]?.num).val}}{{ formatNum(incomeObj.member_income_compose[index]?.num).unit }}</span>
                  <span class="percent">{{ incomeObj.member_income_compose[index]?.percentage }}%</span>
                </div>
                <progress-bar
                  v-if="incomeObj.member_income_compose"
                  :current="Number(incomeObj.member_income_compose[index]?.percentage)"
                  :total="100"
                  :show-text="false"
                  :height="6"
                />
              </div>
            </div>
          </template>
          <template v-else>
            <div class="nodata no-data">
              <img src="@/assets/images/guild/calculate.png" alt="">
              <p>{{ $t('guild.guild_calculating') }}</p>
            </div>
          </template>
        </swiper-slide>
        <swiper-slide class="swipe-item">
          <template v-if="!incomeObj.waiting">
            <div class="earning">
              <div class="boxs" v-show="active==1">
                <div 
                  v-for="(item,index) in incomeObj.top_data"
                  :key="item.d_type"
                  :class="{boxItem: true, active: incomeType==index+1}" 
                  @click="chooseIncomeType(index+1)"
                >
                  <div class="box-item-num">{{formatNum(incomeObj.top_data[index].num).val}}{{ formatNum(incomeObj.top_data[index].num).unit }}</div>
                  <div class="box-item-text">{{ incomeObj.top_data[index].name }}</div>
                  <img v-show="incomeType==index+1" class="active-gou" src="@/assets/images/guild/active.png" alt="">
                </div>
              </div>
            </div>
            <div v-show="active==1" ref="chart2" class="charts-content"></div>
            <div class="member-list">
              <div class="member-list-title">
                {{ menberTitle }}
              </div>
              <div class="nodata" v-if="!incomeObj.guild_user_info">
                <img src="@/assets/images/common/emptyh5.png" alt="">
                <p>{{ $t('common.common_content_yet') }}</p>
              </div>
              <template v-else>
                <div class="menber-item" v-for="item in incomeObj.guild_user_info" :key="item.account">
                  <div class="avatar">
                    <img :src="item.avatar" alt="">
                  </div>
                  <gap :gap="8" />
                  <div class="member-info">
                    <div class="member-name">
                      <flag :flag="item.flag" :size="16"></flag>
                      <gap v-if="item.flag" :gap="6" />
                      <span>{{item.nickname}}</span>
                      <gap :gap="4" />
                      <img v-if="item.auth_type!==0" :src="getImageSrc(item)" alt="">
                    </div>
                    <div class="member-zuan">
                      <div style="display: flex;">
                        <span>ID</span>
                        <span>:</span>
                        <span>{{item.account}}</span>
                      </div>
                      <span v-if="active === 1&&[4,5].includes(incomeType)" class="member-income"><img src="@/assets/images/guild/diamond.png" alt=""><span>+{{formatNum(item.income).val}}{{ formatNum(item.income).unit }}</span></span>
                    </div>
                    <div class="member-num" v-if="active===1&&incomeType===1">{{$t('guild.guild_join_time')}}:{{ item.join_time }}</div>
                    <div class="member-num" v-if="active===1&&incomeType===2">{{$t('guild.guild_verification_time')}}:{{ item.auth_time }}</div>
                    <div class="member-num" v-if="active === 1 && [ 4, 5, 6, 7].includes(incomeType)">{{$t('guild.guild_last_login_time')}}:{{ item.last_time }}</div>
                    <div class="member-num" v-if="active===1&&incomeType===3">{{$t('guild.guild_registration_time')}}:{{ item.register_time }}</div>
                  </div>
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div class="nodata no-data">
              <img src="@/assets/images/guild/calculate.png" alt="">
              <p>{{ $t('guild.guild_calculating') }}</p>
            </div>
          </template>
        </swiper-slide>
        <swiper-slide class="swipe-item">
          <template v-if="!incomeObj.waiting">
            <div class="earning">
              <div class="boxs">
                <div 
                  v-for="(item,index) in incomeObj.top_data"
                  :key="item.d_type"
                  :class="{boxItem: true, active: incomeType==index+1}" 
                  @click="chooseIncomeType(index+1)"
                >
                  <div class="box-item-num">{{formatNum(incomeObj.top_data[index].num).val}}{{ formatNum(incomeObj.top_data[index].num).unit }}</div>
                  <div class="box-item-text">{{ incomeObj.top_data[index].name }}</div>
                  <img v-show="incomeType==index+1" class="active-gou" src="@/assets/images/guild/active.png" alt="">
                </div>
              </div>
            </div>
            <div ref="chart3" class="charts-content"></div>
            <div class="member-list">
              <div class="member-list-title">
                {{ menberTitle }}
              </div>
              <div class="nodata" v-if="!incomeObj.guild_user_info">
                <img src="@/assets/images/common/emptyh5.png" alt="">
                <p>{{ $t('common.common_content_yet') }}</p>
              </div>
              <template v-else>
                <div class="menber-item" v-for="item in incomeObj.guild_user_info" :key="item.account">
                  <div class="avatar">
                    <img :src="item.avatar" alt="">
                  </div>
                  <gap :gap="8" />
                  <div class="member-info">
                    <div class="member-name">
                      <flag :flag="item.flag" :size="16"></flag>
                      <gap v-if="item.flag" :gap="6" />
                      <span>{{item.nickname}}</span>
                      <gap :gap="2" />
                      <img v-if="item.auth_type!==0" :src="getImageSrc(item)" alt="">
                    </div>
                    <div class="member-account">
                      <span>ID</span>
                      <span>:</span>
                      <span>{{item.account}}</span>
                    </div>
                    <div class="member-num" v-if="active===2&&incomeType===1" v-html="changeToTarget(item,'guild_party_qualified_detail',0)"></div>
                    <div class="member-num" v-if="active===2&&incomeType===2" v-html="changeToTarget(item,'guild_party_unqualified_detail',1)"></div>
                  </div>
                </div>
              </template>
            </div>
          </template>
          <template v-else>
            <div class="nodata no-data">
              <img src="@/assets/images/guild/calculate.png" alt="">
              <p>{{ $t('guild.guild_calculating') }}</p>
            </div>
          </template>
        </swiper-slide>
      </swiper>
      <van-popup  position="bottom" v-model:show="showTimeSelect" class="pop">
        <div class="pop-title">{{$t('guild.guild_date_picker')}}</div>
        <van-picker v-model="timeRangeVal" :columns="rangeColumns" :show-toolbar="false" />
        <van-button class="okbtn" type="primary" @click="onConfirm">{{$t('common.common_comfirm')}}</van-button>
      </van-popup>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick,computed } from 'vue'
import guildApi from '@/api/guild.js'
import { i18n } from '@/i18n/index.js'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts'; // 引入 ECharts
import boyImage from '@/assets/images/guild/boy.png';
import girlImage from '@/assets/images/guild/girl.png';
import authenticationImage from '@/assets/images/guild/authentication.png';
import {getTimeRange} from '@/utils/util.js'
import { Controller } from 'swiper/modules'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { numberWithUnit } from '@/utils/util';

const modules = [Controller]

const t = i18n.global.t
const tm = i18n.global.tm
const router = useRouter()

const tabList = ref([t('guild.guild_income_2'),t('guild.guild_members'),t('guild.guild_party')])
const controlledSwiper = ref(null);
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};
const formatNum = (val) => {
  return numberWithUnit(val)
}
const currentProgressList = ref([])
const active = ref(0)
const showTimeSelect = ref(false)
const incomeType = ref(1)
const startTs = ref(0)
const endTs = ref(0)
const timeType = ref(2)
const incomeObj = ref({})
const chart1 = ref(null);
const chart2 = ref(null);
const chart3 = ref(null);
const myChart1 = ref(null);
const myChart2 = ref(null);
const myChart3 = ref(null);
const progressList = ref([t('guild.guild_1v1_video'),t('guild.guild_1v1_chat'),t('guild.guild_1v1_gift'),t('guild.guild_1v1_voice')])
const progressList1 = ref([t('guild.guild_1v1_video'),t('guild.guild_1v1_chat'),t('guild.guild_1v1_gift'),t('guild.guild_1v1_voice')])
const progressList2 = ref([t('guild.guild_party_gift'),t('guild.guild_party_bag_gift')])
const progressList3 = ref([t('guild.guild_1v1_video'),t('guild.guild_1v1_chat'),t('guild.guild_1v1_gift'),t('guild.guild_1v1_voice'),t('guild.guild_party_gift')])
const arr1 = ref([t('guild.guild_income_member_1v1'),t('guild.guild_income_member_party'),t('guild.guild_income_member_total'),t('guild.guild_income_member_commission')])
const arr11 = ref([t('guild.guild_income_member_1v1_'),t('guild.guild_income_member_party_'),t('guild.guild_income_member_total_'),t('guild.guild_income_member_commission_')])
const arr2 = ref([t('guild.guild_new_member'),t('guild.guild_verified_member'),t('guild.guild_unverified_member'),t('guild.guild_earning_member'),t('guild.guild_non_earning_member'),t('guild.guild_active_member'),t('guild.guild_inactive_member')])
const arr3 = ref([t('guild.guild_party_qualified_member'),t('guild.guild_party_unqualified_member')])
const timeRange = ref({
  text: t('guild.this_week'),
  value: 3,
})
const timeRangeVal = ref([3])
const onTouchEnd = (event) => {
  const endX = event.changedTouches[0].clientX;
  const deltaX = endX - 0;

  if (active.value === 0 && deltaX > 400) {    //  400为返回上一路由的阈值
    router.back();
  }
};
const rangeColumns = ref([])
const menberTitle = computed(() => {
  if(active.value == 1){
    return arr2.value[incomeType.value-1]
  } else if(active.value == 2){
    return arr3.value[incomeType.value-1] 
  } else {
    return arr1.value[incomeType.value-1] 
  }
})
const incomeTabTitle = computed(() => {
  if(active.value == 0){
    return arr11.value[incomeType.value-1]
  } 
})
const changeToTarget = (item,key,val) => {
  const parts = tm(`guild.${key}`);
  let values = []
  if(val) values = [item.income,item.left_income,item.party_day_num];   // 未达标成员
  else values = [item.level,item.income,item.party_day_num]     // 达标成员
  values = values.map(value => {
    return `${formatNum(value).val}${formatNum(value).unit}`; 
  })
  // 确保 parts 是普通数组
  const processedParts = Array.isArray(parts) ? [...parts] : [parts];
  // 创建新数组进行替换
  const resultParts = processedParts.map((part, index) => {
    if (index < values.length) {
      return part.replace('__X__', `<span>${values[index]}</span>`);
    }
    return part;
  });
  
  return resultParts.map(part => `<p style="margin-top:2px">${part}</p>`).join('');
}
const onConfirm = () => {
  setTimeout(() => {
    timeRange.value = rangeColumns.value.find(item=>item.value==timeRangeVal.value)
    timeType.value = timeRange.value.value
    getData()
    showTimeSelect.value = false
  }, 500);
}
const chooseIncomeType = (type) => {
  incomeType.value = type
  if(active.value == 0){
    if(type == 1 || type == 4){
      progressList.value = progressList1.value 
    } else if(type == 2){
      progressList.value = progressList2.value 
    } else if(type == 3){
      progressList.value = progressList3.value 
    }
  }
  getData()
}
const getImageSrc = (item) => {
  if (item.auth_type === 1) {
    if (item.gender === 1) {
      return boyImage; // 男孩图片
    } else if (item.gender === 11) {
      return girlImage; // 女孩图片
    }
  } else if (item.auth_type === 2) {
    return authenticationImage; // 认证图片
  }
}
const getData = () => {
  guildApi.imcome_detail({
    dType: active.value+1,
    typeint: incomeType.value,
    timeType: timeType.value,
  }).then(res => {
    if(res.data.top_data) {
      if(active.value == 0){
        arr1.value.forEach((item,index) => {
          if(res.data.top_data[index]) res.data.top_data[index].name = item
        })
      }else if( active.value == 1){
        arr2.value.forEach((item,index) => {
          if(res.data.top_data[index]) res.data.top_data[index].name = item 
        })
      }else if( active.value == 2){
        arr3.value.forEach((item,index) => {
          if(res.data.top_data[index]) res.data.top_data[index].name = item
        }) 
      }
    }
     // 根据 incomeType.value 选择对应的 progressList
     let currentProgressList;
    if ([1, 4].includes(incomeType.value)) {
      currentProgressList = progressList1.value;
    } else if (incomeType.value === 2) {
      currentProgressList = progressList2.value;
    } else if (incomeType.value === 3) {
      currentProgressList = progressList3.value;
    }

    // 为 res.data.member_income_compose 添加 name 字段
    if (res.data.member_income_compose) {
      res.data.member_income_compose.forEach((item, index) => {
        item.name = currentProgressList[index];
      });
      res.data.member_income_compose.sort((a, b) => b.num - a.num);
    }

    incomeObj.value = res.data;
    initChart()
  })
}
const getProgressList = computed(() => {
  if (active.value === 0) {
    return progressList1.value;
  } else if (active.value === 1) {
    return progressList2.value;
  } else if (active.value === 2) {
    return progressList3.value;
  }
  return [];
});
const initChart = () => {
  // 配置图表选项
  const option = {
    backgroundColor: '#fff',
    grid: {
      left: '13%',   // 距离容器左侧的距离
      right: '8%',  // 距离容器右侧的距离
      top: '10%',    // 距离容器顶部的距离
      bottom: '10%', // 距离容器底部的距离
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: incomeObj.value.time_item.map(item => item.time_date),
    },
    yAxis: {},
    series: [
      {
        name: '销量',
        type: 'line',
        data: incomeObj.value.time_item.map(item => item.num),
      },
    ],
  };
  if(active.value == 0){
    if(!myChart1.value) myChart1.value = echarts.init(chart1.value);
    myChart1.value.setOption(option);
  } else if(active.value == 1){
    if(!myChart2.value) myChart2.value = echarts.init(chart2.value);
    myChart2.value.setOption(option);
  } else if(active.value == 2){
    if(!myChart3.value) myChart3.value = echarts.init(chart3.value);
    myChart3.value.setOption(option); 
   }
}
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex
  if (idx === active.value) return
  changeTab(idx)
};
const onChangeTab = (idx) => {
  controlledSwiper.value.slideTo(idx)
}
const changeTab = (index) => {
  active.value = index
  timeRangeVal.value = [3]
  if(index == 2){
    rangeColumns.value = [
      {
        text: t('guild.this_week'),
        value: 3,
      },
      {
        text: t('guild.common_last_week'),
        value: 4,
      },
      {
        text: t('guild.guild_first_two_weeks'),
        value: 9,
      },
      {
        text: t('guild.guild_first_three_weeks'),
        value: 10,
      },
    ]
  } else {
    rangeColumns.value = [
      {
        text: t('guild.this_week'),
        value: 3,
      },
      {
        text: t('guild.common_today'),
        value: 1,
      },
      {
        text: t('guild.yesterday'),
        value: 2,
      },
      {
        text:  t('guild.common_last_week'),
        value: 4,
      },
      {
        text: t('guild.common_this_month'),
        value: 5,
      },
      {
        text: t('guild.common_last_month'),
        value: 6,
      },
      {
        text: t('guild.guild_seven_day'),
        value: 7,
      },
      {
        text: t('guild.common_last_30_day'),
        value: 8,
      },
    ]
  }
  onConfirm()
  incomeType.value = 1
  getData()
}
onMounted(() => {
  startTs.value = getTimeRange(3).startTime
  endTs.value = getTimeRange(3).endTime
  changeTab(0)
})

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.page-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: px2rem(390);
  height: 100vh;
  background: $white;
  color: $fontColorB0;
}
main {
  height: calc(100vh - px2rem(100));
  overflow: scroll;
  position: relative;
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
.gapp {
  background-color: $bgColorB7;
  height: px2rem(8);
  width: 100vw;
  position: relative;
}
.tab-header {
  // height: px2rem(40);
  padding: px2rem(8) px2rem(16);
  position: relative;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: $white;
  border-bottom: px2rem(8) solid $bgColorB7;
  .tab-left {
    flex: 1;
    display: flex;
    line-height: px2rem(30);
    // justify-content: space-between;
    margin-right: px2rem(6);
    height: px2rem(30);
    .tab-name {
      font-size: $fontSize15;
      color: $fontColorB3;
      margin-right: px2rem(20);
      &.active {
        font-weight: $fontWeightBold;
        color: $fontColorB0;
        font-size: $fontSize17;
      }
    }
  }
}
.tabbar {
  display: flex;
  align-items: center;
  padding-bottom: px2rem(2);
  position: relative;
  .left-aligned-tabs {
    width: 100vw;
    overflow-x: auto; 
    position: relative;
    ::v-deep(.van-tabs__wrap) {
      justify-content: flex-start;
      width: 70%;
      overflow-x: scroll;
      padding-left: px2rem(8);
    }
  }
}
.choose-time-range {
  height: px2rem(30);
  font-size: $fontSize13;
  color: $mainColor;
  border-color: $mainColor;
  border-radius: $radius8;
  padding: px2rem(7) px2rem(8) px2rem(7) px2rem(12);
  box-shadow: px2rem(-16) 0 px2rem(40) $white;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  .icon-arrow {
    width: px2rem(12);
  }
}
::v-deep(.van-button__text) {
  display: flex;
  white-space: nowrap;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
}
::v-deep(.van-tab__text--ellipsis) {
  text-overflow: clip; 
  // 取消溢出隐藏
  overflow: visible; 
  white-space: nowrap;
}
::v-deep(.van-tab) {
  font-size: px2rem(15); /* 未选中时的字号 */
  font-weight: normal; /* 未选中时的字体粗细 */
}

::v-deep(.van-tab--active) {
  font-size: px2rem(17); /* 选中时的字号 */
  font-weight: $fontWeightBold !important; /* 选中时的字体粗细 */
}
.earning{
  background-color: #fff;
  .boxs {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: px2rem(12);
    position: relative;
    .boxItem {
      width: 49%;
      height: px2rem(90);
      background: #F5F5FF;
      border-radius: $radius12;
      justify-content: center;
      text-align: center;
      padding: px2rem(12);
      margin-bottom: px2rem(8);
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .box-item-num{ 
        font-size: px2rem(32);
        font-weight: 700;
      }
      .box-item-text {
        font-size: $fontSize13;
        color: $fontColorB3;
        line-height: 1;
      }
      &.active {
        border: 2px solid $mainColor;
      }
      .active-gou{
        width: px2rem(20);
        position: absolute;
        top: 0;
        right: 0;
        border-radius: 0 $radius8 0 0;
      }
   }
  }
}
.charts-content {
  width: 100vw;
  height: 34vh;
}
.progress-list {
  padding: px2rem(16);
  padding-bottom: px2rem(50);
  background-color: $white;
  position: relative;
  .progress-title {
    font-size: $fontSize17;
    font-weight: 700;
    margin: px2rem(12) 0;
  }
  .progress-detail {
    font-size: $fontSize15;
    font-weight: 500;
    color: $fontColorB2;
    margin-bottom: px2rem(8);
    margin-top: px2rem(16);
    display: flex;
    justify-content: space-between;
    align-items: center;
    .percent {
      color: $mainColor;

    }
  }
}
.member-list {
  background-color: #fff;
  position: relative;
  padding: px2rem(20) px2rem(16) px2rem(16) px2rem(16);
  .member-list-title {
    font-size: $fontSize17;
    font-weight: 700;
  }
  .menber-item {
    display: flex;
    margin-top: px2rem(20);
    align-items: flex-start;
    .avatar {
      width: px2rem(56);
      height: px2rem(56);
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .member-info {
      flex: 1;
      .member-name {
        font-size: $fontSize15;
        font-weight: 700;
        display: flex;
        align-items: center;
        img {
          width: px2rem(15);
          height: px2rem(15);
          margin-top: px2rem(2);
        }
      }
      .member-num {
        font-size: $fontSize11;
        color: $fontColorB3;
        margin-top: px2rem(6);
        .member-nump {
          margin-top: px2rem(2);
        }
      }
      .member-zuan {
        font-size: $fontSize13;
        color: $fontColorB3;
        margin-top: px2rem(6);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .member-income {
          font-size: $fontSize17;
          font-weight: 700;
          color: $fontColorB1;
          display: flex;
          align-items: center;
          img {
            width: px2rem(16);
            height: px2rem(16);
          }
        }
      }
      .member-account {
        font-size: $fontSize13;
        color: $fontColorB3;
        margin-top: px2rem(6);
        display: flex;
        align-items: center;
      }
    }
  }
}
.pop {
  border-radius: px2rem(26) px2rem(26) 0 0;
  padding: px2rem(16);
  padding-bottom: px2rem(32);
  .okbtn {
    width: 100%;
    margin-top: px2rem(16);
    font-weight: $fontWeightBold;
  }
  .pop-title {
    font-size: $fontSize17;
    font-weight: 700;
    text-align: center;
    margin: px2rem(16) 0;
  }
}
.nodata {
  margin: px2rem(30) auto;
  width: fit-content;
  text-align: center;
  img {
    width: px2rem(120);
  }
  p {
    font-size: $fontSize15;
    font-weight: 500;
    color: $fontColorB2;
  }
}
.no-data {
  margin: px2rem(150) auto;
  position: relative;
}
</style>
  