<template>
  <div class="main-box">
    <div class="inner-top">
      <div class="light"></div>
      <div class="inner-top-desc">
        {{ $t("invite.invite_limit_msg2") }}
      </div>
      <div class="inner-top-number">
        <span>{{ maxBonus }}</span>
        <gap :gap="4"></gap>
        <img v-if="gender === 1" src="@/assets/images/invite/coins.png" />
        <img v-else src="@/assets/images/invite/diamond.png" />
      </div>
    </div>
    <div class="inner-bottom">
      <div class="inner-bottom-title">{{ $t("invite.invite_reward") }}</div>
      <div class="rewards">
        <div class="reward">
          <div class="reward-tag">{{ $t("invite.reward_1") }}</div>
          <img
            class="reward-icon"
            src="@/assets/images/invite/reward-login.png"
          />
          <div class="reward-name">{{ $t("invite.friend_login") }}</div>
        </div>
        <div class="reward">
          <div class="reward-tag">{{ $t("invite.reward_2") }}</div>
          <img
            class="reward-icon"
            src="@/assets/images/invite/reward-recharge.png"
          />
          <div class="reward-name">{{ $t("invite.friend_recharge") }}</div>
        </div>
        <div class="reward">
          <div class="reward-tag">{{ $t("invite.reward_3") }}</div>
          <img
            class="reward-icon"
            src="@/assets/images/invite/reward-revenue-diamond.png"
          />
          <div class="reward-name">
            {{ $t("invite.friend_receive_diamond") }}
          </div>
        </div>
      </div>
      <div class="invite-info">
        <div class="invite-text">{{ $t("invite.my_invite_code") }}</div>
        <div class="invite-code">{{ inviteCode || "-" }}</div>
        <div class="invite-copy" @click="handleCopy">
          {{ $t("common.copy") }}
        </div>
      </div>
      <canvas class="large-button" id="pagCanvas" @click="openShare()"></canvas>
    </div>
  </div>
  <copy-popup ref="copyPopupRef" :invite-code="inviteCode || '-'"></copy-popup>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted, nextTick } from "vue";
import { i18n, getLang } from "@/i18n/index.js";
import CopyPopup from "./CopyPopup.vue";
import clipboard from "@/utils/clipboard";
import { debounce } from "lodash";
import { AnimationPAGInitLocal, isAndroid } from "@/utils/util.js";
const { proxy } = getCurrentInstance();
const t = i18n.global.t;
const props = defineProps({
  maxBonus: {
    type: Number,
    default: 0,
  },
  gender: {
    type: Number,
    default: 1,
  },
  inviteCode: {
    type: String,
    default: "-",
  },
  isBlacklisted: {
    type: Boolean,
    default: false,
  },
});
const lang = getLang();
const copyPopupRef = ref();
const handleCopy = (event) => {
  if (props.isBlacklisted) {
    proxy.$toast(t("invite.invitation_unavailable"));
    return;
  }
  clipboard(props.inviteCode, event, t("common.copy_success")).then(() => {
    copyPopupRef.value.open();
  });
};

const openShare = debounce(
  () => {
    if (props.isBlacklisted) {
      proxy.$toast(t("invite.invitation_unavailable"));
      return;
    }
    playAnimation();
    proxy.$siyaApp("showShareAlter", {
      inviteType: 1,
    });
  },
  1000,
  {
    leading: true,
    trailing: false,
  }
);

let animationObj = null;
const initAnimation = async () => {
  const pagUrl = lang === "ar" ? "share-button-ar.pag" : "share-button.pag";
  animationObj = await AnimationPAGInitLocal(
    pagUrl,
    "#pagCanvas",
    (_, pagFile, types) => {
      const textDoc = pagFile.getTextData(0);
      if (textDoc) {
        textDoc.text = t("invite.invite_friend");
        textDoc.fauxBold = true;
        if (lang === "tr") {
          textDoc.fontSize = 58;
        }
        pagFile.replaceText(0, textDoc);
      }
    }
  );
  animationObj.setRepeatCount(0);
};

const playAnimation = async () => {
  await animationObj?.play();
};

onMounted(async () => {
  await nextTick();
  await initAnimation();
  playAnimation();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
#pagCanvas {
  direction: rtl;
  font-family: "Noto Sans Arabic", sans-serif;
  text-align: right;
}
.main-box {
  align-self: stretch;
  border-radius: px2rem(24);
  margin: 0 px2rem(12);
  margin-top: px2rem(42);
  padding-bottom: px2rem(6);
  position: relative;
  background: linear-gradient(180deg, #fffafb 0%, #ffafc3 100%);

  &::after {
    content: "";
    position: absolute;
    inset: px2rem(2);
    background: linear-gradient(180deg, #ffeaef 0%, #ff819f 50%, #ff8181 100%);
    border-radius: px2rem(22);
    backdrop-filter: blur(px2rem(8));
  }

  .inner-top {
    position: relative;
    z-index: 1;
    width: px2rem(342);
    height: px2rem(148);
    flex-shrink: 0;
    border-radius: px2rem(20) px2rem(20) 0px 0px;
    background: linear-gradient(99deg, #ff6b8e 0%, #ff496c 98.74%);
    box-shadow: 0px px2rem(7) px2rem(7) 0px rgba(252, 176, 186, 0.7);
    margin: 0 auto;
    margin-top: px2rem(-33);
    overflow: hidden;

    &::after {
      content: "";
      position: absolute;
      border-radius: px2rem(16) px2rem(16) 0px 0px;
      inset: px2rem(8);
      background: #fff5f8;
      box-shadow: 0px 4px 6px 0px #e58187;
    }

    &::before {
      content: "";
      position: absolute;
      right: px2rem(-28);
      top: px2rem(20);
      width: px2rem(68);
      height: px2rem(86);
      transform: rotate(23.197deg);
      flex-shrink: 0;
      background-size: 100% 100%;
      background-image: url("@/assets/images/invite/coin.png");
      filter: blur(3.5028464794158936px);
    }

    .light {
      position: absolute;
      top: 0;
      width: 100%;
      height: px2rem(2);
      flex-shrink: 0;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        #fff 47.48%,
        rgba(255, 255, 255, 0) 100%
      );
    }

    .inner-top-desc {
      position: relative;
      z-index: 1;
      color: var(---C2, #ff4771);
      text-align: center;
      height: px2rem(42);
      display: flex;
      justify-content: center;
      align-items: center;

      /* T17/B */
      font-family: Gilroy;
      font-size: px2rem(17);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      width: px2rem(294);
      margin: 0 auto;
      margin-top: px2rem(22);
    }

    .inner-top-number {
      position: relative;
      z-index: 1;
      height: px2rem(60);
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        color: var(---B0-Black, #000);
        font-family: Gilroy;
        font-size: px2rem(48);
        font-style: normal;
        font-weight: 900;
        line-height: normal;
      }
      img {
        height: px2rem(58);
        width: px2rem(58);
      }
    }
  }
  .inner-bottom {
    position: relative;
    z-index: 2;
    width: px2rem(354);
    flex-shrink: 0;
    margin: 0 auto;
    margin-top: px2rem(-10);
    padding-bottom: px2rem(8);

    border-radius: px2rem(20);
    border: 1px solid var(--64, rgba(255, 255, 255, 0.64));
    background-color: #fffcfd;
    box-shadow: px2rem(-4) px2rem(-4) px2rem(12) 0px #ffdbe6 inset,
      px2rem(4) px2rem(4) px2rem(12) 0px #ffdbe6 inset;
    background-size: px2rem(354) px2rem(76);
    background-image: url("@/assets/images/invite/inner-bottom-bg.png");
    background-repeat: no-repeat;

    .inner-bottom-title {
      display: flex;
      width: px2rem(331);
      justify-content: center;
      align-items: center;
      gap: px2rem(12);

      color: #642628;
      text-align: center;

      /* T20/B */
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 700;
      line-height: normal;

      margin: 0 auto;
      margin-top: px2rem(16);
      &::before {
        content: "";
        width: px2rem(40);
        height: px2rem(2);
        flex-shrink: 0;
        background: linear-gradient(90deg, #ffebef 0%, #e9c1c1 100%);
      }
      &::after {
        content: "";
        width: px2rem(40);
        height: px2rem(2);
        flex-shrink: 0;
        background: linear-gradient(90deg, #e9c1c1 0%, #ffebef 100%);
      }
    }

    .rewards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      margin-top: px2rem(16);
      padding: 0 px2rem(12);
      gap: px2rem(8);
      .reward {
        display: flex;
        height: 100%;
        padding: px2rem(32) px2rem(12) px2rem(16) px2rem(12);
        flex-direction: column;
        align-items: center;
        border-radius: px2rem(12);
        border: px2rem(1) solid #ffb7c6;
        background: #fff5f7;
        position: relative;
        overflow: hidden;

        .reward-tag {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          padding: px2rem(4) px2rem(6);
          justify-content: center;
          align-items: center;
          border-radius: 0px 0px px2rem(12) 0px;
          background: linear-gradient(90deg, #ff914b -4.72%, #fe4e4b 100%);
          min-width: px2rem(53);
          color: var(---White, #fff);
          text-align: center;

          /* T10/R */
          font-family: Gilroy;
          font-size: px2rem(10);
          font-style: normal;
          font-weight: 500;
          line-height: 112%; /* 11.2px */
        }

        .reward-icon {
          width: px2rem(48);
          height: px2rem(48);
          border-radius: px2rem(48);
        }

        .reward-name {
          color: var(---C2, #ff4771);
          text-align: center;

          /* T13/B */
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          line-height: px2rem(16);
          margin-top: px2rem(8);
        }
      }
    }
    .invite-info {
      margin-top: px2rem(14);
      padding: 0 px2rem(12);
      gap: px2rem(8);
      display: flex;
      align-items: center;
      justify-content: center;
      .invite-text {
        color: #642628;

        /* T13/B */
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      .invite-code {
        color: var(---C2, #ff4771);
        text-align: center;

        /* T17/B */
        font-family: Gilroy;
        font-size: 17px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      .invite-copy {
        display: flex;
        padding: 0 px2rem(12);
        min-width: px2rem(66);
        height: px2rem(33);
        justify-content: center;
        align-items: center;
        border-radius: px2rem(100);
        border: px2rem(1) solid #ffdc60;
        background: var(---C2, #ff4771);
        color: var(---White, #fff);
        text-align: center;

        /* T17/B */
        font-family: Gilroy;
        font-size: px2rem(17);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
    }
    .large-button {
      display: block;
      width: px2rem(366);
      height: px2rem(110);
      margin-top: px2rem(4);
      margin-left: px2rem(-6);
      margin-right: px2rem(-6);
    }
  }
}

.rtl-html {
  .inner-bottom-title {
    flex-direction: row-reverse;
  }
  .inner-bottom-title,
  .reward-tag {
    left: unset !important;
    right: 0;
    border-radius: 0px 0px 0px px2rem(12) !important;
  }
}
</style>
