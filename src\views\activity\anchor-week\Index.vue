<template>
  <div class="app-container page-anchor-week" ref="pageContainer">
    <img class="bg-img" src="@/assets/images/anchor-week/bg.jpg" alt="">
    <header>
      <header-bar back-icon-show
                  :show-title="showPageTitle"
                  is-scroll-change-bg
                  :content-padding="false"
                  close-type="close"
                  @emit:scrollChangeBgShow="scrollChangeBgShow"
      />
      <img class="title-img" :src="getImageUrl('title.png')" alt="">
      <div class="count-down-wrap flex">
        <!-- <div class="date">4.21-5.31</div> -->
        <img class="icon-time" src="@/assets/images/anchor-week/icon-time.svg" alt="">
        <van-count-down
            class="count-down"
            :time="countdown"
            format="DDd:HHh:mmm:sss"
        />
      </div>
      <div class="rule" @click="toggleHintDialog(true)">{{ $t('anchor_week.rule') }}</div>
    </header>

    <main>
      <div class="card-wrap">
        <div class="card-title-wrap flex">
          <photo :url="config.avatar" :width="28" :height="28" :radius="28" />
          <gap :gap="6"/>
          <div class="card-title-val">{{ $t('anchor_week.weekly_data') }}</div>
          <img class="title-img" src="@/assets/images/anchor-week/card-icon1.png" alt="">
        </div>
        <div class="card-body flex">
          <div class="card-body-item flex">
            <div class="card-item-top flex" dir="ltr">
              <div class="card-item-num">{{ config.effect_day }}</div>
              <div class="card-item-unit">d</div>
            </div>
            <div class="item-tips">{{ $t('anchor_week.valid_days') }}</div>
          </div>
          <gap :gap="12"/>
          <div class="card-body-item flex">
            <div class="card-item-top flex">
              <coin-number class="card-item-num" :num="config.total_point" :unit-size="13" />
              <img class="card-icon" src="@/assets/images/common/diamond.png" alt="">
            </div>
            <div class="item-tips">{{ $t('anchor_week.during_income') }}</div>
          </div>
        </div>
      </div>
      <div class="card-wrap">
        <div class="card-title-wrap flex">
          <div class="card-title-val">{{ $t('anchor_week.reward_level') }}</div>
          <img class="title-img" src="@/assets/images/anchor-week/card-icon2.png" alt="">
        </div>
        <div class="card-body card-table">
          <table>
            <tbody>
              <tr>
                <th v-for="(title, idx) in tableTitle" :key="idx">
                 <div :class="{title:true, fs:!(lang=='pt'||lang=='ar')}">{{ title.text }}</div>
                </th>
              </tr>
              <tr v-for="(col, idx) in tableData" :key="idx">
                <td v-for="(content, cIdx) in col" :key="cIdx"
                    :rowspan="content.rowspan"
                >
                  <div class="content">{{content.text}}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="page-tips">{{ $t('anchor_week.copyright') }}</div>
    </main>


    <!--  提示弹窗  -->
    <rule-modal :show="hintDialogShow"
                @confirm="toggleHintDialog(false)"
                @update:show="toggleHintDialog"
    >
    </rule-modal>

  </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue'
import guildApi from '@/api/guild.js'
import RuleModal from './components/RuleModal.vue'
import { i18n } from '@/i18n/index.js'

const { proxy } = getCurrentInstance()
const t = i18n.global.t

const tableTitle = [
  { text: t('anchor_week.reward_level'), rowspan: 1 },
  { text: t('anchor_week.valid_days'), rowspan: 1 },
  { text: t('anchor_week.income_target'), rowspan: 1 },
  { text: t('anchor_week.pre_reward'), rowspan: 1 },
  { text: t('anchor_week.now_reward'), rowspan: 1 },
]
const tableData = [
  [
    { text: '0', rowspan: 1 },
    { text: '/', rowspan: 1 },
    { text: '<10,000', rowspan: 1 },
    { text: '0', rowspan: 1 },
    { text: '0', rowspan: 1 },
  ],
  [
    { text: 'A', rowspan: 1 },
    { text: '5d', rowspan: 1 },
    { text: '10,000', rowspan: 1 },
    { text: '1,800', rowspan: 1 },
    { text: '3,000', rowspan: 1 },
  ],
  [
    { text: 'A-', rowspan: 1 },
    { text: '<5d', rowspan: 1 },
    { text: '10,000', rowspan: 1 },
    { text: '1,260', rowspan: 1 },
    { text: '2,100', rowspan: 1 },
  ],
  [
    { text: 'B', rowspan: 1 },
    { text: '5d', rowspan: 1 },
    { text: '25,000', rowspan: 1 },
    { text: '5,000', rowspan: 1 },
    { text: '10,000', rowspan: 1 },
  ],
  [
    { text: 'B-', rowspan: 1 },
    { text: '<5d', rowspan: 1 },
    { text: '25,000', rowspan: 1 },
    { text: '3,500', rowspan: 1 },
    { text: '7,000', rowspan: 1 },
  ],
  [
    { text: 'C', rowspan: 1 },
    { text: '5d', rowspan: 1 },
    { text: '50,000', rowspan: 1 },
    { text: '10,000', rowspan: 1 },
    { text: '25,000', rowspan: 1 },
  ],
  [
    { text: 'C-', rowspan: 1 },
    { text: '<5d', rowspan: 1 },
    { text: '50,000', rowspan: 1 },
    { text: '7,000', rowspan: 1 },
    { text: '17,500', rowspan: 1 },
  ],
  [
    { text: 'D', rowspan: 1 },
    { text: '5d', rowspan: 1 },
    { text: '100,000', rowspan: 1 },
    { text: '25,000', rowspan: 1 },
    { text: '50,000', rowspan: 1 },
  ],
  [
    { text: 'D-', rowspan: 1 },
    { text: '<5d', rowspan: 1 },
    { text: '100,000', rowspan: 1 },
    { text: '17,500', rowspan: 1 },
    { text: '35,000', rowspan: 1 },
  ],
  [
    { text: 'E', rowspan: 1 },
    { text: '/', rowspan: 6 },
    { text: '240,000', rowspan: 1 },
    { text: '58,000', rowspan: 1 },
    { text: '120,000', rowspan: 1 },
  ],
  [
    { text: 'F', rowspan: 1 },
    { text: '360,000', rowspan: 1 },
    { text: '88,000', rowspan: 1 },
    { text: '180,000', rowspan: 1 },
  ],
  [
    { text: 'G', rowspan: 1 },
    { text: '720,000', rowspan: 1 },
    { text: '180,000', rowspan: 1 },
    { text: '360,000', rowspan: 1 },
  ],
  [
    { text: 'H', rowspan: 1 },
    { text: '1,500,000', rowspan: 1 },
    { text: '380,000', rowspan: 1 },
    { text: '750,000', rowspan: 1 },
  ],
  [
    { text: 'I', rowspan: 1 },
    { text: '2,700,000', rowspan: 1 },
    { text: '685,000', rowspan: 1 },
    { text: '1,350,000', rowspan: 1 },
  ],
  [
    { text: 'S', rowspan: 1 },
    { text: '4,500,000', rowspan: 1 },
    { text: '1,150,000', rowspan: 1 },
    { text: '2,250,000', rowspan: 1 },
  ],
]


const countdown = computed(() => {
  const {
    current_ts: cur = 0,
    end_ts: end = 0,
  } = config.value

  return Math.floor((end - cur) * 1000)
})

const config = ref({})
const getConfig = async () => {
  const response = await guildApi.room_inspire()
  if (response.code === 200) {
    config.value = response.data || {}
  }
}

// 下滑展示标题处理
const showPageTitle = ref(false)
const scrollChangeBgShow = (show) => {
  showPageTitle.value = show
}


const hintDialogShow = ref(false)
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool
}

// 滚动
const pageContainer = ref()
watch(() => hintDialogShow.value, (val) => {
  pageContainer.value.style.overflow = val ? 'hidden' : 'scroll'
})

const lang = proxy.$languageFile
function getImageUrl(name) {
  return new URL(`../../../assets/images/anchor-week/${lang}/${name}`, import.meta.url).href
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  position: relative;
  height: 100vh;
  overflow-y: scroll;
  background: #8572FF;

  .bg-img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
  }

  header {
    position: relative;
    width: 100%;
    height: px2rem(354);
    text-align: center;

    .title-img {
      width: px2rem(348);
      margin-top: $gap2;
    }

    .rule {
      position: fixed;
      top: px2rem(243);
      right: 0;
      z-index: 10;
      padding: $gap4 $gap8 $gap4 $gap12;
      color: #FFFDFE;
      background: linear-gradient(180deg, #8BA0F0 0%, #A262F4 100%);
      border: px2rem(1) solid #F0F3FD;
      border-radius: $radius12 0 0 $radius12;

      @include fontSize13;
    }
  }
}

main {
  position: relative;
  z-index: 1;
  padding: $gap14;
}

.count-down-wrap {
  justify-content: center;
  width: fit-content;
  height: px2rem(24);
  padding-right: $gap8;
  margin: 0 auto;
  border-style: solid;
  border-image-slice: 30 22 30 22;
  border-image-width: px2rem(20) px2rem(20) px2rem(20) px2rem(20);
  border-image-outset: 0 0 0 0;
  border-image-repeat: stretch stretch;
  border-image-source: url("@/assets/images/anchor-week/countdown-bg.png");
  align-items: center;
  border-width: thin;

  .date {
    height: px2rem(22);
    line-height: px2rem(22);
    background: linear-gradient(90deg, #79BEFF 0.74%, #8C7FFF 100%);
    color: $white;
    font-size: $fontSize13;
    font-weight: 700;
    border-radius: px2rem(20) 0 0 px2rem(20);
    padding: 0 $gap8;
  }
  .icon-time {
    width: px2rem(16);
    height: px2rem(16);
    margin: 0 $gap4;
  }

  .count-down {
    line-height: 1.1 !important;
    font-weight: $fontWeightBold;
    color: #9562FF;

    @include fontSize13;
  }
}

.card-wrap {
  padding: $gap4;
  margin-bottom: $gap16;
  background-color: rgba($white, .3);
  border: px2rem(1) solid #FFFFFF99;
  border-radius: $radius12;

  .card-title-wrap {
    position: relative;
    height: px2rem(44);
    padding: $gap8 $gap12;
    background: linear-gradient(90deg, #EEDAFD 0%, #C0CDFD 100%);
    border-radius: $gap8 $gap8 0 0;

    .card-title-val {
      font-size: px2rem(19);
      font-weight: $fontWeightHeavy;
      font-family: 'GilroyItalic';
      color: #3591F4;
      text-shadow: px2rem(2) px2rem(2) 0 $white,
      px2rem(-2) px2rem(-2) 0 $white;
    }

    .title-img {
      position: absolute;
      right: 0;
      bottom: 0;
      width: px2rem(76);
      height: px2rem(60);
    }
  }

  .card-body {
    align-items: stretch;
    min-height: px2rem(44);
    padding: $gap12;
    background: linear-gradient(180deg, #F8F3FD 0%, #F3F4FD 100%);
    border-radius: 0 0 $gap8 $gap8;

    &.card-table {
      padding: $gap12 $gap8;
    }
  }

  table {
    width: 100%;
    --border-color: #C8CFFD;

    border-radius: $radius8;
    /* 消除单元格之间的空隙 */
    border-collapse: collapse;
    /* 消除掉外边框 */
    border: 1px hidden transparent;
    /* hack一下加个阴影 */
    box-shadow: 0 0 0 1px var(--border-color);
    overflow: hidden;

    th,td {
      border: 1px solid var(--border-color);
    }
    
    th {
      .title {
        min-height: px2rem(48);
        padding: 0 $gap8;
        color: $fontColorB1;
        font-weight: $fontWeightBold;
        
        @include fontSize11;
        @include centerV;
      }
      .fs {
        @include fontSize13;
      }
      &:last-child {
        .title {
          background-color: #DFDFFF;
          color: #FF4771;
        }
      }
    }

    td {
      .content {
        min-height: px2rem(40);
        padding: $gap4 $gap8;
        color: $fontColorB2;
        font-weight: $fontWeightNormal;

        @include fontSize13;
        @include centerL;
      }
    }
    td:last-child {
      .content {
        background-color: #DFDFFF;
        color: #FF4771;
      }
    }
  }

  .card-body-item {
    flex: 1;
    flex-direction: column;
    min-height: px2rem(82);
    padding: $gap12 0;
    background-color: #EEECFF;
    border-radius: $radius8;

    &:not(:last-child) {
      // margin-right: $gap12;
    }

    .card-item-top {
      justify-content: flex-end;
      align-items: flex-end;
      font-size: 0;
    }

    .card-item-num {
      font-size: px2rem(32);
      line-height: 1;
      font-weight: $fontWeightBold;
      color: #2F3842;

      :deep(.unit) {
        line-height: 1.3em;
      }
    }

    .card-item-unit {
      color: #2F3842;
      line-height: 1;

      @include fontSize13;
    }

    .card-icon {
      width: px2rem(16);
      height: px2rem(16);
    }

    .item-tips {
      color: #585B5D;
      text-align: center;

      @include fontSize13;
    }
  }
}

.page-tips {
  text-align: center;
  color: rgba($white, .64);

  @include fontSize11;
}

</style>
