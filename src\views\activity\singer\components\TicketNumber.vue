<template>
  <div class="ticket-number-box">
    <div class="ticket"></div>
    <gap :gap="3"></gap>
    <div class="ticket-number">{{ number }}</div>
  </div>
</template>
<script setup>
const props = defineProps({
  number: {
    type: Number,
    default: 0,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.ticket-number-box {
  display: flex;
  align-items: center;
  .ticket {
    width: px2rem(20);
    height: px2rem(20);
    background-image: url("@/assets/images/activity/singer/ticket.png");
    background-size: 100% 100%;
  }
  .ticket-number {
    color: #d5b8ff;
    text-align: right;
    font-family: <PERSON><PERSON>;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 14px */
  }
}
</style>
