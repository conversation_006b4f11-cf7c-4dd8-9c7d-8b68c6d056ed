<template>
  <base-modal
    :show="hintDialogShow"
    conform-text="Go to browser"
    @confirm="openExternalBrowser"
    @update:show="toggleHintDialog"
  >
    <template #title>{{ hintDialogTitle }}</template>
    <template #content>{{ hintDialogMsg }}</template>
  </base-modal>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import rechargeApi from "@/api/recharge.js";
import { useRouter } from "vue-router";

const router = useRouter();
const { proxy } = getCurrentInstance();

let payConfig = {};
// 获取商品信息
const getPayInfo = async () => {
  // data: {
  //   productId: long,  商品id
  //   payMethod: int,  支付方式  ： 1. google， 2. apple ，3.payerMax 4 dlocal
  //   payWayId: long,  支付方式 id
  //   payScene: string,  支付场景： recharge， vip
  //   price: string, 价格
  //   productUpdateTime: long, 商品更新时间
  //   isExternalBrowser bool  是否需要跳转外部浏览器
  //   source   充值来源参数
  // }
  const result = await proxy.$siyaApp("getAppPayParams");
  if (result && result.data) {
    payConfig = result.data;
    // 付款方式 Clips 且没有填写付款信息，跳转填写
    if (payConfig.payMethod === 6) {
      const paymentInfoRes = await rechargeApi.get_payment_info({
        // pay_cfg_id: payConfig[proxy.$appOS == "1" ? "payWayId" : "payMethodId"],
        pay_cfg_id: payConfig.payWayId || payConfig.payMethodId,
        pay_scene: payConfig.payScene,
      });
      if (!paymentInfoRes.data.have) {
        sessionStorage.setItem("pageShowStep", "0");
        router.push({
          name: "PayInformation",
          query: {
            payWayId: payConfig.payWayId || payConfig.payMethodId,
            payScene: payConfig.payScene,
          },
        });
        return false;
      }
    }
  }
  return true;
};

let sendApiConfig = {};
const setPayApiConfig = () => {
  const wayMap = {
    recharge: {
      apiName: "app_pay_pre",
      query: {
        id: "productId",
      },
    },
    vip: {
      apiName: "app_vip_pay_pre",
      query: {
        id: "payWayId",
      },
    },
  };
  sendApiConfig = wayMap[payConfig.payScene];
};

// 预下单
let jumpUrl = "";
const checkPrePay = async () => {
  const host = location.host;
  const params = {
    platform: payConfig.payMethod,
    id: payConfig[sendApiConfig?.query?.id],
    productUpdateTime: payConfig.productUpdateTime,
    source: payConfig?.source,
    domainName: host,
  };

  const response = await rechargeApi[sendApiConfig.apiName](params);
  if (response.code === 200) {
    jumpUrl = response.data.jumpUrl;
    // 是否需要跳转外部浏览器
    if (response.data.external_browser) {
      hintDialogTitle.value = response.data.channel_name;
      hintDialogMsg.value =
        "You need to switch to your mobile browser to complete the payment";
      toggleHintDialog(true);
    } else {
      // 跳转三方支付收银台
      // window.location.href = jumpUrl
      const a = document.createElement("a");
      a.href = jumpUrl;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  } else {
    proxy.$closeAfterToast(response.msg);
    // 除开200的情况，告知客户端
    proxy.$siyaApp("setRainbowResponse", {
      code: response.code,
      msg: response.msg,
    });
  }
};

const checkPayBack = () => {
  const pageShowStep = Number(sessionStorage.getItem("pageShowStep")) || 0;
  if (pageShowStep >= 1) {
    sessionStorage.setItem("pageShowStep", "0");
    proxy.$siyaApp("closeWebview");
    return true;
  }
  sessionStorage.setItem("pageShowStep", "1");
  return false;
};

const openExternalBrowser = () => {
  proxy.$siyaApp("openExternalBrowser", { url: jumpUrl });
};

const hintDialogShow = ref(false);
const hintDialogTitle = ref("");
const hintDialogMsg = ref("");
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool;
};

onMounted(async () => {
  if (checkPayBack()) return;

  const result = await getPayInfo();
  if (!result) return;
  setPayApiConfig();

  await checkPrePay();
});
</script>

<style scoped lang="scss"></style>
