<template>
  <van-overlay :show="dialogShow" @touchmove.prevent z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <img class="wrapper-bg" src="@/assets/images/anchor-week/rule-title.png" alt="">
      <img class="icon-close" @click="handleCancel" src="@/assets/images/icon/icon-close-white.svg" alt="">
      <div class="container">
        <div class="wrapper-content" @touchmove.stop>
          <div class="content-text">
            <div v-for="(text, idx) in $tm('anchor_week.rule_detail')" :key="idx">
              <span v-html="text"></span>
            </div>
          </div>
          <div class="content-shadow"></div>
        </div>
        <div class="wrapper-button active"
             @click="handleCancel"
        >
          {{ $t('common.common_ok') }}
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
import { ref, watch } from 'vue'

const emit = defineEmits(["update:show", "confirm"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const dialogShow = ref(false)

watch(() => props.show, (val) => {
  dialogShow.value = !!val
})

const handleCancel = () => {
  emit('update:show', false);
};

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.base-modal-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  width: px2rem(300);
  padding: px2rem(64) $gap16 $gap16;
  background: #FFFFFF;
  border-radius: px2rem(26);
  transform: translate(-50%, -50%);

  .wrapper-bg {
    position: absolute;
    left: 0;
    top: px2rem(-48);
    width: px2rem(300);
    height: px2rem(168);
  }

  .icon-close {
    position: absolute;
    top: px2rem(10);
    right: px2rem(10);
    width: px2rem(24);
    height: px2rem(24);
  }

  .container {
    position: relative;
    z-index: 1;
    flex-direction: column;
  }

  .wrapper-content {
    position: relative;
    height: px2rem(317);
  }

  .content-text {
    height: 100%;
    overflow-y: scroll;
    padding: $gap16 0;
    color: $fontColorB2;

    @include fontSize15;

    div {
      margin-bottom: $gap16;
    }
  }

  .content-shadow {
    position: absolute;
    bottom: px2rem(-2);
    left: 0;
    width: 100%;
    height: px2rem(44);
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, #FFFFFF 100%);
  }

  .wrapper-button {
    height: px2rem(34);
    margin-top: px2rem(12);
    font-size: px2rem(17);
    line-height: px2rem(34);
    color: $fontColorB3;
    text-align: center;
    border-radius: $radius16;


    &.active {
      height: px2rem(50);
      line-height: px2rem(50);
      color: $white;
      background-color: #8572FF;
    }
  }
}
</style>
