<template>
  <div class="container" :data-theme="theme">
    <header-bar
      :title="$t('wheel_lottery.activity_rules')"
      :right-icon="rightIcon"
      :fontColor="fontColor"
      is-scroll-change-bg
      @emit:scrollChangeBgShow="scrollChangeBgShow"
      @emit:right-icon="goFeedback()"
    />
    <div class="rule">
      <div class="inner">
        <p v-for="text in $tm('wheel_lottery.rule')" v-html="text"></p>
      </div>
    </div>
  </div>
</template>
<script setup>
import rightIconWhite from "@/assets/images/wheel-lottery/common/icon-contact-white.svg";
import rightIconBlack from "@/assets/images/wheel-lottery/common/icon-contact-black.svg";
import { useRouter, useRoute } from "vue-router";
import { computed, ref } from "vue";
const router = useRouter();
const route = useRoute();

const timezone = computed(() => route.query?.timezone || "UTC+8");
const theme = computed(() => route.params.theme || "normal");

const rightIcon = computed(() =>
  isScrolled.value ? rightIconBlack : rightIconWhite
);

const fontColor = computed(() => (isScrolled.value ? "#000" : "#fff"));

const isScrolled = ref(false);
const scrollChangeBgShow = (flag) => {
  isScrolled.value = flag;
};

const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
@import "./theme.scss";
.container {
  min-height: 100vh;
  background: var(--rule-bg-color);
  padding: px2rem(12);
  .rule {
    border-radius: px2rem(20);
    border: 1px solid var(--64, rgba(255, 255, 255, 0.64));
    background: var(--16, rgba(255, 255, 255, 0.16));
    padding: px2rem(4);
    margin-top: px2rem(8);
    .inner {
      width: 100%;
      padding: px2rem(16) px2rem(16) px2rem(12) px2rem(16);
      border-radius: px2rem(16);
      background: #fff8f4;
      p {
        color: #8c4a08;
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 500;
        line-height: 128%; /* 19.2px */
        margin-bottom: px2rem(16);
      }
    }
  }
}
</style>
