import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver, VantImports } from "@vant/auto-import-resolver";
import { loadEnv as myLoadEnv } from "./src/utils/loadEnv.js";
import legacy from "@vitejs/plugin-legacy";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import viteCompression from "vite-plugin-compression";

const _resolve = (dir) => path.resolve(__dirname, dir);

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  myLoadEnv(mode);

  const outDirName =
    process.env.VITE_COMPANY === "PUHUA" ? "siya_puhua" : "siya";

  return {
    base: process.env.VITE_STATIC_BUILD_BASE,
    define: {
      "process.env": process.env,
    },
    plugins: [
      vue(),
      AutoImport({
        // imports: [VantImports()],
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
      legacy({
        targets: ["defaults", "not IE 11"],
      }),
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), "src/assets/icons")],
        symbolId: "icon-[name]",
      }),
      // viteCompression({
      //   ext: ".br", // 指定生成的压缩文件后缀为 .br
      //   algorithm: "brotliCompress", // 使用 Brotli 算法
      //   threshold: 10240, // 文件大于 10KB 才进行压缩
      //   deleteOriginFile: false, // 不删除原始文件
      //   filter: /\.(js|mjs|json|css|html|wasm)$/i, // 匹配需要压缩的文件类型
      //   compressionOptions: {
      //     level: 11, // Brotli 压缩质量级别，最高11
      //   },
      // }),
      // 同时也可以保留 Gzip 压缩（可选）
      viteCompression({
        ext: ".gz",
        algorithm: "gzip",
        threshold: 10240,
        deleteOriginFile: false,
        filter: /\.(js|mjs|json|css|html|wasm)$/i,
        compressionOptions: {
          level: 9, // Gzip 最高级别
        },
      }),
    ],
    // 配置项目别名
    resolve: {
      alias: {
        "@": _resolve("src"),
      },
    },
    build: {
      outDir: outDirName,
      assetsInlineLimit: 1024 * 10, // 小于 10kb 的图片转为 base64
      rollupOptions: {
        output: {
          chunkFileNames: `js/[name]-[hash].js`, // 引入文件名的名称
          entryFileNames: `js/[name]-[hash].js`, // 包的入口文件名称
          assetFileNames: `assets/[name]-[hash].[ext]`, // 资源文件像 字体，图片等
        },
      },
      reportCompressedSize: false,
    },
    assetsInclude: ["**/*.pag"],
    server: {
      port: 8088,
    },
  };
});
