<template>
  <div class="gender-age" :class="`type-${gender}`">
    <div class="gender"></div>
    <div class="age">{{ age }}</div>
  </div>
</template>
<script setup>
const props = defineProps({
  gender: {
    type: Number,
    default: 1, // 1 男或11 女
  },
  age: {
    type: Number,
    default: 0,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.gender-age {
  display: flex;
  height: px2rem(15);
  padding: 0px px2rem(4);
  align-items: center;
  border-radius: px2rem(13);
  .gender {
    width: px2rem(13);
    height: px2rem(13);
    background-size: 100% 100%;
  }
  .age {
    /* T11/R */
    font-family: Gilroy;
    font-size: px2rem(11);
    font-style: normal;
    font-weight: 500;
    line-height: 1; /* 13.2px */
    height: px2rem(15);
    display: flex;
    align-items: center;
    text-align: center;
    padding-top: px2rem(2);
  }
}
.type-1 {
  background: rgba(211, 209, 255, 0.8);
  .gender {
    background-image: url("@/assets/images/common/male.png");
  }
  .age {
    color: var(---C1, #5f56ed);
  }
}
.type-11 {
  background: rgba(255, 213, 223, 0.8);
  .gender {
    background-image: url("@/assets/images/common/female.png");
  }
  .age {
    color: var(---C2, #ff4771);
  }
}
</style>
