/*
项目Gilroy字体指定：
常规(Medium)：500
加粗(Bold)：700
特粗：900

规范：
100 - 淡体 Thin ( Hairline )
200 - 特细 Extra-light ( ultra-light )
300 - 细体 Light 350 - 次细 Demi-Light
400 - 标准 Regular ( normal / book / plain )
500 - 适中 Medium
600 - 次粗 Demi-bold / semi-bold
700 - 粗体 Bold
800 - 特粗 Extra-bold / extra
900 - 浓体 Black ( Heavy )
950 - 特浓 Extra-black ( Ultra-black )
*/


@font-face {
  font-family: 'Gilroy';
  font-weight: 100;
  src: url("@/assets/fonts/Gilroy-Medium-2.otf") format("truetype");
}
@font-face {
  font-family: '<PERSON>roy';
  font-weight: 200;
  src: url("@/assets/fonts/Gilroy-Medium-2.otf") format("truetype");
}
@font-face {
  font-family: 'Gilroy';
  font-weight: 300;
  src: url("@/assets/fonts/Gilroy-Medium-2.otf") format("truetype");
}
@font-face {
  font-family: '<PERSON><PERSON>';
  font-weight: 400;
  src: url("@/assets/fonts/<PERSON><PERSON>-Medium-2.otf") format("truetype");
}
@font-face {
  font-family: 'Gilroy';
  font-weight: 500;
  src: url("@/assets/fonts/Gilroy-Medium-2.otf") format("truetype");
}

@font-face {
  font-family: 'Gilroy';
  font-weight: 600;
  src: url("@/assets/fonts/gilroy-bold-4.otf") format("truetype");
}
@font-face {
  font-family: 'Gilroy';
  font-weight: 700;
  src: url("@/assets/fonts/gilroy-bold-4.otf") format("truetype");
}
@font-face {
  font-family: 'Gilroy';
  font-weight: 800;
  src: url("@/assets/fonts/gilroy-bold-4.otf") format("truetype");
}

@font-face {
  font-family: 'Gilroy';
  font-weight: 900;
  src: url("@/assets/fonts/gilroy-black-6.otf") format("truetype");
}

@font-face {
  font-family: 'GilroyItalic';
  font-weight: 900;
  src: url("@/assets/fonts/Gilroy-HeavyItalic-8.otf") format("truetype");
}

@font-face {
  font-family: 'DomaineDisp';
  src: url('@/assets/fonts/DomaineDisp-Bold.otf') format('truetype');
  font-weight: 700;
}
  
// 国旗emoji转换
@font-face {
  font-family: 'Twemoji';
  font-weight: 400;
  src: url("@/assets/fonts/TwemojiCountryFlags.woff2") format("truetype");
}
