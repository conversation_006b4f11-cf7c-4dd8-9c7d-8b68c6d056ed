@import "config/colors";
@import "config/dimens";

* {
  box-sizing: border-box;
}

img {
  object-fit: contain;
}

.flex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.flex-col {
  flex-direction: column;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 文字渐变色
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

// 提交按钮
.submit-button {
  font-weight: $fontWeightBold;
}

@mixin center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin centerV() {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

@mixin centerL() {
  display: flex;
  align-items: center;
}
.van-popup {
  max-height: 80vh;
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
