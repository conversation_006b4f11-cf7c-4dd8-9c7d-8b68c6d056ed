<template>
  <div
    class="prize-button"
    :class="{
      animation: isAnimation,
      disabled: isDisabled,
      scale: !isApp
    }"
  >
    <template v-if="!isApp">
      <div
        @click="download()"
        v-track:click
        trace-key="star_ambassador_sign_up_click"
      >
        {{ $t(`promoter_star.register_now`) }}
      </div>
    </template>
    <!-- 上传视频时间内 -->
    <template v-else-if="isInSubmitTime">
      <div
        @click="handleSubmit()"
        v-track:click
        trace-key="star_ambassador_video_upload_click"
      >
        <div v-if="starInfo.submit_status === 1">
          {{ $t(`promoter_star.update_video`) }}
        </div>
        <div v-else>{{ $t(`promoter_star.submit_video`) }}</div>
        <div class="button-desc">
          <van-count-down
            class="button-desc"
            v-if="countDownTime"
            :time="countDownTime"
          />
          {{ $t(`promoter_star.event_end_template`) }}
        </div>
      </div>
    </template>
    <!-- 报名时间内、上传视频时间外 -->
    <template v-else-if="isInRegisterTime">
      <div v-if="isRegisted">
        <div v-if="starInfo.submit_status === 1">
          {{ $t(`promoter_star.update_video`) }}
        </div>
        <div v-else>{{ $t(`promoter_star.submit_video`) }}</div>
        <div class="button-desc">
          {{
            $t(`promoter_star.event_start_template`, [
              formattStartTime(starInfo.submit_start_time),
            ])
          }}
        </div>
      </div>
      <div
        v-else
        @click="handleRegister()"
        v-track:click
        trace-key="star_ambassador_sign_up_click"
      >
        {{ $t(`promoter_star.register_now`) }}
      </div>
    </template>
    <div v-else>
      <div>{{ $t(`promoter_star.event_ended`) }}</div>
    </div>
  </div>
</template>
<script setup>
import { getCurrentInstance, computed, ref, watch } from "vue";
import { formatDate, downloadApp } from "@/utils/util.js";
import activityApi from "@/api/activity.js";
import { useRouter } from "vue-router";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const router = useRouter();
const { appContext } = getCurrentInstance();
const props = defineProps({
  starInfo: {
    defult: () => ({}),
  },
  disabledAnimation: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["registerSuccess"]);

const countDownTime = ref(null);

const isAnimation = computed(() => {
  return (
    isApp.value &&
    !props.disabledAnimation &&
    !isRegisted.value &&
    !isDisabled.value
  );
});

// 是否在APP内
const isApp = computed(() => {
  return appContext?.config.globalProperties.$isApp;
});

// 是否已报名
const isRegisted = computed(() => {
  return props.starInfo?.register_status === 1;
});

// 在报名时间内
const isInRegisterTime = computed(() => {
  if (!props.starInfo) {
    return false;
  }
  const now = Date.now() / 1000;
  return (
    now > props.starInfo.register_start_time &&
    now < props.starInfo.register_end_time
  );
});

// 在上传视频时间内
const isInSubmitTime = computed(() => {
  if (!props.starInfo) {
    return false;
  }
  const now = Date.now() / 1000;
  return (
    now > props.starInfo.submit_start_time &&
    now < props.starInfo.submit_end_time
  );
});

const isDisabled = computed(
  () =>
    (!isInRegisterTime.value && !isInSubmitTime.value) ||
    (isInRegisterTime.value && !isInSubmitTime.value && isRegisted.value)
);

const formattStartTime = (time) => {
  return formatDate(time * 1000, `YYYY/MM/DD HH:mm`);
};

const isRegisting = ref(false);
const handleRegister = () => {
  if (!isApp.value) {
    download();
    return;
  }
  if (isRegisting.value || isDisabled.value) {
    return;
  }
  isRegisting.value = true;
  activityApi
    .star_register()
    .then((res) => {
      console.log(res);
      if (res.code === 200) {
        showToast(t("promoter_star.register_successfully"));
        emits("registerSuccess");
      } else {
        throw new Error(res.msg);
      }
    })
    .catch(() => {
      setTimeout(() => {
        isRegisting.value = false;
      }, 1500);
    });
};

const handleSubmit = () => {
  if (!isApp.value) {
    download();
    return;
  }
  router.push({
    name: "PromoterSubmit",
  });
};

function download() {
  downloadApp("https://app.adjust.com/1plcmo6o");
}

watch(
  () => props.starInfo,
  (newValue) => {
    if (newValue) {
      countDownTime.value = newValue.submit_end_time * 1000 - Date.now();
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.92);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.92);
  }
  100% {
    transform: scale(1);
  }
}
.animation {
  animation: scaleAnimation 1.5s ease-in-out infinite;
}
.prize-button {
  display: flex;
  width: 100%;
  height: px2rem(72);
  margin: 0 auto;
  justify-content: center;
  align-items: center;
  border-radius: px2rem(100);
  background: #ffd42f;
  box-shadow: 0px 0px px2rem(50) 0px #fff inset;
  margin-top: px2rem(8);
  color: #7c0001;
  & > div {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  & :first-child {
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(28);
    font-style: normal;
    font-weight: 900;
    line-height: normal;
  }
  .button-desc {
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(15);
    font-style: normal;
    font-weight: 500;
    line-height: 128%; /* 19.2px */
    display: flex;
    margin: 0 px2rem(5);
    color: inherit;
  }
}
.disabled {
  background: #fff0b8;
  color: rgba(124, 0, 1, 0.32);
}
.scale{
  transform: scale(0.8);
}
</style>
