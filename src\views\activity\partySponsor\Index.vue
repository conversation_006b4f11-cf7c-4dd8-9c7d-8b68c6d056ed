<template>
  <div class="app-container">
    <div class="header">
      <div class="title">{{$t('party.roomParty_activitySponsor')}}</div>
      <svg-icon
        class="icon-close"
        icon="close"
        :color="'#fff'"
        @click="handleClose"
      />
    </div>
    <div class="content">
      <p class="info">{{$t('party.roomParty_during')}}</p>
      <div class="during-item" v-for="item in activityList" :key="item">
        <svg-icon
          class="icon-choose"
          :icon="item.bool?'choose':'un-choose'"
        />
        <gap :gap="6" />
        <div class="item-text">{{ item.title }}</div>
      </div>
      <!-- <p class="info mt20">{{$t('party.roomParty_hostReward')}}</p> -->
      <div class="gift-title mt20">{{$t('party.roomParty_hostReward')}}</div>
      <div class="reward">
        <div class="reward-item" v-for="(jtem,i) in rewards[0]" :key="i">
          <span class="right-icon"> {{ jtem.duration + t('rules.room_day') }} </span>
          <img :src="jtem.icon" alt="" />
          <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
        </div>
      </div>
      <div class="gift-title">{{$t('party.roomParty_giftTop1')}}</div>
      <div class="reward">
        <div class="reward-item" v-for="(jtem,i) in rewards[1]" :key="i">
          <span class="right-icon"> {{ jtem.duration + t('rules.room_day') }} </span>
          <img :src="jtem.icon" alt="" />
          <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
        </div>
      </div>
      <div class="gift-title">{{$t('party.roomParty_giftTop2To3')}}</div>
      <div class="reward">
        <div class="reward-item" v-for="(jtem,i) in rewards[2]" :key="i">
          <span class="right-icon"> {{ jtem.duration + t('rules.room_day') }} </span>
          <img :src="jtem.icon" alt="" />
          <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
        </div>
      </div>
      <div class="gift-title">{{$t('party.roomParty_receiveGiftTop1')}}</div>
      <div class="reward">
        <div class="reward-item" v-for="(jtem,i) in rewards[3]" :key="i">
          <span class="right-icon"> {{ jtem.duration + t('rules.room_day') }} </span>
          <img :src="jtem.icon" alt="" />
          <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
        </div>
      </div>
      <div class="gift-title">{{$t('party.roomParty_receiveGiftTop2To3')}}</div>
      <div class="reward">
        <div class="reward-item" v-for="(jtem,i) in rewards[4]" :key="i">
          <span class="right-icon"> {{ jtem.duration + t('rules.room_day') }} </span>
          <img :src="jtem.icon" alt="" />
          <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance,ref,onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'
import activityApi from "@/api/activity.js";
import { useRoute, useRouter } from "vue-router";

const t = i18n.global.t
const { proxy } = getCurrentInstance()
const route = useRoute();
const activityList = ref([])
const rewards = ref([])

const lang = proxy.$languageFile

onMounted(() => {
  activityApi.party_info({ party_id: route.query.partyId }).then(res => {
    activityList.value = res.data.e_list
    rewards.value = res.data.rewards
  })
})
const handleClose = () => {
  proxy.$siyaApp("closeWebview");
};

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.app-container {
  width: 100%;
  background-image: url("@/assets/images/activity/room-party/bg.jpg"), linear-gradient(#827EC5, #827EC5);
  background-repeat: no-repeat, no-repeat;
  background-position: top, top;
  background-size: 100% auto, 100% 100%; // 图片宽度100%，高度自适应；渐变铺满容器
  color: #fff;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(16);
    height: px2rem(50);
    .title {
      color: #FFF;
      text-shadow: 0 0 px2rem(10) #BA52FF;
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: italic;
      font-weight: 900;
    }
    .icon-close {
      font-size: px2rem(14);
    }
  }
  .content {
    padding: 0 px2rem(16);
    .info {
      font-size: px2rem(13);
      font-weight: 700;
      color: rgba(255, 255, 255, 0.80);
    } 
    .mt20 {
      margin-top: px2rem(20);
    }
    .during-item {
      border: 1px solid rgba(255, 255, 255, 0.30);
      background-color: #6E5EA6;
      padding: px2rem(8) px2rem(6);
      border-radius: px2rem(8);
      color: #FCC5FF;
      font-size: px2rem(15);
      margin-top: px2rem(4);
      display: flex;
      align-items: flex-start;
      .icon-choose {
        font-size: px2rem(16);
        width: px2rem(16);
      }
      .item-text {
        flex: 1;
      }
    }
    .gift-title {
      font-size: $fontSize11;
      color: rgba(255, 255, 255, 0.70);
      margin-top: px2rem(8);
    }
    .reward {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: px2rem(0) px2rem(24) ;
      padding-bottom: px2rem(16);
      .reward-item {
        width: px2rem(90);
        height: px2rem(90);
        background: url("@/assets/images/ranking/weekly-star/reward-bg.png")
          top/contain no-repeat;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: px2rem(4);
        margin-top: px2rem(16);
        .right-icon {
          border-radius: 0px 8px;
          padding: px2rem(2) px2rem(5);
          background: linear-gradient(90deg, #ffea64 0%, #ffd395 100%),
            linear-gradient(90deg, #ffca64 0%, #ff976b 100%);
          font-size: px2rem(9);
          color: #9b401f;
          font-weight: 500;
          position: absolute;
          right: 0;
          top: 0;
        }
        img {
          width: px2rem(44);
          height: px2rem(44);
        }
        p {
          font-size: px2rem(10);
          color: #fff;
          font-weight: 500;
          margin-top: px2rem(2);
          padding: 0 px2rem(4);
          text-align: center;
          max-width: px2rem(92);
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.mt20 {
  margin-top: px2rem(20) !important;
}
</style>
  