<template>
  <div class="container">
    <right-button
      :text="$t('recharge_ranking.rules_title')"
      @click="toRulePage()"
      v-track:click
      trace-key="r_recharge_button_click"
      :track-params="JSON.stringify({ active_button_name: 1 })"
    ></right-button>
    <div class="bg-box" v-track:exposure trace-key="r_recharge_expo">
      <header-bar back-icon-show :show-title="false" close-type="close" />
      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>
    </div>
    <div class="main-box">
      <div class="title">{{ $t("recharge_ranking.weekly_title") }}</div>
      <div class="count">
        <CountDown
          :left-time="(weekData?.week_countdown ?? 0) - requestTime"
        ></CountDown>
      </div>
      <div class="tip">
        {{ $t("recharge_ranking.weekly_desc", [20000000]) }}
      </div>
      <div class="reward-process">
        <div class="reward-process-box">
          <PrizeProcess
            :rewords="weekData?.items ?? []"
            :current-process="weekData?.week_current_coin ?? 0"
            :is-readyed="!!weekData"
            @preview="handlePreview"
          ></PrizeProcess>
        </div>
        <div
          class="recharge-button"
          @click="openRechargeDialog()"
          v-track:click
          trace-key="r_recharge_button_click"
          :track-params="JSON.stringify({ active_button_name: 2 })"
        >
          <div>{{ $t("recharge_ranking.recharge_now") }}</div>
          <div>
            <span>{{ $t("recharge_ranking.recharged_this_week") }}</span>
            <span>{{ weekData?.week_current_coin ?? 0 }}</span>
          </div>
        </div>
      </div>
      <div class="title mt-43">
        {{ $t("recharge_ranking.monthly_rank_title") }}
      </div>
      <div class="count">
        <CountDown
          :left-time="(monthData?.month_countdown ?? 0) - requestTime"
        ></CountDown>
      </div>
      <div class="tip">
        {{ $t("recharge_ranking.monthly_rank_desc") }}
      </div>
      <div class="r-swiper">
        <RewardSwiper :rewards="monthData?.rewards ?? []"></RewardSwiper>
      </div>
      <div class="top3">
        <div class="top-2">
          <div class="avatar">
            <frame-avatar
              v-if="top2"
              :avatar="top2?.avatar"
              :frame="top2?.frame"
              :size="54"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top2?.nickname || "-" }}</div>
        </div>
        <div class="top-1">
          <div class="avatar">
            <frame-avatar
              v-if="top1"
              :avatar="top1?.avatar"
              :frame="top1?.frame"
              :size="54"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top1?.nickname || "-" }}</div>
        </div>
        <div class="top-3">
          <div class="avatar">
            <frame-avatar
              v-if="top3"
              :avatar="top3?.avatar"
              :frame="top3?.frame"
              :size="54"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top3?.nickname || "-" }}</div>
        </div>
      </div>
      <div class="ranking-list">
        <div
          class="rank-item"
          v-for="(item, index) in rankAfter3List"
          :key="index"
        >
          <div class="index">{{ item.rank }}</div>
          <gap :gap="8"></gap>
          <div class="avatar">
            <frame-avatar
              :avatar="item?.avatar"
              :frame="item.frame"
              :size="36"
            ></frame-avatar>
          </div>
          <gap :gap="8"></gap>
          <div class="nickname">{{ item.nickname }}</div>
        </div>
      </div>
    </div>
    <div class="bottom-box">
      <div class="rank-item">
        <div class="index">
          {{
            monthData?.current_rank === -1
              ? "-"
              : (monthData?.current_rank ?? 0) > 99
              ? "99+"
              : monthData?.current_rank
          }}
        </div>
        <gap :gap="8"></gap>
        <div class="avatar">
          <frame-avatar
            :avatar="monthData?.user_item?.avatar"
            :frame="monthData?.user_item?.frame"
            :size="36"
          ></frame-avatar>
        </div>
        <gap :gap="8"></gap>
        <div class="nickname">{{ monthData?.user_item?.nickname }}</div>
        <div class="coin-box">
          <GenderCoin :size="18"></GenderCoin>
          <gap :gap="3"></gap>
          <div class="coin-number">{{ monthData?.current_coin ?? 0 }}</div>
        </div>
      </div>
      <div class="bottom-desc">
        <div>{{ $t("recharge_ranking.recharged_this_month") }}</div>
        <div>
          {{
            $t("recharge_ranking.previous", [
              (monthData?.prev_coin || 0) > 0 ? monthData?.prev_coin : "-",
            ])
          }}
        </div>
      </div>
    </div>
  </div>
  <PrizeDialog ref="prizeDialogRef"></PrizeDialog>
</template>
<script setup>
import RightButton from "./components/RightButton.vue";
import CountDown from "./components/CountDown.vue";
import PrizeProcess from "./components/PrizeProcess.vue";
import RewardSwiper from "./components/RewardSwiper.vue";
import GenderCoin from "@/components/gender-coin/index.vue";
import PrizeDialog from "./components/PrizeDialog.vue";
import activityApi from "@/api/activity.js";
import { onMounted, ref, getCurrentInstance, computed } from "vue";
import { useRouter } from "vue-router";
import { i18n, getLang } from "@/i18n/index.js";
import rewardImg from "@/assets/images/activity/recharge-ranking/reward.png";
import { preloadImage } from "@/utils/util";

const t = i18n.global.t;
const prizeDialogRef = ref();
const { proxy } = getCurrentInstance();
const router = useRouter();

const handlePreview = (prize) => {
  prizeDialogRef.value.open(prize);
};

const lang = getLang();
function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/recharge-ranking/${lang}/${name}`,
    import.meta.url
  ).href;
}

const weekData = ref();
const monthData = ref();
const rankList = computed(() => {
  return monthData.value?.rank_items ?? [];
});
const top1 = computed(() => {
  return rankList.value.find((i) => i.rank === 1);
});
const top2 = computed(() => {
  return rankList.value.find((i) => i.rank === 2);
});
const top3 = computed(() => {
  return rankList.value.find((i) => i.rank === 3);
});
const rankAfter3List = computed(() => rankList.value.slice(3));
const requestTime = ref(0);
const fetchData = async () => {
  requestTime.value = 0;
  const now = Date.now();
  const weekRes = await activityApi.week_recharge();
  const monthRes = await activityApi.month_recharge();
  requestTime.value = Math.floor((Date.now() - now) / 1000);
  if (weekRes.code === 200) {
    try {
      const weekItems = weekRes.data?.items || [];
      weekItems.forEach(({ rewards }) => {
        rewards.forEach(({ reward_img }) => {
          preloadImage(reward_img);
        });
      });
      const sIndex = weekItems.findIndex((i) => i.grade === "S");
      if (sIndex !== -1) {
        weekRes.data?.items[sIndex].rewards.unshift({
          reward_name: t("recharge_ranking.reward_name"),
          reward_img: rewardImg,
          duration: 7,
          time_limited: false,
        });
      }
    } catch (e) {
      console.log(e);
    }
    weekData.value = weekRes.data;
  }
  if (weekRes.code === 200) {
    monthData.value = monthRes.data;
  }
};

const openRechargeDialog = async () => {
  const result = await proxy.$siyaApp("openSiyaUrl", {
    url: "siya://siya.com/app?method=goRechargeDialog&source=1",
  });
  if (result.data?.scene === "recharge") {
    setTimeout(() => {
      fetchData();
    }, 1000);
  }
};

const toRulePage = () => {
  router.push({
    name: "RechargeRankingRule",
  });
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  min-height: 100vh;
  background-color: #5b1303;
  overflow: hidden;
  .bg-box {
    background-image: url("@/assets/images/activity/recharge-ranking/index-bg.jpg");
    background-size: 100% px2rem(405);
    width: 100%;
    height: px2rem(405);
    background-repeat: no-repeat;
    overflow: hidden;
    .banner-title {
      width: px2rem(337);
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(-6);
      img {
        opacity: 0;
        width: px2rem(337);
        vertical-align: bottom;
      }
    }
  }
  .main-box {
    position: relative;
    z-index: 2;
    overflow: hidden;
    margin-top: px2rem(-87);

    background-image: linear-gradient(
      180deg,
      rgba(161, 0, 0, 0) 0%,
      rgba(94, 21, 1, 0.61) 1.71%,
      #5e1501 3.64%,
      #500d07 75.95%,
      #8c0500 99.17%
    );
    &::before,
    &::after {
      opacity: 0.5;
      content: "";
      width: px2rem(75);
      height: 100%;
      position: absolute;
      top: px2rem(106);
      background-size: px2rem(75) px2rem(496);
      background-repeat: no-repeat;
      background-position: 0 0, 0, px2rem(609);
    }
    &::before {
      left: px2rem(-21);
      transform: scaleX(-1);
      background-image: url("@/assets/images/activity/recharge-ranking/bg-1.png"),
        url("@/assets/images/activity/recharge-ranking/bg-1.png");
    }
    &::after {
      right: px2rem(-21);
      background-image: url("@/assets/images/activity/recharge-ranking/bg-1.png"),
        url("@/assets/images/activity/recharge-ranking/bg-1.png");
    }
    .title {
      margin-top: px2rem(21);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      background: linear-gradient(0deg, #fb0 0%, #fff 86.54%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
      position: relative;
    }
    .mt-43 {
      margin-top: px2rem(43);
    }
    .count {
      position: relative;
      margin-top: px2rem(8);
    }
    .tip {
      display: flex;
      padding: px2rem(8) px2rem(11);
      width: px2rem(302);
      margin: 0 auto;
      margin-top: px2rem(8);
      justify-content: center;
      align-items: center;
      align-self: stretch;
      border-radius: px2rem(8);
      border: 1px solid rgba(210, 107, 4, 0.4);
      background: rgba(255, 140, 0, 0.08);
      color: rgba(255, 240, 130, 0.4);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      position: relative;
      line-height: normal;
    }
    .r-swiper {
      position: relative;
      z-index: 2;
      margin-top: px2rem(51);
    }
    .reward-process {
      position: relative;
      z-index: 2;
      .reward-process-box {
        margin-top: px2rem(37);
      }
      .recharge-button {
        min-width: px2rem(175);
        width: fit-content;
        height: px2rem(46);
        flex-shrink: 0;
        border-radius: px2rem(40);
        padding: 0 px2rem(24);
        border: px2rem(2) solid #fffaf6;
        background: linear-gradient(180deg, #ffde58 0%, #fb0 100%);
        box-shadow: 0px px2rem(-3) px2rem(4) 0px #ffef96 inset,
          0px px2rem(-2) px2rem(4) 0px #d8bf00 inset,
          0px px2rem(-8) px2rem(8) px2rem(1) #c99800 inset,
          0px px2rem(6) px2rem(8) 0px #fff183 inset;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        margin-top: px2rem(17);
        :first-child {
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(17);
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          background: linear-gradient(
            92deg,
            #ff3030 23.18%,
            #e71f1f 51.71%,
            #cf0000 80.49%
          );
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        & > :last-child {
          display: flex;
          height: px2rem(17);
          align-items: center;
          flex-shrink: 0;
          margin-top: px2rem(-4);
          span {
            color: #f40;
            text-align: center;
            font-family: Gilroy;
            font-size: px2rem(8);
            font-style: normal;
            font-weight: 500;
            line-height: normal;
          }
        }
      }
    }
    .top3 {
      display: flex;
      margin-top: px2rem(38);
      justify-content: center;
      position: relative;
      z-index: 2;
      .top-2 {
        width: px2rem(108);
        height: px2rem(170);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/recharge-ranking/top-2.png");
        background-size: 100% 100%;
        margin-top: px2rem(32);
      }
      .top-1 {
        width: px2rem(120);
        height: px2rem(188);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/recharge-ranking/top-1.png");
        background-size: 100% 100%;
        .avatar {
          margin-top: px2rem(77);
        }
      }
      .top-3 {
        width: px2rem(108);
        height: px2rem(170);
        flex-shrink: 0;
        background-image: url("@/assets/images/activity/recharge-ranking/top-3.png");
        background-size: 100% 100%;
        margin-top: px2rem(32);
      }
      .avatar {
        display: flex;
        width: px2rem(70);
        height: px2rem(70);
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        margin: 0 auto;
        margin-top: px2rem(58);
        position: relative;
        .empty-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: px2rem(54);
          width: px2rem(54);
          height: px2rem(54);
          background-color: #312a3b;
        }
      }
      .nickname {
        width: px2rem(88);
        height: px2rem(16);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        overflow: hidden;
        color: var(---White, #fff);

        /* T13/B */
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-top: px2rem(-4);
        text-align: center;
        margin: 0 auto;
      }
    }
    .ranking-list {
      padding: 0 px2rem(15);
      margin-top: px2rem(18);
      padding-bottom: px2rem(103);
      .rank-item {
        display: flex;
        height: 60px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        border-radius: 8px;
        background: rgba(255, 0, 0, 0.14);
        margin-bottom: px2rem(18);
        .index {
          width: px2rem(28);
          color: #fff;
          text-align: right;
          font-family: "DIN Next W1G";
          font-size: px2rem(17);
          font-style: italic;
          font-weight: 750;
          line-height: normal;
          flex-shrink: 0;
        }
        .avatar {
          display: flex;
          width: px2rem(46);
          height: px2rem(46);
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          position: relative;
        }
        .nickname {
          color: var(---White, #fff);

          /* T13/R */
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 500;
          line-height: 124%; /* 16.12px */
          flex-grow: 1;
        }
      }
    }
  }
  .bottom-box {
    width: px2rem(391);
    height: px2rem(103);
    flex-shrink: 0;
    position: fixed;
    left: 0;
    bottom: px2rem(-12);
    z-index: 3;
    border-radius: px2rem(24) px2rem(24) 0px 0px;
    background: linear-gradient(180deg, #9d0900 0%, #6c0600 100%);
    &::after {
      content: "";
      width: px2rem(391);
      height: px2rem(103);
      position: absolute;
      left: 0;
      bottom: 0;
      background-image: url("@/assets/images/activity/recharge-ranking/bottom-bg.png");
      background-size: 100% 100%;
    }
    .rank-item {
      display: flex;
      align-items: center;
      margin-left: px2rem(15);
      margin-right: px2rem(18);
      margin-top: px2rem(11);
      .index {
        width: px2rem(28);
        color: #fff;
        text-align: right;
        font-family: "DIN Next W1G";
        font-size: px2rem(17);
        font-style: italic;
        font-weight: 750;
        line-height: normal;
      }
      .avatar {
        display: flex;
        width: px2rem(46);
        height: px2rem(46);
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        position: relative;
      }
      .nickname {
        color: var(---White, #fff);

        /* T13/R */
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 500;
        line-height: 124%; /* 16.12px */
        flex-grow: 1;
      }

      .coin-box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        margin-top: px2rem(-9);
        .coin-number {
          color: #ffda6d;
          font-family: "DIN Next W1G";
          font-size: px2rem(16);
          font-style: normal;
          font-weight: 700;
          line-height: 1.2;
          letter-spacing: -0.5px;
        }
      }
    }
    .bottom-desc {
      display: flex;
      height: px2rem(28);
      flex-direction: column;
      justify-content: center;

      position: absolute;
      right: px2rem(18);
      top: px2rem(43);
      text-align: right;
      font-family: Gilroy;
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 10px */
      & > div:first-child {
        color: #ff9791;
      }
      & > div:last-child {
        color: #cc5c55;
        margin-top: px2rem(8);
      }
    }
  }
}

.rtl-html {
  .bottom-desc {
    text-align: left !important;
    left: px2rem(18);
  }
  .index {
    text-align: left !important;
  }
}
</style>
