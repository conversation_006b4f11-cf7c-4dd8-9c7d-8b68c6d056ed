<template>
  <div class="empty-wrap">
    <img class="empty-img" :src="urlSrc" alt="">
    <div class="empty-text">{{ tips }}</div>
  </div>
</template>

<script setup>
import emptyImg from '@/assets/images/common/empty.svg'

const props = defineProps({
  urlSrc: {
    type: String,
    default: emptyImg,
  },
  tips: {
    type: String,
    default: '',
  }
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.empty-wrap {
  padding: px2rem(80) 0;
  text-align: center;

  .empty-img {
    width: px2rem(120);
    height: px2rem(120);
  }

  .empty-text {
    font-size: px2rem(15);
    color: $fontColorB2;

  }
}
</style>
