<template>
  <div class="app-container">
    <div class="list">
      <div class="flex cell" v-for="(msg, idx) in $tm('base_total.no_verify_code_msg')" :key="idx">
<!--        <div class="step">{{idx + 1}}.</div>-->
        <div>{{ msg }}</div>
      </div>
    </div>

    <van-button class="submit-button"
                block
                @click="goFeedback"
    >
      {{ $t('base_total.no_verify_code_btn_text') }}
    </van-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goFeedback = () => {
  router.push({
    name: 'Feedback',
    query: {
      type: 2,
    }
  })
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding: px2rem(43) px2rem(32);
  background-color: $white;
}

.list {
  margin-bottom: px2rem(47);

  .step {
    margin-right: px2rem(4);
  }

  .cell {
    flex-wrap: nowrap;
    align-items: flex-start;
    margin-bottom: px2rem(20);
    font-size: px2rem(17);
    line-height: px2rem(20);
  }
}

.submit-button {
  background-color: #F8F7FF;
  --van-button-default-border-color: #F8F7FF;
  color: $mainColor;
}

</style>
