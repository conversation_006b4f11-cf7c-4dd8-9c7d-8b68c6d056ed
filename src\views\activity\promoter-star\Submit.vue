<template>
  <div class="app-container">
    <header-bar
      :show-title="true"
      :title="$t(`promoter_star.submit_video`)"
      fixed-bg-color="#fff"
    >
    </header-bar>
    <div class="cell-title">
      <span>*</span>{{ $t("promoter_star.select_platform") }}
    </div>
    <div class="reason-title flex" @click="toggleActionVisible(true)">
      <span>{{ form.platform }}</span>
      <img
        class="icon-down"
        src="@/assets/images/icon/icon-arrow-down.svg"
        alt=""
      />
    </div>

    <div class="cell-title">
      <span>*</span>{{ $t("promoter_star.enter_account") }}
    </div>
    <van-field
      class="reason-detail reason-email"
      v-model="form.platform_account"
      :placeholder="$t('promoter_star.please_enter')"
      maxlength="60"
      :border="false"
    />

    <div class="cell-title">
      <span>*</span>{{ $t("promoter_star.video_link") }}
    </div>
    <van-field
      class="reason-detail"
      v-model="form.video_url"
      rows="3"
      autosize
      type="textarea"
      :formatter="formatter"
      format-trigger="onBlur"
      :placeholder="$t('promoter_star.please_enter')"
      :border="false"
    />

    <div class="cell-title">
      <span>*</span>{{ $t("promoter_star.homepage_and_playback_screenshot") }}
    </div>
    <div class="flex flex-start">
      <div class="reason-upload">
        <upload-cell
          @update="
            (files) => handleUpdateImages(files, 'video_screenshot_home')
          "
        />
        <div class="upload-tips">{{ $t("promoter_star.homepage") }}</div>
      </div>
      <gap :gap="24"></gap>
      <div class="reason-upload">
        <upload-cell
          @update="
            (files) =>
              handleUpdateImages(files, 'video_screenshot_playback_number')
          "
        />
        <div class="upload-tips">
          {{ $t("promoter_star.video_playback_number") }}
        </div>
      </div>
    </div>

    <div class="note">
      {{ $t("promoter_star.note") }}
    </div>

    <div class="botton-box">
      <van-button
        class="submit-button"
        type="primary"
        block
        :disabled="buttonDisabled"
        @click="handleSubmit"
      >
        {{ $t("promoter_star.confirm") }}
      </van-button>
    </div>

    <van-action-sheet
      class-name="action-sheet"
      v-model:show="actionVisible"
      :actions="reasonList"
      @select="onSelectReason"
      close-on-click-action
    />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, computed } from "vue";
import activityApi from "@/api/activity.js";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";
import UploadCell from "./components/UploadCell.vue";
const router = useRouter();

const t = i18n.global.t;
const { proxy } = getCurrentInstance();

const form = ref({
  platform: "TikTok",
  platform_account: "",
  video_url: "",
  video_screenshot_home: "",
  video_screenshot_playback_number: "",
});

// 按钮禁用状态
const buttonDisabled = computed(() => {
  return Object.values(form.value).filter(Boolean).length !== 5;
});
// 过滤输入的空格
const formatter = (value) => value.trim();

// 原因列表
const reasonList = ref([
  {
    id: "TikTok",
    name: "TikTok",
  },
  {
    id: "Kwai",
    name: "Kwai",
  },
  {
    id: "Instagram",
    name: "Instagram",
  },
  {
    id: "Facebook",
    name: "Facebook",
  },
]);

// 选择原因
const actionVisible = ref(false);
const toggleActionVisible = (bool) => {
  actionVisible.value = bool;
};
const onSelectReason = (item) => {
  form.value.platform = item.id;
};

// 上传图片
const handleUpdateImages = (files, key) => {
  console.log("handleUpdateImages===>", files);
  form.value[key] = files[0].url;
};

// 提交
const handleSubmit = async () => {
  if (buttonDisabled.value) return;

  const params = {
    platform: form.value.platform,
    platform_account: form.value.platform_account,
    video_url: form.value.video_url,
    video_screenshot: [
      form.value.video_screenshot_home,
      form.value.video_screenshot_playback_number,
    ],
  };
  const response = await activityApi.star_submit(params);
  if (response.code === 200) {
    proxy.$toast(`${t("common.upload_success")}`);
    setTimeout(() => {
      router.back();
    }, 1500);
  }
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.app-container {
  padding: $gap16;
  padding-bottom: px2rem(130);
  --van-uploader-border-radius: #{$radius16};
}

.cell-title {
  padding-top: px2rem(8);
  padding-left: px2rem(12);
  margin-bottom: $gap8;
  font-size: px2rem(15);
  font-weight: $fontWeightBold;
  line-height: px2rem(24);

  span {
    color: $subColorRed;
    font-size: px2rem(17);
  }
}

.reason-title {
  justify-content: space-between;
  padding: px2rem(13) px2rem(12);
  margin-bottom: $gap8;
  font-size: px2rem(15);
  color: $fontColorB0;
  background-color: $white;
  border-radius: $radius16;
}

.icon-down {
  width: px2rem(24);
  height: px2rem(24);
}

.reason-detail {
  margin-bottom: $gap8;
  font-size: px2rem(15);
  border-radius: $radius16;
}

.flex {
  display: flex;
}

.flex-start {
  align-items: flex-start;
}

.reason-email {
  padding: px2rem(13) px2rem(12);
}

.reason-upload {
  margin-bottom: $gap8;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;

  .upload-tips {
    font-size: px2rem(13);
    color: $fontColorB2;
    max-width: px2rem(84);
    text-align: center;
  }
}

.note {
  color: var(---C2, #ff4771);
  /* T11/R */
  font-family: Gilroy;
  font-size: px2rem(11);
  font-style: normal;
  font-weight: 500;
  line-height: 120%; /* 13.2px */
  margin-top: px2rem(12);
}

.botton-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: px2rem(20) px2rem(16) px2rem(28) px2rem(16);
  &::after {
    content: "";
    position: absolute;
    inset: 0;
    background: linear-gradient(
      to top,
      rgba(255, 255, 255, 1) 0%,
      rgba(255, 255, 255, 1) 75%,
      rgba(255, 255, 255, 0) 100%
    );
  }
  .submit-button {
    width: 100%;
    position: relative;
    z-index: 2;
  }
}

:deep(.van-action-sheet__item) {
  font-weight: $fontWeightBold;
}
</style>
