<template>
  <div class="avatar-wrap" @click="clickAvatar">
    <van-image
        class="avatar-border"
        :style="{borderColor: borderColor}"
        round
        :width="imgSize"
        :height="imgSize"
        fit="cover"
        lazy-load
        :src="url"
    >
      <!--加载中提示-->
      <template v-slot:loading>
        <van-loading type="spinner" size="20" />
      </template>
    </van-image>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, watch } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  size: {
    type: Number,
    default: 0,
  },
  // 是否显示边框
  borderColor: {
    type: String,
    default: '',
  },
  canClick: {
    type: Boolean,
    default: false,
  },
  userId: {
    type: Number,
    default: 0,
  },
})

const imgSize = ref(0)
watch(() => props.size, (val) => {
  imgSize.value = proxy.$pxToRemPx(val)
}, {
  immediate: true,
})

const clickAvatar = () => {
  if (!props.canClick || !props.userId) return
  proxy.$siyaApp('goUserDetail', { userId: props.userId })
}

</script>

<style scoped lang="scss">
.avatar-wrap {
  font-size: 0;

  .avatar-border {
    border: 1px solid transparent;
  }
}
</style>
