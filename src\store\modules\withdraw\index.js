import { defineStore } from "pinia";
import formStyle from "@/views/income/payment/utils/formStyle.js";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;

const useWithdrawStore = defineStore("withdraw", {
  state: () => {
    return {
      // 当前商品id
      currentProductId: 0,
      // 国家代码
      countryCode: "",
      // 当前渠道信息/表单
      currentChannelInfo: {
        id: 0,
        type: 0,
        platform: 0,
        withdraw_cfg_id: 0,
        bid: 0,
        channel_name: "",
        logo: "",
        service_charge: "",
        complete: false,
        currency: "",
        price: "",
        param: [],
        payment: {},
        service_amount: 0,
        actual_amount: 0,
        addition: "",
      },
      // 当前查看记录
      currentRecord: {},
    };
  },
  actions: {
    setCurrentChannelInfo(newData) {
      this.currentChannelInfo = { ...newData };
    },
    setCurrentRecord(newData) {
      this.currentRecord = { ...newData };
    },

    // 格式化表单数据
    formatFormData(item) {
      const state = this;
      const { type } = item;
      let channelName = "";
      // 5 CARRIER_BILLING 6 SWIFT
      if (type === 5) channelName = item.pay_method_type;
      else if (type === 6) channelName = item.channel_name;
      else if (type === 7) channelName = item.channel_name;
      else if (type === 8) channelName = item.channel_name;
      else channelName = item.target_org || item.pay_method_type;

      return item.param.map((key) => {
        try {
          const countryCode = formStyle()[item.platform][state.countryCode]
            ? state.countryCode
            : "ALL";
          return {
            key: key,
            ...formStyle()[item.platform][countryCode][type][channelName][key],
            value: item.payment[key],
          };
        } catch (e) {
          console.error(e);
          return {
            key: key,
            value: item.payment[key],
          };
        }
      });
    },

    // 处理提现状态
    formatStatusText(status) {
      if ([1, 2].includes(status)) return "PENDING";
      else if ([3].includes(status)) return "SUCCESS";
      else if ([4, 5].includes(status)) return "FAIL";
    },
    // 处理提现状态多语言
    formatStatusI18n(statusText) {
      const statusI18nMap = {
        PENDING: t("withdraw.withdrawal_pending"),
        SUCCESS: t("withdraw.withdrawal_success"),
        FAIL: t("withdraw.withdrawal_fail"),
      };
      return statusI18nMap[statusText];
    },
  },
});

export default useWithdrawStore;
