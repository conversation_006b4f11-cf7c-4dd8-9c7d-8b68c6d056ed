<template>
  <div class="app-container lucky-gift-ranking flex">
    <div class="ami-header">
      <header-bar :padding="false" closeType="close" fontColor="#fff" />
      <canvas class="ami" id="pagCanvas"></canvas>
      <div class="banner">
        <div class="banner-viewport">
          <transition name="slide-up" mode="out-in">
            <div
              class="banner-item-container"
              :key="currentUser.id || currentIndex"
              :style="{ height: proxy.$pxToRemPx(28) + 'px' }"
            >
              <template v-if="isHtmlTextWide(currentUser.name, 800,false)">
                <van-notice-bar scrollable ref="noticeBarRef" :speed="30">
                  <div class="banner-item">
                    <p style="position: absolute;left: 16px;" v-html="currentUser.name"></p>
                  </div>
                </van-notice-bar>
              </template>
              <template v-else>
                <div class="banner-item" v-html="currentUser.name" ></div>
              </template>
            </div>
          </transition>
        </div>
      </div>
    </div>
    <div class="ranking-content">
      <div
        class="rule"
        ref="contentElement"
        @scroll="handleScroll"
        :class="{ 'at-top': isAtTop, 'at-bottom': isAtBottom }"
      >
        <div class="rule-title">{{ $t("ranking.ranking_rule") }}</div>
        <div class="rule-content">
          <div
            class="rule-item"
            v-for="(item, index) in $t('ranking.ranking_rule_list').split('\n')"
            :key="index"
          >
            <span>{{ item.slice(0, 2) }}</span>
            <gap :gap="4" />
            <span>{{ item.slice(2) }}</span>
          </div>
        </div>
      </div>
      <div class="rank-top">
        <div class="rank-top-title">
          <template v-if="isTextWide('ranking.ranking_jackpot', 300)">
            <div class="notice-bar-wrapper">
              <van-notice-bar scrollable>
                {{ $t("ranking.ranking_jackpot") }}
              </van-notice-bar>
            </div>
          </template>
          <template v-else>
            {{ $t("ranking.ranking_jackpot") }}
          </template>
        </div>
        <img
          class="question"
          src="@/assets/images/ranking/lucky-gift/question.svg"
          alt=""
          @click="toDetail('LuckyGiftJackpotRule')"
        />
        <rank-top :list="jackpotList" />
      </div>
      <div class="rank-content">
        <div class="rank-content1">
          <div class="rank-top-title">
            <template v-if="isTextWide('ranking.ranking_ranking1', 300)">
              <div class="notice-bar-wrapper">
                <van-notice-bar scrollable>
                  {{ $t("ranking.ranking_ranking1") }}
                </van-notice-bar>
              </div>
            </template>
            <template v-else>
              {{ $t("ranking.ranking_ranking1") }}
            </template>
          </div>
          <img
            class="question"
            src="@/assets/images/ranking/lucky-gift/question.svg"
            alt=""
            @click="toDetail('LuckyGiftRankingRule')"
          />
          <div class="tab">
            <div
              class="tab-item"
              :class="{ active: currentTab == 'lucky' }"
              @click="currentTab = 'lucky'"
            >
              <p>
                <span :class="isTextWide('ranking.ranking_ou', 150)?'small':''">{{ $t("ranking.ranking_ou") }}</span>
              </p>
            </div>
            <div
              class="tab-item"
              :class="{ active: currentTab == 'rich' }"
              @click="currentTab = 'rich'"
            >
              <p> 
                <span :class="isTextWide('ranking.ranking_tu', 150)?'small':''">{{ $t("ranking.ranking_tu") }}</span>
              </p>
            </div>
          </div>
          <div class="time-tab-container">
            <div class="time-tab">
              <div
                class="time-tab-item"
                :class="{ active: period == 'day' }"
                @click="period = 'day'"
              >
                {{ $t("ranking.ranking_day") }}
              </div>
              <div
                class="time-tab-item"
                :class="{ active: period == 'week' }"
                @click="period = 'week'"
              >
                <p>{{ $t("ranking.ranking_week") }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="rank-content2">
          <rank-top :list="rankList.slice(0, 3)" :isSecond="true" />
          <div class="user-list" v-if="rankList.length > 3">
            <div
              class="user-item"
              v-for="(item, index) in rankList.slice(3)"
              :key="index"
            >
              <div class="num">{{ index + 4 }}</div>
              <gap :gap="8" />
              <div class="avatar">
                <img :src="item?.avatar" alt="" />
              </div>
              <gap :gap="12" />
              <div class="info">
                <p class="name">{{ item?.username }}</p>
                <div class="coin">
                  <img src="@/assets/images/common/coin.png" alt="" />
                  <gap :gap="2" />
                  <p>{{ item?.coin_num }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rank-content3"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  onMounted,
  getCurrentInstance,
  ref,
  onUnmounted,
  computed,
  watch,
} from "vue";
import { AnimationPAGInitLocal } from "@/utils/util.js";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";
import rankingApi from "@/api/ranking.js";
import coinImg from "@/assets/images/common/coin.png";
const { proxy } = getCurrentInstance();
import iconBack from "@/assets/images/icon/icon-back-circle.svg";
import RankTop from "./components/RankTop.vue";
import { debounce } from "lodash";

const router = useRouter();
const t = i18n.global.t;

const userList = ref([]);
const jackpotList = ref([]);
const rankList = ref([]);
const currentIndex = ref(0);
let intervalId = null;
let animationObj = null;
const pagCanvas = "#pagCanvas";
const contentElement = ref(null);
const isAtTop = ref(true);
const isAtBottom = ref(false);
const currentTab = ref("lucky");
const period = ref("day");
const noticeBarRef = ref(null);

const currentUser = computed(() => {
  return userList.value[currentIndex.value] || {};
});

const isHtmlTextWide = (htmlContent, maxWidth, isTran = false) => {
  const content = isTran ? t(htmlContent) : htmlContent;
  const tempElement = document.createElement("div");
  tempElement.style.position = "absolute";
  tempElement.style.visibility = "hidden";
  tempElement.style.whiteSpace = "nowrap";
  tempElement.style.display = "inline-block";
  tempElement.innerHTML = content;
  document.body.appendChild(tempElement);
  const width = tempElement.offsetWidth;
  document.body.removeChild(tempElement);
  return width > maxWidth;
};
// 计算文本宽度的函数
const isTextWide = (translationKey, num) => {
  const text = t(translationKey);
  const tempElement = document.createElement("span");
  tempElement.style.position = "absolute";
  tempElement.style.visibility = "hidden";
  tempElement.style.whiteSpace = "nowrap";
  tempElement.textContent = text;
  document.body.appendChild(tempElement);
  const width = tempElement.offsetWidth;
  document.body.removeChild(tempElement);
  return width > num;
};
const debouncedNextUser = debounce(() => {
  currentIndex.value = (currentIndex.value + 1) % userList.value.length;
}, 500);
const closeWebview = () => {
  proxy.$siyaApp("closeWebview");
};
const startCarousel = () => {
  currentIndex.value = Math.floor(Math.random() * userList.value.length);
  intervalId = setInterval(() => {
    debouncedNextUser();
  }, 10000);
};
const playAnimation = async () => {
  animationObj = await AnimationPAGInitLocal("lucky-bg.pag", pagCanvas);
  animationObj.setRepeatCount(0);
  await animationObj.play();
};
const handleScroll = () => {
  if (!contentElement.value) return;
  const { scrollTop, scrollHeight, clientHeight } = contentElement.value;
  const threshold = 1; // 滚动阈值
  // 判断是否滚动到顶部
  isAtTop.value = scrollTop <= threshold;

  // 判断是否滚动到底部
  isAtBottom.value =
    Math.abs(scrollHeight - (scrollTop + clientHeight)) <= threshold;
};
const toDetail = (name) => {
  router.push({ name });
};
const getRealRank = async () => {
  const res = await rankingApi.real_rank();
  let list = res.data.data || [];
  if (!list.length) return;

  userList.value = list.map((item) => {
    const replacements = {
      __NAME__: `<span class="name" style="white-space: nowrap;">${item.username}</span>`,
      __GIFT__: `<span class="gift" style="white-space: nowrap;">${item.gift_name}</span>`,
      __ICON__: `<img class="icon" style="white-space: nowrap;" src="${item.icon}" width="16">`,
      __COIN__: `<span class="coin" style="white-space: nowrap;"><img src="${coinImg}" width="11"><span>${item.coin_num}</span></span>`,
    };

    // 获取翻译文本（保留原始占位符顺序）
    let template = t("ranking.ranking_congratulations");

    // 1. 先找出所有占位符及其位置
    const placeholders = [];
    Object.keys(replacements).forEach(key => {
      const index = template.indexOf(key);
      if (index !== -1) {
        placeholders.push({ key, index });
      }
    });

    // 2. 按原始位置排序（从左到右处理）
    placeholders.sort((a, b) => a.index - b.index);

    // 3. 分段处理
    const segments = [];
    let lastPos = 0;

    placeholders.forEach(({ key, index }) => {
      // 添加占位符之前的内容
      if (index > lastPos) {
        segments.push(template.substring(lastPos, index));
      }
      // 添加替换内容
      segments.push(replacements[key]);
      lastPos = index + key.length;
    });

    // 添加剩余内容
    if (lastPos < template.length) {
      segments.push(template.substring(lastPos));
    }

    // 处理普通文本的空格
    const processed = segments.map(segment => {
      if (Object.values(replacements).includes(segment)) {
        return segment; // 已经是替换内容
      }
      return `<span class="text" style="white-space: nowrap;">${segment.replace(/ /g, "&nbsp;")}</span>`;
    });

    return {
      ...item,
      name: processed.join(""),
    };
  });
};
const getJackpotRank = async () => {
  const res = await rankingApi.jackpot();
  let list = res.data.data || [];
  if (!list.length) return;
  jackpotList.value = list;
  if (list.length > 1) {
    const temp = jackpotList.value[0];
    jackpotList.value[0] = jackpotList.value[1];
    jackpotList.value[1] = temp;
  }
};
const getRank = async () => {
  const res = await rankingApi.rank({
    tab: currentTab.value,
    period: period.value,
  });
  let list = res.data.data || [];
  if (!list.length) {
    rankList.value = [];
    return;
  }
  rankList.value = list;
  if (rankList.value.length > 1) {
    const temp = rankList.value[0];
    rankList.value[0] = rankList.value[1];
    rankList.value[1] = temp;
  }
};
watch([currentTab, period], async () => {
  getRank();
});

onMounted(() => {
  playAnimation();
  startCarousel();
  handleScroll();
  getRealRank();
  getJackpotRank();
  getRank();
});
onUnmounted(() => {
  clearInterval(intervalId);
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.lucky-gift-ranking {
  display: flex;
  flex-direction: column;
  color: #fff;
  &::-webkit-scrollbar {
    display: none;
  }
  .ami-header {
    width: 100vw;
    height: px2rem(292);
    // background-color: #040112;
    position: relative;
    background: url("@/assets/images/ranking/lucky-gift/500x.jpg") 100%/100%;
    .ami {
      width: 100%;
      height: 100%;
    }
    .banner {
      width: px2rem(330);
      height: px2rem(28);
      background-color: rgba(65, 60, 123, 0.96);
      border: 1px solid #fff;
      box-shadow: 0px 0px 7.6px 0px #d562ff inset,
        0px 0px 13.6px 0px #7439ff inset, 0px 0px 4px 0px #fff,
        0px 0px 4px 0px #fff, 0px 0px 21.9px 0px #e83dff;
      border-radius: px2rem(100);
      position: absolute;
      bottom: px2rem(33);
      left: 50%;
      transform: translateX(-50%);
      overflow: hidden;
      .banner-viewport {
        position: relative;
        height: 100%;
        width: 100%;
      }
      .banner-item-container {
        position: relative;
        width: 100%;
      }
      .banner-item {
        position: absolute;
        width: 100%;
        height: px2rem(28);
        line-height: 28px;
        text-align: center;
        color: rgba(255, 255, 255, 0.7);
        font-size: $fontSize13;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      ::v-deep(.van-notice-bar) {
        background-color: transparent;
        width: 100%;
        height: px2rem(28);
        line-height: px2rem(28);
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        padding: 0;
        .van-notice-bar__content {
          width: 150%;
          height: 100%;
        }
      }
    }
  }
  .ranking-content {
    background: url("@/assets/images/ranking/lucky-gift/lucky-bg.png") repeat;
    background-size: 100% auto;
    width: 100vw;
    padding: 0 px2rem(10);
    .rule {
      width: px2rem(346);
      height: px2rem(120);
      padding: px2rem(8) px2rem(18);
      border: 1px solid transparent;
      border-image: radial-gradient(
          circle at center,
          transparent 0%,
          #cbacff 90%,
          transparent 100%
        )
        1;
      border-radius: px2rem(6);
      background: rgba(57, 43, 77, 0.92);
      overflow: scroll;
      transform: translateY(-6px);
      position: relative; // 为伪元素定位
      margin: 0 auto; // 居中
      &.at-top::before {
        opacity: 0 !important;
      }

      &.at-bottom::after {
        opacity: 0 !important;
      }
      &::after {
        content: "";
        position: sticky;
        display: block;
        bottom: 0;
        left: 0;
        right: 0;
        width: auto;
        height: px2rem(30);
        margin: 0 px2rem(-18); // 抵消水平padding
        margin-top: px2rem(-30); /* 关键4：负margin抵消占位 */
        background: linear-gradient(
          to top,
          rgba(57, 43, 77, 0.92),
          #f6f5f800
        ); // 渐变背景
        pointer-events: none; // 避免遮罩层干扰内容交互
        transform: translateY(px2rem(8));
      }
      &::before {
        content: "";
        position: sticky;
        display: block;
        top: 0;
        left: 0;
        right: 0;
        width: auto;
        height: px2rem(30);
        margin: 0 px2rem(-18); // 抵消水平padding
        margin-top: px2rem(-30); /* 关键4：负margin抵消占位 */
        background: linear-gradient(
          to bottom,
          rgba(57, 43, 77, 0.92),
          #f6f5f800
        ); // 渐变背景
        pointer-events: none; // 避免遮罩层干扰内容交互
        transform: translateY(px2rem(-10));
      }
      .rule-content {
        // height: px2rem(82);
        // overflow: auto;
        .rule-item {
          color: #ccbce9;
          font-family: Gilroy;
          font-size: $fontSize11;
          line-height: 1.3;
          display: flex;
        }
      }
      .rule-title {
        color: #fff;
        font-family: Gilroy;
        font-size: $fontSize13;
        font-weight: 700;
        text-align: center;
        margin-bottom: px2rem(4);
      }
    }
    .rank-top {
      height: px2rem(290);
      background-image: url("@/assets/images/ranking/lucky-gift/top-bg.png");
      background-size: 107% 100%;
      background-position: center bottom;
      position: relative;
      padding: px2rem(10) px2rem(48) px2rem(60) px2rem(48);
      display: flex;
      justify-content: flex-end;
      // 让子元素在交叉轴上靠底部对齐
      align-items: flex-end;
      .question {
        width: px2rem(30);
        height: px2rem(30);
        position: absolute;
        right: px2rem(32);
        top: px2rem(50);
      }
    }
    .rank-top-content {
      height: px2rem(170);
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .coin {
        display: flex;
        align-items: center;
        font-size: $fontSize11;
        color: #fad772;
        justify-content: center;
        & > img {
          width: px2rem(11);
          height: px2rem(11);
        }
      }
      .rank-top-item {
        position: relative;
        font-size: $fontSize13;
        text-align: center;
        &::before {
          content: "";
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image: url("@/assets/images/ranking/lucky-gift/top1.png");
          background-size: 100%;
          background-repeat: no-repeat;
          z-index: 1; /* 关键：高于默认内容层级 */
        }
        & > img {
          width: px2rem(65);
          height: px2rem(65);
          transform: translateY(px2rem(6));
        }
        .username {
          max-width: px2rem(64);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 1.5;
        }
        .ava {
          width: px2rem(65);
          height: px2rem(65);
          border-radius: 50%;
          overflow: hidden;
          img {
            width: 82%;
            height: 82%;
            object-fit: cover;
            transform: translateY(px2rem(6));
            border-radius: 50%;
          }
        }
      }
      .top2 {
        &::before {
          background-image: url("@/assets/images/ranking/lucky-gift/top2.png");
        }
      }
      .top3 {
        &::before {
          background-image: url("@/assets/images/ranking/lucky-gift/top3.png");
        }
      }
      .second1 {
        &::before {
          background-image: url("@/assets/images/ranking/lucky-gift/second1.png");
        }
      }
      .second2 {
        &::before {
          background-image: url("@/assets/images/ranking/lucky-gift/second2.png");
        }
      }
      .second3 {
        &::before {
          background-image: url("@/assets/images/ranking/lucky-gift/second3.png");
        }
      }
      .top {
        transform: translateY(px2rem(-36));
        & > img {
          width: px2rem(78);
          height: px2rem(78);
        }
        .ava {
          width: px2rem(78);
          height: px2rem(78);
        }
      }
    }
    .rank-content {
      .rank-content1 {
        height: px2rem(194);
        background-image: url("@/assets/images/ranking/lucky-gift/top-bg1.png");
        background-size: 107% 100%;
        background-position: center bottom;
        position: relative;
        padding-top: px2rem(90);
        .rank-top-title {
          top: px2rem(32);
        }
        .question {
          width: px2rem(30);
          height: px2rem(30);
          position: absolute;
          right: px2rem(32);
          top: px2rem(56);
        }
        .tab {
          display: flex;
          align-items: center;
          height: px2rem(48);
          overflow: hidden;
          justify-content: center;
          .tab-item {
            height: px2rem(48);
            text-shadow: 0px 0px 2px #ff00d0;
            font-size: px2rem(16);
            font-weight: 900;
            background: url("@/assets/images/ranking/lucky-gift/tab.png") center / 100%
              auto no-repeat;
            width: px2rem(130);
            // background-size: auto 100%;
            // background-repeat: no-repeat;
            padding: 0 px2rem(14) 0 px2rem(34);
            line-height: px2rem(52);
            white-space: nowrap;
            p {
              text-align: center;
              width: px2rem(90);
              &>.small {
                font-size: px2rem(13);
              }
              .notice-bar-wrapper {
                width: 100%;
                position: relative;
                left: px2rem(-6);
              }
              ::v-deep(.van-notice-bar) {
                background-color: transparent;
                height: px2rem(50);
                text-shadow: 0px 0px 2px #ff00d0;
                color: $white;
                font-size: px2rem(16);
                font-weight: 900;
                white-space: nowrap;
                padding: 0;
              }
            }
            &::-webkit-scrollbar {
              display: none;
            }
            &.active {
              background-image: url("@/assets/images/ranking/lucky-gift/tab-active.png");
            }
            &:last-child {
              transform: scaleX(-1);
              & > p {
                transform: scaleX(-1);
              }
            }
          }
        }
        .time-tab-container {
          margin-top: px2rem(12);
          display: flex;
          justify-content: center;
        }
        .time-tab {
          border-radius: 100px;
          background: rgba(255, 255, 255, 0.16);
          height: px2rem(30);
          padding: px2rem(2);
          display: flex;
          align-items: center;
          justify-content: center;
          display: inline-flex;
          .time-tab-item {
            font-size: $fontSize13;
            text-align: center;
            height: px2rem(28);
            display: inline-block;
            padding: 0 px2rem(14);
            line-height: px2rem(28);
            border-radius: 100px;
            &.active {
              background-color: #7d2db1;
            }
          }
        }
      }
      .rank-content2 {
        background-image: url("@/assets/images/ranking/lucky-gift/top-bg2.png");
        background-size: 107% px2rem(158);
        background-position: center;
        height: auto;
        padding: 0 px2rem(48);
        .rank-top-content {
          height: px2rem(140);
        }
        .user-list {
          margin-top: 10px;
          .user-item {
            display: flex;
            height: px2rem(50);
            align-items: center;
            padding-top: 10px;
            .num {
              font-size: $fontSize17;
              width: px2rem(16);
              text-align: right;
            }
            .avatar {
              width: px2rem(36);
              height: px2rem(36);
              border-radius: 50%;
              display: flex;
              align-items: center;
              overflow: hidden;
              & > img {
                width: 90%;
                height: 90%;
                object-fit: cover;
                border-radius: 50%;
              }
            }
            .info {
              .name {
                font-size: $fontSize13;
                margin-bottom: px2rem(2);
              }
              .coin {
                display: flex;
                align-items: center;
                font-size: $fontSize11;
                color: #fad772;
                & > img {
                  width: px2rem(11);
                  height: px2rem(11);
                }
              }
            }
          }
        }
      }
      .rank-content3 {
        height: px2rem(86);
        background-image: url("@/assets/images/ranking/lucky-gift/top-bg3.png");
        background-size: 107% px2rem(86);
        background-position: center bottom;
        background-repeat: no-repeat;
        height: px2rem(86);
      }
    }
    .rank-top-title {
      color: #fff;
      text-shadow: 0px 0px px2rem(4) #ff00d0;
      font-family: Gilroy;
      font-size: px2rem(19);
      font-weight: 900;
      text-align: center;
      position: absolute;
      top: px2rem(30);
      left: 50%;
      width: px2rem(160);
      transform: translateX(-50%);
      height: px2rem(38);
      line-height: px2rem(38);
      ::v-deep(.van-notice-bar) {
        background-color: transparent;
        color: #fff;
        text-shadow: 0px 0px px2rem(4) #ff00d0;
        font-family: Gilroy;
        font-size: px2rem(19);
        font-weight: 900;
        text-align: center;
        position: absolute;
        left: 50%;
        width: px2rem(160);
        transform: translateX(-50%);
        height: px2rem(38);
        line-height: px2rem(38);
      }
    }
  }
}
/* 上滑动画效果 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform 0.5s ease, opacity 0.5s ease;
  will-change: transform, opacity; /* 提示浏览器提前准备 */
}

.slide-up-enter-from {
  transform: translateY(28px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-28px);
  opacity: 0;
}
.notice-bar-wrapper {
  overflow: hidden; // 隐藏超出部分
  width: px2rem(160); // 与原样式中宽度保持一致
  margin: 0 auto; // 居中
  .van-notice-bar {
    padding: 0;
  }
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 0;
    width: 20px; // 渐隐区域宽度
    height: 100%;
    z-index: 1; // 确保在 notice-bar 之上
    pointer-events: none; // 避免影响交互
  }
  &::before {
    left: px2rem(-2);
    background: rgb(0, 0, 0); // 左侧渐隐
    backdrop-filter: blur(calc(20px - (20px * (var(--x) / 20)))); 
    mask: linear-gradient(to right, black 0%, transparent 100%);
  }
  &::after {
    right: px2rem(-2);
    background: rgb(0, 0, 0); // 右侧渐隐
    backdrop-filter: blur(calc(20px - (20px * (var(--x) / 20)))); 
    mask: linear-gradient(to left, black 0%, transparent 100%);
  }
}
</style>
