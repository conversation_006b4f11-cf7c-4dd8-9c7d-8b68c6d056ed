import request from "@/utils/request";

export default {
  // 币商钱包
  agent_wallet(query) {
    return request({
      url: "/coin_agent/wallet",
      method: "get",
      params: query,
    });
  },
  // 金币明细
  coin_Detail(query) {
    return request({
      url: "/coin_agent/trade_detail",
      method: "get",
      params: query,
    });
  },
  // 最近交易人
  recent_traders(query) {
    return request({
      url: "/coin_agent/recent_traders",
      method: "get",
      params: query,
    });
  },
  // 最近联系人
  recent_contacts(query) {
    return request({
      url: "/coin_agent/recent_contacts",
      method: "get",
      params: query,
    });
  },
  // 查询转账用户信息
  transfer_userinfo(query) {
    return request({
      url: "/coin_agent/transfer_user",
      method: "get",
      params: query,
    });
  },
  // 转账条件判断
  transfer_check(query) {
    return request({
      url: "/coin_agent/transfer_check",
      method: "get",
      params: query,
    });
  },
  // 转账金币
  transfer_coins(query) {
    return request({
      url: "/coin_agent/transfer_coins",
      method: "post",
      data: query,
    });
  },

  // 币商列表
  merchant_list(query) {
    return request({
      url: "/coin_agent/merchant_list",
      method: "get",
      params: query,
    });
  },
};
