{"room_task_rule": "Aturan untuk Pemilik Ruangan Tugas\n1. Periode Evaluasi:\n<PERSON><PERSON><PERSON> dilakukan untuk menentukan apakah tugas indikator data telah tercapai dalam 7 hari terakhir (termasuk hari ini). Contoh: jika hari ini adalah 10 April, maka periode evaluasinya adalah dari 4 April hingga 10 April.\n2. Data Evaluasi:\nSetelah menyelesaikan semua tugas berikut, kamu akan menerima hadiah level yang sesuai.\na. Durasi Mic Pemilik/Admin (menit) Total waktu saat pemilik room atau admin menggunakan mic di dalam room; Catatan: Selama pemilik atau admin sedang on mic, waktu tersebut akan dihitung dalam data tugas.\nb. Total Berlian yang Diterima di Room: Jumlah total berlian yang diterima oleh pengguna di dalam room; Contoh: Jika 200 pengguna menerima berlian di room kamu selama 7 hari terakhir, maka jumlah total berlian yang mereka terima akan dihitung sebagai data tugas. <PERSON><PERSON>, dorong pengguna untuk menerima lebih banyak berlian.\nc. Ju<PERSON>lah Pengguna yang Bertahan Lebih dari 3 Menit di Room:\nTotal pengguna (tidak termasuk pemilik room) yang tinggal di room lebih dari 3 menit;Contoh: Jika pemain A masuk ke room kamu dan bertahan lebih dari 3 menit setiap hari dari 4 April sampai 10 April, maka data untuk tugas ini adalah 7. Jadi, pastikan setiap pengunjung bertahan cukup lama karena semua orang bisa bantu kamu menyelesaikan tugas dan dapat hadiah.", "room_bronze": "<PERSON><PERSON><PERSON>", "room_silver": "<PERSON><PERSON><PERSON>", "room_gold": "<PERSON><PERSON><PERSON>", "room_day": "<PERSON>", "pk_info1": "1. Apa itu PK Tim?\nMic di dalam ruangan dibagi menjadi dua tim yang akan saling bertanding (PK). <PERSON>elah PK be<PERSON>, tim dengan nilai PK tertinggi akan menjadi pemenang.\n2. Bagaimana cara menghitung nilai PK?\nSetiap mic dalam satu tim akan mendapatkan 1 nilai PK untuk setiap 1 diamond yang diterima.\n3. Animasi Upgrade Senjata PK\nSetelah PK dimulai, jika nilai PK dari salah satu tim terus meningkat dan mencapai batas tertentu, senjata di mic mereka akan otomatis di-upgrade dengan animasi khusus.", "pk_info2": "4. <PERSON>apa yang bisa memulai atau menghentikan PK?\nHanya host dan admin yang bisa memulai PK. Host bisa menutup PK kapan saja, sementara admin hanya bisa menutup PK yang dia mulai sendiri.\n5. Bagaimana cara mendukung tim?\nKamu bisa mengirim hadiah ke siapa saja yang ada di mic. Hadiah yang kamu kirim akan menambah nilai diamond tim mereka.", "pk_redWeapon": "<PERSON><PERSON><PERSON>", "pk_blueWeapon": "<PERSON><PERSON><PERSON>", "pk_detail": "Penjelasan PK", "pk_value": "Skor Popularitas", "pk_info3": "1. <PERSON><PERSON> pemilik room dan admin yang bisa memulai PK. Pemilik room bisa menonaktifkan PK, sementara admin hanya bisa menonaktifkan PK yang mereka mulai sendiri.\n2. <PERSON><PERSON> bisa mengirim hadiah ke siapa pun yang sedang berada di mic. <PERSON><PERSON> akan menambah nilai PK. Setiap 1 koin hadiah = 1 nilai PK, hadiah keb<PERSON>untungan 10 koin = 1 nilai PK.\n3. Tidak bisa mengirim hadiah ke diri sendiri.\n4. Jika turun dari mic selama PK, maka nilai PK di mic tersebut akan direset ke nol. <PERSON>ka naik mic kembali, nilai sebelumnya akan dipulihkan. Hasil akhir dihitung dari total nilai PK dari sepuluh pengguna di mic dengan nilai tertinggi selama PK berlangsung.", "face_point": "Panduan Video", "face_real_interaction": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>!", "face_friendly_interaction": "Pengguna pria lebih suka gaya ngobrol yang ramah. <PERSON><PERSON><PERSON> diri buat nunjukin wajah, sapa duluan, atau cukup angguk kecil—cara simpel buat dapetin lebih banyak dukungan dan gift!", "face_success_case": "<PERSON><PERSON><PERSON><PERSON>", "face_error_case": "<PERSON><PERSON>", "face_interaction_opening": "<PERSON><PERSON><PERSON>", "face_first_sentence": "<PERSON>pa yang kamu ucapkan di awal akan membentuk kesan pertama.", "face_enthusiasm_high_return": "<PERSON><PERSON> dengan topik ringan seperti cuaca, hobi, atau per<PERSON>aan sederhana seperti \"Asalnya dari mana?\" bisa langsung mencairkan suasana. <PERSON><PERSON><PERSON><PERSON>, jika kamu hanya diam, kem<PERSON><PERSON>an besar lawan bicara akan segera keluar dari panggilan.", "face_positive": "<PERSON><PERSON><PERSON><PERSON> = <PERSON><PERSON><PERSON><PERSON>", "face_active_high_interaction": "Inisiatif → Interaksi Lebih Aktif → Gift Lebih Banyak → Penghasilan Lebih Tinggi", "face_data_active_interaction": "Data menunjukkan bahwa host wanita yang aktif memulai interaksi memiliki kemung<PERSON> <span class=\"bold\">5 kali lebih</span> besar untuk mendapatkan gift. <PERSON>an ragu untuk menampilkan diri, setiap momen adalah peluang untuk meningkatkan pendapatan dari panggilan!", "face_face_reveal_easy": "Menampilkan Wajah Tidak Sesulit yang <PERSON> - Filter Kecantikan Siap Membantu", "face_beauty_filter_protection": "Malu tampil di kamera? <PERSON><PERSON>, filter kecantikan dan efek stylish dari kami bikin kamu tetap pede dan tampil menarik.", "face_platform_beauty_features": "Filter cantik dan efek seru dari kami siap bantu kamu—tampil menarik, tetap jaga privasi, dan bikin suasana tetap asik!", "face_interaction_tips": "<PERSON><PERSON>s <PERSON>i", "face_popular_person": "Ingin jadi host favorit? <PERSON><PERSON><PERSON>n hal-hal penting berikut:", "face_good_lighting": "Lingkungan video memiliki pencahayaan yang baik.", "face_greet_at_start": "<PERSON><PERSON><PERSON> percakapan dengan sapaan yang ramah", "face_smile_while_showing_face": "<PERSON><PERSON><PERSON><PERSON> wajah dan jaga senyuman", "face_ask_questions_guidance": "Ajukan per<PERSON>aan di waktu yang tepat untuk menjaga alur obrolan", "face_patient_polite_conversation": "Berb<PERSON><PERSON><PERSON> dengan sabar dan sopan."}