<template>
  <div class="rank-top-content">
    <div
      :class="getItemClasses(index)"
      v-for="(item, index) in dataList"
      :key="index"
    >
      <div class="ava">
        <img :src="item?.avatar" alt="" />
      </div>
      <p class="username">{{ item?.username }}</p>
      <div class="coin">
        <img src="@/assets/images/common/coin.png" alt="" />
        <gap :gap="2" />
        <p>{{ item?.coin_num }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  onMounted,
  getCurrentInstance,
  ref,
  onUnmounted,
  computed,
  watch,
} from "vue";
import sofa from "@/assets/images/ranking/lucky-gift/sofa.png";
import { i18n } from "@/i18n/index.js";

const { proxy } = getCurrentInstance();
const t = i18n.global.t;

const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
  isSecond: {
    type: Boolean,
    default: false,
  },
});
const dataList = ref([]);
watch(
  () => props.list,
  (newVal, oldVal) => {
    if (newVal.length === 1) {
      // 清空 dataList
      dataList.value = [];
      // 在前面补齐一个元素
      dataList.value.push({
        username: t("ranking.ranking_gift_xu"),
        avatar: sofa,
        coin_num: 0,
      });
      // 添加原有的一条数据
      dataList.value.push(...newVal);
      // 在后面补齐一个元素
      dataList.value.push({
        username: t("ranking.ranking_gift_xu"),
        avatar: sofa,
        coin_num: 0,
      });
    } else if (newVal.length < 3) {
      dataList.value = [];
      dataList.value.push(...newVal);
      // 补齐到三个元素
      while (dataList.value.length < 3) {
        dataList.value.push({
          username: t("ranking.ranking_gift_xu"),
          avatar: sofa,
          coin_num: 0,
        });
      }
    } else {
      dataList.value = newVal;
    }
  },
  { deep: true, immediate: true }
);
const getItemClasses = (index) => {
  const classes = ["rank-top-item"];
  if (dataList.value.length === 1) {
    classes.push("top", "second1");
  } else {
    if (index === 1) {
      classes.push("top");
      if (props.isSecond) {
        classes.push("second1");
      }
    }
    if (index === 0) {
      classes.push("top2");
      if (props.isSecond) {
        classes.push("second2");
      }
    }
    if (index === 2) {
      classes.push("top3");
      if (props.isSecond) {
        classes.push("second3");
      }
    }
  }
  return classes;
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.rank-top-content {
  height: px2rem(170);
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .coin {
    display: flex;
    align-items: center;
    font-size: $fontSize11;
    color: #fad772;
    justify-content: center;
    & > img {
      width: px2rem(11);
      height: px2rem(11);
    }
  }
  .rank-top-item {
    position: relative;
    font-size: $fontSize13;
    text-align: center;
    width: px2rem(66);
    &::before {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url("@/assets/images/ranking/lucky-gift/top1.png");
      background-size: 100%;
      background-repeat: no-repeat;
      z-index: 1; /* 关键：高于默认内容层级 */
    }
    & > img {
      width: px2rem(65);
      height: px2rem(65);
      transform: translateY(px2rem(6));
    }
    .username {
      position: relative; 
      z-index: 2; 
      display: inline-block;
      width: px2rem(110);
      text-align: center;
      transform: translateX(-50%);
      left: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.5;
    }
    .ava {
      width: px2rem(65);
      height: px2rem(65);
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 82%;
        height: 82%;
        object-fit: cover;
        transform: translateY(px2rem(6));
        border-radius: 50%;
      }
    }
  }
  .top2 {
    &::before {
      background-image: url("@/assets/images/ranking/lucky-gift/top2.png");
    }
  }
  .top3 {
    &::before {
      background-image: url("@/assets/images/ranking/lucky-gift/top3.png");
    }
  }
  .second1 {
    &::before {
      background-image: url("@/assets/images/ranking/lucky-gift/second1.png");
    }
  }
  .second2 {
    &::before {
      background-image: url("@/assets/images/ranking/lucky-gift/second2.png");
    }
  }
  .second3 {
    &::before {
      background-image: url("@/assets/images/ranking/lucky-gift/second3.png");
    }
  }
  .top {
    transform: translateY(px2rem(-36));
    width: px2rem(80);
    & > img {
      width: px2rem(78);
      height: px2rem(78);
    }
    .ava {
      width: px2rem(78);
      height: px2rem(78);
    }
  }
}
</style>
