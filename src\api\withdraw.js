import request from '@/utils/request'

export default {
  // 提现档位
  withdraw_list(query) {
    return request({
      url: '/withdraw/withdraw_list',
      method: 'get',
      params: query
    })
  },
  // 提现渠道
  withdraw_channel(query) {
    return request({
      url: '/withdraw/withdraw_channel',
      method: 'get',
      params: query
    })
  },
  // 银行转账的银行列表
  bank_list(query) {
    return request({
      url: '/withdraw/bank_list',
      method: 'get',
      params: query
    })
  },
  // 获取付款信息
  get_payment_info(query) {
    return request({
      url: '/withdraw/get_payment_info',
      method: 'get',
      params: query
    })
  },
  // 更新付款信息
  set_payment_info(query) {
    return request({
      url: '/withdraw/set_payment_info',
      method: 'post',
      data: query
    })
  },
  // 提现
  withdraw(query) {
    return request({
      url: '/withdraw/withdraw',
      method: 'post',
      data: query
    })
  },

  // 提现记录
  record(query) {
    return request({
      url: '/withdraw/record',
      method: 'get',
      params: query
    })
  },

  // 代提现确认收款
  confirm(query) {
    return request({
      url: '/withdraw/confirm',
      method: 'post',
      data: query
    })
  },
}
