<template>
  <van-overlay :show="showDialog" @touchmove.prevent z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <div class="box">
        <div class="dialog-top"></div>
        <div class="inner">
          <div class="cover-block">
            <img
              v-if="prize.reward_type === 1"
              src="@/assets/images/wheel-lottery/common/wheel/prize-coin.png"
            />
            <img v-else :src="prize.reward_img" />
            <div class="tag" v-if="!prize.time_limited && prize.duration">
              {{
                prize.reward_type !== 5
                  ? `${$t(
                      prize.duration > 1
                        ? "common.common_days"
                        : "common.common_day",
                      [prize.duration]
                    )}`
                  : `x${prize.num}`
              }}
            </div>
          </div>
          <div
            class="text"
            v-html="$t(`wheel_lottery.coins_value_template`, [prize.coin])"
          ></div>
          <div class="confirm-button" @click="handleConfirm">OK</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";

const showDialog = ref(false);
const prize = ref(null);

const handleOpen = (data) => {
  prize.value = data;
  showDialog.value = true;
};

const handleConfirm = () => {
  showDialog.value = false;
};

defineExpose({
  open: handleOpen,
  close: handleConfirm,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .box {
    width: px2rem(300);
    padding: px2rem(6);
    border-radius: px2rem(26);
    border: 1px solid var(--32, rgba(255, 255, 255, 0.32));
    background: var(--48, rgba(255, 255, 255, 0.48));
    position: relative;
    animation: show 0.3s 1;
    .dialog-top {
      position: absolute;
      left: px2rem(16);
      top: px2rem(-52);
      width: px2rem(268);
      height: px2rem(52);
      background-image: url("@/assets/images/wheel-lottery/common/dialog-top.png");
      background-size: 100% 100%;
    }
    .inner {
      display: flex;
      width: 100%;
      padding: px2rem(32) px2rem(16) px2rem(20) px2rem(16);
      flex-direction: column;
      align-items: center;

      border-radius: px2rem(20);
      background: #fffdfa;

      .cover-block {
        display: flex;
        width: px2rem(120);
        height: px2rem(120);
        padding: px2rem(8);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: px2rem(12);
        border: px2rem(1) solid #ffe4c1;
        background: #fff8ef;
        position: relative;
        img {
          width: px2rem(96);
          height: px2rem(96);
        }
        .tag {
          position: absolute;
          right: px2rem(-24);
          top: px2rem(-12);
          height: px2rem(22);
          min-width: px2rem(62);
          display: flex;
          padding: px2rem(2) px2rem(12);
          justify-content: center;
          align-items: center;
          border-radius: px2rem(100) px2rem(100) px2rem(100) px2rem(20);
          background: linear-gradient(90deg, #ff8d58 0%, #ff55d5 100%);
          color: var(---White, #fff);
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(15);
          font-style: normal;
          font-weight: 700;
          line-height: px2rem(22);
        }
      }
      .text {
        color: #382a06;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(13);
        margin-top: px2rem(13);
        font-style: normal;
        font-weight: 500;
        line-height: 124%; /* 16.12px */
        .number {
          color: #ff4771;
          font-weight: 700;
        }
      }
      .confirm-button {
        display: flex;
        height: px2rem(50);
        padding: px2rem(14) px2rem(8);
        justify-content: center;
        align-items: center;
        margin-top: px2rem(20);
        align-self: stretch;
        border-radius: px2rem(100);
        border: 1px solid #ffeab6;
        background: linear-gradient(92deg, #ffb744 0.19%, #ffaa56 100%);
        color: #fff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(17);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        position: relative;
        &::after,
        &::before {
          content: "";
          inset: 0;
          border-radius: px2rem(50);
          position: absolute;
          z-index: 2;
        }
        &::after {
          background: linear-gradient(
            176deg,
            rgba(255, 255, 255, 0.33) 3.11%,
            rgba(255, 255, 255, 0) 53.04%
          );
        }
        &::before {
          background: linear-gradient(
            174deg,
            rgba(255, 255, 255, 0.33) 4.46%,
            rgba(255, 255, 255, 0) 37.44%
          );
        }
      }
    }
  }
}

.rtl-html {
  .tag {
    left: px2rem(-24);
    right: unset !important;
    border-radius: px2rem(100) px2rem(100) px2rem(16) px2rem(100) !important;
  }
}
</style>
