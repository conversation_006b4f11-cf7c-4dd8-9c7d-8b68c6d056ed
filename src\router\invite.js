const routes = [
  {
    path: "/invite/index",
    name: "InviteIndex",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "InviteIndex"*/ "../views/invite/Index.vue"),
  },
  {
    path: "/i/m",
    name: "InviteMaleLink",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "InviteMaleLink"*/ "../views/invite/MaleLink.vue"),
  },
  {
    path: "/i/f",
    name: "InviteFemaleLink",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(
        /*webChunkName: "InviteFemaleLink"*/ "../views/invite/FemaleLink.vue"
      ),
  },
  {
    path: "/invite/rule",
    name: "InviteRule",
    meta: {
      fullScreen: true,
    },
    component: () =>
      import(/*webChunkName: "InviteRule"*/ "../views/invite/Rule.vue"),
  },
];

export default routes;
