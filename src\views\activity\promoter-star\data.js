import { checkLang } from "@/i18n/index.js";
import { getRandomInt } from "@/utils/util.js";

// 获取数组内随机项
const getRandomItem = (data, length) => {
  const options = JSON.parse(JSON.stringify(data));
  const result = [];
  for (let i = 0; i < length; i++) {
    const randomIndex = getRandomInt(0, options.length - 1);
    // 确保不会取重复
    const [item] = options.splice(randomIndex, 1);
    result.push(item);
  }
  return result;
};

// 获取标签
export const getTagList = (length = 3) => {
  const enData = [
    "#dating",
    "#crush",
    "#onlinedating",
    "#Siya",
    "#SiyaChat",
    "#SiyaApp",
    "#SiyaDating",
  ];
  const arData = [
    "#تعارف",
    "#علاقة",
    "#الحب",
    "#dating",
    "#crush",
    "#onlinedating",
    "#Siya",
    "#SiyaChat",
    "#SiyaApp",
    "#SiyaDating",
  ];
  const ptData = [
    "#solteira",
    "#relacionamento",
    "#dating",
    "#crush",
    "#onlinedating",
    "#Siya",
    "#SiyaChat",
    "#SiyaApp",
    "#SiyaDating",
  ];
  const data = {
    "zh-CN": enData,
    zh_tw: enData,
    en: enData,
    tr: enData,
    ar: arData,
    pt: ptData,
  };
  const lang = checkLang();
  const langData = data[lang] || enData;
  return getRandomItem(langData, length);
};

// 获取宣传文案
export const getPromotionalList = (length = 2) => {
  const cnData = [
    "三秒钟内，您将在此应用程序🔥🔥🔥中结交很多新朋友",
    "在 3 秒内解锁有关新朋友的大量信息！ 🔥🔥🔥",
    "👏你并不孤单，快来 Siya 找你的朋友吧。 👀🔥",
    "如果你感到孤独，没有朋友......下载 Siya！",
    "找朋友聊天试试 Siya ❤️",
    "🔥🔥下载 Siya，结交新朋友",
    "Siya： 实时语音聊天派对",
    "厌倦了孤独？🤕 立即找到你的灵魂伴侣😎",
    "厌倦了左滑？💔 10秒认识新朋友😎",
    "只需10秒即可认识新朋友",
    "快速认识新女孩😎",
    "💅睡前聊天吧✨",
    "你想认识一个在同一个城市的女孩吗？",
    "🌈🌈来这里打发无聊的时间",
    "如果你愿意的话，在siya见😎",
    "siya-认识亲密的朋友",
    "现在就和你身边的美女来一场约会吧🔥🔥🔥",
    "一款用于安排与附近单身女性约会的应用程序",
  ];
  const twData = [
    "三秒鐘內，您將在此應用程式🔥🔥🔥中結交很多新朋友",
    "在 3 秒內解鎖有關新朋友的大量資訊！ 🔥🔥🔥",
    "👏你並不孤單，快來 Siya 找你的朋友吧。 👀🔥",
    "如果你感到孤獨，沒有朋友......下載 Siya！",
    "找朋友聊天試試 Siya ❤️",
    "🔥🔥下載 Siya，結交新朋友",
    "Siya： 即時語音聊天派對",
    "厭倦了孤獨？🤕 立即找到你的靈魂伴侶😎",
    "厭倦了左滑？💔 10秒認識新朋友😎",
    "只需10秒即可認識新朋友",
    "快速認識新女孩😎",
    "💅睡前聊天吧✨",
    "你想認識一個在同一個城市的女孩嗎？",
    "🌈🌈來這裡打發無聊的時間",
    "如果你願意的話，在siya見😎",
    "siya-認識親密的朋友",
    "現在就和你身邊的美女來一場約會吧🔥🔥🔥",
    "一款用於安排與附近單身女性約會的應用程式",
  ];
  const enData = [
    "So Fun, Can't Stop Chatting. Let's Chat Before Bed.",
    "😎Meet New People in 10 Seconds✨",
    "Tired of feeling lonely? 🤕 Find your soulmate Now😎",
    "Tired of swiping left?💔 10 Sec to New Friends😎",
    "It only takes 10 seconds to meet new friends",
    "Meet New Girls Fast😎",
    "💅Let's chat before bed✨",
    "Tired of swiping left?💔 10 Sec to New Friends😎",
    "Video chat with hot girls",
    "Meet with local singles",
    "Beautiful lingerie is my weakness 🤤🤤🤤🤤 How do you like this set? 💕",
    "Looking for someone to break the rules with? 😈",
    "I’m the answer to your late-night thoughts… 🌙🔥",
    "DMs open for *spicy* conversations only. 💬🌶️",
    "Your screen just got hotter. 🔥 Click below ",
    "Curious? Let’s get dangerous. 😈",
    "Download Now if you’re not shy… 💋",
    "The content you hide from others… is here. 🔞🔓",
    "Not Your Safe Zone 🔥",
    "72% Can’t Handle This 😏",
    "Rebels Wanted 👄",
    "Thrill-seeker needed. Ready to play? 😈🔥",
    "Your 2AM thoughts deserve company… 🌙👄",
    "Download for spicy chats only. 💬🌶️",
    "Curiosity killed the cat... satisfaction brought it back. 😏",
    "Your screen just got hotter. 😈 Slide in! 💋",
    "24/7 access to untold stories. 🌙📖",
    "Let's skip the small talk. I know what you really want... 😏🔥",
    "Download for NSFW vibes only. 💬🔞",
    "Click link below to get a glimpse of all things me🔞✨",
    "Let's skip texting and go straight to...🤫🛏️",
    "if you saw me out would you say hi?🤭",
    "Can i be ur bby?🥺👉🏻👈🏻",
  ];
  const arData = [
    "في ثلاث ثوان ، سوف تجعل الكثير من الأصدقاء الجدد في هذا التطبيق🔥🔥🔥",
    "فتح الكثير من المعلومات عن أصدقاء جدد في 3 ثوان !🔥🔥🔥",
    "👏لست وحدك ، تعال إلى siyaلتجد أصدقائك.👀🔥",
    "ذا كنت تشعر بالوحدة وليس لديك أصدقاء ... قم بتنزيل Siya!",
    "تبحث عن صديق للدردشه جرب Siya ❤️",
    "🔥🔥تحميل Siya ، تكوين صداقات جديدة",
    "Siya: حفلات الدردشة الصوتية",
    `لا تشعر بالوحدة، تحدث الآن✨
    ✨مباراة عشوائية الدردشة الحية`,
    `"لا تشعر بالوحدة، تحدث الآن✨
    قابل أصدقاء جدد في 10 ثوان فقط😎"`,
    "الفضول سيجرك هنا… 🔍✨",
    " 🤫🌙 أسرارك معي… لكن قد تكشفها بنفسك.",
    "لنبدأ محادثة… بعيدًا عن الأعين. 👀✨",
    "الفضول سيجرك هنا… 🔍✨",
    " محتوى لا تراه في مكان آخر. 🤫",
    "انقر للاكتشاف… بعيدًا عن العيون. 👀🔒",
    " هل أنت مستعد للمغامرة؟ 😌",
    "ممنوع على الخجولين.  تعالى نبدأ! ",
    "هذا ليس للمترددين. 😏 هل أنت مستعد؟ 💥",
    "إذا كنتَ خجولًا، ابتعد. 😈 انضم إلينا! 🔥",
    "مغامرة أم خسارة؟ 😼",
    "للجرئين فقط 💋",
    "انضم الآن قبل فوات الأوان 🔥",
    "هل تريد إثارة خفية؟ 😌🌙",
    "محادثاتنا… سر لن يُكشف. 🤐✨",
    "لنخلق قصتنا بعيدًا عن الضوء. 🔒😈",
    " لنكن صريحين… ماذا تريد حقًا 🌙😌",
    "محادثاتنا قد تدفعك للاحمرار. 😳🔥",
    "لنلعب معًا تحت الأغطية. 😏🛏️",
    " الليل يخفي أسرارنا… هل نكشفها؟ 🌙😌",
    "همسة واحدة تكفي لإشعال اللهيب. 🤫🔥",
    "فتاة حقيقية على بعد 0.5km 💬 → Siya",
    "ابدأ الحديث معها",
  ];
  const ptData = [
    "Um aplicativo para marcar encontros com mulheres solteiras por perto👩👩👩",
    "Aplicativo de papo que melhor entende as necessidades dos homens!",
    "Seu bairro 💝🥰🥰",
    "apenas para pessoas solteiras🔥🔥🔥",
    "Coisas interessantes e pessoas interessantes",
    "Queria encontrar-se com menina na mesma cidade?",
    "🌈🌈Venha aqui para passar o tempo chato",
    "Te vejo no siya se quiser😎",
    "siya-Conhecer amigos próximos",
    "Agora mesmo, marque um encontro com mulheres bonitas próximas a você.🔥🔥🔥",
    "Um aplicativo para marcar encontros com mulheres solteiras por perto",
    "Aquí está todo👅",
    "Baixe agora e ganha um beijo meu 💋↓",
    "Tu hotwheel ♿️ favorito 💕",
    "👇🏻Lo que buscas está aquí👇🏻",
    "Aquí también la subo jeje ₍˄·͈༝·͈˄₎👅⬇️",
    "Quer algo mais quente hoje? 😏",
    "Seus segredos estão seguros comigo… ou não? 🤫🔥",
    "Vamos conversar onde ninguém nos vê? 👀🔒",
    "O que você faz aqui é segredo… 😏",
    "Arraste para cima se quiser mais… 😉",
    "Aqui, o proibido é permitido. 🔞💋",
    "Só para Corajosos 😈",
    "Clique ou Perde 💋",
    "Audácia Exigida 🔥",
    "Pronto(a) para o que ninguém vê? 😏🌑",
    "Seus desejos secretos… Vamos falar? 🤫💋",
    "Chama no privado se for quente 🔥🔐",
    "Quer algo além de conversa? Eu mostro... 😈🛏️",
    "Sua fantasia secreta… Vamos realizá-la? 🤫💦",
    "Chama se quiser quente e molhado. 🔥💧",
    "Sua mente pede, seu corpo precisa. Vamos conversar? 😌🔥",
    "Privado = Sem limites. 😈 Clique aqui！",
    "O que você esconde à noite? 🌙🤫",
    "Gata ao Vivo: 600m 😏 → Siya",
    "Ela tá a 1km… Chama AGORA! 🔥 → Siya",
    "0.4km📍Mulher REAL na área 💃 → Siya",
  ];
  const trData = [
    "Üç saniyede bu uygulamada tonlarca yeni arkadaş edineceksin. 🔥🔥🔥",
    "3 saniyede yeni arkadaşlar hakkında tonlarca bilgi edin! 🔥🔥🔥",
    "👏Yalnız değilsin, arkadaşlarını bulmak için Siya'ya gel. 👀🔥",
    "Kendini yalnız hissediyorsan ve hiç arkadaşın yoksa... Siya'yı indir!",
    "Bir sohbet arkadaşı mı arıyorsun? Siya'yı dene ❤️",
    "🔥🔥Siya'yı indir, yeni arkadaşlar edin",
    "Siya: Sesli Sohbet Partileri",
    "Yalnız hissetme, hemen konuş✨",
    "✨Rastgele Eşleşme Canlı Sohbeti",
    "Yalnız hissetme, hemen konuş✨",
    "Sadece 10 saniyede yeni arkadaşlar edin😎",
    "Merak seni buraya çekecek... 🔍✨",
    "🤫🌙 Sırların bende... ama sen de onları açığa çıkarabilirsin. Hadi bir sohbet başlatalım... gözlerden uzak. 👀✨",
    "Merak sizi buraya çekecek... 🔍✨",
    "Başka yerde göremeyeceğiniz içerikler. 🤫",
    "Keşfetmek için tıklayın... Gözlerden uzak. 👀🔒",
    "Maceraya hazır mısınız? 😌",
    "Utangaç insanlara izin verilmiyor. Başlayalım!",
    "Bu çekingenler için değil. 😏 Hazır mısınız? 💥",
    "Utangaçsanız uzak durun. 😈 Bize katılın! 🔥",
    "Macera mı yoksa kayıp mı? 😼",
    "Sadece cesurlar için 💋",
    "Çok geç olmadan hemen katılın 🔥",
    "Gizli heyecan mı istiyorsunuz? 😌🌙",
    "Sohbetlerimiz... Açığa çıkmayacak bir sır. 🤐✨",
    "Hikayemizi gözlerden uzak yaratalım. 🔒😈",
    "Dürüst olalım... Gerçekten ne istiyorsun? 🌙😌",
    "Sohbetlerimiz seni utandırabilir. 😳🔥",
    "Hadi örtülerin altında birlikte oynayalım. 😏🛏️",
    "Gece sırlarımızı saklıyor... Onları açığa çıkaralım mı? 🌙😌",
    "Bir fısıltı alevleri tutuşturmaya yeter. 🤫🔥",
    "Gerçek bir kız 0,5 km uzakta 💬 → Siya",
    "Onunla konuşmaya başla",
  ];
  const data = {
    "zh-CN": cnData,
    zh_tw: twData,
    en: enData,
    tr: trData,
    ar: arData,
    pt: ptData,
  };
  const lang = checkLang();
  const langData = data[lang] || enData;
  return getRandomItem(langData, length);
};

// 获取视频列表
export const getVideoList = () => {
  const data = {
    en: [
      {
        url: "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-en.mp4",
        cover:
          "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-en.jpg",
      },
    ],
    pt: [
      {
        url: "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-pt.mp4",
        cover:
          "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-pt.jpg",
      },
    ],
    ar: [
      {
        url: "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-ar.mp4",
        cover:
          "https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/activity/promoter-star/example/example-ar.jpg",
      },
    ],
  };
  const lang = checkLang();
  const langData = data[lang] || data["en"];
  return langData;
};
