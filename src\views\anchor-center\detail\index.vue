<template>
  <div class="app-container page-anchor-center">
    <header-bar
      is-scroll-change-bg
      :title="$t('guild.guild_detail')"
      closeType="back"
    />
    <div class="content">
      <div class="tab-header fixed">
        <div class="tab-left">
          <span
            :class="{ 'tab-name': true, active: active == index }"
            v-for="(item, index) in tabList"
            :key="index"
            @click="onChangeTab(index)"
            >{{ item }}</span
          >
        </div>
      </div>
      <swiper
        class="fa-swiper"
        :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
        :modules="modules"
        slides-per-view="auto"
        :space-between="20"
        @swiper="setControlledSwiper"
        @slideChange="onSlideChange"
      >
        <swiper-slide v-for="item in tabList" :key="item" class="swipe-item">
          <div class="tool">
            <div class="tool-title">{{ $t("anchor.anchor_common_tools") }}</div>
            <div class="tool-box">
              <div
                class="box"
                v-for="(toolItem, toolIndex) in toolList"
                :key="toolIndex"
              >
                <div class="box-num">{{ info.total_data[toolIndex] }}</div>
                <div class="box-text">{{ toolItem }}</div>
              </div>
            </div>
          </div>
          <div class="son-tab-content">
            <div class="son-tab">
              <div
                :class="{ 'tab-name': true, active: sonActive === index }"
                v-for="(sonTabItem, index) in sonTabList"
                :key="index"
                @click="onChangeSonTab(index)"
              >
                {{ sonTabItem }}
                <div
                  :class="{
                    'icon-active-line': true,
                    active: sonActive === index,
                  }"
                ></div>
              </div>
            </div>
            <swiper
              class="sub-swiper"
              :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
              :modules="modules"
              slides-per-view="auto"
              :space-between="20"
              :resistanceRatio="0" 
              @swiper="setControlledSwiperSub"
              @slideChange="onSlideChangeSub"
            >
              <swiper-slide
                v-for="subSonTabItem in sonTabList"
                :key="subSonTabItem"
                class="sub-swipe-item"
              >
                <div class="total-income">
                  <p>{{t('anchor.anchor_total_diamonds')}}</p>
                  <div class="total-income-box">
                    <img src="@/assets/images/common/diamond.png" />
                    <gap :gap="2" />
                    <div class="total-income-text">+{{ sonActive==0 ? info.chat_income.total : info.room_income.total }}</div>
                  </div>
                </div>
                <div class="progress-content" v-for="item in barList" :key="item">
                  <div class="progress-title">
                    <p>{{item.text}}</p>
                    <div class="progress-text">
                      <img src="@/assets/images/common/diamond.png" />
                      <gap :gap="2" />
                      <div class="total-income-text1">+{{ item.num }}</div>
                    </div>
                  </div>
                  <progress-bar
                    :current="item.process"
                    :total="100"
                    color="#6DB0FF"
                    :showText="false"
                    :height="6"
                  />
                </div>
                <div class="line" v-if="sonActive===0"></div>
                <div class="duration-title" v-if="sonActive===0">
                  {{t('anchor.anchor_online_time')}}
                </div>
                <div class="online-content" v-if="sonActive===0">
                  <div class="online-box">{{t('anchor.anchor_online_time_note')}}</div>
                  <gap :gap="16" />
                  <div class="online-right">
                    <img src="@/assets/images/anchor-center/time.svg" />
                    <span>{{ info.chat_income.online_minute }}min</span>
                  </div>
                </div>
                <div class="duration-title" v-if="sonActive===0">
                  {{t('anchor.anchor_room_two_reply')}}
                </div>
                <div class="online-content" v-if="sonActive===0">
                  <div class="online-box">{{t('anchor.anchor_room_two_reply_detail')}}</div>
                  <gap :gap="16" />
                  <div class="online-right">
                    <span>{{ info.chat_income.reply_rate }}</span>
                  </div>
                </div>
                <div class="line"></div>
                <div class="duration-title">
                  {{t('anchor.anchor_call_duration')}}
                </div>
                <div class="call-content">
                  <div class="call-box" v-for="item in durations" :key="item">
                    <p class="time">{{ formatDuration(item.duration) }}</p>
                    <p class="text">{{ item.text }}</p>
                  </div>
                </div>
              </swiper-slide>
            </swiper>
          </div>
        </swiper-slide>
      </swiper>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick, watch } from "vue";
import { i18n } from "@/i18n/index.js";
import { Controller } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/vue";
import { useRoute, useRouter } from "vue-router";
import { color } from "echarts";
import anchorApi from "@/api/anchor.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

const modules = [Controller];
const t = i18n.global.t;
const tm = i18n.global.tm;
const active = ref(0);
const info = ref({chat_income:{},room_income:{},total_data:{}})
const barList = ref([]);
const durations = ref([])
const tabList = ref([
  t("anchor.anchor_today"),
  t("anchor.anchor_week"),
  t("anchor.anchor_month"),
]);
const sonTabList = ref([
  t("anchor.anchor_chat_income"),
  t("anchor.anchor_room_income"),
]);
const toolList = ref([
  t("anchor.anchor_total_diamonds"),
  t("anchor.anchor_room_income"),
  t("anchor.anchor_room_chat_money"),
  t("anchor.anchor_room_platform"),
]);
const controlledSwiper = ref(null);
const controlledSwiperSub = ref(null);
const sonActive = ref(0);

const formatDuration = (seconds) => {
  if (!seconds || isNaN(seconds)) return '00:00';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  if (hours > 0) {
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  } else {
    return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  }
};
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};

let prevActiveIndex = 0; // 记录上一次的父 tab 索引
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === active.value) return;
  // 判断滑动方向
  const isForward = idx > prevActiveIndex;
  changeTab(idx, isForward);
  prevActiveIndex = idx;
};
const onChangeTab = (idx) => {
  controlledSwiper.value.slideTo(idx);
};
const changeTab = async (index, isForward) => {
  active.value = index;
  let targetSonIndex = 0;
  if (!isForward) {
    targetSonIndex = sonTabList.value.length - 1;
  }
  // 设置子 tab 激活状态
  sonActive.value = targetSonIndex;
  // 使用 nextTick 确保 DOM 更新后再滑动子 swiper
  if (controlledSwiperSub.value) {
    await nextTick(() => {
      controlledSwiperSub.value.slideTo(targetSonIndex,0);
    });
  }
};
watch(() => active.value,
  (val) => {
    getData(val+1)
  },
)
watch(() => sonActive.value,
  (val) => {
    barList.value = val ? info.value.room_income.bars : info.value.chat_income.bars;
    durations.value = val? info.value.room_income.durations : info.value.chat_income.durations;
  },
  { immediate: true }
)

const onChangeSonTab = (idx) => {
  controlledSwiperSub.value.slideTo(idx);
  sonActive.value = idx;
};

const setControlledSwiperSub = (swiper) => {
  controlledSwiperSub.value = swiper;
};

const onSlideChangeSub = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === sonActive.value) return;
  sonActive.value = idx;
};
const getData = (time_type) => {
  anchorApi.anchor_detail({ time_type }).then((res) => { 
    if (res.code === 200) {
      info.value = res.data
      barList.value = sonActive.value ? info.value.room_income.bars : info.value.chat_income.bars;
      durations.value = sonActive.value? info.value.room_income.durations : info.value.chat_income.durations;
    }
  })
}
onMounted(() => {
  getData(1)
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.page-anchor-center {
  background-color: $bgColorB7;
  height: 100vh;
  overflow: hidden;
  .content {
    padding: 0 px2rem(16) px2rem(16) px2rem(16);
    background-color: $bgColorB7;
    overflow: scroll;
    height: 88vh;
  }
  .tab-header {
    display: flex;
    align-items: center;
    &.fixed {
      position: sticky;
      top: -1px;
      background-color: $bgColorB7;
      z-index: 999;
    }
    .tab-left {
      flex: 1;
      display: flex;
      .tab-name {
        width: 32%;
        font-size: $fontSize15;
        color: $fontColorB3;
        text-align: center;
        padding: px2rem(10) 0;
        &.active {
          font-weight: $fontWeightBold;
          color: $fontColorB0;
          font-size: $fontSize17;
          // border-bottom: 3px solid $mainColor;
          // border-radius: px2rem(2);
          position: relative;
          &::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: px2rem(4); // 边框高度
            background-color: $mainColor;
            border-radius: px2rem(8); // 四个角都圆润
            z-index: 1; // 防止挡住文字
          }
        }
      }
    }
    .more {
      display: flex;
      align-items: center;
      .more-text {
        font-size: $fontSize13;
        background: linear-gradient(90deg, #ffbad5 5%, #3dbaff 100%);
        -webkit-background-clip: text; /* 兼容 WebKit/Blink 浏览器 */
        background-clip: text;
        color: transparent; /* 让文字透明，显示背景渐变 */
        margin-right: px2rem(2);
      }
    }
  }
  .fa-swiper {
    padding: px2rem(12) 0;
  }
  .swipe-item {
    .tool {
      background-color: $white;
      border-radius: px2rem(12);
      padding: px2rem(12);
      .tool-title {
        font-size: px2rem(19);
        color: $fontColorB0;
        font-weight: 700;
        margin-bottom: px2rem(12);
      }
      .tool-box {
        display: flex;
        border-radius: px2rem(12);
        overflow: hidden;
        flex-wrap: wrap;
        background-color: $bgColorB7;
      }
    }
    .box {
      width: 50%;
      text-align: center;
      position: relative;
      padding: px2rem(12) px2rem(8);
      background-color: $bgColorB7;
      .box-num {
        color: #0a2a3b;
        font-size: px2rem(20);
        font-weight: 900;
      }
      .box-text {
        color: $fontColorB2;
        font-size: $fontSize13;
        margin-top: px2rem(2);
      }
      &::after {
        content: "";
        width: px2rem(1);
        height: px2rem(32);
        background-color: rgba(0, 0, 0, 0.04);
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      &:nth-child(2),
      &:nth-child(4) {
        &::after {
          display: none;
        }
      }
    }
    .son-tab-content {
      background-color: $white;
      border-radius: px2rem(12);
      padding: 0 px2rem(12);
      margin-top: px2rem(12);
      color: $fontColorB0;
      .son-tab {
        display: flex;
        .tab-name {
          font-size: px2rem(17);
          font-weight: 500;
          color: $fontColorB3;
          padding: px2rem(12) 0;
          position: relative;
          z-index: 1;
          line-height: px2rem(22);
          text-align: center;
          &.active {
            font-weight: 700;
            color: $fontColorB0;
            font-size: px2rem(19);
          }
          &:not(:last-child) {
            margin-right: px2rem(16);
          }
          .icon-active-line {
            position: absolute;
            left: 50%;
            bottom: px2rem(-2);
            width: px2rem(34);
            height: px2rem(30);
            transform: translateX(-50%);
            &.active {
              background: url("@/assets/images/anchor-center/tab-active.png")
                100% / cover no-repeat;
              z-index: -1;
            }
          }
        }
      }
    }
  }
  .sub-swipe-item {
    .total-income {
      display: flex;
      border-radius: 12px;
      border: 1px solid #FFE5EC;
      justify-content: space-between;
      align-items: center;
      padding: px2rem(16) px2rem(12);
      background: linear-gradient(90deg, #FFF3F6 0%, #F2F2FF 49.76%, #EEFAFF 100%);
      margin-bottom: px2rem(12);
      p {
        font-size: $fontSize17;
        font-weight: 700;
      }
      .total-income-text {
        font-size: px2rem(20);
        font-weight: 900;
      }
      .total-income-box {
        display: flex;
        align-items: center;
        img {
          width: px2rem(16);
          height: px2rem(16);
        }
      }
    }
    .progress-content {
      margin-bottom: px2rem(20);
      .progress-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #666;
        font-size: $fontSize15;
        margin-bottom: px2rem(6);
        .progress-text {
          display: flex;
          font-size: $fontSize13;
          .total-income-text1 {
            font-size: px2rem(13);
            font-weight: 700;
          }
          img {
            width: px2rem(16);
            height: px2rem(16);
          }
        }
      }
    }
    .line {
      width: 100%;
      height: px2rem(1);
      background-color: #E6E6E6;
      margin-bottom: px2rem(24);
    }
    .duration-title {
      font-size: px2rem(17);
      font-weight: 700;
    }
    .online-content {
      display: flex;
      justify-content: space-between;
      margin-bottom: px2rem(24);
      margin-top: px2rem(8);
      .online-box {
        font-size: px2rem(15);
        font-weight: 500;
        color: $fontColorB2;
      }
      .online-right {
        display: flex;
        align-items: center;
        transform: translateY(px2rem(-16));
        img {
          width: px2rem(16);
          height: px2rem(16);
          margin-right: px2rem(2);
        }
        span {
          font-size: px2rem(15);
          font-weight: 700;
          color: #0F83FF;
        }
      }
    }
    .call-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: px2rem(12);
      .call-box {
        margin-bottom: px2rem(8);
        border-radius: px2rem(8);
        background-color: $bgColorB7;
        width: 49%;
        padding: px2rem(8);
        .time {
          font-size: px2rem(15);
          font-weight: 700;
        }
        .text {
          font-size: $fontSize13;
          font-weight: 500;
          color: $fontColorB3;
          margin-top: px2rem(2);
        }
      }
    }
  }
}
.rtl-html {
  .page-anchor-center .swipe-item .son-tab-content .son-tab .tab-name[data-v-b1ff8c89]:not(:last-child) {
    margin-left: px2rem(16);
  }
}
</style>
