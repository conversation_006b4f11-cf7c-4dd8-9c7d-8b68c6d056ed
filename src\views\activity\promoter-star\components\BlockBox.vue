<template>
  <div class="block-box">
    <div class="box-icon">
      <div
        class="top-icon"
        :style="{
          backgroundImage: `url('${icon}')`,
        }"
      ></div>
    </div>
    <div class="box-container">
      <div class="box-title">
        <div>{{ title }}</div>
      </div>
      <div class="content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
  },
  icon: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block-box {
  position: relative;
  .box-icon {
    position: absolute;
    right: 0;
    top: px2rem(8);
    background-image: url("@/assets/images/promoter-star/block-box-right-top.png");
    background-position: right top;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: px2rem(115);
    height: px2rem(59);
    z-index: 1;

    .top-icon {
      position: absolute;
      top: px2rem(-16);
      right: px2rem(14);
      width: px2rem(50);
      height: px2rem(56);
      background-size: 100% 100%;
    }
  }
  .box-container {
    position: relative;
    width: 100%;
    height: fit-content;
    padding: px2rem(12);
    padding-top: px2rem(20);
    z-index: 2;
    background-image: url("@/assets/images/promoter-star/block-box-bg.svg"),
      url("@/assets/images/promoter-star/block-box-bg.svg"),
      url("@/assets/images/promoter-star/block-box-bg.svg"),
      url("@/assets/images/promoter-star/block-box-bg.svg");
    background-position: left top, right top, left bottom, right bottom;
    background-repeat: no-repeat;
    background-size: px2rem(158) px2rem(120), px2rem(158) px2rem(120),
      px2rem(158) px2rem(120), px2rem(158) px2rem(120);
    &::before,
    &::after {
      content: "";
      background-color: white;
      position: absolute;
      left: 0;
      z-index: 2;
    }
    &::before {
      top: 0;
      left: px2rem(30);
      width: calc(100% - px2rem(150));
      height: 100%;
    }
    &::after {
      bottom: px2rem(20);
      width: 100%;
      height: calc(100% - px2rem(80));
    }

    .box-title {
      position: relative;
      background-image: url("@/assets/images/promoter-star/title-bg.svg");
      background-position: left center;
      background-repeat: no-repeat;
      background-size: px2rem(80) px2rem(16);
      color: #083c44;
      font-family: Gilroy;
      font-size: px2rem(24);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      line-height: px2rem(30);
      height: px2rem(30);
      z-index: 3;
      text-align: left;
    }

    .content {
      position: relative;
      padding-top: px2rem(16);
      z-index: 4;
    }
  }
}
.rtl-html {
  .block-box {
    transform: scaleX(-1);
    .content {
      transform: scaleX(-1);
    }
    .box-title {
      div {
        transform: scaleX(-1);
        display: inline-block;
      }
    }
  }
}
</style>
