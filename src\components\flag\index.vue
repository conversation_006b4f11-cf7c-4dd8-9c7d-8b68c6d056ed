<template>
  <div v-if="flag"
       class="flag"
       :class='`size${size}`'
  >
    <img class="flag-img"
         :src="flag"
         alt=""
    />
  </div>
</template>

<script setup>

const props = defineProps({
  flag: {
    type: String,
    default: '',
  },
  size: {
    type: Number,
    default: 12, // 12 16 20 24
  }
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.flag {
  display: inline-block;
  background-color: #0000000F;
  font-size: 0;

  .flag-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: $gap2;
  }

  &.size12 {
    width: px2rem(17);
    height: px2rem(12);
    padding: px2rem(1);
    border-radius: px2rem(3);
  }

  &.size16 {
    width: px2rem(22);
    height: px2rem(16);
    padding: px2rem(2);
    border-radius: $gap4;
  }

  &.size20 {
    width: px2rem(28);
    height: px2rem(20);
    padding: px2rem(2);
    border-radius: $gap4;
  }

  &.size24 {
    width: px2rem(34);
    height: px2rem(24);
    padding: px2rem(2);
    border-radius: $gap4;
  }
}

</style>
