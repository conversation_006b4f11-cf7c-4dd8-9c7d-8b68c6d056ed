<template>
  <div class="app-container flex">
    <header>
      <header-bar />
    </header>
    <main>
      <van-list
        v-model:loading="isLoading"
        :finished="finished"
        finished-text=""
        class="list-wrap"
        loading-text="loading..."
        @load="fetchList"
      >
        <template v-if="list.length > 0">
          <div class="list-item flex" v-for="item in list" :key="item.id">
            <div class="item-left">
              <div class="record-title">
                {{ item.change_desc }}
                <span
                  class="highlight-text"
                  @click="jumpPage(item)"
                  v-if="[42, 27].includes(item.change_type)"
                  >{{ " @{" + item.recharge_user_info.nickname + "}" }}</span
                >
              </div>
              <div class="record-time">{{ item.create_time }}</div>
            </div>
            <div
              class="record-num"
              :style="item.is_coin_add ? {} : { color: '#999999' }"
            >
              {{ item.is_coin_add ? "+" : "-" }}
              {{ Object.values(numberWithUnit(item.change_coin, 2)).join("") }}
            </div>
          </div>
        </template>
        <Empty
          v-else
          class="empty"
          :tips="$t('common.common_no_record')"
        ></Empty>
      </van-list>
    </main>
  </div>
</template>

<script setup>
import { onMounted, ref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import { numberWithUnit } from "@/utils/util";
import dealerApi from "@/api/dealer.js";

const { proxy } = getCurrentInstance();
const page = ref(1);
const size = ref(10);
const list = ref([]);
const isLoading = ref(false);
const finished = ref(false);

const fetchList = async () => {
  isLoading.value = true;
  const resResult = await dealerApi.coin_Detail({
    page: page.value,
    size: size.value,
  });
  if (resResult.code === 200) {
    list.value = [...list.value, ...(resResult.data.list || [])];
    page.value++;
    if (resResult.data.list?.length < size.value) finished.value = true;
  }
  isLoading.value = false;
};

const jumpPage = (row) => {
  proxy.$siyaApp("goUserDetail", {
    userId: row.recharge_user_info.user_id,
  });
};

onMounted(() => {
  // fetchList();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  height: 100vh;
  padding: 0 px2rem(16);
  main {
    width: 100%;
    overflow: auto;
    flex: 1;
    padding-bottom: px2rem(16);
    .list-wrap {
      border-radius: $radius16;
      border-radius: px2rem(16);
      background-color: $white;
      .list-item {
        padding: px2rem(19) px2rem(14);
        border-bottom: 0.5px solid #f2f2f2;
        justify-content: space-between;
        .item-left {
          .record-title {
            font-size: $fontSize15;
            color: $fontColorB1;
            font-weight: 700;
            margin-bottom: $gap8;
            .highlight-text {
              color: #5c55d5;
            }
          }
          .record-time {
            color: $fontColorB3;
            font-size: $fontSize13;
          }
        }
        .record-num {
          font-size: $fontSize17;
          font-weight: 700;
          color: #5f56ed;
        }
        &:nth-last-child(2) {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
