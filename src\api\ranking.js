import request from '@/utils/request'

export default {
  // 幸运礼物
  real_rank(query) {
    return request({
      url: '/gift/lucky_gift/real_rank',
      method: 'get',
      params: query
    })
  },
  jackpot(query) {
    return request({
      url: '/gift/lucky_gift/jackpot',
      method: 'get',
      params: query
    }) 
  },
  rank(query) {
    return request({
      url: '/gift/lucky_gift/rank',
      method: 'get',
      params: query
    })
  },
  rank_reward(query) {
    return request({
      url: '/gift/lucky_gift/rank_reward',
      method: 'get',
      params: query
    })
  },
  // 幸运礼物

  // 周星活动
  week_star_time(query) {
    return request({
      url: '/client_web/week_star/week_time',
      method: 'get',
      params: query
    })
  },
  week_star_rank(query) {
    return request({
      url: '/client_web/week_star/rank',
      method: 'get',
      params: query
    })
  },
  week_star_reward(query) {
    return request({
      url: '/client_web/week_star/reward',
      method: 'get',
      params: query
    })
  },
  week_star_history(query) {
    return request({
      url: '/client_web/week_star/history',
      method: 'get',
      params: query
    })
  },
  week_star_rule(query) {
    return request({
      url: '/client_web/week_star/rule',
      method: 'get',
      params: query
    })
  },
}
