import request from "@/utils/request";

export default {
  /* ===== 转盘活动 ===== */
  wheel_lottery_info() {
    return request({
      url: "/activity/turntable/info",
      method: "get",
    });
  },
  wheel_lottery_record() {
    return request({
      url: "/activity/turntable/draw/record",
      method: "get",
    });
  },
  wheel_lottery_prize(data) {
    return request({
      url: "/activity/turntable/task/prize",
      method: "post",
      data,
    });
  },
  wheel_lottery_draw() {
    return request({
      url: "/activity/turntable/draw",
      method: "post",
      meta: {
        customErrorToast: true,
      },
    });
  },
  /* ===== 五月23号星推官活动 ===== */
  star_info(query) {
    return request({
      url: "/activity/star/info",
      method: "get",
      params: query,
    });
  },
  star_register() {
    return request({
      url: "/activity/star/register",
      method: "post",
    });
  },
  star_submit(data) {
    return request({
      url: "/activity/star/submit",
      method: "post",
      data,
    });
  },
  star_ranking(data) {
    return request({
      url: "/activity/star/top",
      method: "get",
      data,
    });
  },
  /* ===== 大R周累充活动 ===== */
  week_recharge() {
    return request({
      url: "/activity/r_recharge/week_recharge",
      method: "get",
    });
  },
  month_recharge() {
    return request({
      url: "/activity/r_recharge/month_recharge",
      method: "get",
    });
  },
  /* ===== Siya歌手大赛 ===== */
  singer_rewards() {
    return request({
      url: "/activity/singer/rewards",
      method: "get",
    });
  },
  singer_info() {
    return request({
      url: "/activity/singer/info",
      method: "get",
    });
  },
  singer_register(data) {
    return request({
      url: "/activity/singer/register",
      method: "post",
      data,
    });
  },
  singer_register_record() {
    return request({
      url: "/activity/singer/register/record",
      method: "get",
    });
  },
  singer_friends(params) {
    return request({
      url: "/activity/singer/friends",
      method: "get",
      params,
    });
  },
  singer_poll(data) {
    return request({
      url: "/activity/singer/poll",
      method: "post",
      data,
    });
  },
  singer_poll_rank(params) {
    return request({
      url: "/activity/singer/poll/rank",
      method: "get",
      params,
    });
  },
  singer_stage_rank(params) {
    return request({
      url: "/activity/singer/stage/rank",
      method: "get",
      params,
    });
  },
  singer_remind(data) {
    return request({
      url: "/activity/singer/remind",
      method: "post",
      data,
    });
  },
  singer_search(params) {
    return request({
      url: "/activity/singer/search",
      method: "get",
      params,
    });
  },
  /* ===== 埃及国庆活动 ===== */
  egypt_info(params) {
    return request({
      url: "/activity/egypt/info",
      method: "get",
      params,
    });
  },
  egypt_rank(params) {
    return request({
      url: "/activity/egypt/rank",
      method: "get",
      params,
    });
  },
  egypt_room_rank(params) {
    return request({
      url: "/activity/egypt/room/rank",
      method: "get",
      params,
    });
  },
  egypt_rewards(params) {
    return request({
      url: "/activity/egypt/rewards",
      method: "get",
      params,
    });
  },
  egypt_task_receive(data) {
    return request({
      url: "/activity/egypt/task/receive",
      method: "post",
      data,
    });
  },
  /* ===== 房间PK活动 ===== */
  pk_info(params) {
    return request({
      url: "/activity/pk/info",
      method: "get",
      params,
    });
  },
  pk_rank(params) {
    return request({
      url: "/activity/pk/rank",
      method: "get",
      params,
    });
  },
  pk_room_rank(params) {
    return request({
      url: "/activity/pk/room/rank",
      method: "get",
      params,
    });
  },
  pk_rewards(params) {
    return request({
      url: "/activity/pk/rewards",
      method: "get",
      params,
    });
  },
  pk_task_receive(data) {
    return request({
      url: "/activity/pk/task/receive",
      method: "post",
      data,
    });
  },
  // 派对活动
  party_info(query) {
    return request({
      url: "/client_web/party/tip",
      method: "get",
      params: query,
    });
  }
};
