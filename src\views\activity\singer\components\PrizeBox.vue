<template>
  <div class="reward-item">
    <img :src="img" />
    <div class="reward-cover"></div>
    <div class="tag">{{ tag }}</div>
  </div>
</template>
<script setup>
const props = defineProps({
  img: {
    type: String,
  },
  tag: {
    type: String,
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.reward-item {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: px2rem(6);
  overflow: hidden;
  padding: px2rem(4);
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 2;
    position: relative;
  }
  .reward-cover {
    position: absolute;
    inset: 0;
    background-image: url("@/assets/images/activity/singer/prize-bg.png"); /* 中间部分的图片 */
    background-size: 100% 100%;
  }
  .tag {
    position: absolute;
    z-index: 3;
    top: 0;
    right: 0;
    min-width: px2rem(32);
    padding: px2rem(1) px2rem(4);
    border-radius: 0px px2rem(8);
    background: linear-gradient(90deg, #ffea64 0%, #ffd395 100%),
      linear-gradient(90deg, #ffca64 0%, #ff976b 100%);

    overflow: hidden;
    color: #9b401f;
    text-align: center;
    text-overflow: ellipsis;
    font-family: Gilroy;
    font-size: px2rem(8);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 8px */
  }
}
</style>
