@import "colors";
@import "dimens";

:root:root {
  --van-primary-color: #{$mainColor};

  // 字体颜色
  --van-text-color: #{$fontColorB0};

  //  按钮
  --van-button-default-height: #{px2rem(50)};
  --van-button-radius: #{$radius16};
  --van-button-default-color: $white;
  --van-button-default-font-size: #{px2rem(17)};
  --van-button-normal-font-size: #{px2rem(17)};

  //  输入框
  --van-field-input-text-color: #{$fontColorB0};
  --van-field-placeholder-text-color: #{$fontColorB4};
  --van-field-word-limit-color: #{$fontColorB4};

  //  Tab
  --van-tabs-line-height: #{px2rem(40)};
  --van-tab-text-color: #{$fontColorB3};
  --van-tab-font-size: #{px2rem(15)};
  
  // search
  --van-search-padding: #{px2rem(8)} #{px2rem(16)} #{px2rem(8)} #{px2rem(16)};
}
