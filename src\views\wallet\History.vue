<template>
  <div class="app-container">
    <van-tabs
      :modal:active="activeTabIdx"
      line-width="83"
      border
      sticky
      :offset-top="-1"
      @click-tab="handleChangeTab"
    >
      <van-tab
        v-for="(tab, idx) in tabList"
        :key="idx"
        :title="tab"
        :disabled="isLoading"
      ></van-tab>

      <van-list
        v-if="!isEmpty"
        class="list-wrap"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text=""
        loading-text="loading..."
        :immediate-check="false"
        @load="getList"
      >
        <div class="cell flex" v-for="(cell, idx) in resultList" :key="idx">
          <div class="cell-left">
            <div class="title">
              {{ cell.name }}
              <span
                class="highlight-text"
                @click="jumpPage(cell)"
                v-if="[80].includes(cell.change_type)"
                >{{ " @{" + cell.nickname + "}" }}
              </span>
            </div>
            <div class="time">{{ formatDate(cell.create_time) }}</div>
          </div>
          <div class="cell-right flex">
            <img
              class="icon-coin"
              src="@/assets/images/common/coin.svg"
              alt=""
            />
            <gap :gap="2" />
            <span class="amount active" v-if="activeTabIdx === 0">
              +{{ cell.value }}
            </span>
            <span class="amount" v-else>
              {{ cell.value }}
            </span>
          </div>
        </div>
      </van-list>
      <Empty v-else :tips="$t('common.common_content_yet')"></Empty>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from "vue";
import walletApi from "@/api/wallet.js";
import { formatDate } from "@/utils/util.js";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();

const tabList = [
  t("base_total.wallet_history_title_1"),
  t("base_total.wallet_history_title_2"),
];
const activeTabIdx = ref(0);
const handleChangeTab = ({ name }) => {
  if (name === activeTabIdx.value) return false;
  activeTabIdx.value = name;
  resultList.value = [];
  finished.value = false;
  pos.value = 0;
  getList();
};

const resultList = ref([]);
const pos = ref(0);
const pageSize = 20;
const isLoading = ref(false);
const finished = ref(false);
const isEmpty = ref(false);
const getList = async () => {
  isLoading.value = true;
  const response = await walletApi.coin_record({
    pos: pos.value,
    type: activeTabIdx.value + 1, // 1增加 2减少
    size: pageSize,
  });
  if (response.code === 200) {
    const list = response.data.items || [];
    resultList.value = resultList.value.concat(list);

    finished.value = !response.data.has_next;
    isEmpty.value = resultList.value.length === 0;
    pos.value = response.data.next_pos;

    // 加载状态结束
    isLoading.value = false;
  }
};

const jumpPage = (row) => {
  proxy.$siyaApp("goUserDetail", {
    userId: row.user_id,
  });
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.list-wrap {
  padding: $gap16 $gap16 px2rem(80);
}

.cell {
  justify-content: space-between;
  // height: px2rem(80);
  padding: px2rem(14);
  margin-bottom: $gap16;
  background-color: $white;
  border-radius: $radius16;
  .cell-left {
    flex: 1;
    // background-color: red;
  }
  .cell-right {
    // max-width: px2rem(100);
    // word-break: break-all;
  }

  .title {
    // width: px2rem(220);
    // max-width: calc(100% - px2rem(120));
    margin-bottom: $gap8;
    font-size: px2rem(15);
    font-weight: $fontWeightBold;
    color: $fontColorB1;
    .highlight-text {
      color: #5c55d5;
    }
  }

  .time {
    font-size: px2rem(13);
    color: $fontColorB3;
  }

  .icon-coin {
    width: px2rem(20);
    height: px2rem(20);
  }

  .cell-right-gap {
    width: px2rem(2);
  }

  .amount {
    font-size: px2rem(20);
    line-height: px2rem(24);
    font-weight: $fontWeightBold;
    color: $fontColorB3;

    &.active {
      color: $mainColor;
    }
  }
}
</style>
