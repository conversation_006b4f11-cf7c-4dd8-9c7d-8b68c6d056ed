const setRegExp = (v) => new RegExp(v);

export const validatorMap = {
  // 以 {startVal} 开头，【加】长度为 {length} 的数字
  startsWithAndNum(val, startVal, length) {
    return setRegExp(`^${startVal}\\d{${length}}$`).test(val);
  },

  // 以 {startVal} 开头，【加】最少 {min} 最多 {max} 的数字
  startsWithAndMinAndMax(val, startVal, min, max) {
    return setRegExp(`^${startVal}\\d{${min},${max}}$`).test(val);
  },

  // 以 {startVal} 开头，【加】长度为 {length} 的数字和字母
  startsWithAndNumOrLetter(val, startVal, length) {
    return setRegExp(`^${startVal}[a-zA-Z0-9]{${length}}$`).test(val);
  },

  // 以 {startVal} 开头，【总】长度为 {length} 的数字
  startsWithAndTotalNum(val, startVal, sum) {
    if (String(val).length !== sum) return false;
    return setRegExp(`^${startVal}\\d+$`).test(val);
  },

  // 以 {startVal} 开头，【总】长度为 {length} 的数字和字母
  startsWithAndTotalNumOrLetter(val, startVal, sum) {
    if (String(val).length !== sum) return false;
    return setRegExp(`^${startVal}[a-zA-Z0-9]+$`).test(val);
  },

  // 以 {startVal} 开头，【总】长度小于等于 {max} 的数字和字母
  startsWithAndMaxAndNumOrLetter(val, startVal, max) {
    if (String(val).length > max) return false;
    return setRegExp(`^${startVal}[a-zA-Z0-9]+$`).test(val);
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 {chars} 指定字符
  minAndMaxAndLetterOrChars(val, min, max, chars) {
    return setRegExp(`^[a-zA-Z${chars}]{${min},${max}}$`).test(val);
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字 和 {chars} 指定字符
  minAndMaxAndLetterAndNumOrChars(val, min, max, chars) {
    return setRegExp(`^[a-zA-Z0-9${chars}]{${min},${max}}$`).test(val);
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 空格
  minAndMaxAndLetterOrChars1(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "\\s");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 空格、中划线（-）、点（.)、逗号(,)
  minAndMaxAndLetterOrChars2(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "\\s\\.,-");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字 和 空格、点(.)、逗号(,)、单引号(')、斜杠(/)、中划线(-）、问号(?)、冒号(:)、左右括号（）、加号（+）
  minAndMaxAndNumLetterOrChars1(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "\\s0-9\\.,'/?:()+-");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字 和 空格 、点（.)
  minAndMaxAndNumLetterOrChars2(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "\\s0-9\\.");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字
  minAndMaxAndNumLetterOrChars3(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "0-9");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字 和 空格、中划线（-）、点（.)
  minAndMaxAndNumLetterOrChars4(val, min, max) {
    return this.minAndMaxAndLetterOrChars(val, min, max, "0-9\\s\\.-");
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 数字 和 空格、逗号(,)、点(.)、下划线(_)和阿拉伯语
  minAndMaxAndNumLetterOrChars5(val, min, max) {
    return this.minAndMaxAndLetterOrChars(
      val,
      min,
      max,
      "0-9\\s,\\._\\u0621-\\u064Aa\\u0660-\\u0669"
    );
  },

  // 最少 {min} 最多 {max} 支持英文字符 和 空格、逗号(,)、点(.)、下划线(_)和阿拉伯语
  minAndMaxAndLetterOrChars5(val, min, max) {
    return this.minAndMaxAndLetterOrChars(
      val,
      min,
      max,
      "\\s,\\._\\u0621-\\u064Aa\\u0660-\\u0669"
    );
  },

  // 长度 最少 {min} 的数字
  minNum(val, min) {
    return setRegExp(`^\\d{${min},}$`).test(val);
  },

  // 长度 最少 {min} 最多 {max} 的数字
  minAndMaxNum(val, min, max) {
    return setRegExp(`^\\d{${min},${max}}$`).test(val);
  },

  // 长度 最少 {min} 最多 {max} 的任意字符
  minAndMaxValue(val, min, max) {
    return setRegExp(`^.{${min},${max}}$`).test(val);
  },

  // 长度 最少 {min} 最多 {max} 的任意字符 不支持阿拉伯语
  minAndMaxValue2(val, min, max) {
    return setRegExp(
      `^[^\\u0621-\\u064Aa\\u0660-\\u0669]{${min},${max}}$`
    ).test(val);
  },

  // SWIFT 银行编码 8或11位，允许数字和大写字母
  SWIFTBankCode(val) {
    return (
      setRegExp(`^[A-Z0-9]{8}$`).test(val) ||
      setRegExp(`^[A-Z0-9]{11}$`).test(val)
    );
  },

  // +国家编码-手机号
  prefixPhoneNumber(val) {
    return setRegExp(`^\\+\\d{1,5}-\\d{1,20}$`).test(val);
  },

  // +国家编码和手机号
  prefixPhoneNumber2(val, max) {
    return setRegExp(`^\\+\\d{1,5}\\d{1,${max}}$`).test(val);
  },

  // 邮箱验证
  email(val) {
    return setRegExp(
      "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$"
    ).test(val);
  },

  // CPF/CNPJ（个人/企业税号）
  CPFOrCNPJCode(val) {
    // return setRegExp(`^\\d{3}.\\d{3}.\\d{3}-\\d{2}$`).test(val)
    //   || setRegExp(`^\\d{2}.\\d{3}.\\d{3}/\\d{4}-\\d{2}$`).test(val)
    // 暂时不限制格式
    return this.minAndMaxValue2(val, 1, 100);
  },

  // 银行网点号
  bankBranchCode(val) {
    return this.minAndMaxNum(val, 4, 6);
  },

  // JO开头的30位数字字母的组合（具体规则：JO +2位数字校验位+4位大写字母银行识别码+4位数字 +18位任意字符）
  BankAccount_JO(val) {
    return setRegExp(`^JO\\d{2}[A-Z]{4}\\d{4}[a-zA-Z0-9]{18}$`).test(val);
  },
};
