import ta from "@/utils/thinkingdata";

export default class Click {
  add(entry) {
    const traceVal = entry.el.attributes["track-params"]?.value;
    const traceKey = entry.el.attributes["trace-key"]?.value;
    //  debugger
    entry.el.addEventListener("click", function () {
      console.log(
        "上报点击埋点===>",
        `事件名:${traceKey}`,
        `属性：${traceVal || {}}`
      );
      ta.track(
        traceKey, //事件名称
        traceVal ? JSON.parse(traceVal) : {} //事件属性
      );
    });
  }
}
