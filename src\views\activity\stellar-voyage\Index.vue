<template>
  <div class="container">
    <right-button
      :text="$t('pk_king.rules')"
      @click="toRulePage()"
      v-track:click
      trace-key="pk_activity_button_click"
      :track-params="JSON.stringify({ pk_activity_button_name: '1' })"
    ></right-button>
    <div
      class="bg-box"
      v-track:exposure
      trace-key="pk_activity_expo"
      :track-params="JSON.stringify({ pk_activity_expo_source: from })"
    >
      <header-bar
        back-icon-show
        :padding="false"
        :content-padding="false"
        :show-title="false"
        close-type="close"
      />

      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>

      <div class="activity-time">
        <div class="inner">
          <div class="content">{{ activityInfo?.week_date_time || "-" }}</div>
        </div>
      </div>
    </div>

    <div class="tabs">
      <div
        class="tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("pk_king.task") }}</span>
      </div>
      <gap :gap="2"></gap>
      <div
        class="tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
        v-track:click
        trace-key="pk_activity_button_click"
        :track-params="JSON.stringify({ pk_activity_button_name: '4' })"
      >
        <span>{{ $t("pk_king.user_ranking") }}</span>
      </div>
      <gap :gap="2"></gap>
      <div
        class="tab"
        :class="current === 2 ? 'active' : ''"
        @click="current = 2"
        v-track:click
        trace-key="pk_activity_button_click"
        :track-params="JSON.stringify({ pk_activity_button_name: '5' })"
      >
        <span>{{ $t("pk_king.room_ranking") }}</span>
      </div>
    </div>

    <TasksBlock
      v-show="current === 0"
      :activityInfo="activityInfo"
      :rewordInfo="rewordInfo"
      @refresh="fetchActivityInfo()"
    ></TasksBlock>
    <UserRankingBlock
      v-show="current === 1"
      :activityInfo="activityInfo"
      :rewordInfo="rewordInfo"
    ></UserRankingBlock>
    <RoomRankingBlock
      v-show="current === 2"
      :activityInfo="activityInfo"
      :rewordInfo="rewordInfo"
    ></RoomRankingBlock>
  </div>
  <RewordDialog ref="rewordDialogRef"></RewordDialog>
</template>
<script setup>
import { ref } from "vue";
import { getLang } from "@/i18n/index.js";
import RightButton from "./components/RightButton.vue";
import TasksBlock from "./components/TasksBlock.vue";
import UserRankingBlock from "./components/UserRankingBlock.vue";
import RoomRankingBlock from "./components/RoomRankingBlock.vue";
import RewordDialog from "./components/RewordDialog.vue";
import { useRouter, useRoute } from "vue-router";
import activityApi from "@/api/activity.js";

const router = useRouter();
const route = useRoute();
const { from } = route.query;
const rewordDialogRef = ref();
const current = ref(0);
const lang = getLang();
const activityInfo = ref();
const rewordInfo = ref();

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/pk-king/${lang}/${name}`,
    import.meta.url
  ).href;
}
const toRulePage = () => {
  router.push({
    name: "PKKingRule",
  });
};

const requestTime = ref(0);
const fetchActivityInfo = async () => {
  try {
    requestTime.value = 0;
    const now = Date.now();
    const res = await activityApi.pk_info();
    requestTime.value = Math.floor((Date.now() - now) / 1000);
    if (res.code === 200) {
      activityInfo.value = {
        ...res.data,
        requestTime: requestTime.value,
      };
    }
  } catch (e) {
    console.error(e);
  }
};

const fetchRewardsInfo = async () => {
  try {
    const res = await activityApi.pk_rewards();
    if (res.code === 200) {
      rewordInfo.value = res.data;
    }
  } catch (e) {
    console.error(e);
  }
};

fetchActivityInfo();
fetchRewardsInfo();
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  min-height: 100vh;
  width: 100%;
  background-color: #24145f;
  overflow: hidden;
  background-image: url("@/assets/images/activity/pk-king/bg4.png");
  background-repeat: no-repeat;
  background-size: 100% px2rem(587);
  background-position: 0 px2rem(400);
  .bg-box {
    background-image: url("@/assets/images/activity/pk-king/bg1.png");
    background-repeat: no-repeat;
    background-size: 100% px2rem(431);
    width: 100%;
    height: px2rem(431);
    overflow: hidden;
    .banner-title {
      width: px2rem(221);
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(125);
      img {
        opacity: 0;
        width: px2rem(221);
        vertical-align: bottom;
      }
    }
    .activity-time {
      margin: 0 auto;
      margin-top: px2rem(78);
      width: px2rem(281);
      height: px2rem(61);
      display: flex;
      align-items: center;
      position: relative;
      &::before,
      &::after {
        position: absolute;
        content: "";
        background-image: url("@/assets/images/activity/pk-king/title-bg.png");
        background-repeat: no-repeat;
        background-size: cover;
        display: block;
        width: px2rem(11);
        height: px2rem(61);
      }
      &::before {
        left: 0;
      }
      &::after {
        right: 0;
      }
      .inner {
        padding: px2rem(1);
        background: linear-gradient(
          92deg,
          #ffb41a 1.82%,
          #fde3aa 25.31%,
          #ffb41a 51.6%,
          #fde3aa 74.63%,
          #ffb41a 99.68%
        );
        .content {
          padding: 0 px2rem(12);
          height: px2rem(33);
          background: linear-gradient(
            92deg,
            #f435cb 1.82%,
            #8b0ca6 25.31%,
            #f736cc 51.6%,
            #8e0ea7 74.63%,
            #f636cc 99.68%
          );
          color: #ffd32f;
          text-align: center;
          text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 700;
          line-height: px2rem(13); /* 92.857% */
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
        }
      }
    }
  }

  .tabs {
    width: 100%;
    padding: 0 px2rem(4);
    display: flex;
    position: relative;
    z-index: 1;
    .tab {
      width: px2rem(127);
      height: px2rem(45);
      padding: 0 px2rem(10);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/pk-king/button11.png");
      background-repeat: no-repeat;
      background-size: cover;
      color: #deb8ff;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }

    .tab.active {
      background-image: url("@/assets/images/activity/pk-king/button1.png");
      span {
        background: linear-gradient(180deg, #fff 28.26%, #ffae00 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}
</style>
