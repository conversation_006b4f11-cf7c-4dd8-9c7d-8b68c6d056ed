import request from '@/utils/request'

export default {
  coin_record(query) {
    return request({
      url: '/wallet/coin_record',
      method: 'get',
      params: query
    })
  },
  balance(query) {
    return request({
      url: '/wallet/balance',
      method: 'get',
      params: query
    })
  },

  withdraw_list(query) {
    return request({
      url: '/wallet/withdraw_list',
      method: 'get',
      params: query
    })
  },
  withdraw(query) {
    return request({
      url: '/wallet/withdraw',
      method: 'post',
      data: query,
      meta: {
        customErrorToast: true,
      }
    })
  },

  exchange_coin_list(query) {
    return request({
      url: '/wallet/exchange_coin_list',
      method: 'get',
      params: query
    })
  },
  exchange_coin(query) {
    return request({
      url: '/wallet/exchange_coin',
      method: 'post',
      data: query,
      meta: {
        customErrorToast: true,
      }
    })
  },
  diamond_record(query) {
    return request({
      url: '/wallet/diamond_record',
      method: 'get',
      params: query
    })
  },
}
