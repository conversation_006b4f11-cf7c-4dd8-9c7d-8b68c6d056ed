<template>
  <div class="app-container flex task-container">
    <header-bar
      :statusBarPadding="0.01"
      fontColor="#fff"
      is-scroll-change-bg
      @emit:scrollChangeBgShow="scrollChangeBgShow"
      observer-root-margin="1px"
      fixed-bg-color="#ffffff10"
      :opacity-rate="100"
      closeType="close"
    >
      <template #right>
        <img
          class="close"
          @click="close"
          src="@/assets/images/game/close.png"
          alt=""
      /></template>
      <div class="head-wrap flex">
        <div class="top">
          <img src="@/assets/images/game/text-icon.png" alt="" />
          {{ $t("game.game_task_title") }}
        </div>
        <div class="count-time">{{ showTime }}</div>
      </div>
    </header-bar>
    <main>
      <div class="task-wrap" v-for="(item, idx) in list" :key="idx">
        <div class="task-title">
          <div class="dot"></div>
          <gap :gap="8"></gap>
          <span>{{ item.task_name }}</span>
        </div>
        <div class="progress-container flex">
          <div class="progress-wrap">
            <div
              class="current-progress flex"
              :style="{ width: `${item.process}%` }"
            >
              <div class="recent-color"></div>
            </div>
          </div>
          <gap :gap="10"></gap>
          <div class="progress-num">{{ item.current }}/{{ item.target }}</div>
        </div>
        <div class="content flex">
          <div class="card">
            <div class="card-bg">
              <div
                class="card-top"
                v-for="(reward, rewardIdx) in item.rewards"
                :rewardIdx
              >
                <img :src="reward.icon" alt="" />
                <gap :gap="4"></gap>
                <span>{{ rewardText(reward) }}</span>
              </div>
              <div class="card-bottom flex">
                <div class="avatar-list flex">
                  <div class="avatar-wrap" v-for="i in 3" :key="i">
                    <img
                      :src="item?.avatar_list?.[i - 1]"
                      alt=""
                      v-if="item?.avatar_list?.[i - 1]"
                      class="avatar"
                    />
                    <img
                      src="@/assets/images/game/placeholder.png"
                      alt=""
                      v-else
                      class="avatar"
                    />
                  </div>
                </div>
                <div class="desc">
                  {{ $t("game.game_task_today_complete", [item.total]) }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="btn"
            :class="{ 'disabled-btn': item.task_status === 1 }"
            @click="close(item.task_status === 2)"
          >
            {{
              item.task_status === 1
                ? $t("game.game_task_completed")
                : $t("game.game_task_go_complete")
            }}
          </div>
        </div>
        <div class="bottom-border" v-if="list.length !== idx + 1"></div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onUnmounted, onMounted } from "vue";
import { i18n } from "@/i18n/index.js";
import gameTaskApi from "@/api/game.js";
import dayjs from "dayjs";
import duration from "dayjs/plugin/duration";

dayjs.extend(duration);
const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;
const list = ref([]);

let timer = null;
let diffTime = 0;
const showTime = ref("0:0:0");

const clear = () => {
  clearTimeout(timer);
  timer = null;
};

const initTask = async () => {
  const result = await gameTaskApi.getGameTask();
  const { end_time_left, task_infos } = result.data;
  diffTime = end_time_left;
  list.value = task_infos;
  startCountdown();
};

const startCountdown = () => {
  if (diffTime <= 0) {
    clear();
    initTask();
    return;
  }
  const durationObj = dayjs.duration(diffTime * 1000);
  const hours = durationObj.hours().toString().padStart(2, "0");
  const minutes = durationObj.minutes().toString().padStart(2, "0");
  const seconds = durationObj.seconds().toString().padStart(2, "0");

  showTime.value = `${hours}:${minutes}:${seconds}`;
  timer = setTimeout(() => {
    diffTime = diffTime - 1;
    startCountdown();
  }, 1000);
};

const scrollChangeBgShow = (show) => {
  const targetBgEl = document.querySelector(".task-container .header-bar-bg");
  targetBgEl.style["backdrop-filter"] = "blur(10px)";
  targetBgEl.style["-webkit-backdrop-filter"] = "blur(10px)";
};

const rewardText = (row) => {
  const { rewardType, day, num, name } = row;
  switch (rewardType) {
    case 1:
      // 金币
      return `${name}*${num}`;
    case 2:
      // 钻石
      return t("game.game_task_reward_diamond", [num]);
    case 3:
      // 头像框
      return t("game.game_task_reward_avatar", [num]);
    case 5:
      // 礼物
      return `${name}*${num}`;
    case 1004:
      // 座驾
      return t("game.game_task_reward_entrance", [num]);
    case 1008:
      // 私聊卡
      return t("game.game_task_reward_pm_card", [num]);
    case 1009:
      // 通话卡
      return t("game.game_task_reward_call_card", [num]);
    default:
      return "";
  }
};

const close = (isClose = true) => {
  if (isClose) proxy.$siyaApp("closeWebview");
};

onUnmounted(() => {
  clear();
});

onMounted(() => {
  initTask();
  scrollChangeBgShow();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: url("@/assets/images/game/task-bg.jpg") top left no-repeat;
  background-size: 100%;
  background-color: #a8a4df;
  display: flex;
  flex-direction: column;
  .close {
    width: px2rem(24);
    height: px2rem(24);
  }
  .head-wrap {
    height: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .top {
      color: $white;
      font-size: $fontSize17;
      font-weight: $fontWeightBold;
      @include centerL();
      img {
        width: px2rem(24);
        height: px2rem(24);
      }
    }
    .count-time {
      background: #4b448b;
      height: px2rem(16);
      font-size: px2rem(10);
      text-align: center;
      border-radius: px2rem(16);
      color: #ffffffb2;
      padding: px2rem(2.5) px2rem(12);
    }
  }
  main {
    width: 100%;
    color: $white;
    flex: 1;
    .task-wrap {
      padding: px2rem(12) px2rem(12) 0 px2rem(12);
      .task-title {
        font-size: $fontSize15;
        font-weight: $fontWeightBold;
        padding: px2rem(8) 0;
        @include centerL();
        .dot {
          width: px2rem(6);
          height: px2rem(6);
          border-radius: 50%;
          background-color: $white;
        }
      }
      .progress-container {
        font-size: px2rem(10);
        .progress-wrap {
          width: px2rem(180);
          height: px2rem(8);
          border-radius: px2rem(8);
          background: #ffffff4d;
          overflow: hidden;
          .current-progress {
            background: #a2d0ff;
            height: 100%;
            display: flex;
            justify-content: flex-end;
            border-radius: px2rem(8);
            .recent-color {
              border-radius: px2rem(8);
              height: 100%;
              flex: 1;
              max-width: px2rem(40);
              background: v-bind(
                "lang==='ar'?'linear-gradient(90deg,#ffffff 0%,#a2d0ff  100%)':'linear-gradient(90deg, #a2d0ff 0%, #ffffff 100%)'"
              );
            }
          }
        }
      }

      .content {
        font-size: $fontSize13;
        margin: px2rem(12) 0;
        align-items: flex-start;
        .card {
          flex: 1;
          background: v-bind(
            "lang==='ar'?'linear-gradient( 90deg,rgba(38, 32, 144, 0) 0%, #ffffff30 80% )':'linear-gradient( 90deg, #ffffff30 0%, rgba(38, 32, 144, 0) 80% )'"
          );
          // background: red;
          border-radius: $radius12;
          padding: px2rem(1);
          position: relative;
          overflow: hidden;
          .card-bg {
            padding: px2rem(8) px2rem(10);
            border-radius: $radius12;
            height: 100%;
            width: 100%;
            background: v-bind(
              "lang==='ar'?'linear-gradient( 90deg,rgba(38, 32, 144, 0) 0%, rgba(120, 118, 159, 0.9) 100% )':'linear-gradient( 90deg, rgba(120, 118, 159, 0.9) 0%,  rgba(38, 32, 144, 0) 100% )'"
            );
            .card-top {
              @include centerL();
              img {
                width: px2rem(26);
                height: px2rem(26);
              }
            }
            .card-bottom {
              align-items: flex-start;
              .avatar-list {
                .avatar-wrap {
                  position: relative;
                  width: px2rem(12);
                  height: px2rem(12);
                  border-radius: 50%;
                  overflow: hidden;
                  border: 1px solid #e1dfff;
                  box-sizing: content-box;
                  .avatar {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                  }
                  &::before {
                    content: "";
                    position: absolute;
                    inset: 0;
                    z-index: 10;
                    border-radius: 50%;
                    backdrop-filter: blur(1px);
                    -webkit-backdrop-filter: blur(1px);
                  }
                  &:nth-child(2) {
                    transform: v-bind(
                      "lang==='ar'?'translateX(30%)':'translateX(-30%)'"
                    );
                    z-index: 101;
                  }
                  &:nth-child(3) {
                    transform: v-bind(
                      "lang==='ar'?'translateX(60%)':'translateX(-60%)'"
                    );
                    z-index: 102;
                  }
                }
              }
              .desc {
                color: #ffffff50;
                flex: 1;
                font-size: $fontSize10;
                margin: v-bind(
                  "lang==='ar'? `0 ${proxy.$pxToRemPx(-4)}px 0 0`:`0 0 0 ${proxy.$pxToRemPx(-4)}px`"
                );
              }
            }
          }
        }
        .btn {
          background: #5f56ed;
          min-width: px2rem(64);
          max-width: px2rem(80);
          min-height: px2rem(30);
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          border-radius: $radius10;
          padding: px2rem(2) px2rem(4);
          margin-top: px2rem(-6);
          word-break: keep-all;
          font-size: v-bind(
            "lang==='id'?`${proxy.$pxToRemPx(11)}px`:`${proxy.$pxToRemPx(13)}px`"
          );
        }
        .disabled-btn {
          opacity: 0.5;
        }
      }
      .bottom-border {
        width: 100%;
        height: 1px;
        background: #ffffff20;
      }
    }
  }
}

.app-container::v-deep(.icon-back) {
  display: none;
}
</style>
