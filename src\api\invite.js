import request from "@/utils/request";

export default {
  inviter_info() {
    return request({
      url: "/invite/inviter_info",
      method: "get",
    });
  },
  check_is_blacklisted() {
    return request({
      url: "/invite/is_blacklisted",
      method: "get",
    });
  },
  marquee(params) {
    return request({
      url: "/invite/marquee",
      method: "get",
      params,
    });
  },
  task_register_reward() {
    return request({
      url: "/invite/task_register_reward",
      method: "get",
    });
  },
  revenue_stats() {
    return request({
      url: "/invite/revenue_stats",
      method: "get",
    });
  },
  user_register_list(params) {
    return request({
      url: "/invite/user_register_list",
      method: "get",
      params,
    });
  },
  user_reward_list(params) {
    return request({
      url: "/invite/user_reward_list",
      method: "get",
      params,
    });
  },
  task_reward_desc() {
    return request({
      url: "/invite/task_reward_desc",
      method: "get",
    });
  },
  link_exchange_rate(params) {
    return request({
      url: "/invite/inviter_bonus_exchange_rate",
      method: "get",
      params,
    });
  },
  short_link_restore(params) {
    return request({
      url: "/invite/short_link_restore",
      method: "get",
      params,
    });
  },
};
