<template>
  <svg
    class="icon"
    :style="{ color }"
    aria-hidden="true"
    v-bind="$attrs"
  >
    <use :xlink:href="`#icon-${icon}`" />
  </svg>
</template>

<script setup>
/**
 * 已自动加载src/assets/icons下面所有svg文件
 * 将svg文件放到src/assets/icons即可使用该组件
 * 示例：<svg-icon icon="arrow-right"></svg-icon>
 */
const props = defineProps({
  /**
   * svg文件名，例如arrow-right，无需后缀
   */
  icon: {
    type: String,
    required: true,
  },
  /**
   * 图标颜色
   */
  color: {
    type: String,
    default: "currentColor",
  },
});
</script>

<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  fill: currentColor;
  overflow: hidden;
  path {
    fill: inherit;
    stroke: inherit;
  }
}
</style>
