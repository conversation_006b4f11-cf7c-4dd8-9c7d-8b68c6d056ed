<template>
  <div class="count-down">
    <div class="number">{{ d }}</div>
    <gap :gap="6"></gap>
    <div class="unit">D</div>
    <gap :gap="6"></gap>
    <div class="number">{{ h }}</div>
    <gap :gap="6"></gap>
    <div class="unit">H</div>
    <gap :gap="6"></gap>
    <div class="number">{{ m }}</div>
    <gap :gap="6"></gap>
    <div class="unit">M</div>
    <gap :gap="6"></gap>
    <div class="number">{{ s }}</div>
    <gap :gap="6"></gap>
    <div class="unit">S</div>
  </div>
</template>
<script setup>
import { computed, onMounted, onUnmounted, watch, ref, nextTick } from "vue";

const props = defineProps({
  leftTime: {
    type: Number,
    default: 0,
  },
});

const time = ref(props.leftTime);
const d = computed(() => {
  return Math.max(Math.floor(time.value / 60 / 60 / 24), 0);
});
const h = computed(() => {
  return Math.max(Math.floor((time.value / 60 / 60) % 24), 0);
});
const m = computed(() => {
  return Math.max(Math.floor((time.value / 60) % 60), 0);
});
const s = computed(() => {
  return Math.max(Math.floor(time.value % 60), 0);
});

const timer = ref(null);
const startTimer = (second) => {
  time.value = second;
  clearInterval(timer.value);
  timer.value = null;
  timer.value = setInterval(() => {
    if (time.value > 0) {
      time.value--;
    }
  }, 1000);
};

watch(() => props.leftTime, startTimer);

onMounted(() => {
  nextTick(() => {
    startTimer(props.leftTime);
  });
});

onUnmounted(() => {
  clearInterval(timer.value);
  timer.value = null;
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.count-down {
  display: flex;
  justify-content: center;
  align-items: center;
  .number {
    width: px2rem(28);
    height: px2rem(20);
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url("@/assets/images/activity/pk-king/step.png");
    background-size: 100% 100%;
    color: #fff;
    text-align: center;
    text-shadow: 0px 0px 6px #fff;
    font-family: "DomaineDisp";
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 700;
    line-height: 1;
  }
  .unit {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    /* T17/B */
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}
</style>
