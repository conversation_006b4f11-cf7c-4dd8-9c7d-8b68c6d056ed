const routes = [
  {
    path: "/income/list",
    name: 'IncomeList',
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "IncomeList"*/ '../views/income/Index.vue')
  },
  {
    path: "/income/list_f",
    name: 'IncomeList_F',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "IncomeList"*/ '../views/income/Fake.vue')
  },
  {
    path: "/income/record",
    name: 'IncomeRecord',
    component: () => import(/*webChunkName: "IncomeRecord"*/ '../views/income/Record.vue')
  },



  /* ===== 收款方式 ===== */
  {
    path: "/withdraw/list",
    name: 'WithdrawalList',
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "WithdrawalList"*/ '../views/income//payment/WithdrawalList.vue')
  },

  /* ===== 请填写你的收款信息 ===== */
  {
    path: "/withdraw/form",
    name: 'WithdrawalForm',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "WithdrawalForm"*/ '../views/income/payment/WithdrawalForm.vue')
  },

  /* ===== 提现信息 ===== */
  {
    path: "/withdraw/confirm",
    name: 'WithdrawalInformation',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "WithdrawalInformation"*/ '../views/income/payment/WithdrawalConfirm.vue')
  },

  /* ===== 提现记录 ===== */
  {
    path: "/withdraw/order/record",
    name: 'WithdrawalRecord',
    meta: {
      fullScreen: true,
      // keepAlive: true,
    },
    component: () => import(/*webChunkName: "WithdrawalRecord"*/ '../views/income/payment/OrderRecord.vue')
  },

  /* ===== 提现详情 ===== */
  {
    path: "/withdraw/order/detail",
    name: 'WithdrawalDetail',
    meta: {
      fullScreen: true,
    },
    component: () => import(/*webChunkName: "WithdrawalDetail"*/ '../views/income/payment/OrderDetail.vue')
  },
]

export default routes;
