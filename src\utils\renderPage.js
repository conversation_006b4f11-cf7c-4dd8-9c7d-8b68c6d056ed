import { i18n } from "../i18n";

const responseResultFnObj = {};
let app = null;
let appResultFn = null;

export function siyaApp(method, data = {}, sync = true) {
  return new Promise((resolve, reject) => {
    if (sync) {
      responseResultFnObj[method] = resolve;
      window.appCallBackResult = (res) => {
        try {
          let result = JSON.parse(res);
          let resultData;
          if (result.code === 1) {
            resultData = JSON.parse(
              decodeURIComponent(window.atob(result.data))
            );
          } else {
            resultData = {};
          }
          result = {
            ...result,
            data: resultData,
          };
          console.log("解析数据", result);
          appResultFn && appResultFn(result);
          for (const key in responseResultFnObj) {
            if (result.methodName === key) {
              responseResultFnObj[result.methodName](result);
              delete responseResultFnObj[result.methodName];
            }
          }
        } catch (e) {
          console.log("响应解析错误");
          console.log(e);
          showToast("error:" + res);
        }
      };
    }
    const params = JSON.stringify(data);
    if (window.webkit && window.webkit.messageHandlers[method]) {
      window.webkit.messageHandlers[method].postMessage(params);
    } else {
      try {
        if (params === "{}") {
          // eslint-disable-next-line no-undef
          syWebViewModel[method]();
        } else {
          // eslint-disable-next-line no-undef
          syWebViewModel[method](params);
        }
      } catch (e) {
        reject();
        if (process.env.NODE_ENV !== "production") {
          console.log(`请求方法:${method}不存在`);
        }
      }
    }
    if (!sync) resolve();
  });
}

export async function beforeRenderPage(context) {
  app = context;
  app.config.globalProperties.$appAllResult = (callback) =>
    (appResultFn = callback);

  const appInfoResult = await siyaApp("getAppInfo").catch((e) => e);
  console.log("getAppInfo:", appInfoResult);

  // 存在结果即表示在app内
  if (appInfoResult) {
    app.config.globalProperties.$appBottomBarHeight =
      appInfoResult.data.appBottomBarHeight;
    app.config.globalProperties.$appNavBarHeight =
      appInfoResult.data.appNavBarHeight;
    app.config.globalProperties.$appVersion = +appInfoResult.data.appVersion;
    app.config.globalProperties.$appVersionName =
      appInfoResult.data.appVersionName;
    app.config.globalProperties.$appOS = appInfoResult.data.os; // 1 android 2 ios
    app.config.globalProperties.$languageFile = appInfoResult.data.lang;
    app.config.globalProperties.$cloned = appInfoResult.data.cloned || "";
    app.config.globalProperties.$isApp = true;
    localStorage.setItem("siya_version", appInfoResult.data.appVersion);
    localStorage.setItem("siya_os", appInfoResult.data.os);

    i18n.global.locale = appInfoResult.data.lang;
    localStorage.setItem("siya_lang", appInfoResult.data.lang);
    localStorage.setItem("siya_cloned", appInfoResult.data.cloned || "");

    const httpAuthTokenResult = await siyaApp("getHttpAuthToken");
    const token = httpAuthTokenResult?.data?.token || "";
    localStorage.setItem("siya_token", token);
  }
}
