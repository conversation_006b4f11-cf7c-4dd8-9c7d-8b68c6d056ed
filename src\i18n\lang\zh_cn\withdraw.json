{"withdrawal_rule_title": "提现说明", "withdrawal_rule_detail": ["申请提现成功后，预计1-7个工作日到账，如已过预计到账时间未到账请确认您的收款账户无误，并耐心等待或联系客服", "根据提现金额需支付不同金额的提现手续费，具体以实际到账为准", "若您在提现期间注销账号或因违规被封号，我们将冻结您的提现订单"], "withdrawal_select_methods_tips": "提现需绑定您的收款方式，请选择您的收款方式并确保你的收款信息真实有效", "service_fee": "手续费: {0}", "withdrawal_methods": "收款方式", "withdrawal_information": "提现信息", "withdrawal_info_check": "请确认您的提现信息", "estimated_withdrawal_amount": "预计提现金额", "estimated_amount_received": "预计到账金额", "estimated_service_fee": "预计支付手续费", "current_selected": "当前选择", "completed": "已填写", "need_complete": "待完善", "withdrawal_amount": "提现金额", "total_withdrawal_amount": "累计提现金额", "withdrawal_pending": "提现中", "withdrawal_success": "提现成功", "withdrawal_fail": "提现失败", "fail_reason": "提现失败原因", "empty": "暂无提现记录", "BANK_TRANSFER_receive_bank": "收款银行", "BANK_TRANSFER_receive_bank_placeholder": "请选择收款银行", "BANK_TRANSFER_fill_info_tips": "请填写银行转账的收款信息", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "例如***********", "payeeInfo_documentId_hint": "请输入CPF/CNPJ（个人/企业税号）", "payeeInfo_address_title": "地址", "payeeInfo_email_title": "邮箱", "payeeInfo_email_placeholder": "例如1234566{0}gmail.com", "payeeInfo_email_hint": "请输入邮箱，符合邮箱基本格式", "payeeInfo_phone_title": "手机号码", "WALLET_accountNo_title": "{0}账号", "WALLET_accountType_title": "{0}绑定账号类型", "WALLET_accountNo_placeholder_SA": "例如************", "WALLET_accountNo_placeholder_EG": "例如***********", "WALLET_accountNo_placeholder_AE": "例如+971-*********", "WALLET_accountNo_placeholder_TR": "例如**********", "WALLET_accountNo_placeholder_PH": "例如***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "例如mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "例如***********", "WALLET_accountType_placeholder_BR_PIX": "请选择绑定账号类型", "WALLET_accountNo_placeholder_BR_PagBank": "例如************{0}123.com", "WALLET_accountNo_placeholder_ID": "例如***********", "WALLET_accountNo_placeholder_MY": "例如**********", "WALLET_accountNo_placeholder_TH": "例如**********", "WALLET_accountNo_placeholder_VN": "例如***********", "WALLET_accountNo_hint_SA": "请输入钱包绑定的手机号，9665开头的12位数字", "WALLET_accountNo_hint_EG": "请输入钱包绑定的手机号，01开头的11位数字", "WALLET_accountNo_hint_EG_2": "请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "收款方手机号--必填--钱包绑定的手机号，01开头的11位数字或201开头的12位数字", "WALLET_accountNo_hint_AE": "请输入钱包绑定的手机号，格式必须满足+国家编码-手机号", "WALLET_accountNo_hint_TR": "请输入钱包绑定的手机号，10位数字或PL加10位数字", "WALLET_accountNo_hint_PH": "请输入钱包绑定的手机号，09开头11位数字", "WALLET_accountNo_hint_PH_LAZADAPH": "请输入钱包绑定的手机号，09开头11位数字或邮箱", "WALLET_accountNo_hint_BR_MERCADOPAGO": "请输入钱包绑定的用户账户（邮箱或id）", "WALLET_accountNo_hint_BR_PIX": "请输入钱包绑定的对应账号", "WALLET_accountNo_hint_BR_PagBank": "请输入钱包绑定的邮箱，最长60位", "WALLET_accountNo_hint_ID": "请输入钱包绑定的账号，08开头的9～14位数字", "WALLET_accountNo_hint_MY": "请输入钱包绑定的账号，0开头的10/11位数字", "WALLET_accountNo_hint_TH": "请输入钱包绑定的账号，0开头的10位数字", "WALLET_accountNo_hint_VN": "请输入钱包绑定的账号，84开头的11位数字", "WALLET_fullName_title": "收款方姓名", "WALLET_fullName_placeholder": "例如<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "例如Li <PERSON>", "WALLET_fullName_placeholder_PH": "例如<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "例如<PERSON> Evan<PERSON>", "WALLET_fullName_hint": "请输入你的英文全名", "WALLET_lastName_title": "收款方姓氏", "WALLET_lastName_placeholder_EG": "例如Evangelista", "WALLET_lastName_hint": "请填写你的姓氏", "WALLET_accountType_option_E_PIX": "email", "WALLET_accountType_option_P_PIX": "phone", "WALLET_accountType_option_C_PIX": "CPF/CNPJ（个人/企业税号）", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "例如123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "SWIFT账号", "SWIFT_accountNo_placeholder_SA": "例如SA44200000**********1234", "SWIFT_accountNo_placeholder_AE": "例如AE46009000000**********", "SWIFT_accountNo_placeholder_KW": "例如******************************", "SWIFT_accountNo_placeholder_QA": "例如*****************************", "SWIFT_accountNo_placeholder_TR": "例如TR32001000999990**********", "SWIFT_accountNo_placeholder_BH": "例如**********************", "SWIFT_accountNo_placeholder_YE": "例如**********", "SWIFT_accountNo_placeholder_IQ": "例如***********************", "SWIFT_accountNo_placeholder_IL": "例如***********************", "SWIFT_accountNo_placeholder_PS": "例如**********", "SWIFT_accountNo_placeholder_AZ": "例如AZ77VTBA0000000000**********", "SWIFT_accountNo_placeholder_LB": "例如****************************", "SWIFT_accountNo_placeholder_MA": "例如**********", "SWIFT_accountNo_placeholder_PH": "例如**********", "SWIFT_accountNo_placeholder_BR": "例如*****************************", "SWIFT_accountNo_placeholder_EG": "例如*****************************", "SWIFT_accountNo_placeholder_JO": "例如JO71CBJO000000000000**********", "SWIFT_accountNo_hint_SA": "请输入SWIFT账号，SA开头的24位字符", "SWIFT_accountNo_hint_AE": "请输入SWIFT账号，AE开头的23位字符", "SWIFT_accountNo_hint_KW": "请输入SWIFT账号，KW开头的30位字符", "SWIFT_accountNo_hint_QA": "请输入SWIFT账号，QA开头的29位字符", "SWIFT_accountNo_hint_TR": "请输入SWIFT账号，TR开头的26位字符", "SWIFT_accountNo_hint_BH": "请输入SWIFT账号，BH开头的22位字符", "SWIFT_accountNo_hint_YE": "请输入SWIFT账号，小于等于34位数字和字母", "SWIFT_accountNo_hint_IQ": "请输入SWIFT账号，IQ开头的23位字符", "SWIFT_accountNo_hint_IL": "请输入SWIFT账号，IL开头的23位字符", "SWIFT_accountNo_hint_PS": "请输入SWIFT账号，小于等于34位数字和字母", "SWIFT_accountNo_hint_AZ": "请输入SWIFT账号，AZ开头的28位字符", "SWIFT_accountNo_hint_LB": "请输入SWIFT账号，LB开头的28位字符", "SWIFT_accountNo_hint_MA": "请输入SWIFT账号，小于等于34位数字和字母", "SWIFT_accountNo_hint_PH": "请输入SWIFT账号，小于等于34位数字和字母", "SWIFT_accountNo_hint_BR": "请输入SWIFT账号，BR开头的29位字符", "SWIFT_accountNo_hint_EG": "请输入SWIFT账号，EG开头的29位字符", "SWIFT_accountNo_hint_JO": "请输入SWIFT账号，JO开头的30位字符", "SWIFT_fullName_title": "收款方姓名", "SWIFT_fullName_placeholder": "例如Li <PERSON>", "SWIFT_fullName_hint": "请输入你的英文全名", "SWIFT_bankCode_title": "银行编号（SWIFTCode）", "SWIFT_bankCode_placeholder": "例如SCBLHKHH", "SWIFT_bankCode_hint": "请输入银行编号，8或11位，允许数字和大写字母", "CARD_accountNo_title": "银行账号", "CARD_accountNo_placeholder_EG": "例如***********", "CARD_accountNo_placeholder_TR": "例如****************", "CARD_accountNo_hint_EG": "请输入银行账户，长度>=8", "CARD_accountNo_hint_TR": "请输入银行卡号16位数字，仅支持借记卡", "CARD_fullName_title": "收款方姓名", "CARD_fullName_placeholder": "例如<PERSON> Evan<PERSON>", "CARD_fullName_hint": "请输入你的英文全名", "CARRIER_BILLING_accountNo_title": "手机号", "CARRIER_BILLING_accountNo_placeholder": "例如63**********", "CARRIER_BILLING_accountNo_hint": "请输入电话号码", "CASH_accountNo_title": "{0}账号", "CASH_accountNo_placeholder": "例如***********", "CASH_accountNo_hint": "请输入{0}账号，0开头的11位数字", "BANK_TRANSFER_accountNo_title_SA": "银行账号（IBAN）", "BANK_TRANSFER_accountNo_title_AE": "银行账号（IBAN）", "BANK_TRANSFER_accountNo_title_TR": "银行账号（IBAN）", "BANK_TRANSFER_accountNo_title": "银行账号", "BANK_TRANSFER_accountNo_title_EG": "银行账号", "BANK_TRANSFER_accountNo_title_KW": "银行账号", "BANK_TRANSFER_accountNo_title_QA": "银行账号", "BANK_TRANSFER_accountNo_title_MA": "银行账号（RIB）", "BANK_TRANSFER_accountNo_title_PH": "银行账号", "BANK_TRANSFER_accountNo_title_BR": "银行账号", "BANK_TRANSFER_accountNo_placeholder_SA": "例如************************", "BANK_TRANSFER_accountNo_placeholder_EG": "例如************", "BANK_TRANSFER_accountNo_placeholder_AE": "例如***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "例如******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "例如*****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "例如**************************", "BANK_TRANSFER_accountNo_placeholder_MA": "例如123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "例如************", "BANK_TRANSFER_accountNo_placeholder_BR": "例如***********", "BANK_TRANSFER_accountNo_placeholder_JO": "例如******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "例如**********", "BANK_TRANSFER_accountNo_placeholder_TH": "例如*********", "BANK_TRANSFER_accountNo_placeholder_ID": "例如***********", "BANK_TRANSFER_accountNo_hint_SA": "请输入SA+22位字母，数字的组合", "BANK_TRANSFER_accountNo_hint_EG": "请输入IBAN和本地银行账号，IBAN;29位字符（EG+27位数字）", "BANK_TRANSFER_accountNo_hint_AE": "请输入AE+21位数字,AE必须大写", "BANK_TRANSFER_accountNo_hint_KW": "请输入长度<=50位，IBAN格式", "BANK_TRANSFER_accountNo_hint_QA": "请输入长度<=50位，IBAN格式", "BANK_TRANSFER_accountNo_hint_TR": "请输入TR开头加24位数字，IBAN格式", "BANK_TRANSFER_accountNo_hint_MA": "请输入RIB账户代码24位数字", "BANK_TRANSFER_accountNo_hint_PH": "请输入6~128位数字", "BANK_TRANSFER_accountNo_hint_BR": "请输入4~15位数字", "BANK_TRANSFER_accountNo_hint_JO": "请输入JO开头的30位数字字母的组合", "BANK_TRANSFER_accountNo_hint_MY": "请输入最多35位的账号", "BANK_TRANSFER_accountNo_hint_TH": "请输入10～18位数字账号", "BANK_TRANSFER_accountNo_hint_ID": "请输入数字账号", "BANK_TRANSFER_bankBranch_title": "银行网点号", "BANK_TRANSFER_bankBranch_placeholder_BR": "例如1234", "BANK_TRANSFER_bankBranch_hint_BR": "请输入银行网点号，4~6位数字", "BANK_TRANSFER_address_placeholder_SA": "例如aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "例如ALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "例如kuwait xx street", "BANK_TRANSFER_address_hint_SA": "请输入地址，长度限制10~200字符，只允许数字、点、大小写字母，空格", "BANK_TRANSFER_address_hint_AE": "请输入地址，长度限制10~200字符，1~200位字符，银行会进行风控校验", "BANK_TRANSFER_address_hint_KW": "请输入地址，长度<=255", "BANK_TRANSFER_fullName_placeholder_TR": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_PH": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_BR": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_MA": "例如Jorge", "BANK_TRANSFER_fullName_placeholder_KW": "例如<PERSON> kin", "BANK_TRANSFER_phone_placeholder_PH": "例如***********", "BANK_TRANSFER_phone_placeholder_BR": "例如*************", "BANK_TRANSFER_phone_hint_PH": "请输入你的移动电话号码，09开头11位的数字 （必须加0）", "BANK_TRANSFER_phone_hint_BR": "请输入你的移动电话号码，55开头的12-13位数字", "BANK_TRANSFER_bankCode_title_MY": "银行编号（SWIFTCode） ", "BANK_TRANSFER_bankCode_placeholder_MY": "例如CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "请输入8-11位数字和字母的组合", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT代码", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "请输入8或11个大写字母数字的组合", "BANK_TRANSFER_city_title_SA": "城市", "BANK_TRANSFER_city_placeholder_SA": "例如New York", "BANK_TRANSFER_city_hint_SA": "请输入城市名称，最长35位", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "例如：001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "请输入24位数字的组合", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "出生日期", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "例如********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "请输入出生日期", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "证件类型", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "请选择证件类型", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "证件ID", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "例如NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "例如ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "请输入证件id", "AGENT_accountNo_Whatsapp_title_ALL": "Whatsapp号码", "AGENT_accountNo_Whatsapp_placeholder_ALL": "例如+90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "请输入你的Whatsapp账号", "AGENT_accountNo_vodafone_title_EG": "沃达丰账号", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "区块链名称", "USDT_address_title_ALL": "发币地址", "USDT_address_placeholder_ALL": "例如11111111111111111111111111", "USDT_address_hint_ALL": "请输入1、3、bc开头的地址", "USDT_address_hint_common": "请输入你的usdt地址"}