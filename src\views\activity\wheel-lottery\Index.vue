<template>
  <template v-if="!isAuditMode">
    <div
      :data-theme="theme"
      class="container"
      v-track:exposure
      trace-key="prize_wheel_expo"
      :track-params="JSON.stringify({ prize_wheel_expo_source: from })"
    >
      <header-bar back-icon-show :show-title="false" close-type="close" />
      <template v-if="activityInfo?.status === 3">
        <div class="ended">{{ $t("wheel_lottery.activity_has_ended") }}</div>
      </template>
      <template v-else>
        <div class="top-banner">
          <img :src="getImageUrl('top-banner.png')" alt="" />
        </div>
        <div class="activity-tip">
          {{ $t("wheel_lottery.activity_tip") }}
        </div>
        <div class="reward-tip" @click="showRewordDialog = true">
          {{ $t("wheel_lottery.guaranteed_win") }}
        </div>
        <div class="wheel">
          <wheel
            ref="wheelRef"
            :prizes="activityInfo?.prizes || []"
            :use-count="activityInfo?.use_count || 0"
            :draw-count="activityInfo?.draw_count || 0"
            :get-result-api="getResultApi"
            @result="handlePrizeResult"
            @prize-click="handlePrizeClick"
          ></wheel>
        </div>
        <div class="card-block" ref="taskBlockRef">
          <div class="card-title">{{ $t("wheel_lottery.tasks") }}</div>
          <div class="task-list">
            <sub-title :title="$t('wheel_lottery.daily_task')"></sub-title>
            <div class="task-item">
              <div class="cover">
                <div class="prize-1"></div>
              </div>
              <gap :gap="12"></gap>
              <div class="content">
                <div class="content-top">
                  {{
                    activityInfo?.gender === 1
                      ? $t("wheel_lottery.gift_sent_target")
                      : $t("wheel_lottery.gift_received_target")
                  }}
                  <span class="color-999"
                    >(<span class="color-F70">{{
                      activityInfo?.daily_task_progress || 0
                    }}</span
                    >/15000)</span
                  >
                </div>
                <div class="content-bottom">
                  <span>{{ $t("wheel_lottery.reward_label") }}</span>
                  <span>{{ $t("wheel_lottery.draw_chance_reward") }}</span>
                </div>
              </div>
              <gap :gap="12"></gap>
              <div class="btn">
                <div
                  class="button plain"
                  v-if="activityInfo?.daily_task_status === 0"
                  @click="openMessageList()"
                  v-track:click
                  trace-key="prize_wheel_go_click"
                  :track-params="JSON.stringify({ prize_wheel_task_type: 1 })"
                >
                  {{ $t("wheel_lottery.go_complete") }}
                </div>
                <div
                  class="button"
                  v-if="activityInfo?.daily_task_status === 1"
                  @click="getPrize(1)"
                  v-track:click
                  trace-key="prize_wheel_claim_click"
                  :track-params="JSON.stringify({ prize_wheel_task_type: 1 })"
                >
                  {{ $t("wheel_lottery.claim_button") }}
                </div>
                <div
                  class="button disabled"
                  v-if="activityInfo?.daily_task_status === 2"
                >
                  {{ $t("wheel_lottery.claimed_status") }}
                </div>
              </div>
            </div>
            <div class="line"></div>
            <sub-title :title="$t('wheel_lottery.limited_task')"></sub-title>
            <div class="task-item">
              <div class="cover">
                <div
                  class="prize-2"
                  :style="{
                    backgroundImage: `url(${activityInfo?.recharge_prize?.reward_img})`,
                  }"
                ></div>
              </div>
              <gap :gap="12"></gap>
              <div class="content">
                <div class="content-top">
                  {{ $t("wheel_lottery.recharge_condition_1") }}
                  <span class="color-999"
                    >(<span class="color-F70">{{
                      activityInfo?.recharge_task_progress?.amount || "0"
                    }}</span
                    >/36000)</span
                  >
                  {{ $t("wheel_lottery.recharge_condition_2") }}
                  <span class="color-999"
                    >(<span class="color-F70">{{
                      activityInfo?.recharge_task_progress?.count || "0"
                    }}</span
                    >/3)</span
                  >
                </div>
                <div class="content-bottom">
                  <span>{{ $t("wheel_lottery.reward_label") }}</span>
                  <span>{{ $t("wheel_lottery.rare_vehicle_reward") }}</span>
                </div>
              </div>
              <gap :gap="12"></gap>
              <div class="btn">
                <div
                  class="button plain"
                  v-if="activityInfo?.recharge_task_status === 0"
                  @click="openRechargeDialog()"
                  v-track:click
                  trace-key="prize_wheel_go_click"
                  :track-params="JSON.stringify({ prize_wheel_task_type: 2 })"
                >
                  {{ $t("wheel_lottery.go_complete") }}
                </div>
                <div
                  class="button"
                  v-if="activityInfo?.recharge_task_status === 1"
                  @click="getPrize(2)"
                  v-track:click
                  trace-key="prize_wheel_claim_click"
                  :track-params="JSON.stringify({ prize_wheel_task_type: 2 })"
                >
                  {{ $t("wheel_lottery.claim_button") }}
                </div>
                <div
                  class="button disabled"
                  v-if="activityInfo?.recharge_task_status === 2"
                >
                  {{ $t("wheel_lottery.claimed_status") }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-block">
          <div class="card-title">
            {{ $t("wheel_lottery.winning_records") }}
          </div>
          <div class="prize-list">
            <div class="prize-header">
              <div>{{ $t("wheel_lottery.winning_time") }}</div>
              <div>{{ $t("wheel_lottery.prize") }}</div>
            </div>
            <div v-for="prize in prizeRecords" class="prize-row">
              <div>{{ formatDate(prize.create_time) }}</div>
              <div>
                <template v-if="prize.reward_type === 1">
                  <img
                    src="@/assets/images/wheel-lottery/common/wheel/prize-coin.png"
                  />
                  <gap :gap="2"></gap>
                  <span><span>x</span>{{ prize.num }}</span>
                </template>
                <template v-else>
                  <img :src="prize.reward_img" />
                  <gap :gap="2"></gap>
                  <span v-if="prize.reward_type !== 5">
                    <span>
                      <span>x</span
                      >{{
                        $t(
                          prize.duration > 1
                            ? "common.common_days"
                            : "common.common_day",
                          [prize.duration]
                        )
                      }}
                    </span>
                  </span>
                  <span v-else><span>x</span>{{ prize.num }}</span>
                </template>
              </div>
            </div>
            <div v-if="prizeRecords.length === 0">
              <Empty :tips="$t('common.common_no_record')"></Empty>
            </div>
          </div>
        </div>
        <div class="footer">
          <div
            class="recharge-button"
            @click="openRechargeDialog()"
            v-track:click
            trace-key="prize_wheel_recharge_button_click"
          >
            <div>{{ $t("wheel_lottery.recharge_cta") }}</div>
            <gap :gap="4"></gap>
            <div class="icon-arrow"></div>
          </div>
        </div>
        <div class="rule-btn" @click="toRulePage"><div></div></div>
      </template>
    </div>
    <reword-dialog ref="rewordDialogRef"></reword-dialog>
    <prize-dialog ref="prizeDialogRef"></prize-dialog>
  </template>
</template>
<script setup>
import SubTitle from "./components/SubTitle.vue";
import RewordDialog from "./components/RewordDialog.vue";
import PrizeDialog from "./components/PrizeDialog.vue";
import Wheel from "./components/Wheel.vue";
import { useRouter, useRoute } from "vue-router";
import { computed, getCurrentInstance, onMounted, ref } from "vue";
import activityApi from "@/api/activity.js";
import { smoothScrollTo, formatDate } from "@/utils/util.js";
import { i18n, checkLang } from "@/i18n/index.js";
const t = i18n.global.t;

const { proxy } = getCurrentInstance();

const isAuditMode = ref(true);
const router = useRouter();
const route = useRoute();
const wheelRef = ref();
const prizeDialogRef = ref();
const rewordDialogRef = ref();
const taskBlockRef = ref();
const showRewordDialog = ref(false);
const activityInfo = ref(null);
const prizeRecords = ref([]);

const { from } = route.query;
const theme = computed(() => route.params.theme || "normal");
const lang = checkLang();
function getImageUrl(name) {
  return new URL(
    `../../../assets/images/wheel-lottery/${theme.value}/${lang}/${name}`,
    import.meta.url
  ).href;
}

const toRulePage = () => {
  router.push({
    name: "WheelLotteryRule",
    query: {
      ...route.query,
      timezone: activityInfo.value?.timezone,
    },
    params: {
      theme: theme.value,
    },
  });
};

const openRechargeDialog = async () => {
  const result = await proxy.$siyaApp("openSiyaUrl", {
    url: "siya://siya.com/app?method=goRechargeDialog",
  });
  if (result.data?.scene === "recharge") {
    setTimeout(() => {
      loadData();
    }, 1000);
  }
};

const openMessageList = () => {
  proxy.$siyaApp("openSiyaUrl", {
    url: "siya://siya.com/app?method=messageList",
  });
};

const getResultApi = async () => {
  const res = await activityApi.wheel_lottery_draw();
  if (res.code === 200) {
    return res.data[0];
  } else if (res.code === 10001) {
    await loadData();
    if (activityInfo.value?.status === 1) {
      return;
    }
    if (activityInfo.value?.status === 3) {
      return;
    }
    if (activityInfo.value?.recharge_task_status === 0) {
      openRechargeDialog();
    } else if (activityInfo.value?.daily_task_status === 0) {
      smoothScrollTo(taskBlockRef.value, 150);
    } else if (
      activityInfo.value?.recharge_task_status === 1 ||
      activityInfo.value?.daily_task_status === 1
    ) {
      smoothScrollTo(taskBlockRef.value, 150);
      showToast(t("wheel_lottery.get_prize_toast"));
    } else {
      showToast(t("wheel_lottery.draw_limit_message"));
    }
    return null;
  } else {
    showToast(res.msg || t("wheel_lottery.draw_fail"));
    return null;
  }
};

const getActivityInfo = async () => {
  const res = await activityApi.wheel_lottery_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
  }
};

const getPrizeRecord = async () => {
  const res = await activityApi.wheel_lottery_record();
  if (res.code === 200) {
    prizeRecords.value = res.data || [];
  }
};

const isGetPrizeLoading = ref(false);
const getPrize = async (type) => {
  if (isGetPrizeLoading.value === true) {
    return;
  }
  isGetPrizeLoading.value = true;
  const res = await activityApi.wheel_lottery_prize({ type }).catch(() => {});
  if (res.code === 10002) {
    showToast(t("wheel_lottery.get_prize_fail_toast"));
  }
  loadData();
  isGetPrizeLoading.value = false;
};

const loadData = async () => {
  await getActivityInfo();
  await getPrizeRecord();
};

const handlePrizeResult = (prize) => {
  prizeDialogRef.value.close();
  rewordDialogRef.value.open(prize);
  loadData();
};

const handlePrizeClick = (prize) => {
  prizeDialogRef.value.open(prize);
};

onMounted(async () => {
  try {
    const appInfoResult = await proxy.$siyaApp("getAppInfo");
    if (appInfoResult.data?.appEnv) {
      isAuditMode.value = true;
      // ios有bug，加0.5秒兜底
      setTimeout(() => {
        proxy.$siyaApp("openSiyaUrl", {
          url: "siya://siya.com/app?method=goRecharge",
        });
      }, 500);
    } else {
      isAuditMode.value = false;
      loadData();
    }
  } catch (error) {
    console.error(error);
    isAuditMode.value = false;
    loadData();
  }
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
@import "./theme.scss";

[data-theme="normal"] {
  .activity-tip {
    margin-top: px2rem(-16) !important;
    color: #ff4771 !important;
  }
  .wheel {
    margin-top: px2rem(-24) !important;
  }
}

.container {
  min-height: 100vh;
  background-image: var(--index-bg);
  background-size: 100% px2rem(754);
  background-repeat: no-repeat;
  background-color: var(--index-bg-color);
  overflow: auto;
  padding-bottom: px2rem(100);
  display: flex;
  flex-direction: column;

  .ended {
    flex-grow: 1;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;

    color: #000;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(20);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .top-banner {
    margin-top: px2rem(-30);
    width: 100%;
    height: px2rem(130);
    img {
      width: 100%;
      height: 100%;
    }
  }
  .wheel {
    margin-top: px2rem(-28);
    width: 100%;
    overflow: hidden;
  }
  .activity-tip {
    color: #ffcf75;
    text-align: center;

    /* T20/B */
    font-family: Gilroy;
    font-size: px2rem(20);
    height: px2rem(40);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    padding: px2rem(8) 0;
    margin-top: px2rem(-8);
  }
  .reward-tip {
    display: flex;
    padding: px2rem(4) px2rem(8);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(100);
    background: var(--reward-tip-bg);
    width: fit-content;
    height: px2rem(30);
    margin: 0 auto;

    color: #fff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .card-block {
    border-radius: px2rem(26) px2rem(26) px2rem(16) px2rem(16);
    border: px2rem(2) solid #fff2c7;
    background: linear-gradient(180deg, #fff1d7 19.12%, #fff 100%);
    margin: 0 px2rem(12);
    margin-bottom: px2rem(24);
    .card-title {
      padding: px2rem(12) 0;
      background-image: url("@/assets/images/wheel-lottery/common/card-title-bg.png");
      background-size: 100% px2rem(68);
      background-repeat: no-repeat;
      border-radius: px2rem(26) px2rem(26) 0 0;

      color: #382a06;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
    .task-list {
      padding: px2rem(20) px2rem(12) px2rem(16) px2rem(12);
      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: px2rem(12);
        .cover {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: px2rem(10);
          width: px2rem(56);
          height: px2rem(56);
          background: #ffedcc;
          div {
            width: px2rem(56);
            height: px2rem(56);
          }
          .prize-1 {
            background-image: url("@/assets/images/wheel-lottery/common/prize-1.png");
            background-size: 100% 100%;
          }
          .prize-2 {
            background-size: 100% 100%;
          }
        }
        .content {
          flex-grow: 1;
          .content-top {
            color: var(---B0-Black, #000);
            font-family: Gilroy;
            font-size: px2rem(15);
            font-style: normal;
            font-weight: 700;
            line-height: normal;
          }
          .color-999 {
            color: var(---B3, #999);
            /* T11/R */
            font-family: Gilroy;
            font-size: px2rem(11);
            font-style: normal;
            font-weight: 500;
            line-height: 120%;
          }
          .color-F70 {
            color: #f70;
            font-family: Gilroy;
            font-size: px2rem(11);
            font-style: normal;
            font-weight: 500;
            line-height: 120%;
          }
          .content-bottom {
            margin-top: px2rem(8);
            font-family: Gilroy;
            font-size: 13px;
            font-style: normal;
            line-height: normal;
            & :first-child {
              color: #666;
            }
            & :last-child {
              color: #f70;
              font-weight: 700;
            }
          }
        }
        .btn {
          flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          .button {
            display: flex;
            height: 30px;
            padding: px2rem(6) px2rem(8);
            justify-content: center;
            align-items: center;
            min-width: px2rem(56);
            color: #fff;
            text-align: center;
            font-family: Gilroy;
            font-size: px2rem(15);
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            border-radius: px2rem(10);

            border: 1px solid #f70;
            background: #f70;
          }
          .plain {
            color: #f70;
            background: none;
          }
          .disabled {
            opacity: 0.48;
          }
        }
      }
      .line {
        height: 1px;
        width: 100%;
        background: #e6e6e6;
        margin: px2rem(20) 0;
      }
    }
    .prize-list {
      padding: px2rem(20) px2rem(12);
      min-height: px2rem(380);
      .prize-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: px2rem(6) px2rem(16) px2rem(6) px2rem(16);
        border-radius: px2rem(4);
        background: #fce4b9;
        margin-bottom: px2rem(12);
        div {
          color: #9d752e;
          font-family: Gilroy;
          font-size: px2rem(15);
          font-style: normal;
          font-weight: 500;
          line-height: 128%; /* 19.2px */
        }
      }
      .prize-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: px2rem(4) px2rem(16);
        margin-bottom: px2rem(4);
        :first-child {
          color: #999;
          font-family: Gilroy;
          font-size: px2rem(13);
          font-weight: 500;
          line-height: 124%; /* 16.12px */
        }
        :last-child {
          color: #f70;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-weight: 700;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: px2rem(24);
            height: px2rem(24);
          }
        }
      }
    }
  }

  .footer {
    position: fixed;
    bottom: -1px;
    left: 0;
    z-index: 999;
    display: flex;
    width: 100%;
    padding-top: px2rem(20);
    padding-bottom: px2rem(28);
    justify-content: center;
    align-items: center;
    background: var(--index-footer-bg);
    @keyframes action {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(0.92);
      }
      100% {
        transform: scale(1);
      }
    }
    .recharge-button {
      display: flex;
      width: px2rem(358);
      height: px2rem(72);
      justify-content: center;
      align-items: center;

      border-radius: px2rem(100);
      background: #ffd42f;
      box-shadow: 0px 0px px2rem(50) 0px #fff inset;

      color: #7c0001;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(28);
      font-style: normal;
      font-weight: 900;
      line-height: normal;

      animation: action 1s infinite;

      .icon-arrow {
        width: px2rem(24);
        height: px2rem(24);
        background-image: url("@/assets/images/wheel-lottery/common/icon-arrow-right.svg");
        background-size: 100% 100%;
      }
    }
  }
}

.rule-btn {
  position: fixed;
  right: 0;
  z-index: 99;
  top: px2rem(230);
  width: px2rem(40);
  height: px2rem(32);
  border-radius: px2rem(100) 0px 0px px2rem(100);
  border: px2rem(2) solid #ffe69b;
  background: var(--rule-btn-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: px2rem(24);
    height: px2rem(24);
    background-image: url("@/assets/images/wheel-lottery/common/rule.png");
    background-size: 100% 100%;
  }
}
.rtl-html {
  .icon-arrow {
    transform: rotateY(180deg);
  }
  .rule-btn {
    right: unset !important;
    left: 0;
    border-radius: 0 px2rem(100) px2rem(100) 0;
  }
}
</style>
