<template>
  <div class="app-container flex">
    <header-bar close-type="close"/>
    <main>
      <div class="wrap">
        <div class="cell" v-for="(tip, idx) in tips" :key="idx">
          <div class="cell-desc">{{ tip.desc }}</div>
          <div class="cell-img-wrap"
               :class="{avatar: tip.isAvatar, flex: tip.isAvatar}"
          >
            <div class="cell-img nice" v-if="tip.niceImg">
              <img class="img" :src="tip.niceImg" alt="">
              <img class="tips" :src="tipsImg.nice" alt="">
            </div>
            <div class="cell-img" v-if="tip.badImg">
              <img class="img" :src="tip.badImg" alt="">
              <img class="tips" :src="tipsImg.bad" alt="">
            </div>
            <div class="cell-img" v-if="tip.normalImg">
              <img class="img" :src="tip.normalImg" alt="">
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { i18n } from '@/i18n/index.js'
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()
const tm = i18n.global.tm
const lang = proxy.$languageFile

const tipsImg = {
  nice: getImageUrl('nice'),
  bad: getImageUrl('bad'),
}

const tips = [
  {
    desc: tm('base_total.chat_tips_msg')[0],
    niceImg: getImageUrl('tips1-s'),
    badImg: getCommonImageUrl('tips1-f'),
    isAvatar: true,
  },
  {
    desc: tm('base_total.chat_tips_msg')[1],
    normalImg: getImageUrl('tips2-n'),
  },
  {
    desc: tm('base_total.chat_tips_msg')[2],
    niceImg: getImageUrl('tips3-s'),
    badImg: getImageUrl('tips3-f'),
  },
  {
    desc: tm('base_total.chat_tips_msg')[3],
    niceImg: getImageUrl('tips4-s'),
    badImg: getImageUrl('tips4-f'),
  },
  {
    desc: tm('base_total.chat_tips_msg')[4],
    niceImg: getImageUrl('tips5-s'),
    badImg: getImageUrl('tips5-f'),
  },
  {
    desc: tm('base_total.chat_tips_msg')[5],
    niceImg: getImageUrl('tips6-s'),
    badImg: getImageUrl('tips6-f'),
  },
]

function getImageUrl(name) {
  return new URL(`../../../assets/images/chat-tips/${lang}/${name}.png`, import.meta.url).href
}

function getCommonImageUrl(name) {
  return new URL(`../../../assets/images/chat-tips/common/${name}.png`, import.meta.url).href
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  flex-direction: column;
  height: 100vh;
  background: url("@/assets/images/chat-tips/bg.png") top/contain no-repeat, #FF6697;
}

main {
  flex: 1;
  width: 100%;
  overflow-y: scroll;
  padding: $gap8 $gap12 $gap24;
}

.wrap {
  padding: $gap16 $gap12;
  background-color: #FDEFF8;
  border-radius: $radius16;

  .cell {
    &:not(:last-child) {
      margin-bottom: $gap32;
    }
  }

  .cell-desc {
    margin-bottom: $gap12;
    font-size: px2rem(15);
    line-height: px2rem(19);
    color: #661F35;
  }

  .cell-img-wrap {
    padding: px2rem(24) px2rem(17);
    background: linear-gradient(127.73deg, #E8E7FF 0%, #FFDCF3 101.41%);
    border: 1px solid #8F57FF1A;
    border-radius: $radius12;

    &.avatar {
      justify-content: space-between;
      padding: px2rem(20) px2rem(35);

      .cell-img {
        width: px2rem(120);
        height: px2rem(120);
        margin-bottom: 0 !important;

        &.nice::after {
          bottom: px2rem(26);
          right: px2rem(-10);
        }
      }
    }

    .cell-img {
      position: relative;
      width: 100%;
      font-size: 0;

      &:not(:last-child) {
        margin-bottom: $gap32;
      }

      &.nice {
        &::before,
        &::after {
          content: '';
          position: absolute;
          background: url("@/assets/images/chat-tips/star.png") 100%/cover no-repeat;
        }

        &::before {
          top: px2rem(-8);
          left: px2rem(-8);
          width: px2rem(24);
          height: px2rem(24);
        }

        &::after {
          bottom: px2rem(8);
          right: px2rem(-8);
          width: px2rem(20);
          height: px2rem(20);
        }
      }

      .tips {
        position: absolute;
        left: 50%;
        bottom: px2rem(-12);
        width: px2rem(120);
        height: px2rem(32);
        margin-left: px2rem(-60);
      }

      .img {
        width: 100%;
      }
    }
  }
}
</style>
