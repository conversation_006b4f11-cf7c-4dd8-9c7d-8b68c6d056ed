<template>
  <div class="top-bg">
    <div class="bg">
      <div class="bg-padding"></div>
    </div>
    <div class="top-inner">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
// 计算进度条的宽度
const { proxy } = getCurrentInstance();

const props = defineProps({
  width: {
    type: Number,
    default: 120,
  },
});

const baseWidth = 120,
  baseBgHeight = 68,
  basePaddingHeight = 2;

const realWidth = computed(
  () => `${proxy.$pxToRemPx((props.width / baseWidth) * baseWidth)}px`
);

const realBgHeight = computed(
  () => `${proxy.$pxToRemPx((props.width / baseWidth) * baseBgHeight)}px`
);

const realPaddingHeight = computed(
  () => `${proxy.$pxToRemPx((props.width / baseWidth) * basePaddingHeight)}px`
);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.top-bg {
  position: relative;
  min-height: px2rem(100);
  overflow: auto;
  width: v-bind(realWidth);
  .bg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .bg-padding {
      flex-grow: 1;
      background-image: url("@/assets/images/activity/singer/top-padding.png"); /* 中间部分的图片 */
      background-repeat: repeat-y; /* 垂直重复 */
      background-position: 0 0;
      background-size: 100% v-bind(realPaddingHeight);
    }

    &::before {
      content: "";
      height: v-bind(realBgHeight); /* 顶部图片的高度 */
      background-image: url("@/assets/images/activity/singer/top-2.png"); /* 顶部图片 */
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100% 100%;
      margin-bottom: 0;
      box-sizing: border-box;
    }

    &::after {
      content: "";
      height: v-bind(realBgHeight);
      background-image: url("@/assets/images/activity/singer/top-bottom.png"); /* 使用相同的顶部图片 */
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100% 100%;
    }
  }
  .top-inner {
    position: relative;
  }
}
</style>
