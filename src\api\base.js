import request from '@/utils/request'

export default {
  obs_config(query) {
    return request({
      url: '/index/obs_config',
      method: 'get',
      params: query,
    })
  },
  oss_config(query) {
    return request({
      url: '/index/oss_config',
      method: 'get',
      params: query,
    })
  },
  qiniu_config(query) {
    return request({
      url: '/index/kodo_config',
      method: 'get',
      params: query,
    })
  },
}
