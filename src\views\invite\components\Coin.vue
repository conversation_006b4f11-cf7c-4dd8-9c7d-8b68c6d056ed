<template>
  <div class="coin" :style="style"></div>
</template>
<script setup>
import coinPng from "@/assets/images/common/coin.png";
import diamondPng from "@/assets/images/common/diamond.png";
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  gender: {
    type: Number,
    default: 1,
  },
  size: {
    type: Number,
    default: 20,
  },
});

const style = computed(() => {
  return {
    backgroundImage: `url("${props.gender === 1 ? coinPng : diamondPng}")`,
    width: `${proxy.$pxToRemPx(props.size)}px`,
    height: `${proxy.$pxToRemPx(props.size)}px`,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.coin {
  display: block;
  background-size: 100% 100%;
  flex-shrink: 0;
}
</style>
