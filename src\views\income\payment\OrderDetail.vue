<template>
  <div class="app-container">
    <header-bar is-scroll-change-bg />
    <main>
      <form-display :current-item="currentItem" />
      <van-button
        type="primary"
        block
        @click="showConfirm = true"
        v-if="currentItem.platform === 5 && currentItem.agent_status === 2"
      >
        {{ $t("income.withdraw_confirm_payment") }}
      </van-button>
    </main>
  </div>
  <confirm-action
    v-model:show="showConfirm"
    @confirm="confirm"
  ></confirm-action>
</template>

<script setup>
import { ref } from "vue";
import { useWithdrawStore } from "@/store/index.js";
import { storeToRefs } from "pinia";
import withdrawApi from "@/api/withdraw.js";
import ConfirmAction from "./components/ConfirmAction.vue";
import FormDisplay from "@/views/income/payment/components/FormDisplay.vue";

const withdrawStore = useWithdrawStore();
const { currentRecord: currentItem } = storeToRefs(withdrawStore);

const showConfirm = ref(false);

const confirm = async () => {
  const response = await withdrawApi.confirm({
    id: currentItem.value.id,
  });
  if (response.code === 200) {
    showConfirm.value = false;
    withdrawStore.setCurrentRecord({ ...currentItem.value, ...response.data });
  }
};
</script>

<style scoped lang="scss">
@import "./assets/payment.scss";

.app-container {
  padding: $gap8 $gap16 $pageBottomPadding;
}
</style>
