<template>
  <div class="rule-rewards" v-if="rewardData">
    <div class="tabs">
      <div
        class="tab"
        :class="currentTab === 0 ? 'active' : ''"
        @click="currentTab = 0"
      >
        {{ $t("singer.rules_rewards_competition_rewards_title") }}
      </div>
      <gap :gap="6"></gap>
      <div
        class="tab"
        :class="currentTab === 1 ? 'active' : ''"
        @click="currentTab = 1"
      >
        {{ $t("singer.rules_rewards_rankings_rewards_title") }}
      </div>
    </div>
    <template v-if="currentTab === 0">
      <div class="title mt-60">
        {{ $t("singer.rules_rewards_competition_rewards_audition_top20") }}
      </div>
      <div class="rewards mt-16">
        <template v-for="(item, index) in rewardData?.stage2">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="title mt-28">
        {{ $t("singer.rules_rewards_competition_rewards_qualifying_top10") }}
      </div>
      <div class="rewards mt-16">
        <template v-for="(item, index) in rewardData?.stage3">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="mt-42">
        <BlockTitle
          :show-line="true"
          :font-size="22"
          :lineWidth="30"
          :iconGap="14"
          :lineGap="3"
          icon-size="small"
          :title="
            $t(`singer.rules_rewards_competition_rewards_final_rewards_title`)
          "
        ></BlockTitle>
      </div>
      <div class="title mt-34">
        {{ $t("singer.rules_rewards_competition_rewards_final_rewards_top1") }}
      </div>
      <div class="rewards mt-22">
        <template v-for="(item, index) in rewardData?.stage4?.top1">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="title mt-50">
        {{ $t("singer.rules_rewards_competition_rewards_final_rewards_top2") }}
      </div>
      <div class="rewards mt-22">
        <template v-for="(item, index) in rewardData?.stage4?.top2">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="title mt-42">
        {{ $t("singer.rules_rewards_competition_rewards_final_rewards_top3") }}
      </div>
      <div class="rewards mt-22">
        <template v-for="(item, index) in rewardData?.stage4?.top3">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="mt-60">
        <BlockTitle
          :show-line="false"
          :show-icon="false"
          :font-size="18"
          :title="
            $t(`singer.rules_rewards_competition_rewards_final_rewards_others`)
          "
        ></BlockTitle>
      </div>
      <div class="rewards mt-22">
        <template v-for="(item, index) in rewardData?.stage4?.other">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
    </template>
    <template v-if="currentTab === 1">
      <div class="title mt-60">
        {{ $t("singer.rules_rewards_rankings_rewards_popularity_top1") }}
      </div>
      <div class="rewards mt-16">
        <template v-for="(item, index) in rewardData?.popularity">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
      <div class="title mt-28">
        {{ $t("singer.rules_rewards_rankings_rewards_support_top1") }}
      </div>
      <div class="rewards mt-16">
        <template v-for="(item, index) in rewardData?.support">
          <div class="reward">
            <div class="prize">
              <PrizeBox
                :img="
                  item.reward_type === 1
                    ? CoinIcon
                    : item.reward_img || CoinIcon
                "
                :tag="formatTag(item)"
              ></PrizeBox>
            </div>
            <div class="prize-name">{{ item.reward_name }}</div>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>
<script setup>
import CoinIcon from "@/assets/images/common/coin.svg";
import PrizeBox from "./PrizeBox.vue";
import BlockTitle from "./BlockTitle.vue";
import singerApi from "@/api/activity.js";
import { onMounted, ref } from "vue";
import { i18n, getLang } from "@/i18n/index.js";
const t = i18n.global.t;
const currentTab = ref(0);
const rewardData = ref(null);
const fetchData = async () => {
  const res = await singerApi.singer_rewards();
  if (res.code === 200) {
    rewardData.value = res.data;
  }
};
const formatTag = (item) => {
  const lang = getLang();
  if (item.reward_type === 1) {
    return lang === "ar" ? `${item.num}X` : `X${item.num}`;
  }
  return item.duration > 1
    ? t("common.common_days", [item.duration])
    : t("common.common_day", [item.duration]);
};
onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule-rewards {
  width: 100%;
  padding-top: px2rem(16);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  padding-bottom: px2rem(60);
  background-image: url("@/assets/images/activity/singer/bg.png"),
    url("@/assets/images/activity/singer/bg.png"),
    url("@/assets/images/activity/singer/bg.png");
  background-size: 100% px2rem(434);
  background-repeat: no-repeat;
  background-position: 0 px2rem(0), 0 px2rem(625), 0 px2rem(1305);
  .tabs {
    display: flex;
    width: px2rem(292);
    height: px2rem(38);
    display: inline-flex;
    padding: px2rem(4);
    align-items: center;
    border-radius: px2rem(38);
    background: #2e0c33;

    .tab {
      width: px2rem(138);
      height: px2rem(30);
      flex-shrink: 0;
      border-radius: px2rem(30);
      border: px2rem(2) solid rgba(134, 134, 134, 0.6);
      background: #2e0c33;

      color: #fff;
      text-align: center;
      text-shadow: 0px px2rem(2) px2rem(3) rgba(0, 0, 0, 0.34);
      font-family: Gilroy;
      font-size: px2rem(11);
      font-style: normal;
      font-weight: 500;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      transition: all 0.2s linear;
    }

    .active {
      border: px2rem(2) solid #f4a9ff;
      background: linear-gradient(180deg, #ff61f0 13.41%, #9430ff 77.63%);
    }
  }

  .title {
    color: #fffbe0;
    text-align: center;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #ffa100;
    font-family: Gilroy;
    font-size: px2rem(16);
    font-style: italic;
    font-weight: 900;
    line-height: px2rem(21); /* 131.25% */
    letter-spacing: 1px;
  }
}

.rewards {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: px2rem(281);
  flex-wrap: wrap;
  gap: px2rem(10);

  .reward {
    width: px2rem(87);
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;

    .prize {
      width: px2rem(70);
      height: px2rem(70);
    }

    .prize-name {
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 14px */
      margin-top: px2rem(12);
      width: 100%;
      height: px2rem(24);
    }
  }
}

@for $i from 1 through 7 {
  $values: 16, 22, 28, 34, 42, 50, 60;
  $mt: nth($values, $i);
  .mt-#{$mt} {
    margin-top: px2rem($mt);
  }
}
</style>
