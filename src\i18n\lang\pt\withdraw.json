{"withdrawal_rule_title": "Instruções de saque", "withdrawal_rule_detail": ["1. após uma solicitação de saque bem-sucedida, espera-se que os fundos cheguem dentro de 1 a 7 dias úteis. Se os fundos não chegarem dentro do prazo estimado, confirme se a sua conta de recebimento está correta e aguarde pacientemente ou entre em contato com o atendimento ao cliente.", "2.Diferentes taxas de saque são exigidas com base no valor do saque, sendo que o valor específico está sujeito ao valor real recebido.", "3.Se você cancelar sua conta ou for banido devido a violações durante o período de saque, congelaremos seu pedido de saque."], "withdrawal_select_methods_tips": "A retirada requer a vinculação de seu método de pagamento. Selecione seu método de pagamento e certifique-se de que suas informações de pagamento sejam verdadeiras e válidas.", "service_fee": "taxa de administração: {0} %", "withdrawal_methods": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_information": "Informações de retirada", "withdrawal_info_check": "Por favor, confirme suas informações de saque.", "estimated_withdrawal_amount": "Valor estimado de retirada {0}", "estimated_amount_received": "Valor estimado a ser recebido", "estimated_service_fee": "Taxa de manuseio estimada", "current_selected": "<PERSON><PERSON><PERSON> atual", "completed": "<PERSON><PERSON>", "need_complete": "A ser concluído", "withdrawal_amount": "<PERSON><PERSON> da retirada", "total_withdrawal_amount": "<PERSON><PERSON> acumulado da retirada", "withdrawal_pending": "Retirada em andamento", "withdrawal_success": "<PERSON><PERSON><PERSON> bem-sucedida", "withdrawal_fail": "Falha na retirada", "fail_reason": "Motivo da falha na retirada", "empty": "Nenhum registro de retirada", "BANK_TRANSFER_receive_bank": "Banco do beneficiário", "BANK_TRANSFER_receive_bank_placeholder": "Selecione o banco do beneficiário", "BANK_TRANSFER_fill_info_tips": "Preencha suas informações de pagamento", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "Por exemplo, ***********", "payeeInfo_documentId_hint": "Informar o CPF/CNPJ (número de identificação pessoa fiscal/empresa) ， Digite apenas os números, sem pontos ou traços、letras inglesas", "payeeInfo_address_title": "Endereço", "payeeInfo_email_title": "E-mail", "payeeInfo_email_placeholder": "e.g.1234566{0}gmail.com", "payeeInfo_email_hint": "Por favor, digite o formato correto de e-mail", "payeeInfo_phone_title": "Número de telefone", "WALLET_accountNo_title": "{0} conta", "WALLET_accountNo_placeholder_SA": "e.g.************", "WALLET_accountNo_placeholder_EG": "e.g.0**********", "WALLET_accountNo_placeholder_AE": "e.g.+971-*********", "WALLET_accountNo_placeholder_TR": "e.g.**********", "WALLET_accountNo_placeholder_PH": "e.g.***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "e.g.mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "e.g.***********", "WALLET_accountNo_placeholder_BR_PagBank": "e.g.************{0}123.com", "WALLET_accountNo_placeholder_ID": "Por exemplo ***********", "WALLET_accountNo_placeholder_MY": "Por exemplo **********", "WALLET_accountNo_placeholder_TH": "Por exemplo **********", "WALLET_accountNo_placeholder_VN": "Por exemplo ***********", "WALLET_accountNo_hint_SA": "Digite o número de telefone vinculado à sua carteira, 12 dígitos começando com 9665", "WALLET_accountNo_hint_EG": "Digite o número de telefone vinculado à sua carteira, 11 dígitos começando com 01", "WALLET_accountNo_hint_EG_2": "Ddigite o número de telefone vinculado à sua carteira, 12 dígitos começando com 01 ou código do país mais 10 dígitos", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "Número de telefone do pagador - obrigatório - O número de telefone vinculado à carteira, 11 dígitos começando com 01 ou 201", "WALLET_accountNo_hint_AE": "Digite o número de telefone vinculado à sua carteira. O formato deve ser: +código do país - número de telefone.", "WALLET_accountNo_hint_TR": "Digite o número de telefone vinculado à sua carteira, um número de 10 dígitos ou PL mais 10 dígitos", "WALLET_accountNo_hint_PH": "Digite o número de telefone vinculado à sua carteira, 11 dígitos começando com 09", "WALLET_accountNo_hint_PH_LAZADAPH": "Digite o número de telefone vinculado à sua carteira, 11 dígitos começando com 09 ou e-mail", "WALLET_accountNo_hint_BR_MERCADOPAGO": "Digite a conta de usuário (e-mail ou ID) vinculada à carteira", "WALLET_accountNo_hint_BR_PIX": "Insira a conta que corresponde ao seu tipo de conta Pix ，Se o seu PIX for CPF, CNPJ ou telefone， Digite apenas os números, sem pontos ou traços、letras inglesas", "WALLET_accountNo_hint_BR_PagBank": "Digite o endereço de e-mail vinculado à sua carteira, com até 60 caracteres", "WALLET_accountNo_hint_ID": "Por favor, insira o número da conta vinculada à sua carteira, um número de 9 a 14 dígitos começando com 08", "WALLET_accountNo_hint_MY": "Por favor, insira o número da conta vinculada à sua carteira, um número de 10/11 dígitos começando com 0", "WALLET_accountNo_hint_TH": "Por favor, introduza o número da sua conta ligada à carteira: 10 dígitos a começar por 0", "WALLET_accountNo_hint_VN": "Por favor, insira o número da conta vinculada à sua carteira, um número de 11 dígitos começando com 84", "WALLET_fullName_title": "Nome do beneficiário", "WALLET_fullName_placeholder": "e.g.<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "e.g.<PERSON>", "WALLET_fullName_placeholder_PH": "e.g.<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "e.g.<PERSON>", "WALLET_fullName_hint": "Digite seu nome completo em inglês", "WALLET_lastName_title": "Sobrenome do beneficiário", "WALLET_lastName_placeholder_EG": "ex. <PERSON><PERSON><PERSON>", "WALLET_lastName_hint": "Digite seu sobrenome", "WALLET_accountType_title": "{0}Tipo de conta vinculada", "WALLET_accountType_placeholder_BR_PIX": "Selecione o tipo de conta para vincular", "WALLET_accountType_option_E_PIX": "E-mail", "WALLET_accountType_option_P_PIX": "Telefone ", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (Cadastro de Pessoa Física/Cadastro Nacional da Pessoa Jurídica)", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "ex. 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "Conta SWIFT", "SWIFT_accountNo_placeholder_SA": "e.g.SA44200000**********1234", "SWIFT_accountNo_placeholder_AE": "e.g.AE46009000000**********", "SWIFT_accountNo_placeholder_KW": "e.g.******************************", "SWIFT_accountNo_placeholder_QA": "e.g.*****************************", "SWIFT_accountNo_placeholder_TR": "e.g.TR32001000999990**********", "SWIFT_accountNo_placeholder_BH": "e.g.**********************", "SWIFT_accountNo_placeholder_YE": "e.g.**********", "SWIFT_accountNo_placeholder_IQ": "e.g.***********************", "SWIFT_accountNo_placeholder_IL": "e.g.***********************", "SWIFT_accountNo_placeholder_PS": "e.g.**********", "SWIFT_accountNo_placeholder_AZ": "e.g.AZ77VTBA0000000000**********", "SWIFT_accountNo_placeholder_LB": "e.g.****************************", "SWIFT_accountNo_placeholder_MA": "e.g.**********", "SWIFT_accountNo_placeholder_PH": "e.g.**********", "SWIFT_accountNo_placeholder_BR": "e.g.*****************************", "SWIFT_accountNo_placeholder_EG": "e.g.*****************************", "SWIFT_accountNo_placeholder_JO": "e.g.JO71CBJO000000000000**********", "SWIFT_accountNo_hint_SA": "Informe a conta SWIFT", "SWIFT_accountNo_hint_AE": "Digite sua conta SWIFT, 23 caracteres começando com AE", "SWIFT_accountNo_hint_KW": "Digite sua conta SWIFT, 30 caracteres começando com KW", "SWIFT_accountNo_hint_QA": "Por favor, digite sua conta SWIFT, 29 caracteres começando com QA", "SWIFT_accountNo_hint_TR": "Por favor, digite sua conta SWIFT, 26 caracteres começando com TR", "SWIFT_accountNo_hint_BH": "Por favor, digite sua conta SWIFT, 22 caracteres começando com BH", "SWIFT_accountNo_hint_YE": "Digite sua conta SWIFT, com 34 dígitos e letras ou menos", "SWIFT_accountNo_hint_IQ": "Digite sua conta SWIFT, 23 caracteres começando com IQ", "SWIFT_accountNo_hint_IL": "Por favor, digite sua conta SWIFT, 23 caracteres começando com IL", "SWIFT_accountNo_hint_PS": "Digite sua conta SWIFT, com 34 dígitos e letras ou menos", "SWIFT_accountNo_hint_AZ": "Por favor, digite sua conta SWIFT, 28 caracteres começando com AZ", "SWIFT_accountNo_hint_LB": "Por favor, digite sua conta SWIFT, 28 caracteres começando com LB", "SWIFT_accountNo_hint_MA": "Digite sua conta SWIFT, com 34 dígitos e letras ou menos", "SWIFT_accountNo_hint_PH": "Digite sua conta SWIFT, com 34 dígitos e letras ou menos", "SWIFT_accountNo_hint_BR": "Digite sua conta SWIFT, 29 caracteres começando com BR", "SWIFT_accountNo_hint_EG": "Digite sua conta SWIFT, 29 caracteres começando com EG", "SWIFT_accountNo_hint_JO": "Digite sua conta SWIFT, 30 caracteres começando com JO", "SWIFT_fullName_title": "Nome do beneficiário", "SWIFT_fullName_placeholder": "e.g.<PERSON>", "SWIFT_fullName_hint": "Digite seu nome completo em inglês", "SWIFT_bankCode_title": "Número do banco (código SWIFT)", "SWIFT_bankCode_placeholder": "e.g.SCBLHKHH", "SWIFT_bankCode_hint": "Favor digitar o código do banco, 8 ou 11 dígitos, números e letras maiúsculas são permitidos", "CARD_accountNo_title": "Conta bancária", "CARD_accountNo_placeholder_EG": "e.g. ***********", "CARD_accountNo_placeholder_TR": "e.g. ****************", "CARD_accountNo_hint_EG": "Digite a conta bancária, comprimento>=8", "CARD_accountNo_hint_TR": "Digite o número do cartão bancário de 16 dígitos; somente cartões de débito são aceitos", "CARD_fullName_title": "Nome do beneficiário", "CARD_fullName_placeholder": "e.g.<PERSON>", "CARD_fullName_hint": "Digite seu nome completo em inglês", "CARRIER_BILLING_accountNo_title": "Número de telefone", "CARRIER_BILLING_accountNo_placeholder": "e.g.63**********", "CARRIER_BILLING_accountNo_hint": "Insira número de telefone, por favor.", "CASH_accountNo_title": "{0} conta", "CASH_accountNo_placeholder": "e.g.***********", "CASH_accountNo_hint": "Digite {0} account,11 dígitos começando com 0", "BANK_TRANSFER_accountNo_title_SA": "Conta bancária （IBAN）", "BANK_TRANSFER_accountNo_title_AE": "Conta bancária （IBAN）", "BANK_TRANSFER_accountNo_title_TR": "Conta bancária （IBAN）", "BANK_TRANSFER_accountNo_title": "número da conta bancária", "BANK_TRANSFER_accountNo_title_EG": "Conta bancária", "BANK_TRANSFER_accountNo_title_KW": "Conta bancária", "BANK_TRANSFER_accountNo_title_QA": "Conta bancária", "BANK_TRANSFER_accountNo_title_MA": "Conta bancária（RIB）", "BANK_TRANSFER_accountNo_title_PH": "Conta bancária", "BANK_TRANSFER_accountNo_title_BR": "Conta bancária", "BANK_TRANSFER_accountNo_placeholder_SA": "e.g.************************", "BANK_TRANSFER_accountNo_placeholder_EG": "e.g.************", "BANK_TRANSFER_accountNo_placeholder_AE": "e.g.***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "e.g.******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "e.g.*****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "e.g.**************************", "BANK_TRANSFER_accountNo_placeholder_MA": "e.g.123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "e.g.************", "BANK_TRANSFER_accountNo_placeholder_BR": "e.g.***********", "BANK_TRANSFER_accountNo_placeholder_JO": "e.g.******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "Por exemplo **********", "BANK_TRANSFER_accountNo_placeholder_TH": "Por exemplo *********", "BANK_TRANSFER_accountNo_placeholder_ID": "Por exemplo ***********", "BANK_TRANSFER_accountNo_hint_SA": "Digite uma combinação de SA+22 letras e números", "BANK_TRANSFER_accountNo_hint_EG": "Digite o IBAN e a conta bancária local , IBAN; 29 caracteres (EG+27 dígitos)", "BANK_TRANSFER_accountNo_hint_AE": "Digite AE+21 dígit<PERSON>, AE deve estar em maiúsculas", "BANK_TRANSFER_accountNo_hint_KW": "Digite um comprimento de menos de 50 caracteres, no formato IBAN", "BANK_TRANSFER_accountNo_hint_QA": "Digite um comprimento de menos de 50 caracteres, no formato IBAN", "BANK_TRANSFER_accountNo_hint_TR": "Digite um número que comece com TR mais 24 dígitos, no formato IBAN", "BANK_TRANSFER_accountNo_hint_MA": "Insira o código de conta RIB de 24 dígitos.", "BANK_TRANSFER_accountNo_hint_PH": "Por favor, digite de 6 a 128 dígitos", "BANK_TRANSFER_accountNo_hint_BR": "Por favor, digite de 4 a 15 dígitos", "BANK_TRANSFER_accountNo_hint_JO": "Digite uma combinação de 30 caracteres de números e letras começando com JO", "BANK_TRANSFER_accountNo_hint_MY": "Insira um número de conta com até 35 dígitos.", "BANK_TRANSFER_accountNo_hint_TH": "Por favor, introduza um número de conta de 10 a 18 dígitos", "BANK_TRANSFER_accountNo_hint_ID": "Por favor, insira o número da conta numérica", "BANK_TRANSFER_bankBranch_title": "Número da agência bancária", "BANK_TRANSFER_bankBranch_placeholder_BR": "e.g.1234", "BANK_TRANSFER_bankBranch_hint_BR": "Por favor, insira o número da agência bancária, de 4 a 6 dígitos", "BANK_TRANSFER_address_placeholder_SA": "e.g.aabbcccccccccc", "BANK_TRANSFER_address_placeholder_AE": "e.g.ALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "e.g.kuwait xx rua", "BANK_TRANSFER_address_hint_SA": "Digite o endereço, com um limite de comprimento de 10 a 200 caracteres, permitindo apenas números, pontos, letras maiúsculas e minúsculas e espaços.", "BANK_TRANSFER_address_hint_AE": "Digite o endereço, com limite de comprimento de 1 a 200 caracteres, o banco realizará verificações de controle de risco.", "BANK_TRANSFER_address_hint_KW": "Por favor, digite seu endereço, comprimento<=255", "BANK_TRANSFER_fullName_placeholder_TR": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_PH": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_BR": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_MA": "e.g.<PERSON>", "BANK_TRANSFER_fullName_placeholder_KW": "e.g.<PERSON>", "BANK_TRANSFER_phone_placeholder_PH": "e.g.***********", "BANK_TRANSFER_phone_placeholder_BR": "e.g.*************", "BANK_TRANSFER_phone_hint_PH": " Por favor, digite seu número de telefone celular, de 0 a 9 dígitos, começando com 11 (o 0 deve ser adicionado) ", "BANK_TRANSFER_phone_hint_BR": " ‘Favor digitar seu número de telefone celular, de 12 a 13 dígitos, começando com 55", "BANK_TRANSFER_bankCode_title_MY": "Número do banco (código SWIFT) ", "BANK_TRANSFER_bankCode_placeholder_MY": "Por exemplo, CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "Por favor, insira uma combinação de 8 a 11 dígitos e letras", "BANK_TRANSFER_bankBranch_title_SA": "Código SWIFT", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "Please enter a combination of 8 or 11 uppercase letters and numbers", "BANK_TRANSFER_city_title_SA": "Cidade", "BANK_TRANSFER_city_placeholder_SA": "Por exemplo: Nova York", "BANK_TRANSFER_city_hint_SA": "Digite o nome da cidade, com até 35 caracteres.", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "Por exemplo: 0011232111111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "Insira uma combinação de 24 dígitos", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "Data de nascimento", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "Por exemplo: ********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "Insira sua data de nascimento", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "Tipo de documento", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "Selecione o tipo de identificação", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "Número de identificação ", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "Por exemplo: NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "Por exemplo: ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "Por favor, insira seu número de identificação.", "AGENT_accountNo_Whatsapp_title_ALL": "Número de WhatsApp", "AGENT_accountNo_Whatsapp_placeholder_ALL": "<PERSON><PERSON> exemplo, +90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "Por favor, insira a sua conta Whatsapp", "AGENT_accountNo_vodafone_title_EG": "Conta de Vodafone", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "Nome de blockchain", "USDT_address_title_ALL": "Endereço de emissão de moeda", "USDT_address_placeholder_ALL": "Por exemplo, 11111111111111111111111111", "USDT_address_hint_ALL": "Por favor, introduza um endereço começado por 1, 3 ou bc", "USDT_address_hint_common": "Por favor, introduza o seu endereço USDT"}