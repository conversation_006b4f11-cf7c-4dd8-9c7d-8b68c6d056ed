<template>
  <div class="tab">
    <div
      :class="{ active: modelValue === item.value }"
      v-for="item in options"
      :key="item.value"
      @click="emits('update:modelValue', item.value)"
      v-track:click
      :trace-key="item.traceKey"
    >
      {{ item.label }}
    </div>
  </div>
</template>
<script setup>
import { computed } from "vue";

const props = defineProps({
  options: {
    type: Array,
    default: () => [],
  },
  modelValue: {
    default: "",
  },
});
const emits = defineEmits(["update:modelValue"]);
const columns = computed(() => props.options.length);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.tab {
  display: grid;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: px2rem(4);
  grid-template-columns: repeat(v-bind(columns), 1fr);

  /* T15/B */
  border-radius: px2rem(100);
  background: #c9f0ff;
  & > div {
    color: rgba(8, 60, 68, 0.48);
    text-align: center;
    font-family: <PERSON>roy;
    font-size: px2rem(15);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    display: flex;
    justify-content: center;
    align-items: center;
    height: px2rem(34);
  }
  .active {
    transition: background-color 0.25s;
    display: flex;
    padding: px2rem(8) px2rem(12);
    justify-content: center;
    align-items: center;
    gap: px2rem(4);
    flex: 1 0 0;
    border-radius: px2rem(100);
    background-color: #fff;
    color: #083c44;
  }
}
</style>
