<template>
  <div class="container flex">
    SM
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed } from "vue";
import { i18n } from "@/i18n/index.js";
import baseForAppApi from "@/api/baseforapp.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const pkList = ref([
  {
    title: t("rules.pk_value"),
  },
  {
    title: t("rules.pk_redWeapon"),
  },
  {
    title: t("rules.pk_blueWeapon"),
  },
]);

onMounted(async () => {
  const getUserInfoResult = await proxy.$siyaApp("getUserInfo");
  baseForAppApi
    .pk_rule({ room_id: getUserInfoResult.data.roomId })
    .then((res) => {
      const list = res.data?.items.map((item, index) => {
        return {
          title: pkList.value[index]?.title || "",
          infos: `${item.minValue}-${item.maxValue}`,
          redWeapon: item.redWeapon.pic,
          blueWeapon: item.blueWeapon.pic,
        };
      });
      pkList.value = list;
    });
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  background-image: url("@/assets/images/rules/pk/bg.jpg"),
    linear-gradient(#a87cc6, #a87cc6);
  background-repeat: no-repeat, no-repeat;
  background-position: top center, top center;
  color: #fff;
  flex-direction: column;
  height: 100vh;
  .close {
    width: px2rem(24);
    height: px2rem(24);
  }
  .content {
    flex: 1;
    overflow-y: scroll;
    padding-bottom: px2rem(10);
    position: relative;
  }
  .rule-content {
    padding: px2rem(8) px2rem(16);
    margin-bottom: px2rem(10);
    font-size: $fontSize13;
    line-height: 124%;
  }
  .pk-content {
    padding: 0 px2rem(24);
    width: 100vw;
  }
}
table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: px2rem(8);
  border: 0.5px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 0px 80px 0px rgba(161, 0, 255, 0.4) inset;
  background: #302e51;
  position: relative;
  width: 100%;
}
.table-tr {
  text-align: center;
  font-size: $fontSize11;
  font-weight: 700;
  width: 100%;
  color: #fff;
}
.table-tr th {
  color: rgba(255, 255, 255, 0.7);
  font-size: px2rem(10);
  font-weight: 400;
}
.table-tr th,
.table-tr td {
  position: relative;
  padding: px2rem(12) px2rem(8);
  width: 33.3%;
}

/* 给单元格添加右侧边框 */
.table-tr td:not(:last-child),
.table-tr th:not(:last-child) {
  border-right: 0.5px solid rgba(255, 255, 255, 0.1);
}

/* 给单元格添加底部边框（包含 th） */
.table-tr:not(:last-child) td,
.table-tr:not(:last-child) th {
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
}

::v-deep(.header-bar .header-bar-fixed .header-content .title) {
  text-align: center;
}
</style>
