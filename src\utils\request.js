import axios from "axios";

const baseURL = process.env.VITE_API_HOST;

const service = axios.create({
  baseURL: `${baseURL}`, // 设置统一的请求前缀
  timeout: 10_000, // 设置统一的超时时长
});

// 请求拦截
service.interceptors.request.use((config) => {
  let token = localStorage.getItem("siya_token");
  // 测试环境H5 写死token
  if (!token && ["development"].includes(process.env.VITE_PROGRESS_ENV)) {
    token =
      "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************.UHxKV3x5-DEPhcnJb0x7LsolEF42SXvypZLGLjsp_eLVZYfdfDxjcT8y2ARZkrU14EVpsMnNcrJNhRSwtQNmiER6l8iXZJc3F5rjP7fyAd4cTL1VBmBiADN05S2qDRVia18aEeY4f3tZJTECEcy7ZmtqUFjFp_lKJGzMR-iedltwttHgbRIUdpHcWruWhQquG6AKTFGCb6XuCsaCX1TOsgzAbHwGabsocom4VkE5DIK2HO0wzkEC524YhwjfzUzfUpoC5lq9SND7WKpLlugfUjGWZLS7geI0gayj8laVz82Vw21h4zyZhep9FaWpvkbyTdiREvOA45GAlzJqHb0hpg";
  }
  if (!!token) {
    token = `sy ${token}`;
    config.headers.Authorization = token;
  }

  const version = localStorage.getItem("siya_version");
  if (version) {
    config.headers["X-App-Version"] = version;
  }

  const appOS = localStorage.getItem("siya_os");
  if (appOS) {
    config.headers["X-App-Os"] = appOS;
  }

  // cloned
  config.headers["X-App-Cloned"] = localStorage.getItem("siya_cloned");

  // lang
  config.headers["Accept-Language"] = localStorage.getItem("siya_lang");

  return config;
});

// response 拦截器
service.interceptors.response.use(
  (response) => {
    // 这里处理一些response 正常放回时的逻辑
    const res = response.data;
    if (res.code === 401) {
      showToast("登录过期");
      localStorage.clear();
    }

    // 统一处理Toast 需自定义处理设置customErrorToast=true
    const config = response.config;
    if (!config?.meta?.customErrorToast && res.code !== 200) {
      showToast(res.msg);
    }

    return res;
  },
  (error) => {
    let msg = "";
    if (
      error.response &&
      (error.response.status === 401 || error.response.data.code === 401)
    ) {
      localStorage.clear();
      msg = "Verification Expired";
    } else if (!error.response) {
      if (error.toString().includes("Network Error")) {
        msg = "Network Error";
      } else if (error.toString().includes("timeout")) {
        msg = "Network Timeout";
      } else {
        msg = "Server Error";
      }
    } else {
      msg = "Server Not Response";
    }

    //主动取消请求不提示
    if (!error.__proto__.__CANCEL__) {
      showToast(msg);
      return Promise.reject(error);
    }
  }
);

export default service;
