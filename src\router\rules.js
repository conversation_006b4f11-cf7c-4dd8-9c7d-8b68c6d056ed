const routes = [
  {
    path: '/rules/homeowner',
    name: 'RulesHomeowner',
    meta: {
      fullScreen: false,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "RulesHomeowner"*/ '../views/rules/homeowner/Index.vue')
  },
  {
    path: '/rules/pk',
    name: 'RulesPK',
    meta: {
      fullScreen: false,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "RulesPK"*/ '../views/rules/pk/Index.vue')
  },
  {
    path: '/rules/pkMic',
    name: 'RulesPKMic',
    meta: {
      fullScreen: false,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "RulesPKMic"*/ '../views/rules/pkMic/Index.vue')
  },
  {
    path: '/rules/sm-match-wait-duration',
    name: 'SmMatchWaitDuration',
    meta: {
      fullScreen: false,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "SmMatchWaitDuration"*/ '../views/rules/sm-match-wait-duration/Index.vue')
  },
  {
    path: '/rules/face',
    name: 'RulesFace',
    meta: {
      fullScreen: true,
      keepAlive: true,
    },
    component: () => import(/*webChunkName: "RulesFace"*/ '../views/rules/face/Index.vue')
  }
]

export default routes;
