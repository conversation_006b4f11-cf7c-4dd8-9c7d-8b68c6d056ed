<template>
  <div class="app-container flex daily-ranking">
    <header>
      <header-bar
        :title="$t('ranking.ranking_jack_detail')"
        font-color="#fff"
        close-type="close"
        fixedBgColor="#190f2c"
        closeType="back"
      />
    </header>
    <div class="content">
      <div class="rule">
        <div
          class="rule-item"
          v-for="(item, index) in $t('ranking.ranking_jack_list').split('\n')"
          :key="index"
        >
          <span>·</span>
          <gap :gap="4" />
          <span>{{ item.slice(2) }}</span>
        </div>
      </div>
      <div class="rule-title flex">
        <img
          class="front-dot"
          src="@/assets/images/daily-ranking/dot-title-left.svg"
          alt=""
        />
        <span>{{ $t("ranking.ranking_bonus") }}</span>
        <img
          class="back-dot"
          src="@/assets/images/daily-ranking/dot-title-right.svg"
          alt=""
        />
      </div>
      <div class="reward-wrap">
        <div class="reward-item flex">
          <div class="cell-wrap sort flex">
            {{ $t("ranking.ranking_ranking") }}
          </div>
          <div class="cell-wrap title flex">
            {{ $t("ranking.ranking_bonus") }}
          </div>
        </div>
        <div
          class="reward-item flex"
          v-for="(item, index) in rewardList"
          :key="index"
        >
          <div
            class="cell-wrap sort flex"
            :style="{ color: levelColor(index + 1) }"
          >
            {{ item.rank }}
          </div>
          <div class="cell-wrap flex">
            <img :src="item.icon" alt="" />
            <div class="reward-time">{{ item.duration }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { i18n } from "@/i18n/index.js";
import img1 from "@/assets/images/ranking/lucky-gift/second1.png";
import img2 from "@/assets/images/ranking/lucky-gift/second2.png";
import img3 from "@/assets/images/ranking/lucky-gift/second3.png";
import rankingApi from "@/api/ranking.js";

const t = i18n.global.t;

const rewardList = ref([]);

const levelColor = (val) => {
  if (val === 1) return "#FFC26A";
  if (val === 2) return "#ff8b00";
  if (val === 3) return "#968881";
};

const getReward = async () => {
  const res = await rankingApi.rank_reward();
  let list = res.data.jackpot || [];
  if (!list.length) return;
  rewardList.value = list;
};
onMounted(() => {
  getReward();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

$pageBgColor: #190f2c;

$borderStyle: 0.5px solid #ffffff1f;
header {
  height: 100px;
}
.app-container {
  flex-direction: column;
  background-color: $pageBgColor;
  .content {
    color: #fff;
    font-size: $fontSize13;
    padding: px2rem(8) px2rem(20);
    .rule {
      .rule-item {
        display: flex;
        font-family: Gilroy;
        font-style: normal;
        font-weight: 500;
      }
    }
    .rule-title {
      justify-content: center;
      margin-bottom: $gap4;
      font-weight: $fontWeightBold;
      text-align: center;
      margin-top: px2rem(24);

      @include fontSize17;

      .front-dot,
      .back-dot {
        width: px2rem(34);
        height: px2rem(8);
      }
      span {
        padding: 0 px2rem(16);
        max-width: 65%;
        display: inline-block;
      }
    }
    .reward-wrap {
      border-radius: px2rem(8);
      border: $borderStyle;
      margin: px2rem(16) 0 px2rem(32) 0;

      .reward-item {
        @include fontSize11;
        border-bottom: $borderStyle;
        font-weight: 500;
        .cell-wrap {
          flex: 1;
          height: px2rem(100);
          flex-direction: column;
          justify-content: center;
          border-left: $borderStyle;
          min-width: 0;
          img {
            width: px2rem(54);
            height: px2rem(54);
          }
          .reward-name {
            padding: px2rem(2) px2rem(4);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
          }
          .reward-time {
            color: #ffffff80;
            margin-top: px2rem(4);
          }
          &:first-child {
            border-left: none;
          }
        }
        .title {
          font-size: px2rem(14);
          font-weight: 700;
        }
        .sort {
          width: 40%;
          font-size: px2rem(14);
          flex: none;
          font-weight: 700;
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
