<template>
  <div>
    <van-popup
      v-model:show="showBottom"
      position="bottom"
      class="copy-popup"
      :z-index="1001"
    >
      <div class="title-bg"></div>
      <div class="desc">{{ $t("invite.invite_code_copied") }}</div>
      <div class="code">{{ inviteCode }}</div>
      <div class="rule">
        <div class="item">
          <div class="index">1</div>
          <gap :gap="4"></gap>
          <div class="text">{{ $t("invite.share_step_1") }}</div>
        </div>
        <div class="item">
          <div class="index">2</div>
          <gap :gap="4"></gap>
          <div class="text">{{ $t("invite.share_step_2") }}</div>
        </div>
      </div>
      <div class="poster">
        <div class="cell">
          <div>{{ $t("invite.invite_code") }}</div>
          <div>
            <svg-icon icon="icon-arrow-right"></svg-icon>
          </div>
        </div>
        <div class="dialog">
          <div class="title">{{ $t("invite.invite_code") }}</div>
          <div class="input">{{ inviteCode }}</div>
          <div class="confirm">{{ $t("common.common_ok") }}</div>
          <div class="cancel">{{ $t("common.common_cancel") }}</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref } from "vue";
const props = defineProps({
  inviteCode: {
    type: String,
    default: "-",
  },
});

const showBottom = ref(false);

const open = () => {
  showBottom.value = true;
};

const close = () => {
  showBottom.value = false;
};

defineExpose({
  open,
  close,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.copy-popup {
  border-radius: px2rem(26) px2rem(26) 0px 0px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, #ffebcc 100%);
  background-size: 100% px2rem(80);
  background-repeat: no-repeat;
  background-color: #fff;
  overflow: visible;
  padding: 0 px2rem(16);

  .title-bg {
    width: px2rem(216);
    height: px2rem(100);
    margin: 0 auto;
    background-image: url("@/assets/images/invite/copy-title-bg.png");
    background-size: 100% 100%;
    margin-top: px2rem(-50);
  }

  .desc {
    margin-top: px2rem(14);
    color: #642628;
    text-align: center;

    /* T20/B */
    font-family: Gilroy;
    font-size: px2rem(20);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    line-height: px2rem(24);
  }

  .code {
    display: flex;
    padding: px2rem(8) px2rem(32);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(12);
    border: px2rem(1) solid var(---C2, #ff4771);
    background: #fff0f4;
    color: var(---C2, #ff4771);
    text-align: center;
    width: fit-content;
    margin: 0 auto;
    margin-top: px2rem(16);

    /* T32/B */
    font-family: Gilroy;
    font-size: px2rem(32);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  .rule {
    margin-top: px2rem(24);
    .item {
      display: flex;
      align-items: flex-start;
      gap: px2rem(2);
      align-self: stretch;
      margin-bottom: px2rem(12);
      .index {
        display: flex;
        align-items: center;
        justify-content: center;
        width: px2rem(20);
        height: px2rem(20);
        flex-shrink: 0;
        border-radius: px2rem(20);
        background: #f9e9b2;
        color: #642628;
        text-align: center;

        /* T11/B */
        font-family: Gilroy;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
      }
      .text {
        color: #7e4141;

        /* T15/R */
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 500;
        line-height: 128%; /* 19.2px */
      }
    }
  }
  .poster {
    border-radius: px2rem(12);
    background-color: var(---B7, #f9f9f9);
    min-height: px2rem(285);
    width: px2rem(358);
    align-self: stretch;
    margin-top: px2rem(16);
    margin-bottom: px2rem(42);
    position: relative;
    background-image: url("@/assets/images/invite/code-example-1.png"),
      url("@/assets/images/invite/code-example-2.png");
    background-size: px2rem(139) px2rem(302);
    background-repeat: no-repeat;
    background-position: px2rem(22) px2rem(29), px2rem(195) px2rem(29);

    .cell {
      display: flex;
      width: px2rem(172);
      height: px2rem(29);
      position: absolute;
      top: px2rem(143);
      left: px2rem(5);
      padding: px2rem(5) px2rem(6);
      align-items: center;
      gap: px2rem(6);
      flex-shrink: 0;
      border-radius: px2rem(6);
      border: px2rem(1) solid #ff4d96;
      background: #fff;
      box-shadow: 0px 0px px2rem(8) 0px rgba(45, 35, 35, 0.32);
      & div:first-child {
        color: var(---B0-Black, #000);
        font-family: Gilroy;
        font-size: px2rem(8);
        text-align: left;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        flex-grow: 1;
      }
      & div:last-child {
        font-size: px2rem(10);
        color: #cccccc;
      }
    }

    .dialog {
      display: flex;
      width: px2rem(108);
      padding: px2rem(6);
      flex-direction: column;
      align-items: center;
      gap: px2rem(4);
      border-radius: px2rem(8);
      background: #fff;
      position: absolute;
      top: px2rem(115);
      right: px2rem(40);
      .title {
        color: var(---B0-Black, #000);
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(6);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-top: px2rem(6);
      }
      .input {
        display: flex;
        width: px2rem(97);
        height: px2rem(18);
        padding: 0px px2rem(4);
        align-items: center;
        gap: px2rem(2);
        border-radius: px2rem(4);
        background: var(---B6, #f2f2f2);
        color: var(---B0-Black, #000);
        font-family: Gilroy;
        font-size: px2rem(6);
        font-style: normal;
        font-weight: 500;
        line-height: 132%; /* 8.132px */
        margin-top: px2rem(4);
        flex-shrink: 0;
      }
      .confirm {
        display: flex;
        width: px2rem(97);
        height: px2rem(18);
        padding: px2rem(5) px2rem(2);
        justify-content: center;
        align-items: center;
        gap: px2rem(2);
        border-radius: px2rem(5);
        background: var(---C1, #5f56ed);
        color: var(---White, #fff);
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(6);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-top: px2rem(9);
      }
      .cancel {
        color: var(---B3, #999);
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(6);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-top: px2rem(7);
      }
    }
  }
}

.rtl-html {
  .cell {
    flex-direction: row-reverse;
  }
}
</style>
