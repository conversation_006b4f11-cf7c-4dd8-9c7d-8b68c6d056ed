<template>
  <div class="block">
    <block-box :title="$t(`promoter_star.video_material`)" :icon="Icon">
      <div class="block-content">
        <sub-title :title="$t(`promoter_star.tag_list`)">
          <div class="change-button" @click="getTags()">
            <div class="icon-change"></div>
            {{ $t(`promoter_star.change_one`) }}
          </div>
        </sub-title>
        <div class="tag-list">
          <div class="tag-item" v-for="tag in tags" :key="tag">
            <div class="tag-content">{{ tag }}</div>
            <div
              class="copy-btn"
              @click="copyText(tag, $event)"
              v-track:click
              trace-key="star_ambassador_tag_copy"
            >
              <img src="@/assets/images/icon/icon-copy.svg" />
            </div>
          </div>
        </div>
        <sub-title :title="$t(`promoter_star.promotional_copy`)">
          <div
            class="change-button"
            @click="getPromotionals()"
            v-track:click
            trace-key="star_ambassador_text_switch"
          >
            <div class="icon-change"></div>
            {{ $t(`promoter_star.change_one`) }}
          </div>
        </sub-title>
        <div class="tag-list">
          <div
            class="tag-item"
            v-for="promotional in promotionals"
            :key="promotional"
          >
            <div class="tag-content">{{ promotional }}</div>
            <div
              class="copy-btn"
              @click="copyText(promotional, $event)"
              v-track:click
              trace-key="star_ambassador_text_copy"
            >
              <img src="@/assets/images/icon/icon-copy.svg" />
            </div>
          </div>
        </div>
        <sub-title :title="$t(`promoter_star.siya_logo`)"> </sub-title>
        <div class="logo-box">
          <img class="logo" :src="Logo" />
          <gap :gap="24"></gap>
          <div
            class="download"
            @click="saveLogo()"
            v-track:click
            trace-key="star_ambassador_logo_download"
          >
            <img
              class="icon-download"
              src="@/assets/images/icon/icon-download.svg"
              alt=""
            />
            <div class="download-text">
              {{ $t(`promoter_star.click_to_save_logo`) }}
            </div>
          </div>
        </div>
        <sub-title :title="$t(`promoter_star.video_example`)"></sub-title>
        <div class="video-list">
          <div class="item" v-for="vdieo in videoList">
            <VideoPlayer :url="vdieo.url" :cover="vdieo.cover"></VideoPlayer>
          </div>
        </div>
      </div>
    </block-box>
  </div>
</template>
<script setup>
import BlockBox from "./BlockBox.vue";
import VideoPlayer from "./VideoPlayer.vue";
import Icon from "@/assets/images/promoter-star/icon-3.png";
import clipboard from "@/utils/clipboard";
import { i18n } from "@/i18n/index.js";
import { downloadFile, urlToBase64 } from "@/utils/util.js";

import { getTagList, getPromotionalList, getVideoList } from "../data.js";
import { onMounted, ref, getCurrentInstance, computed } from "vue";

import Logo from "@/assets/images/logo/logo.png";
import SubTitle from "./SubTitle.vue";

const { proxy, appContext } = getCurrentInstance();
const t = i18n.global.t;

// 是否在APP内
const isApp = computed(() => {
  return appContext?.config.globalProperties.$isApp;
});

const copyText = (text, event) => {
  const msg = t("guild.common_copy_success");
  clipboard(text, event, msg);
};

const tags = ref([]);
const getTags = () => {
  tags.value = getTagList();
};

const promotionals = ref([]);
const getPromotionals = () => {
  promotionals.value = getPromotionalList();
};

const videoList = ref([]);
const getVideos = () => {
  videoList.value = getVideoList();
};

const saveLogo = () => {
  if (isApp.value) {
    urlToBase64(Logo).then((res) => {
      console.log(res);
      proxy.$siyaApp("saveImage", {
        image: res,
      });
    });
  } else {
    downloadFile(Logo, `logo.png`);
  }
};

onMounted(() => {
  getTags();
  getPromotionals();
  getVideos();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.flex-bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.block {
  padding: 0 px2rem(12);
  padding-top: px2rem(24);
  .block-content {
    .change-button {
      color: #3097b4;

      /* T15/R */
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 500;
      line-height: 128%; /* 19.2px */

      display: flex;
      padding: 4px 8px;
      align-items: center;
      gap: 2px;

      border-radius: 100px;
      background: #f9f9f9;

      .icon-change {
        background-image: url("@/assets/images/icon/icon-change.svg");
        background-size: 100% 100%;
        width: px2rem(20);
        height: px2rem(20);
      }
    }
    .tag-list {
      margin-top: px2rem(12);
      padding-bottom: px2rem(14);
      .tag-item {
        padding: px2rem(8) px2rem(12);
        border-radius: px2rem(12);
        background: #e2f7ff;
        margin-bottom: px2rem(12);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tag-content {
          flex-grow: 1;
          color: #145057;

          /* T17/R */
          font-family: Gilroy;
          font-size: px2rem(17);
          font-style: normal;
          font-weight: 500;
          line-height: 132%; /* 22.44px */
        }

        .copy-btn {
          font-size: px2rem(20);
          flex-shrink: 0;
          img {
            width: px2rem(20);
            height: px2rem(20);
          }
        }
      }
    }
    .logo-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: px2rem(12);
      margin-bottom: px2rem(32);
      border-radius: px2rem(12);
      background: #f9f9f9;
      padding: px2rem(24) 0;
      .logo {
        width: px2rem(88);
        height: px2rem(88);
      }
      .download {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: px2rem(160);
        .icon-download {
          width: px2rem(20);
          height: px2rem(20);
        }
        .download-text {
          color: #145057;
          text-align: center;
          margin-top: px2rem(4);

          /* T13/B */
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 700;
          line-height: normal;
        }
      }
    }
    .video-list {
      margin-top: px2rem(12);
      .item {
        height: px2rem(540);
      }
    }
  }
}
</style>
