<template>
  <div class="app-container flex milestone">
    <header>
      <header-bar
        :title="$t('ranking.ranking_weekly_star_milestone_title')"
        is-scroll-change-bg
        @emit:scrollChangeBgShow="scrollChangeBgShow"
        font-color="#fff"
        observer-root-margin="1px"
        fixed-bg-color="#ffffff10"
        :opacity-rate="100"
      >
      </header-bar>
    </header>
    <main>
      <div class="tab-wrap flex">
        <div
          class="tab"
          :class="{ 'active-tab': currentTab === 1 }"
          @click="tabChange(1)"
          v-track:click|exposure
          trace-key="click_weekly_star_reward"
        >
          {{ $t("ranking.ranking_weekly_star_rewards") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="tab"
          :class="{ 'active-tab': currentTab === 2 }"
          v-track:click
          trace-key="click_weekly_star_rule"
          @click="tabChange(2)"
        >
          {{ $t("ranking.ranking_weekly_star_rules") }}
        </div>
      </div>
      <div v-show="currentTab === 1">
        <section>
          <title-bar :title="$t('ranking.ranking_weekly_star_rewards_title')" />
          <div
            class="content-wrap flex"
            v-for="(item, idx) in rewardData?.receive_star_rewards"
            :key="idx"
          >
            <img
              :src="getImageUrl(`top${idx + 1}-text.png`)"
              alt=""
              class="top"
            />
            <div class="reward-wrap flex">
              <div
                class="gift-wrap flex"
                v-for="(subItem, subIdx) in item"
                :key="subIdx"
              >
                <div class="duration" v-if="subItem.duration">
                  {{ subItem.duration }}
                </div>
                <img :src="subItem.icon" alt="" class="gift-img" />
                <div class="bottom flex">
                  <span>{{ subItem.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        <section>
          <title-bar :title="$t('ranking.ranking_weekly_star_rules_title')" />
          <div
            class="content-wrap flex"
            v-for="(item, idx) in rewardData?.send_star_rewards"
            :key="idx"
          >
            <img
              :src="getImageUrl(`top${idx + 1}-text.png`)"
              alt=""
              class="top"
            />
            <div class="reward-wrap flex">
              <div
                class="gift-wrap flex"
                v-for="(subItem, subIdx) in item"
                :key="subIdx"
              >
                <div class="duration" v-if="subItem.duration">
                  {{ subItem.duration }}
                </div>
                <img :src="subItem.icon" alt="" class="gift-img" />
                <div class="bottom flex">
                  <!-- <img src="@/assets/images/common/coin.png" alt="" />
                  <gap :gap="2"></gap> -->
                  <span>{{ subItem.name }}</span>
                </div>
              </div>
            </div>
            <div class="addtional-reward" v-if="idx === 0">
              <div class="top-wrap flex">
                <span>
                  {{ $t("ranking.ranking_weekly_star_badge_rewards") }}
                </span>
                <gap :gap="2"></gap>
                <img
                  src="@/assets/images/ranking/weekly-star/question-icon.png"
                  alt=""
                  @click="toggleHintDialog(true)"
                />
              </div>
              <div class="reward-item flex">
                <div class="cell">
                  {{ $t("ranking.ranking_weekly_star_top_obtained") }}
                </div>
                <div class="border"></div>
                <div class="cell">
                  {{ $t("ranking.ranking_weekly_star_badge_rewards") }}
                </div>
              </div>
              <div
                class="reward-item flex"
                v-for="(subItem, subIdx) in rewardData.send_top_medal"
                :key="subIdx"
              >
                <div class="cell">
                  <img
                    src="@/assets/images/ranking/weekly-star/cup.png"
                    alt=""
                    class="cup-img"
                  />
                  <span> x {{ subItem.condition }}</span>
                </div>
                <div class="border"></div>
                <div class="cell">
                  <img :src="subItem.icon" alt="" class="reward-img" />
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <div v-show="currentTab === 2" class="rules-wrap">
        <div class="rule-item">
          {{
            $t("ranking.ranking_weekly_star_milestone_rule1", [ruleParams?.utc])
          }}
        </div>
        <div class="rule-item">
          {{ $t("ranking.ranking_weekly_star_milestone_rule2") }}
          <div
            class="rule-sub-item flex"
            v-for="(subItem, i) in ruleParams?.gifts"
            :key="i"
          >
            <img :src="subItem.icon" alt="" />: <gap :gap="8"></gap>
            {{ subItem.num }}
          </div>
        </div>
        <div
          class="rule-item"
          v-html="
            $t('ranking.ranking_weekly_star_milestone_rule3', [
              `<img src='${getImageUrl(
                'coin.png'
              )}' style='width:12px; height:12px; margin:0 2px' />`,
              `<img src='${getImageUrl(
                'star-value.png'
              )}' style='width: 12px; margin:0 2px' />`,
            ])
          "
        ></div>
        <div class="rule-item">
          {{ $t("ranking.ranking_weekly_star_milestone_rule4") }}
        </div>
        <div class="rule-item">
          {{ $t("ranking.ranking_weekly_star_milestone_rule5") }}
        </div>
      </div>
    </main>
  </div>
  <rule-modal
    :show="hintDialogShow"
    @confirm="toggleHintDialog(false)"
    @update:show="toggleHintDialog"
  >
  </rule-modal>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import rankingApi from "@/api/ranking.js";
import RuleModal from "./components/RuleModal.vue";
import TitleBar from "./components/TitleBar.vue";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;
const currentTab = ref(1);
const rewardData = ref();
const ruleParams = ref();

const tabChange = (tab) => {
  currentTab.value = tab;
};

const hintDialogShow = ref(false);
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool;
};

const scrollChangeBgShow = (show) => {
  const targetBgEl = document.querySelector(".milestone .header-bar-bg");
  targetBgEl.style["backdrop-filter"] = "blur(10px)";
  targetBgEl.style["-webkit-backdrop-filter"] = "blur(10px)";
};

const fetchData = async () => {
  const result = await rankingApi.week_star_reward();
  rewardData.value = result.data;
};

const fetchRuleParams = () => {
  rankingApi.week_star_rule().then((res) => {
    ruleParams.value = res.data;
  });
};

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/ranking/weekly-star/${name}`,
    import.meta.url
  ).href;
}

fetchRuleParams();
fetchData();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: $white;
  flex-direction: column;
  background: url("@/assets/images/ranking/weekly-star/milestone-bg-first.png")
      top left no-repeat,
    url("@/assets/images/ranking/weekly-star/milestone-bg.png") top left repeat;
  background-color: #190a33;
  background-size: 100% auto;
  header {
    width: 100%;
  }
  main {
    width: 100%;
    padding: 0 px2rem(16) px2rem(70);
    .tab-wrap {
      flex-direction: row;
      font-size: $fontSize13;
      justify-content: center;
      color: $white;
      font-weight: $fontWeightBold;
      padding: px2rem(10) 0;
      .tab {
        height: px2rem(34);
        width: px2rem(167);
        text-align: center;
        line-height: px2rem(34);
        background-image: url("@/assets/images/ranking/weekly-star/tab-inactive.png");
        background-repeat: no-repeat;
        background-size: 100%;
      }
      .active-tab {
        text-shadow: 0px 0px 6px #ff98fa;
        background-image: url("@/assets/images/ranking/weekly-star/tab-active.png");
      }
    }
    section {
      .content-wrap {
        flex-direction: column;
        align-items: center;
        .top {
          height: px2rem(26);
          margin: px2rem(8) 0 px2rem(4);
        }

        .reward-wrap {
          flex-wrap: wrap;
          width: px2rem(340);
          justify-content: center;
          .gift-wrap {
            background-image: url("@/assets/images/ranking/weekly-star/reward-bg.png");
            background-size: 100%;
            background-repeat: no-repeat;
            width: px2rem(70);
            height: px2rem(70);
            position: relative;
            flex-direction: column;
            flex-wrap: nowrap;
            text-align: center;
            justify-content: center;
            align-items: center;
            margin: 0 px2rem(12) px2rem(12);
            .duration {
              position: absolute;
              top: 0;
              right: 0;
              height: px2rem(10);
              min-width: px2rem(20);
              max-width: px2rem(62);
              font-size: px2rem(8);
              padding: 0 px2rem(10);
              line-height: px2rem(10);
              color: #9b401f;
              border-radius: 0px 8px;
              background: linear-gradient(90deg, #ffea64 0%, #ffd395 100%),
                linear-gradient(90deg, #ffca64 0%, #ff976b 100%);
              text-align: center;
            }
            .gift-img {
              width: px2rem(42);
              height: px2rem(42);
            }
            .bottom {
              img {
                width: px2rem(12);
                height: px2rem(12);
              }
              span {
                color: $white;
                font-size: px2rem(10);
                word-break: break-all;
                padding: 0 px2rem(2);
              }
            }
          }
        }
        .addtional-reward {
          position: relative;
          width: px2rem(280);
          border: 0.5px solid #ffe15ecf;
          box-shadow: 0px 0px 20px 0px #ff800066;
          box-shadow: 0px 0px 30.1px 0px #fff60040 inset;
          border-radius: px2rem(8);
          margin: px2rem(14) 0;
          .top-wrap {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translate(-50%, -70%);
            width: 100vw;
            @include center();
            span {
              -webkit-text-stroke: v-bind(
                "['zh-CN','zh_tw'].includes(lang)?'none':' 1px #dfa747'"
              );
              font-weight: 900;
              background: linear-gradient(180deg, #ffe4c6 0%, #ffffff 100%);
              /* 将背景裁剪为文字形状 */
              -webkit-background-clip: text;
              background-clip: text;

              /* 将文字颜色设为透明，以显示背景渐变 */
              color: transparent;
              text-align: center;
              font-family: GilroyItalic;
              font-size: px2rem(14);
              word-wrap: n;
            }
            img {
              width: px2rem(18);
              height: px2rem(18);
            }
          }
          .reward-item {
            // padding: px2rem(2) 0;
            min-height: px2rem(30);
            height: 0;
            border-bottom: px2rem(1) solid #ffffff1a;
            .cell {
              font-size: px2rem(10);
              color: #fff;
              flex: 1;
              text-align: center;
              @include center();
              .reward-img {
                height: px2rem(26);
                width: px2rem(26);
              }
              .cup-img {
                height: px2rem(18);
                width: px2rem(18);
              }
            }
            .border {
              height: 100%;
              width: px2rem(1);
              background: #ffffff1a;
            }
          }
        }
      }
    }
    .rules-wrap {
      width: 100%;
      padding-top: px2rem(12);
      .rule-item {
        font-size: $fontSize13;
        color: #fff;
        width: px2rem(310);
        margin: 0 auto px2rem(6);
        .rule-sub-item {
          padding: 0 px2rem(8);
          img {
            width: px2rem(20);
            height: px2rem(20);
          }
        }
      }
    }
  }
}
</style>
