import request from "@/utils/request";

export default {
  ban_type(query) {
    return request({
      url: "/client_web/ban/ban_type",
      method: "get",
      params: query,
    });
  },

  // 日常排行榜说明
  rank_info(query) {
    return request({
      url: "/client_web/rank/info",
      method: "get",
      params: query,
    });
  },

  // 榜单活动
  rank_activity(query) {
    return request({
      url: "/client_web/template/rank",
      method: "get",
      params: query,
    });
  },

  // 用户是否有官方任务
  task_office(query) {
    return request({
      url: "/client_web/task/office",
      method: "get",
      params: query,
    });
  },

  // 房间PK规则说明
  pk_rule(query) {
    return request({
      url: "/room/pk/weapon_cfg",
      method: "get",
      params: query,
    }); 
  }
};
