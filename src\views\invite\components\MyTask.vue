<template>
  <div class="tab-panel">
    <div class="task-rule">
      <div class="task-rule-title">{{ $t("invite.invite_number_reward") }}</div>
      <div class="task-rule-content">
        {{ $t("invite.monthly_invite_goal") }}
      </div>
    </div>
    <div class="task-list">
      <div class="task-item" v-for="(item, index) in list" :key="index">
        <div class="task-item-icon">
          <img :src="item.icon" />
        </div>
        <gap :gap="8"></gap>
        <div class="task-item-content">
          <span>{{
            item.is_first_invite === 1
              ? $t("invite.gold_first_invite_success")
              : $t("invite.monthly_invited_success")
          }}</span>
          <span class="task-item-process"
            >(<span style="color: #ff4771">{{ item.current_progress }}</span
            >/{{ item.progress_target }})</span
          >
        </div>
        <gap :gap="12"></gap>
        <div class="task-item-number">
          <span>+</span><span>{{ formatCount(item.progress_reward) }}</span>
        </div>
        <gap :gap="2"></gap>
        <div class="task-item-coin">
          <Coin :gender="userGender"></Coin>
        </div>
      </div>
    </div>
    <empty
      v-if="list.length === 0 && !isLoading"
      :tips="$t('common.common_content_yet')"
    ></empty>
  </div>
</template>
<script setup>
import inviteApi from "@/api/invite.js";
import { onMounted, ref, inject } from "vue";
import Coin from "./Coin.vue";
import { formatCount } from "@/utils/util";

const userGender = inject("userGender");
const list = ref([]);
const isLoading = ref(true);
const fetchData = async () => {
  isLoading.value = true;
  const res = await inviteApi.task_register_reward();
  if (res.code === 200) {
    list.value = (res.data?.list || []).map((item) => ({
      ...item.task_reward,
    }));
  }
  isLoading.value = false;
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.tab-panel {
  position: relative;
  z-index: 3;
  padding: 0 px2rem(12);
  padding-top: px2rem(16);
  padding-bottom: px2rem(20);
  min-height: px2rem(450);

  .task-rule {
    width: 100%;
    padding: px2rem(12);

    border-radius: px2rem(12);
    background: #fff0f3;

    .task-rule-title {
      color: var(---C2, #ff4771);

      /* T15/B */
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
    .task-rule-content {
      color: var(---C2, #ff4771);
      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      margin-top: px2rem(4);
    }
  }
  .task-list {
    margin-top: px2rem(16);
    .task-item {
      display: flex;
      align-items: center;
      margin-bottom: px2rem(20);
      .task-item-icon {
        width: px2rem(36);
        height: px2rem(36);
        flex-shrink: 0;
        img {
          width: px2rem(36);
          height: px2rem(36);
          object-fit: cover;
          vertical-align: text-top;
        }
      }
      .task-item-content {
        color: #642628;

        /* T15/R */
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 500;
        line-height: 128%; /* 19.2px */
        flex-grow: 1;

        .task-item-process {
          color: var(---B3, #999);

          /* T13/R */
          font-family: Gilroy;
          font-size: px2rem(13);
          font-style: normal;
          font-weight: 500;
          line-height: 124%;
          padding: 0 px2rem(6);
        }
      }
      .task-item-number {
        color: #642628;
        text-align: right;

        /* T13/B */
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        flex-shrink: 0;
        height: px2rem(20);
        line-height: px2rem(20);
      }
      .task-item-coin {
        width: px2rem(20);
        height: px2rem(20);
        flex-shrink: 0;
      }
      .task-item-right {
        width: px2rem(16);
        height: px2rem(16);
        flex-shrink: 0;
        background-image: url("@/assets/images/invite/icon-arrow-right.svg");
        background-size: 100% 100%;
      }
    }
  }
}
.rtl-html {
  .task-item-right {
    transform: scaleX(-1);
  }
}
</style>
