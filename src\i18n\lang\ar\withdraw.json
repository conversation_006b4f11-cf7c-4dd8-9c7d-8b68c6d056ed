{"withdrawal_rule_title": "قوا<PERSON><PERSON> السحب ", "withdrawal_rule_detail": ["3. إذ<PERSON> قمت بإلغاء حسابك أو تم حظرك بسبب الانتهاكات خلال فترة السحب، فسوف نقوم بتجميد طلب السحب الخاص بك", "2. هناك رسوم سحب مختلفة مطلوبة بناءً على مبلغ السحب، حيث يكون المبلغ المحدد خاضعًا للمبلغ الفعلي المستلم", "1. بعد طلب السحب ناجح، من المتوقع أن تصل الأموال خلال 1-7 أيام عمل. إذا لم تصل الأموال خلال الوقت المقدر، يرجى التأكد من صحة حساب الاستلام الخاص بك والانتظار بصبر أو الاتصال بخدمة العملاء"], "withdrawal_select_methods_tips": "يتطلب السحب ربط طريقة الاستلام الخاصة بك. يرجى تحديد طريقة الاستلام الخاصة بك والتأكد من أن معلومات الاستلام الخاصة بك صحيحة وصالحة", "service_fee": "رسوم: {0}", "withdrawal_methods": "طريقة الاستلام ", "withdrawal_information": "معلومات السحب", "withdrawal_info_check": "يرجي تاكيد معلومات السحب الخاصة بك", "estimated_withdrawal_amount": "م<PERSON><PERSON><PERSON> السحب المقدر{0}", "estimated_amount_received": "المبلغ المقدر الذي سيتم استلامه", "estimated_service_fee": "رسوم المعالجة المقدرة", "current_selected": "الاختيار الحالي", "completed": "تم إدخال", "need_complete": "انتظر الكمال ", "withdrawal_amount": "<PERSON><PERSON><PERSON><PERSON> السحب", "total_withdrawal_amount": "م<PERSON><PERSON><PERSON> السحب المتراكم", "withdrawal_pending": "جاري السحب ", "withdrawal_success": "السحب بنجاح", "withdrawal_fail": "فشل السحب", "fail_reason": "سبب فشل السحب ", "empty": "لا يوجد تسجيل السحب", "BANK_TRANSFER_receive_bank": "البنك المستفيد", "BANK_TRANSFER_receive_bank_placeholder": "حد<PERSON> البنك المستفيد", "BANK_TRANSFER_fill_info_tips": "الرجاء ملء بيانات الدفع الخاصة بك المعلومات", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "مثلا***********", "payeeInfo_documentId_hint": "الرجاء إدخال CPF/CNPJ (الرقم الضريبي الشخصي/المؤسسي)", "payeeInfo_address_title": "الموقع", "payeeInfo_email_title": "الب<PERSON>يد الإلكتروني", "payeeInfo_email_placeholder": "على سبيل المثال، 1234566{0}gmail.com", "payeeInfo_email_hint": "الرجاء إدخال تنسيق البريد الإلكتروني الصحيح", "payeeInfo_phone_title": "رقم الهاتف", "WALLET_accountNo_title": "الحساب{0}", "WALLET_accountNo_placeholder_SA": "مثلا ************", "WALLET_accountNo_placeholder_EG": "مثلا0**********", "WALLET_accountNo_placeholder_AE": "مثلا +971-*********", "WALLET_accountNo_placeholder_TR": "مثلا**********", "WALLET_accountNo_placeholder_PH": "مثلا***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "مثلاmercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "مثلا***********", "WALLET_accountNo_placeholder_BR_PagBank": "مثلا************{0}123.com", "WALLET_accountNo_placeholder_ID": "مثال: ***********", "WALLET_accountNo_placeholder_MY": "مثال: **********", "WALLET_accountNo_placeholder_TH": "مثال: **********", "WALLET_accountNo_placeholder_VN": "مثال: ***********", "WALLET_accountNo_hint_SA": "يرجي إدخال رقم الهاتف المتربط بالمحفظة، المكون من 12 رقمًا يبدأ بـ 9665", "WALLET_accountNo_hint_EG": "يرجي إدخال رقم الهاتف المتربط بالمحفظة،المكون من 11 رقمًا يبدأ بـ 01", "WALLET_accountNo_hint_EG_2": "يرجى، إدخال رقم الهاتف المرتبط بمحفظتك، المكون من 12 رقمًا بدءًا من 10 أو رمز البلد بالإضافة إلى 10 أرقام", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "رقم الهاتف للمستلم- مطلوب - رقم الهاتف المرتبط بالمحفظة،المكون من 11 رقمًا يبدأ بـ 01 أو رقم مكون من 12 رقمًا يبدأ بـ 201", "WALLET_accountNo_hint_AE": "الرجاء إدخال رقم الهاتف المرتبط بمحفظتك. يجب أن يكون التنسيق + رمز البلد ورقم الهاتف.", "WALLET_accountNo_hint_TR": "ير<PERSON>ى إدخال رقم الهاتف المرتبط بالمحفظة، المكون من 10 أرقام أو PL بالإضافة إلى 10 أرقام", "WALLET_accountNo_hint_PH": "يرجي إدخال رقم الهاتف المتربط بالمحفظة،المكون من 11 رقمًا يبدأ بـ 01", "WALLET_accountNo_hint_PH_LAZADAPH": "يرجى إدخال رقم الهاتف المرتبط بالمحفظة، المكون من 11 رقمًا يبدأ بـ 09 أو عنوان البريد الإلكتروني", "WALLET_accountNo_hint_BR_MERCADOPAGO": "ير<PERSON>ى إدخال حساب المستخدم المرتبط بالمحفظة (البريد الإلكتروني أو ID)", "WALLET_accountNo_hint_BR_PIX": "يرجي إدخال رقم الحساب المقابل المرتبط بالمحفظة", "WALLET_accountNo_hint_BR_PagBank": "يرجى إدخال عنوان البريد الإلكتروني المرتبط بالمحفظة، بما يصل إلى 60 رقمًا", "WALLET_accountNo_hint_ID": "يرجى إدخال رقم حسابك المرتبط بالمحفظة: من 9 إلى 14 رقمًا، بدءًا من 08", "WALLET_accountNo_hint_MY": "يرجى إدخال رقم حسابك المرتبط بالمحفظة: من 10 إلى 11 رقمًا، بدءًا من 0", "WALLET_accountNo_hint_TH": "الرجاء إدخال رقم الحساب المرتبط بالمحفظة: 10 أرقام تبدأ من 0", "WALLET_accountNo_hint_VN": "ير<PERSON>ى إدخال رقم حسابك المرتبط بالمحفظة: من 11 رقمًا، بدءًا من 84", "WALLET_fullName_title": "اسم المستلم", "WALLET_fullName_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "<PERSON><PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_PH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "مثلاAntonio Maldonado Evangelista", "WALLET_fullName_hint": "يرجي إدخال اسمك الكامل باللغة الإنجليزية", "WALLET_lastName_title": " الاسم الأخير للمستفيد", "WALLET_lastName_placeholder_EG": " مثال، إيفانجيليستا", "WALLET_lastName_hint": "ير<PERSON>ى إدخال اسمك الأخير", "WALLET_accountType_title": "{0}ربط نوع الحساب", "WALLET_accountType_placeholder_BR_PIX": "الرجاء تحديد نوع الحساب الملزم", "WALLET_accountType_option_E_PIX": "الب<PERSON>يد الكتروني ", "WALLET_accountType_option_P_PIX": "هاتف", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (الرقم الضريبي الشخصي/المؤسسي)", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "على سبيل المثال 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "حساب SWIFT", "SWIFT_accountNo_placeholder_SA": "مثلا************************", "SWIFT_accountNo_placeholder_AE": "مثلا ***********************", "SWIFT_accountNo_placeholder_KW": "مثلا*******************************", "SWIFT_accountNo_placeholder_QA": "مثلا ******************************", "SWIFT_accountNo_placeholder_TR": "مثلا **************************", "SWIFT_accountNo_placeholder_BH": "مثلا **********************", "SWIFT_accountNo_placeholder_YE": "مثلا **********", "SWIFT_accountNo_placeholder_IQ": "مثلا ***********************", "SWIFT_accountNo_placeholder_IL": "مثلا ***********************", "SWIFT_accountNo_placeholder_PS": "مثلا **********", "SWIFT_accountNo_placeholder_AZ": "مثلا ****************************", "SWIFT_accountNo_placeholder_LB": "مثلا ****************************", "SWIFT_accountNo_placeholder_MA": "مثلا **********", "SWIFT_accountNo_placeholder_PH": "مثلا **********", "SWIFT_accountNo_placeholder_BR": "مثلا *****************************", "SWIFT_accountNo_placeholder_EG": "مثلا *****************************", "SWIFT_accountNo_placeholder_JO": "مثلا ******************************", "SWIFT_accountNo_hint_SA": "الرجاء إدخال حساب SWIFT الخاص بك، 24 حرفًا تبدأ بـ SA", "SWIFT_accountNo_hint_AE": "الرجاء إدخال حساب SWIFT الخاص بك، 23 حرفًا تبدأ بـ AE", "SWIFT_accountNo_hint_KW": "الرجاء إدخال حساب SWIFT الخاص بك، 30 حرفًا تبدأ بـ KW", "SWIFT_accountNo_hint_QA": "الرجاء إدخال حساب SWIFT الخاص بك، 29 حرفًا تبدأ بـ QA", "SWIFT_accountNo_hint_TR": "الرجاء إدخال حساب SWIFT الخاص بك، 26 حرفًا تبدأ بـ TR", "SWIFT_accountNo_hint_BH": "الرجاء إدخال حساب SWIFT الخاص بك، 22 حرفًا تبدأ بـ BH", "SWIFT_accountNo_hint_YE": "الرجاء إدخال حساب SWIFT الخاص بك، 34 رقمًا وحرفًا أو أقل", "SWIFT_accountNo_hint_IQ": "الرجاء إدخال حساب SWIFT الخاص بك، 23 حرفًا تبدأ بـ IQ", "SWIFT_accountNo_hint_IL": "الرجاء إدخال حساب SWIFT الخاص بك، 23 حرفًا تبدأ بـ IL", "SWIFT_accountNo_hint_PS": "الرجاء إدخال حساب SWIFT الخاص بك، 34 رقمًا وحرفًا أو أقل", "SWIFT_accountNo_hint_AZ": "الرجاء إدخال حساب SWIFT الخاص بك، 28 حرفًا تبدأ بـ AZ", "SWIFT_accountNo_hint_LB": "الرجاء إدخال حساب SWIFT الخاص بك، 28 حرفًا تبدأ بـ LB", "SWIFT_accountNo_hint_MA": "الرجاء إدخال حساب SWIFT الخاص بك، 34 رقمًا وحرفًا أو أقل", "SWIFT_accountNo_hint_PH": "الرجاء أدخل حساب SWIFT الخاص بك، 34 رقمًا وحرفًا أو أقل", "SWIFT_accountNo_hint_BR": "الرجاء إدخال حساب SWIFT الخاص بك، 29 حرفًا تبدأ بـ BR", "SWIFT_accountNo_hint_EG": "الرجاء إدخال حساب SWIFT الخاص بك، 29 حرفًا تبدأ بـ EG", "SWIFT_accountNo_hint_JO": "الرجاء إدخال حساب SWIFT الخاص بك، 30 حرفًا تبدأ بـ JO", "SWIFT_fullName_title": "اسم Payee", "SWIFT_fullName_placeholder": "<PERSON><PERSON><PERSON><PERSON>", "SWIFT_fullName_hint": "الرجاء إدخال اسمك الكامل باللغة الإنجليزية", "SWIFT_bankCode_title": "رقم البنك (رمز SWIFT)", "SWIFT_bankCode_placeholder": "مثلا SCBLHKHH", "SWIFT_bankCode_hint": "الرجاء إدخال رمز البنك، يُسمح بإدخال 8 أو 11 رقمًا وأرقامًا وأحرفًا كبيرة", "CARD_accountNo_title": "حسا<PERSON> البنك", "CARD_accountNo_placeholder_EG": "مثلا ***********", "CARD_accountNo_placeholder_TR": "على سبيل المثال ****************", "CARD_accountNo_hint_EG": "الرجاء إدخال حساب البنك، الطول>=8", "CARD_accountNo_hint_TR": "الرجاء إدخال رقم بطاقة مصرفية مكون من 16 رقمًا، لا يتم دعم سوى بطاقات الخصم المباشر", "CARD_fullName_title": "اسم Payee", "CARD_fullName_placeholder": "مثلا ***********", "CARD_fullName_hint": "الرجاء إدخال اسمك الكامل باللغة الإنجليزية", "CARRIER_BILLING_accountNo_title": "رقم الهاتف", "CARRIER_BILLING_accountNo_placeholder": "على سبيل المثال ************", "CARRIER_BILLING_accountNo_hint": "الرجاء إدخال رقم الهاتف", "CASH_accountNo_title": "{0}حساب", "CASH_accountNo_placeholder": "على سبيل المثال ***********،", "CASH_accountNo_hint": "الرجاء إدخال{0}حساب، 11 رقمًا تبدأ بـ 0", "BANK_TRANSFER_accountNo_title_SA": "حساب مصرفي (IBAN)", "BANK_TRANSFER_accountNo_title_AE": "حساب مصرفي (IBAN)", "BANK_TRANSFER_accountNo_title_TR": "حساب مصرفي (IBAN)", "BANK_TRANSFER_accountNo_title": "الحساب المصرفي", "BANK_TRANSFER_accountNo_title_EG": "حساب مصرفي", "BANK_TRANSFER_accountNo_title_KW": "حسا<PERSON> البنك", "BANK_TRANSFER_accountNo_title_QA": "حسا<PERSON> البنك", "BANK_TRANSFER_accountNo_title_MA": "", "BANK_TRANSFER_accountNo_title_PH": "حسا<PERSON> البنك", "BANK_TRANSFER_accountNo_title_BR": "حسا<PERSON> البنك", "BANK_TRANSFER_accountNo_placeholder_SA": "مثلا ************************", "BANK_TRANSFER_accountNo_placeholder_EG": "مثلا ************", "BANK_TRANSFER_accountNo_placeholder_AE": "مثلا ****************3138001", "BANK_TRANSFER_accountNo_placeholder_KW": "مثلا ******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "مثلا *****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "مثلا **************************", "BANK_TRANSFER_accountNo_placeholder_MA": "مثلا 123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "مثلا ************", "BANK_TRANSFER_accountNo_placeholder_BR": "مثلا ***********", "BANK_TRANSFER_accountNo_placeholder_JO": "مثلا *******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "مثال: **********", "BANK_TRANSFER_accountNo_placeholder_TH": "مثال: *********", "BANK_TRANSFER_accountNo_placeholder_ID": "مثال: ***********", "BANK_TRANSFER_accountNo_hint_SA": "الرجاء إدخال مجموعة من الأحرف والأرقام SA+22", "BANK_TRANSFER_accountNo_hint_EG": "الرجاء إدخال رقم IBAN والحساب البنك المحلي، IBAN؛ 29 حرفًا (EG+27 رقمًا)", "BANK_TRANSFER_accountNo_hint_AE": "الرجاء إدخال AE+21 رقمًا، يجب كتابة AE بأحرف كبيرة", "BANK_TRANSFER_accountNo_hint_KW": "الرجاء إدخال طول أقل من 50 حرفًا، بتنسيق IBAN", "BANK_TRANSFER_accountNo_hint_QA": "الرجاء إدخال طول أقل من 50 حرفًا، بتنسيق IBAN", "BANK_TRANSFER_accountNo_hint_TR": "الرجاء إدخال رقم يبدأ بـ TR زائد 24 رقمًا، بتنسيق IBAN", "BANK_TRANSFER_accountNo_hint_MA": "ير<PERSON>ى إدخال رمز حساب الضلع المكون من 24 رقمًا", "BANK_TRANSFER_accountNo_hint_PH": "الرجاء إدخال من 6 إلى 128 رقمًا", "BANK_TRANSFER_accountNo_hint_BR": "الرجاء إدخال من 4 إلى 15 رقمًا", "BANK_TRANSFER_accountNo_hint_JO": "الرجاء إدخال مجموعة مكونة من 30 حرفًا من الأرقام والحروف التي تبدأ بـ JO", "BANK_TRANSFER_accountNo_hint_MY": "أدخل رقم حساب بحد أقصى 35 حرفًا.", "BANK_TRANSFER_accountNo_hint_TH": "الرجاء إدخال رقم حساب مكون من 10 إلى 18 رقمًا", "BANK_TRANSFER_accountNo_hint_ID": "أدخل رقم حساب رقميًا.", "BANK_TRANSFER_bankBranch_title": "<PERSON><PERSON><PERSON> البنك ", "BANK_TRANSFER_bankBranch_placeholder_BR": "مثلا1234", "BANK_TRANSFER_bankBranch_hint_BR": "يرجي إدخال رمز البنك، 4 ~ 6 أرقام", "BANK_TRANSFER_address_placeholder_SA": "مثلاaabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "مثلاALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "مثلاkuwait xx street", "BANK_TRANSFER_address_hint_SA": "يرجى إدخال العنوان، يقتصر الطول على 10 إلى 200 حرف. يُسمح فقط بالأرقام والنقاط والأحرف الكبيرة والصغيرة والمسافات", "BANK_TRANSFER_address_hint_AE": "يرجى إدخال العنوان، ويجب أن يتراوح طوله بين 1 إلى 200 حرف، وسوف يقوم البنك بإجراء عمليات التحقق من المخاطر.", "BANK_TRANSFER_address_hint_KW": "الرجاء إدخال العنوان، الطول >=255", "BANK_TRANSFER_fullName_placeholder_TR": "مثلاAntonio", "BANK_TRANSFER_fullName_placeholder_PH": "<PERSON><PERSON><PERSON><PERSON>", "BANK_TRANSFER_fullName_placeholder_BR": "<PERSON><PERSON><PERSON><PERSON>", "BANK_TRANSFER_fullName_placeholder_MA": "م<PERSON><PERSON><PERSON><PERSON>orge", "BANK_TRANSFER_fullName_placeholder_KW": "م<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kin", "BANK_TRANSFER_phone_placeholder_PH": "مثلا ***********", "BANK_TRANSFER_phone_placeholder_BR": "مثلا *************", "BANK_TRANSFER_phone_hint_PH": "الرجاء إدخال رقم هاتفك، من 0 إلى 9 أرقام تبدأ من 11 (يج<PERSON> إضافة 0)", "BANK_TRANSFER_phone_hint_BR": "الرجاء إدخال رقم هاتفك، من 12 إلى 13 رقمًا تبدأ من 55", "BANK_TRANSFER_bankCode_title_MY": "رمز البنك (رمز سويفت)", "BANK_TRANSFER_bankCode_placeholder_MY": "مثال: CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "أدخل مزيجًا من الأحرف والأرقام يتراوح بين 8 و11 حرفًا.", "BANK_TRANSFER_bankBranch_title_SA": "رمز SWIFT", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "الرجاء إدخال مجموعة مكونة من 8 أو 11 حرفًا كبيرًا وأرقامًا", "BANK_TRANSFER_city_title_SA": "المدينة", "BANK_TRANSFER_city_placeholder_SA": "على سبيل المثال، نيويورك", "BANK_TRANSFER_city_hint_SA": "يرجى إدخال اسم المدينة، بحد أقصى 35 حرفًا", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "على سبيل المثال: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "يرجى إدخال رمز مكون من 24 رقمًا", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "تاريخ الميلاد", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "مثال: ********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "ير<PERSON>ى إدخال تاريخ الميلاد", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "نوع المستند", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "ير<PERSON>ى تحديد نوع المستند", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "رقم الهوية", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "كلمة المرور", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "معرف المستند", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "على سبيل المثال، NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "على سبيل المثال، ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "ير<PERSON>ى إدخال معرف المستند", "AGENT_accountNo_Whatsapp_title_ALL": "رقم واتساب", "AGENT_accountNo_Whatsapp_placeholder_ALL": "على سبيل المثال: +90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "الرجاء إدخال حساب الواتساب الخاص بك", "AGENT_accountNo_vodafone_title_EG": "حساب فودافون", "AGENT_fullName_title_ID": "ID وكيل المستلم", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "الرجاء إدخال ايدي الوكيل الصحيح", "USDT_blockchain_title_ALL": "اسم البلوكشين", "USDT_address_title_ALL": "عنوان إصدار العملة", "USDT_address_placeholder_ALL": "على سبيل المثال: 1111111111111111111111111", "USDT_address_hint_ALL": "الرجاء إدخال عنوان يبدأ بـ 1 أو 3 أو bc", "USDT_address_hint_common": "الرجاء إدخال عنوان USDT الخاص بك"}