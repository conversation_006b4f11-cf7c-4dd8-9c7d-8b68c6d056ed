<template>
  <div class="vote-search">
    <div class="box-area">
      <BorderBox>
        <div class="box-inner">
          <div class="box-desc">
            {{ $t(`singer.audition_round_voting`) }}
          </div>
          <div class="search-box">
            <div class="input">
              <van-field v-model="account" class="van-field"></van-field>
            </div>
            <div
              class="btn-search"
              @click="handleSearch"
              v-track:click
              trace-key="singer_button_click"
              :track-params="
                JSON.stringify({
                  singer_button_name: 8,
                  singer_button_click_source: source,
                })
              "
            >
              <svg-icon icon="search" color="#fff"></svg-icon>
            </div>
          </div>
          <div class="user-vote">
            <div class="avatar">
              <frame-avatar
                v-if="userinfo?.avatar"
                :avatar="userinfo?.avatar"
                :frame="userinfo?.frame"
                :size="40"
              ></frame-avatar>
              <div v-else class="empty-avatar">
                <svg-icon icon="vector"></svg-icon>
              </div>
            </div>
            <gap :gap="10"></gap>
            <div class="user">
              <div class="nickname">{{ userinfo?.nickname ?? "-" }}</div>
              <div class="user-id">
                <span>ID </span><span>{{ userinfo?.account }}</span>
              </div>
            </div>
            <div v-if="userinfo?.is_compete" class="action">
              <TicketNumber :number="userinfo?.count"></TicketNumber>
              <div>
                <div>
                  <AudioPlayer :url="userinfo?.media_url"></AudioPlayer>
                </div>
                <gap :gap="4" v-if="showVoteBtn"></gap>
                <div v-if="showVoteBtn">
                  <VoteButton
                    :user-item="userinfo"
                    :source="3"
                    @vote="handleSearch"
                  ></VoteButton>
                </div>
              </div>
            </div>
            <div v-else-if="userinfo" class="not">
              {{ $t("singer.audition_round_did_not_participate") }}
            </div>
          </div>
        </div>
      </BorderBox>
    </div>
    <MyFriendsButton
      :show-vote-btn="showVoteBtn"
      :show-audio-btn="showVoteBtn"
    ></MyFriendsButton>
  </div>
</template>
<script setup>
import BorderBox from "./BorderBox.vue";
import AudioPlayer from "./AudioPlayer.vue";
import TicketNumber from "./TicketNumber.vue";
import VoteButton from "./VoteButton.vue";
import singerApi from "@/api/activity.js";
import MyFriendsButton from "./MyFriendsButton.vue";
import { ref } from "vue";

const props = defineProps({
  showVoteBtn: {
    type: Boolean,
    default: true,
  },
  // 组件来源：1海选赛，2人气榜
  source: {
    type: Number,
    default: 1,
  },
});

const account = ref("");
const userinfo = ref(null);

const handleSearch = async () => {
  const res = await singerApi.singer_search({
    account: account.value,
  });
  if (res.code === 200 && res.data) {
    userinfo.value = res.data;
  } else {
    userinfo.value = null;
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.empty-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: px2rem(40);
  width: px2rem(40);
  height: px2rem(40);
  font-size: px2rem(16);
  background-color: #504a58;
}
.vote-search {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  // margin-top: px2rem(21);
  padding-top: px2rem(35);
  background-image: url("@/assets/images/activity/singer/bo-top.png");
  background-size: px2rem(309) px2rem(52);
  background-position: top center;
  background-repeat: no-repeat;
  .box-area {
    width: px2rem(350);
    .box-inner {
      overflow: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: px2rem(18);
      .box-desc {
        color: rgba(202, 135, 255, 0.8);
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 12px */
        margin-top: px2rem(25);
        width: px2rem(244);
      }
      .search-box {
        display: flex;
        width: px2rem(266);
        height: px2rem(34);
        margin-top: px2rem(20);
        padding: px2rem(4) px2rem(6);
        justify-content: space-between;
        align-items: center;
        border-radius: px2rem(20);
        border: px2rem(1) solid #fff;
        background: #440281;
        .input {
          flex-grow: 1;
          padding: 0 px2rem(6);
          :deep(.van-field) {
            padding: 0;
          }
          .van-field {
            background-color: transparent;
            :deep(.van-field__control) {
              font-size: px2rem(16);
              color: #fff;
              font-family: Gilroy;
              font-style: normal;
              font-weight: 500;
              line-height: 100%; /* 16px */
            }
          }
        }
        .btn-search {
          width: px2rem(49);
          height: px2rem(26);
          font-size: px2rem(22);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          border-radius: px2rem(30);
          border: 0.92px solid #c37eff;
          background: #791ec9;
          box-shadow: 0px -2.454px 2.454px 0px rgba(44, 2, 104, 0.64) inset,
            0px 1.84px 1.84px 0px rgba(255, 255, 255, 0.66) inset;
        }
      }
      .user-vote {
        padding: px2rem(7) px2rem(11);
        align-items: center;
        border-radius: px2rem(8);
        background: rgba(255, 255, 255, 0.16);
        display: flex;
        width: px2rem(290);
        margin-top: px2rem(12);
        .avatar {
          width: px2rem(40);
          height: px2rem(40);
          flex-shrink: 0;
        }
        .user {
          flex-grow: 1;
          div {
            color: #d5b8ff;
            font-family: Gilroy;
            font-size: px2rem(14);
            font-style: normal;
            font-weight: 700;
            line-height: 100%; /* 14px */
          }
          .user-id {
            font-weight: 500;
          }
        }
        .action {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          & > div {
            display: flex;
            align-items: center;
            .ticket {
              width: px2rem(26);
              height: px2rem(26);
              background-image: url("@/assets/images/activity/singer/ticket.png");
              background-size: 100% 100%;
            }
            .ticket-number {
              color: #d5b8ff;
              text-align: right;
              font-family: Gilroy;
              font-size: px2rem(14);
              font-style: normal;
              font-weight: 500;
              line-height: 100%; /* 14px */
            }
          }
        }
        .not {
          width: px2rem(76);
          height: px2rem(30);
          flex-shrink: 0;
          border-radius: px2rem(4);
          border: 1px solid #867389;
          background: rgba(46, 12, 51, 0.7);
          display: flex;
          justify-content: center;
          align-items: center;

          color: #867389;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(10);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 10px */
          padding: px2rem(5) px2rem(12);
        }
      }
    }
  }
}
</style>
