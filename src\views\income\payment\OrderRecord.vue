<template>
  <div class="app-container">
    <header-bar is-scroll-change-bg />

    <header>
      <div class="header-total-amount">{{ currency }} {{ totalAmount }}</div>
      <div class="header-tips">
        {{ $t("withdraw.total_withdrawal_amount") }}
      </div>
    </header>

    <main>
      <van-list
        v-if="!isEmpty"
        class="list-wrap"
        v-model:loading="isLoading"
        :finished="finished"
        finished-text=""
        loading-text="loading..."
        :immediate-check="false"
        @load="getList"
      >
        <div
          class="list-cell"
          v-for="(cell, idx) in resultList"
          :key="idx"
          @click="goDetail(cell)"
        >
          <div class="list-cell-line flex">
            <div class="payment-name">{{ cell.channel_name }}</div>
            <div class="payment-amount">
              {{ cell.currency }} {{ cell.price }}
            </div>
          </div>
          <div class="list-cell-line flex">
            <div class="payment-date">{{ cell.create_time }}</div>
            <div
              class="payment-status"
              :class="{
                success: cell.statusText === 'SUCCESS',
                fail: cell.statusText === 'FAIL',
              }"
            >
              {{ cell.statusTextI18n }}
            </div>
          </div>
          <div
            class="flex confirm-wrap"
            v-if="cell.platform === 5 && cell.agent_status === 2"
          >
            <div class="confirm-divider"></div>
            <div class="btn-wrap">
              <div class="btn" @click.stop="handlePay(cell)">
                {{ $t("income.withdraw_confirm_payment") }}
              </div>
            </div>
          </div>
        </div>
      </van-list>
      <Empty v-else :tips="$t('withdraw.empty')"></Empty>
    </main>
  </div>
  <confirm-action
    v-model:show="showConfirm"
    @confirm="confirm"
  ></confirm-action>
</template>

<script setup>
import { ref, onMounted } from "vue";
import withdrawApi from "@/api/withdraw.js";
import { useWithdrawStore } from "@/store/index.js";
import ConfirmAction from "./components/ConfirmAction.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const withdrawStore = useWithdrawStore();

const resultList = ref([]);
const totalAmount = ref("");
const currency = ref("");
const pageNo = ref(1);
const pageSize = 20;
const isLoading = ref(false);
const finished = ref(false);
const isEmpty = ref(false);

const showConfirm = ref(false);
const currentForm = ref({});

const getList = async () => {
  isLoading.value = true;

  const response = await withdrawApi.record({
    page: pageNo.value,
    size: pageSize,
  });
  if (response.code === 200) {
    let list = response.data.items || [];
    totalAmount.value = response.data.amount;
    currency.value = response.data.currency;

    withdrawStore.$patch({
      countryCode: response.data.country_code,
    });

    if (list.length > 0) {
      list = list.map((item) => {
        const statusText = withdrawStore.formatStatusText(item.status);
        return {
          ...item,
          statusText,
          statusTextI18n: withdrawStore.formatStatusI18n(statusText),
        };
      });
    }
    resultList.value = resultList.value.concat(list);

    finished.value = !response.data.has_next;
    isEmpty.value = resultList.value.length === 0;
    pageNo.value += 1;

    // 加载状态结束
    isLoading.value = false;
  }
};

const handlePay = (cell) => {
  showConfirm.value = true;
  currentForm.value = cell;
};

const confirm = async () => {
  isLoading.value = true;
  const response = await withdrawApi.confirm({
    id: currentForm.value.id,
  });
  if (response.code === 200) {
    resultList.value = [];
    pageNo.value = 1;
    finished.value = false;
    showConfirm.value = false;
    getList();
  }
  isLoading.value = true;
};
const goDetail = (cell) => {
  withdrawStore.setCurrentRecord(cell);
  router.push({
    name: "WithdrawalDetail",
  });
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
@import "./assets/payment.scss";

.app-container {
  padding: $gap8 $gap16 $pageBottomPadding;
}

header {
  padding: $gap24 $gap4;
  margin-bottom: $gap12;
  text-align: center;
  background-color: $white;
  border-radius: $radius16;

  .header-total-amount {
    margin-bottom: $gap4;
    font-weight: $fontWeightBold;
    font-size: px2rem(24);
    line-height: px2rem(29);
    color: $fontColorB1;
  }

  .header-tips {
    color: rgba($fontColorB0, 0.4);

    @include fontSize11;
  }
}

.list-wrap {
  border-radius: $radius16;

  .list-cell {
    padding: $gap18 $gap14;
    background-color: $white;
    margin-bottom: px2rem(8);
    border-radius: $radius16;
  }

  .list-cell-line {
    &:first-child {
      margin-bottom: $gap8;
    }
  }

  .confirm-wrap {
    // padding-top: ;
    .confirm-divider {
      margin: px2rem(16) 0;
      width: 100%;
      height: px2rem(0.5);
      background-color: $bgColorB6;
    }
    .btn-wrap {
      display: flex;
      width: 100%;
      justify-content: flex-end;
      .btn {
        background-color: $mainColor;
        border-radius: $radius8;
        color: #fff;
        height: px2rem(30);
        line-height: px2rem(30);
        font-size: $fontSize13;
        padding: 0 px2rem(8);
      }
    }
  }

  .payment-name {
    font-weight: $fontWeightBold;
    color: $fontColorB1;

    @include fontSize15;
  }

  .payment-amount {
    flex: 1;
    color: $fontColorB1;
    text-align: right;

    @include fontSize15;
  }

  .payment-date {
    color: $fontColorB3;

    @include fontSize13;
  }

  .payment-status {
    flex: 1;
    color: $fontColorB3;
    text-align: right;

    @include fontSize13;

    &.success {
      color: $subColorGreen2;
    }

    &.fail {
      color: $subColorRed;
    }
  }
}
</style>
