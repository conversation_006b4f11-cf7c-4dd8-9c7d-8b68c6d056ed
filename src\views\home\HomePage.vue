<template>
  <div class="page-wrap">
    <div class="dot"></div>

    <div class="page-container">
      <header>
        <div class="logo">
          <img src="@/assets/images/logo/logo-text.png" alt="">
        </div>
      </header>

      <main>
        <div class="section flex">
          <div class="card-left">
            <div class="bg">
              <img class="bg1" src="@/assets/images/home/<USER>" alt="">
              <img class="bg2" src="@/assets/images/home/<USER>" alt="">
            </div>
            <div class="card-title">
              Connect, Share, Grow
              Experience Life with Siya!
            </div>
            <div class="card-text">
              Siya is a global community where, no matter what you want to do, you can find people who share your aspirations. Let's start sharing every interesting thing in life with <PERSON>ya! Share the real moments of your life with everyone in the community!
            </div>
            <div class="card-buttons flex">
              <div class="card-button button-ios flex" @click="jumpStore(1)">
                <img src="@/assets/images/home/<USER>" alt="">
              </div>
              <div class="card-button button-android flex" @click="jumpStore(2)">
                <img src="@/assets/images/home/<USER>" alt="">
              </div>
            </div>
          </div>
          <div class="card-right">
            <img src="@/assets/images/home/<USER>" alt="">
          </div>
        </div>
      </main>

      <footer>
        <agreementComp></agreementComp>
        <div class="gray">Contact Information: <EMAIL></div>
        <div class="white">
          <span @click="goAgreement(1)">Privacy Policy</span>
          ｜
          <span @click="goAgreement(2)">User Agreement</span>
          ｜
          <span @click="goAgreement(4)">Child Safety Policy</span>
          ｜
          <span @click="goAgreement(5)">Siya Platform Guidelines</span>
        </div>
      </footer>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import AndroidComp from './compontents/android.vue'
import IosComp from './compontents/ios.vue'

const agreementComp = process.env.VITE_COMPANY === 'PUHUA' ? IosComp : AndroidComp
const router = useRouter()

const goAgreement = (type) => {
  const routeData = router.resolve({
    name: 'Agreement',
    query: {
      type
    }
  })
  window.open(routeData.href, '_blank');
}

const jumpStore = (type) => {
  // 1 ios  2 android
  const linkMap = {
    1: 'https://apps.apple.com/app/id6738165319',
    2: 'https://play.google.com/store/apps/details?id=com.zr.siya',
  }
  window.open(linkMap[type], '_blank');
}

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.page-wrap {
  position: relative;
  min-width: 1440px;
  min-height: 100vh;
  background-color: #5F56ED;

  .dot {
    position: absolute;
    top: -838px;
    left: -869px;
    width: 1889px;
    height: 1554px;
    background: radial-gradient(50% 50% at 50% 50%, #FFA3B9 0%, rgba(0, 26, 255, 0) 100%);
    transform: scale(.5);
  }
}

.page-container {
  max-width: 1440px;
  margin: 0 auto;
}

header {
  position: relative;
  z-index: 1;
  padding-left: 64px;

  .logo {
    padding-top: 50px;

    img {
      width: 105px;
      height: 36px;
    }
  }
}

main {
  padding-top: 100px;

  .section {
    justify-content: center;
    flex-wrap: nowrap;
    align-items: flex-start;

    .card-left {
      position: relative;
      width: 668px;
      margin: 180px 140px 0 50px;
    }

    .bg {
      .bg1 {
        position: absolute;
        top: -33px;
        left: -46px;
        width: 60px;
      }

      .bg2 {
        position: absolute;
        top: 90px;
        right: -24px;
        width: 60px;
      }
    }

    .card-title {
      position: relative;
      margin-bottom: 19px;
      font-size: 55px;
      font-weight: $fontWeightHeavy;
      line-height: 70px;
      color: $white;
    }

    .card-text {
      margin-bottom: 19px;
      font-size: 15px;
      line-height: 21px;
      color: $white;
    }

    .card-button {
      margin-right: 13px;
      width: 170px;
      height: 50px;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .card-right {
      img {
        width: 484px;
      }
    }
  }
}

footer {
  padding-left: 64px;
  padding-bottom: 50px;

  div {
    font-size: 15px;
    line-height: 24px;

    &.gray {
      color: rgba(#fff, .6);
    }

    &.white {
      color: $white;
    }

    span {
      cursor: pointer;
    }
  }
}
</style>
