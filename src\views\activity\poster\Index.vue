<template>
  <div class="container">
    <header-bar
      back-icon-show
      :show-title="false"
      :padding="false"
      close-type="close"
    />
    <img :src="url" :draggable="false" />
  </div>
</template>
<script setup>
import { computed } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();

const url = computed(() => {
  return `https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/activity/poster/${route.query?.name}`;
});

const bgImage = computed(() => `url(${url.value})`);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.container {
  width: 100%;
  height: fit-content;
  background-image: v-bind(bgImage);
  background-size: 100% 100%;
}
img {
  width: 100%;
  vertical-align: bottom;
  opacity: 0;
}
</style>
