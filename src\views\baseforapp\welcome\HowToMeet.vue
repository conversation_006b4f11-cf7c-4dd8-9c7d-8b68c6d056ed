<template>
  <div class="app-container">
    <header-bar back-icon-show
                :show-title="false"
                :content-padding="false"
                close-type="close"
    />
    <header>
      <img class="header-title" :src="getImageUrl('title.png')" alt="">
      <canvas class="ami" id="pagCanvas"></canvas>
    </header>
    <main>
      <div class="cell-wrap" v-for="(tip, idx) in tips" :key="idx">
        <div class="cell-header" v-html="tip.title"></div>
        <div :class="['cell-body', {mb: !tip.nomb}]">
          <img :src="tip.img" alt="">
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { getCurrentInstance, onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'
import { AnimationPAGInit } from '@/utils/util.js'

const { proxy } = getCurrentInstance()
const tm = i18n.global.tm
const lang = proxy.$languageFile

const tips = [
  {
    title: tm('base_total.how_to_meet_msg')[0],
    img: getImageUrl('tips1.png'),
  },
  {
    title: tm('base_total.how_to_meet_msg')[1],
    img: getImageUrl('tips2.png'),
    nomb: true,
  },
  {
    title: tm('base_total.how_to_meet_msg')[2],
    img: getImageUrl('tips3.png'),
  },
  {
    title: tm('base_total.how_to_meet_msg')[3],
    img: getImageUrl('tips4.png'),
  },
]

const pagCanvas = '#pagCanvas'
let animationObj = null;
const animationUrl = 'https://siya-packet.obs.ap-southeast-3.myhuaweicloud.com/mgmt/development/test/zmu09jf0ssry8z313vv09r607dh92m0l.pag'
const playAnimation = async () => {
  animationObj = await AnimationPAGInit(animationUrl, pagCanvas)
  animationObj.setRepeatCount(0);
  await animationObj.play();
}


function getImageUrl(name) {
  return new URL(`../../../assets/images/how-to-meet/${lang}/${name}`, import.meta.url).href
}

onMounted(() => {
  playAnimation()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  background: url("https://siya-packet.obs.ap-southeast-3.myhuaweicloud.com/mgmt/development/test/a8dzs9dufara2ufioi7nw8svl0zuhzie.png") top/contain no-repeat, #807CF7;
}

header {
  position: relative;
  margin-bottom: px2rem(242);
  width: px2rem(390);
  height: px2rem(160);
  font-size: 0;

  .header-title {
    width: 100%;
  }

  .ami {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

main {
  position: relative;
  z-index: 1;
  padding: 0 px2rem(16) px2rem(20);
}

.cell-wrap {
  position: relative;
  overflow: hidden;
  min-height: px2rem(600);
  font-size: 0;
  background: linear-gradient(180deg, #917CF7 0%, #8082FF 100%);
  border: 4px solid #FFFFFF7A;
  border-radius: px2rem(28);

  &:not(:last-child) {
    margin-bottom: $gap32;
  }

  &::before,
  &::after {
    content: "";
    position: absolute;
  }

  &::before {
    left: 0;
    bottom: 0;
    width: px2rem(211);
    height: px2rem(192);
    background: url("@/assets/images/how-to-meet/cell-heart-left.png") 100%/cover no-repeat;
  }

  &::after {
    top: 0;
    right: 0;
    width: px2rem(203);
    height: px2rem(330);
    background: url("@/assets/images/how-to-meet/cell-heart-right.png") 100%/cover no-repeat;
  }

  .cell-header {
    position: relative;
    z-index: 1;
    margin: px2rem(30) px2rem(22) 0 px2rem(22);
    font-size: px2rem(16);
    line-height: px2rem(24);
    text-align: center;
    color: $white;

    :deep(.bold) {
      font-size: px2rem(28);
      font-weight: $fontWeightBold;
      line-height: px2rem(33);
    }
  }

  .cell-body {
    position: relative;
    z-index: 1;
    font-size: 0;

    &.mb {
      margin-top: $gap16;
    }

    img {
      width: 100%;
    }
  }
}
</style>
