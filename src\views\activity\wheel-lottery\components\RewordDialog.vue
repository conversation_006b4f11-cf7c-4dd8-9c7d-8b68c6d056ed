<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    @touchmove.prevent
    z-index="1001"
  >
    <div v-if="showDialog" class="base-modal-wrapper" @click.stop>
      <div class="title">
        <div>{{ $t("wheel_lottery.congratulations") }}</div>
      </div>
      <div class="prize" v-if="prize">
        <div class="prize-cover">
          <img
            v-if="prize.reward_type === 1"
            src="@/assets/images/wheel-lottery/common/wheel/prize-coin.png"
          />
          <img v-else :src="prize.reward_img" />
        </div>
        <div class="prize-desc">
          {{ prize.reward_name }}
          <span v-if="prize.reward_type === 1">{{ `*${prize.num}` }}</span>
          <span v-else>
            {{
              prize.reward_type !== 5
                ? `*${$t(
                    prize.duration > 1
                      ? "common.common_days"
                      : "common.common_day",
                    [prize.duration]
                  )}`
                : `*${prize.num}`
            }}
          </span>
        </div>
        <div class="prize-bg" v-show="showPrizeBg"></div>
      </div>
      <div class="btn-confirm" @click="handleConfirm()">OK</div>
    </div>
  </van-overlay>
</template>
<script setup>
import { ref } from "vue";

const showDialog = ref(false);
const showPrizeBg = ref(false);
const prize = ref(null);

const handleOpen = (data) => {
  prize.value = data;
  showPrizeBg.value = false;
  showDialog.value = true;
  setTimeout(() => {
    showPrizeBg.value = true;
  }, 500);
};

const handleConfirm = () => {
  showDialog.value = false;
};

defineExpose({
  open: handleOpen,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.88);
}
@keyframes show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title {
    width: 100%;
    height: px2rem(68);
    background-image: url("@/assets/images/wheel-lottery/common/prize-dialog-title-bg.png");
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: show 0.3s 1;

    div {
      width: 100%;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(34);
      font-style: italic;
      font-weight: 900;
      line-height: normal;

      background: linear-gradient(180deg, #fffef3 0%, #feeaac 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .prize {
    width: px2rem(300);
    height: px2rem(300);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    @keyframes cover {
      0% {
        transform: scale(0.2);
        opacity: 0;
      }
      60% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 1;
        transform: scale(1);
      }
    }
    .prize-cover {
      width: px2rem(120);
      height: px2rem(120);
      z-index: 2;
      animation: cover 0.5s 1;
      img {
        width: px2rem(120);
        height: px2rem(120);
      }
    }
    .prize-desc {
      position: absolute;
      bottom: px2rem(18);
      z-index: 3;
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(17);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      animation: show 0.3s 1;
    }

    @keyframes light-bg {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
    .prize-bg {
      z-index: 1;
      position: absolute;
      inset: 0;
      background-image: url("@/assets/images/wheel-lottery/common/light-bg.png");
      background-size: 100% 100%;
      animation: light-bg 2s linear infinite;
    }
  }
  .btn-confirm {
    display: flex;
    width: px2rem(174);
    height: px2rem(50);
    padding: px2rem(14) px2rem(8);
    margin-top: px2rem(32);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(16);
    border: 1px solid rgba(255, 255, 255, 0.64);
    background: rgba(0, 0, 0, 0.24);
    color: #fff;
    text-align: center;
    animation: show 0.3s 1;

    /* T17/B */
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
}
</style>
