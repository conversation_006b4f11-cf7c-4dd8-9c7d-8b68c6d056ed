import { i18n } from "@/i18n/index.js";
import { validatorMap } from "@/views/income/payment/utils/validator.js";

// 3 payermax 4 dlocal 5 agent(币商代提现) 7 usdt
// 1电子钱包(WALLET) 2银行 3CASH 4CARD 5CARRIER_BILLING 6SWIFT 7AGENT 8USDT
const formStyle = () => {
  const t = i18n.global.t;
  return {
    3: {
      SA: {
        1: {
          STCPAY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["STCPAY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_SA"),
              value: "",
              validator: (val) => {
                // 9665 开头的12位数字
                return validatorMap.startsWithAndTotalNum(val, "9665", 12);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_SA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_SA"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_SA"),
              value: "",
              validator: (val) => {
                // 请输入SA+22位字母，数字的组合
                return validatorMap.startsWithAndNumOrLetter(val, "SA", 22);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 限制5~80字符，至少5个字符，仅支持英文字符和空格，不支持阿拉伯语，也不支持特殊字符和数字
                return validatorMap.minAndMaxAndLetterOrChars1(val, 5, 80);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_SA"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_SA"),
              value: "",
              validator: (val) => {
                // 限制10~200字符，只允许数字、点、大小写字母，空格
                return validatorMap.minAndMaxAndNumLetterOrChars2(val, 10, 200);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_SA"),
              hint: t("withdraw.SWIFT_accountNo_hint_SA"),
              value: "",
              validator: (val) => {
                // 请输入SWIFT账号，SA开头的24位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "SA",
                  24
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_SA"),
              hint: t("withdraw.SWIFT_accountNo_hint_SA"),
              value: "",
              validator: (val) => {
                // 请输入SWIFT账号，SA开头的24位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "SA",
                  24
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      EG: {
        1: {
          ETISALAT: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["ETISALAT"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_EG"),
              hint: t("withdraw.WALLET_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // 01 开头的11位数字
                return validatorMap.startsWithAndTotalNum(val, "01", 11);
              },
            },
          },
          MEEZA_NETWORK: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["MEEZA_NETWORK"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_EG"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_MEEZA_NETWORK"),
              value: "",
              validator: (val) => {
                // 01开头的11位数字或201开头的12位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.startsWithAndTotalNum(val, "201", 12)
                );
              },
            },
          },
          ORANGE: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["ORANGE"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_EG"),
              hint: t("withdraw.WALLET_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // 01 开头的11位数字
                return validatorMap.startsWithAndTotalNum(val, "01", 11);
              },
            },
          },
          VODAFONE: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["VODAFONE"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_EG"),
              hint: t("withdraw.WALLET_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // 01 开头的11位数字
                return validatorMap.startsWithAndTotalNum(val, "01", 11);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_EG"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_EG"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // 支持IBAN和本地银行账号，最大支持29位字符，IBAN;29位字符（EG+27位数字）
                return (
                  validatorMap.startsWithAndNum(val, "EG", 27) ||
                  validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 29)
                );
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50字符，仅支持数字、英文字母、空格、逗号(,)、点(.)、下划线(_)和阿拉伯语
                return validatorMap.minAndMaxAndNumLetterOrChars5(val, 1, 50);
              },
            },
          },
        },
        3: {
          AMAN: {
            accountNo: {
              title: t("withdraw.CASH_accountNo_title", ["AMAN"]),
              placeholder: t("withdraw.CASH_accountNo_placeholder"),
              hint: t("withdraw.CASH_accountNo_hint", ["AMAN"]),
              value: "",
              validator: (val) => {
                // 0开头的11位数字
                return validatorMap.startsWithAndTotalNum(val, "0", 11);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50字符，仅支持数字、英文字母、空格、逗号(,)、点(.)、下划线(_)和阿拉伯语
                return validatorMap.minAndMaxAndNumLetterOrChars5(val, 1, 50);
              },
            },
            email: {
              title: t("withdraw.payeeInfo_email_title"),
              placeholder: t("withdraw.payeeInfo_email_placeholder", ["@"]),
              hint: t("withdraw.payeeInfo_email_hint"),
              value: "",
              validator: (val) => {
                // 符合邮箱基本格式
                return validatorMap.email(val);
              },
            },
          },
          FAWRY: {
            accountNo: {
              title: t("withdraw.CASH_accountNo_title", ["FAWRY"]),
              placeholder: t("withdraw.CASH_accountNo_placeholder"),
              hint: t("withdraw.CASH_accountNo_hint", ["FAWRY"]),
              value: "",
              validator: (val) => {
                // 收款方手机号，0开头的11位数字
                return validatorMap.startsWithAndTotalNum(val, "0", 11);
              },
            },
            notifyEmail: {
              title: t("withdraw.payeeInfo_email_title"),
              placeholder: t("withdraw.payeeInfo_email_placeholder", ["@"]),
              hint: t("withdraw.payeeInfo_email_hint"),
              value: "",
              validator: (val) => {
                // 符合邮箱基本格式
                return validatorMap.email(val);
              },
            },
          },
        },
        4: {
          CARD: {
            accountNo: {
              title: t("withdraw.CARD_accountNo_title"),
              placeholder: t("withdraw.CARD_accountNo_placeholder_EG"),
              hint: t("withdraw.CARD_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // 长度>=8
                return validatorMap.minNum(val, 8);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50字符，仅支持数字、英文字母、空格、逗号(,)、点(.)、下划线(_)和阿拉伯语
                return validatorMap.minAndMaxAndNumLetterOrChars5(val, 1, 50);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_EG"),
              hint: t("withdraw.SWIFT_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // EG开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "EG",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_EG"),
              hint: t("withdraw.SWIFT_accountNo_hint_EG"),
              value: "",
              validator: (val) => {
                // EG开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "EG",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      AE: {
        1: {
          PAYBY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["PAYBY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_AE"),
              hint: t("withdraw.WALLET_accountNo_hint_AE"),
              value: "",
              validator: (val) => {
                // +国家编码-手机号，例如：+971-*********包含+号和-
                return validatorMap.prefixPhoneNumber(val);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_AE"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_AE"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_AE"),
              value: "",
              validator: (val) => {
                // AE+21位数字,AE必须大写
                return validatorMap.startsWithAndNum(val, "AE", 21);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 0-100字符 只能包含英文字母、数字、空格、点(.)、逗号(,)、单引号(')、斜杠(/)、中划线(-）、问号(?)、冒号(:)、左右括号（）、加号（+）；
                return validatorMap.minAndMaxAndNumLetterOrChars1(val, 1, 100);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_AE"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_AE"),
              value: "",
              validator: (val) => {
                // 1~200位字符，银行会进行风控校验
                return validatorMap.minAndMaxValue(val, 1, 200);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_AE"),
              hint: t("withdraw.SWIFT_accountNo_hint_AE"),
              value: "",
              validator: (val) => {
                // AE开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "AE",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_AE"),
              hint: t("withdraw.SWIFT_accountNo_hint_AE"),
              value: "",
              validator: (val) => {
                // AE开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "AE",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      KW: {
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_KW"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_KW"),
              value: "",
              validator: (val) => {
                // 长度<=50位，IBAN格式
                return validatorMap.startsWithAndMaxAndNumOrLetter(val, "", 50);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_KW"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符，不支持阿拉伯语
                return validatorMap.minAndMaxValue2(val, 1, 100);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_KW"),
              value: "",
              validator: (val) => {
                // 长度<=255位
                return validatorMap.minAndMaxValue(val, 1, 255);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_KW"),
              hint: t("withdraw.SWIFT_accountNo_hint_KW"),
              value: "",
              validator: (val) => {
                // KW开头的30位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "KW",
                  30
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_KW"),
              hint: t("withdraw.SWIFT_accountNo_hint_KW"),
              value: "",
              validator: (val) => {
                // KW开头的30位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "KW",
                  30
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      QA: {
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_QA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_QA"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_QA"),
              value: "",
              validator: (val) => {
                // 长度<=50位，IBAN格式
                return validatorMap.startsWithAndMaxAndNumOrLetter(val, "", 50);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_KW"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符，不支持阿拉伯语
                return validatorMap.minAndMaxValue2(val, 1, 100);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_KW"),
              value: "",
              validator: (val) => {
                // 长度<=255位
                return validatorMap.minAndMaxValue(val, 1, 255);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_QA"),
              hint: t("withdraw.SWIFT_accountNo_hint_QA"),
              value: "",
              validator: (val) => {
                // QA开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "QA",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_QA"),
              hint: t("withdraw.SWIFT_accountNo_hint_QA"),
              value: "",
              validator: (val) => {
                // QA开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "QA",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      TR: {
        1: {
          PAPARA: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["PAPARA"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_TR"),
              hint: t("withdraw.WALLET_accountNo_hint_TR"),
              value: "",
              validator: (val) => {
                // 10位数字或PL加10位数字
                return (
                  validatorMap.startsWithAndNum(val, "", 10) ||
                  validatorMap.startsWithAndNum(val, "PL", 10)
                );
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_TR"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_TR"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_TR"),
              value: "",
              validator: (val) => {
                // 国际银行账号（IBAN）TR开头加24位数字
                return validatorMap.startsWithAndNum(val, "TR", 24);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 5-100字符，只能包含英文、数字、空格、中划线（-）、点（.)；尽可能收集完整的姓名（包含firstName、middleName、lastName）
                // 临时修改该为最小5，最大100字符
                return validatorMap.minAndMaxValue(val, 5, 100);
              },
            },
          },
        },
        4: {
          CARD: {
            accountNo: {
              title: t("withdraw.CARD_accountNo_title"),
              placeholder: t("withdraw.CARD_accountNo_placeholder_TR"),
              hint: t("withdraw.CARD_accountNo_hint_TR"),
              value: "",
              validator: (val) => {
                // 银行卡号16位数字，仅支持借记卡。系统会做卡bin校验，测试时可以使用示例卡号
                return validatorMap.startsWithAndNum(val, "", 16);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.CARD_fullName_placeholder"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 0-100字符只能包含英文、数字、空格、中划线（-）、点（.)
                return validatorMap.minAndMaxAndNumLetterOrChars4(val, 1, 100);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_TR"),
              hint: t("withdraw.SWIFT_accountNo_hint_TR"),
              value: "",
              validator: (val) => {
                // TR开头的26位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "TR",
                  26
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_TR"),
              hint: t("withdraw.SWIFT_accountNo_hint_TR"),
              value: "",
              validator: (val) => {
                // TR开头的26位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "TR",
                  26
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      BH: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_BH"),
              hint: t("withdraw.SWIFT_accountNo_hint_BH"),
              value: "",
              validator: (val) => {
                // BH开头的22位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "BH",
                  22
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_BH"),
              hint: t("withdraw.SWIFT_accountNo_hint_BH"),
              value: "",
              validator: (val) => {
                // BH开头的22位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "BH",
                  22
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      YE: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_YE"),
              hint: t("withdraw.SWIFT_accountNo_hint_YE"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_YE"),
              hint: t("withdraw.SWIFT_accountNo_hint_YE"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      IQ: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_IQ"),
              hint: t("withdraw.SWIFT_accountNo_hint_IQ"),
              value: "",
              validator: (val) => {
                // IQ开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "IQ",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_IQ"),
              hint: t("withdraw.SWIFT_accountNo_hint_IQ"),
              value: "",
              validator: (val) => {
                // IQ开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "IQ",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      IL: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_IL"),
              hint: t("withdraw.SWIFT_accountNo_hint_IL"),
              value: "",
              validator: (val) => {
                // IL开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "IL",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_IL"),
              hint: t("withdraw.SWIFT_accountNo_hint_IL"),
              value: "",
              validator: (val) => {
                // IL开头的23位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "IL",
                  23
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      PS: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_PS"),
              hint: t("withdraw.SWIFT_accountNo_hint_PS"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_PS"),
              hint: t("withdraw.SWIFT_accountNo_hint_PS"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      AZ: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_AZ"),
              hint: t("withdraw.SWIFT_accountNo_hint_AZ"),
              value: "",
              validator: (val) => {
                // AZ开头的28位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "AZ",
                  28
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_AZ"),
              hint: t("withdraw.SWIFT_accountNo_hint_AZ"),
              value: "",
              validator: (val) => {
                // AZ开头的28位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "AZ",
                  28
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      LB: {
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_LB"),
              hint: t("withdraw.SWIFT_accountNo_hint_LB"),
              value: "",
              validator: (val) => {
                // LB开头的28位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "LB",
                  28
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_LB"),
              hint: t("withdraw.SWIFT_accountNo_hint_LB"),
              value: "",
              validator: (val) => {
                // LB开头的28位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "LB",
                  28
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      MA: {
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_MA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_MA"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_MA"),
              value: "",
              validator: (val) => {
                // RIB账户代码24位数字
                return validatorMap.startsWithAndTotalNum(val, "", 24);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_MA"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个英文字符
                return validatorMap.minAndMaxValue(val, 1, 100);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_MA"),
              hint: t("withdraw.SWIFT_accountNo_hint_MA"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_MA"),
              hint: t("withdraw.SWIFT_accountNo_hint_MA"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      PH: {
        1: {
          GCASH: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["GCASH"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_PH"),
              hint: t("withdraw.WALLET_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号，09开头11位数字
                return validatorMap.startsWithAndTotalNum(val, "09", 11);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文字母、空格、中划线（-）、点（.)、逗号（,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
          GRABPAY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["GRABPAY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_PH"),
              hint: t("withdraw.WALLET_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号，09开头11位数字
                return validatorMap.startsWithAndTotalNum(val, "09", 11);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文、空格、中划线（-）、点（.)、逗号(,)
                // 逗号连接Last name + “,” + First name + “,”+ Middle name + “,” + Suffix[姓氏 + 名字 + 中间名字]
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
          LAZADAPH: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["LAZADAPH"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_PH"),
              hint: t("withdraw.WALLET_accountNo_hint_PH_LAZADAPH"),
              value: "",
              validator: (val) => {
                // 注册Lazada时使用的手机号（09开头11位数字）或邮箱
                return (
                  validatorMap.startsWithAndTotalNum(val, "09", 11) ||
                  validatorMap.email(val)
                );
              },
            },
          },
          PAYMAYA: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["PAYMAYA"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_PH"),
              hint: t("withdraw.WALLET_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号，09开头11位数字
                return validatorMap.startsWithAndTotalNum(val, "09", 11);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文、空格、中划线（-）、点（.)、逗号(,)
                // 逗号连接Last name + “,” + First name + “,”+ Middle name + “,” + Suffix[姓氏 + 名字 + 中间名字]
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_PH"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_PH"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 只允许6~128位数字
                return validatorMap.minAndMaxNum(val, 6, 128);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 4~100个字符
                return validatorMap.minAndMaxValue(val, 4, 100);
              },
            },
            payeePhone: {
              title: t("withdraw.payeeInfo_phone_title"),
              placeholder: t("withdraw.BANK_TRANSFER_phone_placeholder_PH"),
              hint: t("withdraw.BANK_TRANSFER_phone_hint_PH"),
              value: "",
              validator: (val) => {
                // 收款方移动电话号码，09开头11位的数字 （必须加0）
                return validatorMap.startsWithAndTotalNum(val, "09", 11);
              },
            },
          },
        },
        5: {
          CARRIER_BILLING: {
            accountNo: {
              title: t("withdraw.CARRIER_BILLING_accountNo_title"),
              placeholder: t("withdraw.CARRIER_BILLING_accountNo_placeholder"),
              hint: t("withdraw.CARRIER_BILLING_accountNo_hint"),
              value: "",
              validator: (val) => {
                // 请参照【电话号码校验-Phone number rule】填写对应国家的电话号码
                return validatorMap.minAndMaxNum(val, 1, 20);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_PH"),
              hint: t("withdraw.SWIFT_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_PH"),
              hint: t("withdraw.SWIFT_accountNo_hint_PH"),
              value: "",
              validator: (val) => {
                // 小于等于34位数字和字母
                return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 34);
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      BR: {
        1: {
          MERCADOPAGO: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["MERCADOPAGO"]),
              placeholder: t(
                "withdraw.WALLET_accountNo_placeholder_BR_MERCADOPAGO",
                ["@"]
              ),
              hint: t("withdraw.WALLET_accountNo_hint_BR_MERCADOPAGO"),
              value: "",
              validator: (val) => {
                // 请输入MercadoPago用户的账户（邮箱或id）
                return (
                  validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 100) ||
                  validatorMap.email(val)
                );
              },
            },
          },
          PIX: {
            documentId: {
              title: t("withdraw.payeeInfo_documentId_title"),
              placeholder: t("withdraw.payeeInfo_documentId_placeholder"),
              hint: t("withdraw.payeeInfo_documentId_hint"),
              value: "",
              validator: (val) => {
                // CPF/CNPJ（个人/企业税号）
                return validatorMap.minAndMaxValue2(val, 1, 14);
              },
            },
            accountType: {
              type: "select",
              options: [
                {
                  text: t("withdraw.WALLET_accountType_option_E_PIX"),
                  value: "E",
                },
                {
                  text: t("withdraw.WALLET_accountType_option_P_PIX"),
                  value: "P",
                },
                {
                  text: t("withdraw.WALLET_accountType_option_C_PIX"),
                  value: "C",
                },
                {
                  text: t("withdraw.WALLET_accountType_option_B_PIX"),
                  value: "B",
                },
              ],
              textValue: "",
              showPicker: false,
              title: t("withdraw.WALLET_accountType_title", ["PIX"]),
              placeholder: t("withdraw.WALLET_accountType_placeholder_BR_PIX"),
              hint: t("withdraw.WALLET_accountType_placeholder_BR_PIX"),
              value: "",
              validator: (val) => {
                return validatorMap.minAndMaxValue(val, 1, 100);
              },
              selectConfirm: (
                { selectedValues, selectedOptions },
                row,
                form
              ) => {
                row.textValue = selectedOptions[0]?.text;
                row.value = selectedValues[0];
                row.showPicker = false;
                const accountNoRow = form.find((i) => i.key === "accountNo");
                accountNoRow.value = "";
                accountNoRow.preText = row.value === "P" ? "+55" : "";
                if (row.value === "E") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_MERCADOPAGO"
                  );
                } else if (row.value === "P") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_PIX"
                  );
                } else if (row.value === "C") {
                  accountNoRow.placeholder = t(
                    "withdraw.payeeInfo_documentId_placeholder"
                  );
                } else if (row.value === "B") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_PIX_B"
                  );
                } else {
                  accountNoRow.placeholder = "";
                }
              },
              mounted: (row, form) => {
                const accountNoRow = form.find((i) => i.key === "accountNo");
                accountNoRow.preText = row.value === "P" ? "+55" : "";
                if (row.value === "E") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_MERCADOPAGO"
                  );
                } else if (row.value === "P") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_PIX"
                  );
                } else if (row.value === "C") {
                  accountNoRow.placeholder = t(
                    "withdraw.payeeInfo_documentId_placeholder"
                  );
                } else if (row.value === "B") {
                  accountNoRow.placeholder = t(
                    "withdraw.WALLET_accountNo_placeholder_BR_PIX_B"
                  );
                } else {
                  accountNoRow.placeholder = "";
                }
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["PIX"]),
              placeholder: "",
              hint: t("withdraw.WALLET_accountNo_hint_BR_PIX"),
              value: "",
              preText: "",
              orderDetailPreFn: (_, form) => {
                const accountTypeRow = form.find(
                  (i) => i.key === "accountType"
                );
                return accountTypeRow.value === "P" ? "+55" : "";
              },
              validator: (val, form) => {
                // 传入绑定账户类型对应的账号
                const accountTypeRow = form.find(
                  (i) => i.key === "accountType"
                );
                if (accountTypeRow.value === "E") {
                  return validatorMap.email(val);
                } else if (accountTypeRow.value === "P") {
                  return validatorMap.minAndMaxNum(val, 1, 12);
                } else if (accountTypeRow.value === "C") {
                  return validatorMap.minAndMaxNum(val, 11, 14);
                } else if (accountTypeRow.value === "B") {
                  return validatorMap.minAndMaxAndNumLetterOrChars4(val, 1, 36);
                } else {
                  return validatorMap.CPFOrCNPJCode(val);
                }
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_BR"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符
                return validatorMap.minAndMaxValue(val, 1, 100);
              },
            },
          },
          PAGBANK: {
            documentId: {
              title: t("withdraw.payeeInfo_documentId_title"),
              placeholder: t("withdraw.payeeInfo_documentId_placeholder"),
              hint: t("withdraw.payeeInfo_documentId_hint"),
              value: "",
              validator: (val) => {
                // CPF/CNPJ（个人/企业税号）
                return validatorMap.CPFOrCNPJCode(val);
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["PAGBANK"]),
              placeholder: t(
                "withdraw.WALLET_accountNo_placeholder_BR_PagBank",
                ["@"]
              ),
              hint: t("withdraw.WALLET_accountNo_hint_BR_PagBank"),
              value: "",
              validator: (val) => {
                // PagBank绑定的收款方邮箱，最长60位
                return validatorMap.email(val);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_BR"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 不大于64个字符
                return validatorMap.minAndMaxValue(val, 1, 64);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            documentId: {
              title: t("withdraw.payeeInfo_documentId_title"),
              placeholder: t("withdraw.payeeInfo_documentId_placeholder"),
              hint: t("withdraw.payeeInfo_documentId_hint"),
              value: "",
              validator: (val) => {
                // CPF/CNPJ（个人/企业税号）
                return validatorMap.CPFOrCNPJCode(val);
              },
            },
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_BR"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_BR"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_BR"),
              value: "",
              validator: (val) => {
                // 4~15位数字
                return validatorMap.minAndMaxNum(val, 4, 15);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符
                return validatorMap.minAndMaxValue(val, 1, 100);
              },
            },
            payeePhone: {
              title: t("withdraw.payeeInfo_phone_title"),
              placeholder: t("withdraw.BANK_TRANSFER_phone_placeholder_BR"),
              hint: t("withdraw.BANK_TRANSFER_phone_hint_BR"),
              value: "",
              validator: (val) => {
                // 55开头的12-13位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "55", 12) ||
                  validatorMap.startsWithAndTotalNum(val, "55", 13)
                );
              },
            },
            bankBranch: {
              title: t("withdraw.BANK_TRANSFER_bankBranch_title"),
              placeholder: t(
                "withdraw.BANK_TRANSFER_bankBranch_placeholder_BR"
              ),
              hint: t("withdraw.BANK_TRANSFER_bankBranch_hint_BR"),
              value: "",
              validator: (val) => {
                // 4~6位数字
                return validatorMap.bankBranchCode(val);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_BR"),
              hint: t("withdraw.SWIFT_accountNo_hint_BR"),
              value: "",
              validator: (val) => {
                // BR开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "BR",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
          "SWIFT-SHA": {
            accountNo: {
              title: t("withdraw.SWIFT_accountNo_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_BR"),
              hint: t("withdraw.SWIFT_accountNo_hint_BR"),
              value: "",
              validator: (val) => {
                // BR开头的29位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "BR",
                  29
                );
              },
            },
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_bankCode_placeholder"),
              hint: t("withdraw.SWIFT_bankCode_hint"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      JO: {
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_SA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_JO"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_JO"),
              value: "",
              validator: (val) => {
                // JO开头的30位数字字母的组合 具体规则：JO+2位数字校验位+4位大写字母银行识别码+4位数字+18位任意字符
                return validatorMap.BankAccount_JO(val);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_KW"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符，不支持阿拉伯语
                return validatorMap.minAndMaxValue2(val, 1, 100);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_KW"),
              value: "",
              validator: (val) => {
                // 长度<=255位
                return validatorMap.minAndMaxValue(val, 1, 255);
              },
            },
          },
        },
        6: {
          "SWIFT-OUR": {
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_JO"),
              hint: t("withdraw.SWIFT_accountNo_hint_JO"),
              value: "",
              validator: (val) => {
                // 请输入SWIFT账号，JO开头的30位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "JO",
                  30
                );
              },
            },
          },
          "SWIFT-SHA": {
            fullName: {
              title: t("withdraw.SWIFT_fullName_title"),
              placeholder: t("withdraw.SWIFT_fullName_placeholder"),
              hint: t("withdraw.SWIFT_fullName_hint"),
              value: "",
              validator: (val) => {
                // 1-105位字符，支持英文、空格、中划线（-）、点（.)、逗号(,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 105);
              },
            },
            bankCode: {
              title: t("withdraw.SWIFT_bankCode_title"),
              placeholder: t("withdraw.SWIFT_accountNo_placeholder_JO"),
              hint: t("withdraw.SWIFT_accountNo_hint_JO"),
              value: "",
              validator: (val) => {
                // 请输入SWIFT账号，JO开头的30位字符
                return validatorMap.startsWithAndTotalNumOrLetter(
                  val,
                  "JO",
                  30
                );
              },
            },
          },
        },
      },
      ID: {
        1: {
          OVO: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["OVO"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_ID"),
              hint: t("withdraw.WALLET_accountNo_hint_ID"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号(08开头）；9~14位数字
                return validatorMap.startsWithAndMinAndMax(val, "08", 7, 12);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文字母、空格、中划线（-）、点（.)、逗号（,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
          GOPAY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["GOPAY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_ID"),
              hint: t("withdraw.WALLET_accountNo_hint_ID"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号(08开头）；9~14位数字
                return validatorMap.startsWithAndMinAndMax(val, "08", 7, 12);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文字母、空格、中划线（-）、点（.)、逗号（,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
          LINKAJA: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["LINKAJA"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_ID"),
              hint: t("withdraw.WALLET_accountNo_hint_ID"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号(08开头）；9~14位数字
                return validatorMap.startsWithAndMinAndMax(val, "08", 7, 12);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文字母、空格、中划线（-）、点（.)、逗号（,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
          SHOPEEPAY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["SHOPEEPAY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_ID"),
              hint: t("withdraw.WALLET_accountNo_hint_ID"),
              value: "",
              validator: (val) => {
                // 钱包绑定的手机号(08开头）；9~14位数字
                return validatorMap.startsWithAndMinAndMax(val, "08", 7, 12);
              },
            },
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符的英文名称，只能包含英文字母、空格、中划线（-）、点（.)、逗号（,)
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 100);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_ID"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_ID"),
              value: "",
              validator: (val) => {
                // 请输入10～18位数字账号
                return validatorMap.minAndMaxNum(val, 1, 100);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 只能包含英文、空格、中划线（-）、点（.)、逗号（,)；小于100字符
                return validatorMap.minAndMaxAndLetterOrChars2(val, 1, 99);
              },
            },
          },
        },
      },
      MY: {
        1: {
          TNG: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["TRUEMONEY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_MY"),
              hint: t("withdraw.WALLET_accountNo_hint_MY"),
              value: "",
              validator: (val) => {
                // 0开头的10或11位数字
                return validatorMap.startsWithAndMinAndMax(val, "0", 9, 10);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_MY"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_MY"),
              value: "",
              validator: (val) => {
                // 最长35位
                return validatorMap.minAndMaxNum(val, 1, 35);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于96字符，只允许字母，数字和空格
                return validatorMap.minAndMaxAndLetterAndNumOrChars(
                  val,
                  1,
                  95,
                  "\\s"
                );
              },
            },
            bankCode: {
              title: t("withdraw.BANK_TRANSFER_bankCode_title_MY"),
              placeholder: t("withdraw.BANK_TRANSFER_bankCode_placeholder_MY"),
              hint: t("withdraw.BANK_TRANSFER_bankCode_hint_MY"),
              value: "",
              validator: (val) => {
                // 8或11位，允许数字和大写字母
                return validatorMap.SWIFTBankCode(val);
              },
            },
          },
        },
      },
      TH: {
        1: {
          TRUEMONEY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["TRUEMONEY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_TH"),
              hint: t("withdraw.WALLET_accountNo_hint_TH"),
              value: "",
              validator: (val) => {
                //  钱包绑定的手机号(0开头的10位数字）
                return validatorMap.startsWithAndNum(val, "0", 9);
              },
            },
          },
        },
        2: {
          BANK_TRANSFER: {
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_TH"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_TH"),
              value: "",
              validator: (val) => {
                // 请输入10～18位数字账号
                return validatorMap.minAndMaxNum(val, 10, 18);
              },
            },
            fullName: {
              title: t("withdraw.CARD_fullName_title"),
              placeholder: t("withdraw.BANK_TRANSFER_fullName_placeholder_TR"),
              hint: t("withdraw.CARD_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于100个字符，支持英文及数字，使用空格连接Last name和First name
                return validatorMap.minAndMaxAndLetterAndNumOrChars(
                  val,
                  1,
                  99,
                  "\\s"
                );
              },
            },
          },
        },
      },
      VN: {
        1: {
          ZALOPAY: {
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["ZALOPAY"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_VN"),
              hint: t("withdraw.WALLET_accountNo_hint_VN"),
              value: "",
              validator: (val) => {
                // 84开头的11位数字
                return validatorMap.startsWithAndNum(val, "84", 9);
              },
            },
          },
        },
      },
    },
    4: {
      ALL: {
        1: {
          Vodafone: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["Vodafone"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_2"),
              value: "",
              validator: (val) => {
                // 请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.prefixPhoneNumber2(val, 10)
                );
              },
            },
          },
        },
      },
      EG: {
        1: {
          Etisalat: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["Etisalat"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_2"),
              value: "",
              validator: (val) => {
                // 请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.prefixPhoneNumber2(val, 10)
                );
              },
            },
          },
          Orange: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["Orange"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_2"),
              value: "",
              validator: (val) => {
                // 请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.prefixPhoneNumber2(val, 10)
                );
              },
            },
          },
          Vodafone: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.WALLET_accountNo_title", ["Vodafone"]),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_2"),
              value: "",
              validator: (val) => {
                // 请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.prefixPhoneNumber2(val, 10)
                );
              },
            },
          },
        },
      },
      SA: {
        2: {
          BANK_TRANSFER: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_SA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_SA"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_SA"),
              value: "",
              validator: (val) => {
                // 请输入SA+22位字母，数字的组合
                return validatorMap.startsWithAndNumOrLetter(val, "SA", 22);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_AE"),
              value: "",
              validator: (val) => {
                // 1~200位字符，银行会进行风控校验
                return validatorMap.minAndMaxValue(val, 1, 200);
              },
            },
            bankBranch: {
              title: t("withdraw.BANK_TRANSFER_bankBranch_title_SA"),
              placeholder: t(
                "withdraw.BANK_TRANSFER_bankBranch_placeholder_SA"
              ),
              hint: t("withdraw.BANK_TRANSFER_bankBranch_hint_SA"),
              value: "",
              validator: (val) => {
                // 8或11个字母数字的组合
                return validatorMap.SWIFTBankCode(val);
              },
            },
            city: {
              title: t("withdraw.BANK_TRANSFER_city_title_SA"),
              placeholder: t("withdraw.BANK_TRANSFER_city_placeholder_SA"),
              hint: t("withdraw.BANK_TRANSFER_city_hint_SA"),
              value: "",
              validator: (val) => {
                // 1~35位任意字符
                return validatorMap.minAndMaxValue(val, 1, 35);
              },
            },
          },
        },
      },
      MA: {
        2: {
          BANK_TRANSFER: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_SA"),
              placeholder: t(
                "withdraw.DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA"
              ),
              hint: t("withdraw.DLOCAL_BANK_TRANSFER_accountNo_hint_MA"),
              value: "",
              validator: (val) => {
                // 24位数字的组合
                return validatorMap.startsWithAndTotalNum(val, "", 24);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_AE"),
              value: "",
              validator: (val) => {
                // 1~200位字符，银行会进行风控校验
                return validatorMap.minAndMaxValue(val, 1, 200);
              },
            },
          },
        },
      },
      AE: {
        2: {
          BANK_TRANSFER: {
            fullName: {
              title: t("withdraw.WALLET_fullName_title"),
              placeholder: t("withdraw.WALLET_fullName_placeholder_PH"),
              hint: t("withdraw.WALLET_fullName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            lastName: {
              title: t("withdraw.WALLET_lastName_title"),
              placeholder: t("withdraw.WALLET_lastName_placeholder_EG"),
              hint: t("withdraw.WALLET_lastName_hint"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.BANK_TRANSFER_accountNo_title_QA"),
              placeholder: t("withdraw.BANK_TRANSFER_accountNo_placeholder_AE"),
              hint: t("withdraw.BANK_TRANSFER_accountNo_hint_AE"),
              value: "",
              validator: (val) => {
                // AE+21位数字,AE必须大写
                return validatorMap.startsWithAndNum(val, "AE", 21);
              },
            },
            address: {
              title: t("withdraw.payeeInfo_address_title"),
              placeholder: t("withdraw.BANK_TRANSFER_address_placeholder_KW"),
              hint: t("withdraw.BANK_TRANSFER_address_hint_AE"),
              value: "",
              validator: (val) => {
                // 1~200位字符，银行会进行风控校验
                return validatorMap.minAndMaxValue(val, 1, 200);
              },
            },
            bankBranch: {
              title: t("withdraw.BANK_TRANSFER_bankBranch_title_SA"),
              placeholder: t(
                "withdraw.BANK_TRANSFER_bankBranch_placeholder_SA"
              ),
              hint: t("withdraw.BANK_TRANSFER_bankBranch_hint_SA"),
              value: "",
              validator: (val) => {
                // 8或11个字母数字的组合
                return validatorMap.SWIFTBankCode(val);
              },
            },
            city: {
              title: t("withdraw.BANK_TRANSFER_city_title_SA"),
              placeholder: t("withdraw.BANK_TRANSFER_city_placeholder_SA"),
              hint: t("withdraw.BANK_TRANSFER_city_hint_SA"),
              value: "",
              validator: (val) => {
                // 1~35位任意字符
                return validatorMap.minAndMaxValue(val, 1, 35);
              },
            },
            birthday: {
              title: t("withdraw.DLOCAL_BANK_TRANSFER_birthday_title_AE"),
              placeholder: t(
                "withdraw.DLOCAL_BANK_TRANSFER_birthday_placeholder_AE"
              ),
              hint: t("withdraw.DLOCAL_BANK_TRANSFER_birthday_hint_AE"),
              value: "",
              validator: (val) => {
                // 8位数字
                return validatorMap.startsWithAndNum(val, "", 8);
              },
            },
            documentType: {
              type: "select",
              textValue: "",
              showPicker: false,
              options: [
                {
                  text: t(
                    "withdraw.DLOCAL_BANK_TRANSFER_documentType_option_ID_AE"
                  ),
                  value: "ID",
                },
                {
                  text: t(
                    "withdraw.DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE"
                  ),
                  value: "PASS",
                },
              ],
              title: t("withdraw.DLOCAL_BANK_TRANSFER_documentType_title_AE"),
              placeholder: t(
                "withdraw.DLOCAL_BANK_TRANSFER_documentType_placeholder_AE"
              ),
              hint: "",
              value: "",
              validator: (val) => {
                return !!val;
              },
              selectConfirm: (
                { selectedValues, selectedOptions },
                row,
                form
              ) => {
                row.textValue = selectedOptions[0]?.text;
                row.value = selectedValues[0];
                row.showPicker = false;
                const documentIdRow = form.find((i) => i.key === "documentId");
                documentIdRow.value = "";
                if (row.value === "ID") {
                  documentIdRow.placeholder = t(
                    "withdraw.DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE"
                  );
                } else if (row.value === "PASS") {
                  documentIdRow.placeholder = t(
                    "withdraw.DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE"
                  );
                }
              },
            },
            documentId: {
              title: t("withdraw.DLOCAL_BANK_TRANSFER_documentId_title_AE"),
              placeholder: "",
              hint: t("withdraw.DLOCAL_BANK_TRANSFER_documentId_hint_AE"),
              value: "",
              validator: (val, formList) => {
                const documentTypeRow = formList.find(
                  (i) => i.key === "documentType"
                );
                if (documentTypeRow.value === "ID") {
                  // 18位数字或者字母或者（-）
                  return validatorMap.minAndMaxAndLetterAndNumOrChars(
                    val,
                    18,
                    18,
                    "-"
                  );
                } else if (documentTypeRow.value === "PASS") {
                  // 最多12个数字和字母的组合
                  return validatorMap.minAndMaxAndNumLetterOrChars3(val, 1, 12);
                }
              },
            },
          },
        },
      },
    },
    5: {
      ALL: {
        7: {
          AGENT: {
            fullName: {
              title: t("withdraw.AGENT_fullName_title_ID"),
              placeholder: t("withdraw.AGENT_fullName_placeholder_ID"),
              hint: t("withdraw.AGENT_fullName_hint_ID"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.AGENT_accountNo_Whatsapp_title_ALL"),
              placeholder: t(
                "withdraw.AGENT_accountNo_Whatsapp_placeholder_ALL"
              ),
              hint: t("withdraw.AGENT_accountNo_Whatsapp_hint_ALL"),
              value: "",
              validator: (val) => {
                return validatorMap.minAndMaxAndLetterAndNumOrChars(
                  val,
                  1,
                  40,
                  "+ "
                );
              },
            },
          },
        },
      },
      EG: {
        7: {
          AGENT: {
            fullName: {
              title: t("withdraw.AGENT_fullName_title_ID"),
              placeholder: t("withdraw.AGENT_fullName_placeholder_ID"),
              hint: t("withdraw.AGENT_fullName_hint_ID"),
              value: "",
              validator: (val) => {
                // 小于50个字符
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
            },
            accountNo: {
              title: t("withdraw.AGENT_accountNo_vodafone_title_EG"),
              placeholder: t("withdraw.WALLET_accountNo_placeholder_SA"),
              hint: t("withdraw.WALLET_accountNo_hint_EG_2"),
              value: "",
              validator: (val) => {
                // 请输入钱包绑定的手机号，01开头的11位数字，或国家/地区代码加 10 位数字
                return (
                  validatorMap.startsWithAndTotalNum(val, "01", 11) ||
                  validatorMap.prefixPhoneNumber2(val, 10)
                );
              },
            },
          },
        },
      },
    },
    7: {
      ALL: {
        8: {
          USDT: {
            blockchain: {
              type: "select",
              textValue: "",
              showPicker: false,
              options: [
                {
                  text: "BSC",
                  value: "BSC",
                },
                {
                  text: "TRX",
                  value: "TRX",
                },
                {
                  text: "TRC20",
                  value: "TRC20",
                },
                {
                  text: "BEP20",
                  value: "BEP20",
                },
                {
                  text: "SPL",
                  value: "SPL",
                },
                {
                  text: "Arbitrum / Optimism",
                  value: "Arbitrum / Optimism",
                },
              ],
              title: t("withdraw.USDT_blockchain_title_ALL"),
              placeholder: t("withdraw.USDT_blockchain_title_ALL"),
              // hint: t("withdraw.USDT_blockchain_title_ALL"),
              value: "",
              validator: (val) => {
                return validatorMap.minAndMaxValue(val, 1, 50);
              },
              selectConfirm: ({ selectedValues, selectedOptions }, row) => {
                row.textValue = selectedOptions[0]?.text;
                row.value = selectedValues[0];
                row.showPicker = false;
              },
            },
            address: {
              title: t("withdraw.USDT_address_title_ALL"),
              placeholder: t("withdraw.USDT_address_placeholder_ALL"),
              hint: t("withdraw.USDT_address_hint_common"),
              value: "",
              validator: (val) => {
                // 1、3、bc1开头，1或 3开头的地址:26-35 个字符，bc1开头的地址:42-62 个字符 。且禁止字符:'0`、'0`、'I' 、'i'
                // return new RegExp(
                //   "^(?!.*[0OIi])(?:[13][a-km-zA-HJ-NP-Z1-9]{25,34}|bc1[a-zA-HJ-NP-Z0-9]{39,59})$"
                // ).test(val);

                // 不超过62个字符
                return validatorMap.minAndMaxAndNumLetterOrChars5(val, 1, 62);
              },
            },
            accountNo: {
              title: t("withdraw.AGENT_accountNo_Whatsapp_title_ALL"),
              placeholder: t(
                "withdraw.AGENT_accountNo_Whatsapp_placeholder_ALL"
              ),
              hint: t("withdraw.AGENT_accountNo_Whatsapp_hint_ALL"),
              value: "",
              validator: (val) => {
                return validatorMap.minAndMaxAndLetterAndNumOrChars(
                  val,
                  1,
                  40,
                  "+ "
                );
              },
            },
          },
        },
      },
    },
  };
};

export default formStyle;
