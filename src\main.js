import { createApp } from "vue";
import { createPinia } from "pinia";
import { Lazyload } from "vant";
import App from "./App.vue";
import { beforeRenderPage, siyaApp } from "./utils/renderPage";
import { checkLang } from "@/i18n/index.js";
import { i18n, langList } from "@/i18n/index.js";
import router from "@/router/index.js";
import globalComponents from "@/components";
import VConsole from "vconsole";
import "@vant/touch-emulator";
import "virtual:svg-icons-register";
import "./assets/scss/global.scss";
import "swiper/css";

import directive from "./directive";

if (["development", "test", "stage"].includes(process.env.VITE_PROGRESS_ENV)) {
  // eslint-disable-next-line no-new
  new VConsole();
}
const pinia = createPinia();
const app = createApp(App);

// rem单位转成px
app.config.globalProperties.$pxToRemPx = (px) => {
  return ((px / (390 / 10)) * document.body.clientWidth) / 10;
};

app.config.globalProperties.$appBottomBarHeight = 0;
app.config.globalProperties.$app = app;
app.config.globalProperties.$appNavBarHeight = 0;
app.config.globalProperties.$appVersion = 0;
app.config.globalProperties.$isApp = false;
app.config.globalProperties.$appOS = 0; // 1 android 2 ios
app.config.globalProperties.$isHalf = false;
app.config.globalProperties.$cloned = "";

app.config.globalProperties.$siyaApp = siyaApp;
app.config.globalProperties.$toast = (msg, opt) => {
  showToast({
    message: msg,
    wordBreak: "break-word",
    ...opt,
  });
};

app.config.globalProperties.$closeAfterToast = (msg, opt) => {
  showToast({
    message: msg,
    forbidClick: true,
    onClose: () => {
      siyaApp("closeWebview");
    },
    ...opt,
  });
};

app.config.globalProperties.$langList = langList;
app.config.globalProperties.$languageFile = checkLang();

app.use(router);
app.use(i18n);
app.use(globalComponents);
app.use(pinia);
app.use(directive);
app.use(Lazyload, {
  lazyComponent: true,
});

async function renderApp() {
  await beforeRenderPage(app);
  app.mount("#app");
}

renderApp();

export default app;
