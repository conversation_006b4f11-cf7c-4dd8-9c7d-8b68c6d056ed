<template>
  <van-image
      :width="imgWidth"
      :height="imgHeight"
      :radius="imgRadius"
      :src="url"
      :fit="fit"
      lazy-load
  >
    <!--加载中提示-->
    <template v-slot:loading>
      <van-loading type="spinner" size="20" />
    </template>
  </van-image>
</template>

<script setup>
import { getCurrentInstance, ref, watch } from 'vue'

const { proxy } = getCurrentInstance()

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    default: 0,
  },
  height: {
    type: Number,
    default: 0,
  },
  radius: {
    type: Number,
    default: 0,
  },
  fit: {
    type: String,
    default: 'cover',
  },
})

const imgWidth = ref(0)
const imgHeight = ref(0)
const imgRadius = ref(0)
watch(() => props, (val) => {
  imgWidth.value = proxy.$pxToRemPx(val.width)
  imgHeight.value = proxy.$pxToRemPx(val.height)
  imgRadius.value = proxy.$pxToRemPx(val.radius)
}, {
  immediate: true,
})
</script>

<style scoped lang="scss">

</style>
