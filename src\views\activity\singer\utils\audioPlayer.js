import { ref } from "vue";
class AudioPlayer {
  constructor() {
    this.audio = new Audio();
    this.src = ref(null);
    this.audio.onerror = () => {
      this.src.value = null;
    };
    this.audio.onpause = () => {
      this.src.value = null;
    };
  }

  play(src) {
    this.src.value = src;
    this.audio.src = src;
    this.audio.currentTime = 0;
    this.audio.play();
  }

  pause() {
    this.audio.pause();
    this.src.value = null;
  }
}

export default new AudioPlayer();
