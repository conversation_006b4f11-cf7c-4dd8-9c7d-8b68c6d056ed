<template>
  <van-popup
    v-bind="$attrs"
    position="bottom"
    round
    safe-area-inset-bottom
    @click-overlay="close"
  >
    <main>
      <template v-if="!isSuccess">
        <div class="title">
          {{ $t("dealer_coin.coin_dealer_confim_transer_user") }}
        </div>
        <user-card :gender="false" :data="userinfo"></user-card>
        <van-field
          v-model="num"
          readonly
          class="field-class input-container"
          :class="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
          type="digit"
          :placeholder="$t('dealer_coin.coin_dealer_input_transfer_coin')"
          @touchstart.stop="show = true"
        >
          <template #extra>
            <span class="analog-text" ref="inputTextRef">{{ num }}</span>
          </template>
        </van-field>
        <div class="tip" :class="{ 'error-tip': isVerify && !verifyCoin }">
          {{ errorHint }}
        </div>
        <div class="btn-wrap flex">
          <van-button type="primary" class="cancel" plain @click="close">{{
            $t("common.common_cancel")
          }}</van-button>
          <van-button type="primary" @click="transfer">{{
            $t("common.common_confirm")
          }}</van-button>
        </div>
      </template>
      <template v-else>
        <div class="title">
          {{ $t("dealer_coin.coin_dealer_transfer_success") }}
        </div>
        <img
          class="success-icon"
          src="@/assets/images/dealer-coin/success.png"
          alt=""
        />
        <div class="success-wrap flex">
          <user-card
            :gender="false"
            class="user-card"
            :data="userinfo"
          ></user-card>
          <div class="coin-wrap flex">
            <img
              src="@/assets/images/common/coin.png"
              alt=""
              class="coin-img"
            />
            <gap :gap="2" />
            <span>{{ num }}</span>
          </div>
        </div>
        <van-button type="primary" block @click="confirm">{{
          $t("common.common_confirm")
        }}</van-button>
      </template>
    </main>
    <van-number-keyboard
      v-model="num"
      class="isolated"
      :show="!isSuccess"
      :transition="false"
      :extra-key="['00']"
    />
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { i18n } from "@/i18n/index.js";
import dealerApi from "@/api/dealer.js";
import { debounce } from "lodash";
import UserCard from "./UserCard.vue";

const props = defineProps({
  userinfo: {
    type: Object,
  },
  wallet: {
    type: Object,
  },
});
const t = i18n.global.t;
const inputTextRef = ref(null);
const inputWidth = ref(0);
const emits = defineEmits(["update:show", "refresh"]);
const isSuccess = ref(false);
const errorHint = ref(
  t("dealer_coin.coin_dealer_input_coin_rules", [
    props.wallet.min_transfer_coin,
  ])
);
const num = ref();
const isVerify = ref(false);

const verifyCoin = computed(() => {
  if (num.value < props.wallet.min_transfer_coin || num.value % 10 !== 0) {
    errorHint.value = t("dealer_coin.coin_dealer_input_coin_rules", [
      props.wallet.min_transfer_coin,
    ]);
    return false;
  }
  if (num.value > props.wallet.coin) {
    errorHint.value = t("dealer_coin.coin_dealer_transfer_usable_hint");
    return false;
  }
  return true;
});

function updateCursorPosition() {
  if (!inputTextRef?.value) return false;
  nextTick(() => {
    inputWidth.value = inputTextRef.value.offsetWidth;
  });
}

watch(
  () => num.value,
  () => {
    updateCursorPosition();
  },
  { immediate: true }
);

const close = () => {
  emits("update:show", false);
};

const confirm = () => {
  close();
};

const transfer = debounce(async () => {
  isVerify.value = true;
  if (!verifyCoin.value) return;
  const resResult = await dealerApi.transfer_coins({
    account: props.userinfo.account,
    coin: parseInt(num.value),
  });

  if (resResult.code == 200) {
    isSuccess.value = true;
    emits("refresh");
  }
}, 300);
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

main {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $gap24 $gap24 $gap16 $gap24;
  .title {
    color: $fontColorB0;
    font-weight: 700;
    font-size: $fontSize17;
    margin-bottom: $gap16;
  }
  .field-class {
    margin: $gap16 0 $gap8;
  }
  .error-hint {
    color: $subColorRed;
  }
  .tip {
    color: $fontColorB3;
    font-size: $fontSize11;
    margin-bottom: $gap24;
    text-align: left;
    width: 100%;
  }
  .error-tip {
    color: $subColorRed;
  }

  .success-icon {
    width: px2rem(50);
    height: px2rem(50);
  }
  .success-wrap {
    background: $bgColorB7;
    padding: $gap16;
    flex-direction: column;
    border-radius: $radius16;
    width: 100%;
    margin: $gap16 0;
    .user-card {
      background-color: $bgColorB7;
    }
    .coin-wrap {
      color: $fontColorB1;
      font-size: px2rem(20);
      font-weight: 700;
      .coin-img {
        width: px2rem(20);
        height: px2rem(20);
      }
    }
  }

  .btn-wrap {
    justify-content: space-between;
    width: 100%;
    .cancel {
      border: none;
      background: #f8f7ff;
    }
    .van-button {
      width: px2rem(163);
    }
  }
}

.analog-text {
  position: absolute;
  z-index: -100;
}
::v-deep(.input-container .van-field__body::after) {
  content: "";
  position: absolute;
  top: 50%;
  left: v-bind("inputWidth + 'px'");
  width: 2px;
  height: 16px;
  background-color: $fontColorB1;
  transform: translateY(-50%);
  animation: cursorBlink 1s steps(1) infinite;
}
::v-deep(.rtl.input-container .van-field__body::after) {
  right: v-bind("inputWidth + 'px'") !important;
}

@keyframes cursorBlink {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

::v-deep(.van-number-keyboard) {
  position: relative;
}

::v-deep(.van-field) {
  background: $bgColorB6;
  border-radius: $radius12;
}
</style>
