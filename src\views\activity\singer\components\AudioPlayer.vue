<template>
  <div class="audio-player">
    <div
      class="play-btn"
      v-if="unref(audioPlayer.src) !== url"
      @click="play()"
    ></div>
    <div class="pause-btn" v-else @click="pause()"></div>
  </div>
</template>
<script setup>
import audioPlayer from "../utils/audioPlayer";
import { onUnmounted, unref } from "vue";
const props = defineProps({
  url: {
    type: String,
  },
});
const play = () => {
  if (!props.url) {
    return;
  }
  audioPlayer.play(props.url);
};
const pause = () => {
  audioPlayer.pause();
};

onUnmounted(() => {
  pause();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.audio-player {
  width: px2rem(28);
  height: px2rem(28);
  display: block;
  .play-btn,
  .pause-btn {
    width: 100%;
    height: 100%;
    background-image: url("@/assets/images/activity/singer/icon-play.svg");
    background-size: 100% 100%;
  }
  .play-btn {
    background-image: url("@/assets/images/activity/singer/icon-play.svg");
  }
  .pause-btn {
    background-image: url("@/assets/images/activity/singer/icon-pause.svg");
  }
}
</style>
