<template>
  <van-overlay
    class-name="prize-dialog"
    :show="showDialog"
    :lock-scroll="false"
    z-index="10001"
  >
    <div class="prize-box">
      <div class="background">
        <div class="background-top"></div>
        <div class="background-padding"></div>
      </div>
      <div class="prize-content">
        <div class="prize-price">
          <div class="coin-box">
            <GenderCoin :size="23"></GenderCoin>
            <gap :gap="5"></gap>
            <div class="coin-number">{{ data?.coin }}</div>
          </div>
        </div>
        <div class="prize-desc">
          {{ $t("recharge_ranking.recharge_tip", [data?.coin]) }}
        </div>
        <div class="rewards" v-if="data.rewards">
          <div class="reward-inner">
            <div
              class="reward"
              v-for="(item, index) in data.rewards"
              :key="index"
            >
              <div class="reward-cover">
                <PrizeBox
                  :img="
                    item.reward_type === 1
                      ? CoinIcon
                      : item.reward_img || CoinIcon
                  "
                  :tag="formatTag(item)"
                ></PrizeBox>
              </div>
              <div class="reward-name">{{ item.reward_name }}</div>
            </div>
          </div>
        </div>
        <div class="prize-tip">
          {{ $t("recharge_ranking.prize_desc") }}
        </div>
      </div>
    </div>
    <div class="close" @click="showDialog = false"></div>
  </van-overlay>
</template>
<script setup>
import PrizeBox from "./PrizeBox.vue";
import { ref } from "vue";
import CoinIcon from "@/assets/images/common/coin.svg";
import { i18n, getLang } from "@/i18n/index.js";
const t = i18n.global.t;

const showDialog = ref(false);
const data = ref(null);

const open = (prize) => {
  data.value = prize;
  showDialog.value = true;
};

const formatTag = (item) => {
  const lang = getLang();
  if (item.reward_type === 1) {
    return lang === "ar" ? `${item.num}X` : `X${item.num}`;
  }
  return item.duration > 1
    ? t("common.common_days", [item.duration])
    : t("common.common_day", [item.duration]);
};

defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-dialog {
  --van-overlay-background: rgba(0, 0, 0, 0.69);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.prize-box {
  position: relative;
  width: px2rem(346);
  min-height: px2rem(245);
  flex-shrink: 0;
  animation: show 0.3s 1;
  .background {
    width: px2rem(346);
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    .background-top {
      z-index: 4;
      width: px2rem(458);
      height: px2rem(166);
      position: absolute;
      top: px2rem(-58);
      left: px2rem(-53);
      background-image: url("@/assets/images/activity/recharge-ranking/dialog-top.png"); /* 中间部分的图片 */
      background-size: 100% 100%;
      transform: scale(0.8);
    }
    .background-padding {
      flex-grow: 1;
      background-image: url("@/assets/images/activity/recharge-ranking/reward-box-2.png"); /* 中间部分的图片 */
      background-repeat: repeat-y; /* 垂直重复 */
      background-position: center left;
      background-size: 100%;
    }
  }

  .background::before {
    content: "";
    width: 100%;
    height: px2rem(85); /* 顶部图片的高度 */
    background-image: url("@/assets/images/activity/recharge-ranking/reward-box-1.png"); /* 顶部图片 */
    background-repeat: no-repeat;
    background-position: top left;
    background-size: 100% 100%;
    margin-bottom: px2rem(-2);
  }

  .background::after {
    content: "";
    width: 100%;
    height: px2rem(85); /* 顶部图片的高度 */
    background-image: url("@/assets/images/activity/recharge-ranking/reward-box-1.png"); /* 使用相同的顶部图片 */
    background-repeat: no-repeat;
    background-position: top left;
    background-size: 100% 100%;
    transform: scaleY(-1); /* 垂直翻转 */
    margin-top: px2rem(-2);
  }

  .prize-content {
    position: relative;
    .prize-price {
      margin: 0 auto;
      margin-top: px2rem(25);
      display: flex;
      justify-content: center;
      align-items: center;
      height: px2rem(34);
      padding: px2rem(2) px2rem(19);
      width: fit-content;
      min-width: px2rem(147);
      background: linear-gradient(
        90deg,
        rgba(255, 252, 227, 0) 0%,
        rgba(255, 233, 145, 0.36) 50.5%,
        rgba(255, 251, 237, 0) 100%
      );
      .coin-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: px2rem(58);
        flex-shrink: 0;
        .coin-number {
          color: #ffda6d;
          font-family: "DIN Next W1G";
          font-size: px2rem(20);
          font-style: normal;
          font-weight: 700;
          line-height: 1.3;
          letter-spacing: -0.5px;
        }
      }
    }
    .prize-desc {
      width: px2rem(238);
      color: rgba(255, 240, 130, 0.6);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin: 0 auto;
      margin-top: px2rem(17);
    }
    .prize-tip {
      width: px2rem(238);
      color: rgba(255, 240, 130, 0.6);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(12);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin: 0 auto;
      padding-bottom: px2rem(50);
    }
    .rewards {
      margin: 0 auto;
      margin-top: px2rem(23);
      overflow-x: auto;
      overflow-y: hidden;
      width: 90%;
      -ms-overflow-style: none; /* IE和Edge */
      scrollbar-width: none; /* Firefox */
      &::-webkit-scrollbar {
        display: none; /* Chrome/Safari/Opera */
      }
      .reward-inner {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-wrap: nowrap;
        gap: px2rem(4);
        width: fit-content;
        margin: 0 auto;
        padding-bottom: px2rem(10);

        .reward {
          flex-shrink: 0;
          width: px2rem(54);
          .reward-cover {
            width: px2rem(54);
            height: px2rem(54);
          }
          .reward-name {
            color: #e3857b;
            text-align: center;
            font-family: Gilroy;
            font-size: px2rem(8);
            font-style: normal;
            font-weight: 700;
            line-height: 100%; /* 12px */
            margin-top: px2rem(7);
          }
        }
      }
    }
  }
}
.close {
  width: px2rem(40);
  height: px2rem(40);
  font-size: px2rem(40);
  margin-top: px2rem(19);
  color: #ffe8c3;
  background-image: url("@/assets/images/activity/recharge-ranking/dialog-close.svg"); /* 中间部分的图片 */
  background-size: 100% 100%;
}
</style>
