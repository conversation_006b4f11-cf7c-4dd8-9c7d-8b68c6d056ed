@import '@/assets/scss/common.scss';

.info-card {
  padding: $gap16;
  margin-bottom: $gap12;
  background-color: $white;
  border-radius: $radius16;

  .cell {
    &:not(:last-child) {
      margin-bottom: px2rem(28);
    }
  }

  .cell-title {
    justify-content: space-between;
    margin-bottom: $gap8;
    font-weight: $fontWeightBold;
    color: $fontColorB3;

    @include fontSize13;

    .icon-edit {
      width: px2rem(28);
      height: px2rem(28);
    }

    .button-change {
      padding: px2rem(3.5) px2rem(7);
      font-weight: $fontWeightNormal;
      border-radius: $radius16;

      @include fontSize11;

      &.success {
        color: $subColorGreen2;
        background-color: rgba($subColorGreen2, .1);
      }

      &.fail {
        color: $subColorRed;
        background-color: rgba($subColorRed, .1);
      }

      &.pending {
        color: $mainColor;
        background-color: rgba($mainColor, .1);
      }

      .icon-arrow {
        width: px2rem(12);
        height: px2rem(12);
      }
    }
  }

  .total-amount-field {
    font-weight: $fontWeightBold;
    font-size: px2rem(24);
    line-height: px2rem(29);
    color: $fontColorB1;
  }

  .method-field {
    background-color: $bgColorB7;
    border-radius: $radius8;

    .payment-name {
      color: $fontColorB1;

      @include fontSize15;
    }
  }

  .info-field {
    padding: $gap12;
    background-color: $bgColorB7;
    border-radius: $radius8;

    .info-field-child {
      align-items: flex-start;
      flex-wrap: nowrap;
      justify-content: space-between;

      &:not(:last-child) {
        margin-bottom: $gap16;
      }
    }

    .info-field-key {
      // margin-right: $gap16;
      flex: none;
      color: $fontColorB3;

      @include fontSize13;
    }

    .info-field-value {
      // margin-left: auto;
      color: $fontColorB1;
      word-break: break-all;

      @include fontSize13;
    }
  }
}

.fail-reason {
  padding: $gap16;
  background-color: $white;
  border-radius: $radius16;

  .fail-reason-title {
    margin-bottom: $gap8;
    font-weight: $fontWeightBold;
    color: $fontColorB3;

    @include fontSize13;
  }

  .fail-reason-detail {
    color: $fontColorB1;
    word-break: break-all;

    @include fontSize13;
  }
}

.submit-button {
  margin-top: $gap24;
  margin-bottom: $gap24;
}
