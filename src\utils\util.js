import dayjs from "dayjs";
// import OSS from 'ali-oss'
import { PAGInit, types } from "libpag";
import { createDirectUploadTask } from "qiniu-js";
import baseApi from "@/api/base.js";

// 获取链接指定参数
export const getUrlKey = (name) => {
  return (
    decodeURIComponent(
      (new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(
        location.href
      ) || [, ""])[1].replace(/\+/g, "%20")
    ) || null
  );
};

// 格式化时间
export const formatDate = (time, temp = "YYYY-MM-DD HH:mm") => {
  return dayjs(time).format(temp);
};

// 货币 千位数加逗号
export const numberWithCommas = (x) => {
  if (!x) return 0;
  return x.toString().replace(/\d+/, function (n) {
    return n.replace(/(\d)(?=(\d{3})+$)/g, function ($1) {
      return $1 + ",";
    });
  });
};

// 货币 加单位和小数
export const numberWithUnit = (val, float = 2) => {
  const defaultResult = { val: "0", unit: "" };

  if (!val) return defaultResult;

  // 正负号
  let first = String(val)[0];
  let abs = ["-", "+"].includes(first) ? first : "";
  let x = Math.abs(val);

  let T = 1_000_000_000_000;
  let B = 1_000_000_000;
  let M = 1_000_000;
  let K = 1_000;

  // 格式化
  const format = (num, len, unit) => {
    let numLen = Math.pow(10, String(len).length - 1);
    num = String(num / numLen);
    num = Number(num)
      .toFixed(10)
      .slice(0, float - 10);
    return {
      val: `${abs}${Number(num)}`,
      unit,
    };
  };

  // 判断单位
  let result = defaultResult;
  if (x >= T) result = format(x, T, "T");
  else if (x >= B) result = format(x, B, "B");
  else if (x >= M) result = format(x, M, "M");
  else if (x >= K) result = format(x, K, "K");
  else result.val = val;
  return result;
};

// 邮箱验证
export const isEmail = (x) => {
  const reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  return reg.test(x);
};

// // 获取bucket配置
// let ossClient = null;
// let buckets = {};
// let token = {};
// const getClientConfig = async () => {
//   const result = await baseApi.oss_config()
//   if (result.code === 200) {
//     ({ buckets, token } = result.data.ossConfig)

//     const region = result.data?.endpoint?.split('.')[0]
//       ?.split('//')[1] || ''

//     ossClient = new OSS({
//       bucket: buckets[0].bucket,
//       region,
//       accessKeyId: token.accessKeyID,
//       accessKeySecret: token.accessKeySecret,
//       stsToken: token.token,
//     });
//   }
// }

// // oss上传文件
// export const uploadFile = async (fileItem) => {
//   if (!ossClient) await getClientConfig()
//   const path = 'h5/development/';
//   let rdmString = '';
//   for (; rdmString.length < 24; ) {
//     rdmString += Math.random().toString(36).substr(2);
//   }
//   const str = `${fileItem.file.name}.`.split('.')[1];
//   const fileName = `${rdmString}.${str}`;
//   const fullPathFile = `${path}${fileName}`;

//   const result = await ossClient.put(fullPathFile, fileItem.file);
//   return result.url
// }

// 七牛云上传文件
export const uploadFile = async (fileItem, progressCb) => {
  const result = await baseApi.qiniu_config();
  const { token, cdnDomain, buckets, uploadDomains } = result.data.kodoConfig;
  const bucket = buckets[0];
  const uploadHosts = uploadDomains.map((item) =>
    item.replace(/^https?:\/\//, "")
  );
  const path = `h5/${bucket.bucket}/${bucket.dir}/`;
  let rdmString = "";
  for (; rdmString.length < 24; ) {
    rdmString += Math.random().toString(36).substr(2);
  }
  const str = `${fileItem.file.name}.`.split(".")[1];
  const fileName = `${rdmString}.${str}`;
  const fullPathFile = `${path}${fileName}`;
  const fileData = {
    type: "file",
    data: fileItem.file,
    key: fullPathFile,
  };
  function tokenProvider() {
    return new Promise((resolve) => {
      resolve(token.token);
    });
  }
  return new Promise((resolve, reject) => {
    const uploadTask = createDirectUploadTask(fileData, {
      uploadHosts,
      tokenProvider,
    });
    uploadTask.onProgress((progress) => {
      progressCb && progressCb(progress);
    });
    uploadTask.onComplete((res) => {
      resolve(`${cdnDomain}/${JSON.parse(res)?.key}`);
    });
    uploadTask.onError((error) => {
      reject(error);
    });
    uploadTask.start();
  });
  // const result = await ossClient.put(fullPathFile, fileItem.file);
  // return result.url
};

export const AnimationPAGInit = async (fileUrl, pagCanvas) => {
  // 实例化 PAG
  const PAG = await PAGInit({
    // /public/libpag.wasm
    locateFile: (file) => `/${file}`,
  });
  // 获取 PAG 素材数据
  const buffer = await fetch(fileUrl).then((response) =>
    response.arrayBuffer()
  );
  // 加载 PAG 素材为 PAGFile 对象
  const pagFile = await PAG.PAGFile.load(buffer);
  // 实例化 PAGView 对象
  return await PAG.PAGView.init(pagFile, pagCanvas);
};

export const getTimeRange = (val) => {
  const now = new Date();
  // 获取某天的开始时间（00:00:00.000）
  const getStartOfDay = (date) => {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    return Math.floor(d.getTime() / 1000); // 转换为秒精度
  };
  // 获取某天的结束时间（23:59:59.999）
  const getEndOfDay = (date) => {
    const d = new Date(date);
    d.setHours(23, 59, 59, 999);
    return Math.floor(d.getTime() / 1000); // 转换为秒精度
  };
  // 获取本周一
  const getMonday = (date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 如果是周日，需要减6天
    d.setDate(diff);
    return d;
  };

  switch (val) {
    case 1: // 今日
      return {
        startTime: getStartOfDay(now),
        endTime: Math.floor(now.getTime() / 1000), // 转换为秒精度
      };
    case 2: // 昨日
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      return {
        startTime: getStartOfDay(yesterday),
        endTime: getEndOfDay(yesterday),
      };
    case 3: // 本周
      const monday = getMonday(now);
      return {
        startTime: getStartOfDay(monday),
        endTime: Math.floor(now.getTime() / 1000), // 转换为秒精度
      };
    case 4: // 上周
      const lastMonday = getMonday(now);
      lastMonday.setDate(lastMonday.getDate() - 7);
      const lastSunday = new Date(lastMonday);
      lastSunday.setDate(lastMonday.getDate() + 6);
      return {
        startTime: getStartOfDay(lastMonday),
        endTime: getEndOfDay(lastSunday),
      };
    case 5: // 本月
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
      return {
        startTime: getStartOfDay(monthStart),
        endTime: Math.floor(now.getTime() / 1000), // 转换为秒精度
      };
    case 6: // 上月
      const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
      return {
        startTime: getStartOfDay(lastMonthStart),
        endTime: getEndOfDay(lastMonthEnd),
      };
    case 7: // 前7天
      const sevenDaysAgo = new Date(now);
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const yesterday1 = new Date(now);
      yesterday1.setDate(yesterday1.getDate() - 1);
      return {
        startTime: getStartOfDay(sevenDaysAgo),
        endTime: getEndOfDay(yesterday1),
      };
    case 8: // 前30天
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const yesterday2 = new Date(now);
      yesterday2.setDate(yesterday2.getDate() - 1);
      return {
        startTime: getStartOfDay(thirtyDaysAgo),
        endTime: getEndOfDay(yesterday2),
      };
    case 9: // 前两周
      const twoWeeksAgoMonday = getMonday(now);
      twoWeeksAgoMonday.setDate(twoWeeksAgoMonday.getDate() - 14);
      const twoWeeksAgoSunday = new Date(twoWeeksAgoMonday);
      twoWeeksAgoSunday.setDate(twoWeeksAgoMonday.getDate() + 6);
      return {
        startTime: getStartOfDay(twoWeeksAgoMonday),
        endTime: getEndOfDay(twoWeeksAgoSunday),
      };
    case 10: // 前三周
      const threeWeeksAgoMonday = getMonday(now);
      threeWeeksAgoMonday.setDate(threeWeeksAgoMonday.getDate() - 21);
      const threeWeeksAgoSunday = new Date(threeWeeksAgoMonday);
      threeWeeksAgoSunday.setDate(threeWeeksAgoMonday.getDate() + 6);
      return {
        startTime: getStartOfDay(threeWeeksAgoMonday),
        endTime: getEndOfDay(threeWeeksAgoSunday),
      };
    default:
      return {
        startTime: getStartOfDay(now),
        endTime: Math.floor(now.getTime() / 1000), // 转换为秒精度
      };
  }
};

export const AnimationPAGInitLocal = async (
  fileName,
  pagCanvas,
  beforInitFn
) => {
  const PAG = await PAGInit({
    locateFile: (file) => {
      return `/${file}`;
    },
  });

  // 通过import获取资源URL（Vite专用方式）
  const pagUrl = new URL(
    `../assets/images/animations/${fileName}`,
    import.meta.url
  ).href;

  console.log("pagUrl==>", pagUrl);

  const buffer = await fetch(pagUrl).then((response) => response.arrayBuffer());

  const pagFile = await PAG.PAGFile.load(buffer);
  beforInitFn && beforInitFn(PAG, pagFile, types);
  return await PAG.PAGView.init(pagFile, pagCanvas);
};

export const getRandomInt = (min, max) => {
  const range = max - min + 1;
  const maxRange = 4294967295;
  const byteArray = new Uint32Array(1);

  window.crypto.getRandomValues(byteArray);

  if (byteArray[0] >= Math.floor(maxRange / range) * range) {
    return getSecureRandom(min, max);
  }

  return min + (byteArray[0] % range);
};

// H5判断客户端类型=>安卓
export const isAndroid = () => {
  var userAgent = navigator.userAgent.toLowerCase();
  return /android/.test(userAgent);
};

// H5判断客户端类型=>iOS
export const isiOS = () => {
  var userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
};

// 下载文件
export const downloadFile = (url, filename) => {
  console.log("download");

  const a = document.createElement("a");
  a.href = url;
  a.download = filename || "download";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const urlToBase64 = async (url) => {
  try {
    const response = await fetch(url);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new Error("Fetch failed: " + error.message);
  }
};

export const downloadApp = (adjustUrl) => {
  const iosLinkUrl = "https://apps.apple.com/app/id6738165319"; // 替换为实际的iOS应用ID
  const androidLinkUrl =
    "https://play.google.com/store/apps/details?id=com.zr.siya"; // 替换为实际的Android应用包名
  if (isiOS()) {
    window.location.href = iosLinkUrl;
  } else if (isAndroid()) {
    const urlParams = new URLSearchParams(window.location.search);
    const p0 = urlParams.get("p0");
    if (p0) {
      window.location.href = adjustUrl;
    } else {
      window.location.href = androidLinkUrl;
    }
  }
};

// 页面平滑滚动到某个节点
export const smoothScrollTo = (element, offset = 0) => {
  const elementPosition = element.getBoundingClientRect().top;
  const offsetPosition = elementPosition + window.pageYOffset - offset;
  window.scrollTo({
    top: offsetPosition,
    behavior: "smooth",
  });
};

export const preloadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
};

export const maskName = (name) => {
  const len = name.length;

  if (len <= 1) return "*";
  if (len === 2) return name[0] + "*";

  const maskedCount = Math.min(len - 2, 2);
  const masked = "*".repeat(maskedCount);
  return name[0] + masked + name[len - 1];
};

export const formatCount = (number = 0) => {
  if (number < 1000) {
    return number;
  }
  if (number < 1000000) {
    return `${Math.floor(number / 100) / 10}K`;
  }
  return `${Math.floor(number / 100000) / 10}M`;
};
