<template>
  <div class="title-bar isolated">
    <img
      src="@/assets/images/ranking/weekly-star/title-icon.png"
      alt=""
      style="transform: scaleX(-1)"
    />
    <span>{{ title }}</span>
    <img src="@/assets/images/ranking/weekly-star/title-icon.png" alt="" />
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;
const router = useRouter();
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.title-bar {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: px2rem(20) 0;
  img {
    width: px2rem(39);
    height: px2rem(14);
    margin-top: px2rem(6);
  }
  span {
    -webkit-text-stroke: v-bind(
      "['zh-CN','zh-TW'].includes(lang)?'none':'1px #8947df'"
    );
    font-weight: 900;
    background: linear-gradient(180deg, #f4c6ff 0%, #ffffff 100%);
    /* 将背景裁剪为文字形状 */
    -webkit-background-clip: text;
    background-clip: text;

    /* 将文字颜色设为透明，以显示背景渐变 */
    color: transparent;
    text-align: center;
    font-family: GilroyItalic;
    font-size: px2rem(20);
    margin: 0 px2rem(16);
  }
}
</style>
