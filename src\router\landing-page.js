const routes = [
  {
    path: "/landing-page",
    name: "LandingPage",
    component: () =>
      import(/*webChunkName: "LandingPage"*/ "../views/landing-page/Index.vue"),
  },
  {
    path: "/landing-page2",
    name: "LandingPage2",
    component: () =>
      import(/*webChunkName: "LandingPage2"*/ "../views/landing-page/Version2.vue"),
  },
  {
    path: "/landing-page3",
    name: "LandingPage3",
    component: () =>
      import(/*webChunkName: "LandingPage3"*/ "../views/landing-page/Version3.vue"),
  },
  {
    path: "/landing-page4",
    name: "LandingPage4",
    component: () =>
      import(/*webChunkName: "LandingPage4"*/ "../views/landing-page/Version4.vue"),
  },
  {
    path: "/landing-page5",
    name: "LandingPage5",
    component: () =>
      import(/*webChunkName: "LandingPage5"*/ "../views/landing-page/Version5.vue"),
  },
  {
    path: "/landing-page6",
    name: "LandingPage6",
    component: () =>
      import(/*webChunkName: "LandingPage6"*/ "../views/landing-page/Version6.vue"),
  },
];

export default routes;
