<template>
  <div class="party-container">
    <header-bar
      back-icon-show
      :show-title="false"
      :padding="false"
      :contentPadding="false"
      close-type="close"
    />
    <div class="title" :data-title="$t('party_poster.title')">
      <div>{{ $t("party_poster.title") }}</div>
    </div>
    <div class="content">
      <div class="box1 mt-91">
        <div class="box1-content">
          <div class="box1-content-text">
            {{ $t("party_poster.description") }}
          </div>
        </div>
      </div>
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.reward_1')"
              >
                <div>{{ $t("party_poster.reward_1") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">
                  {{ $t("party_poster.heat_value_requirements") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.room_owner_rewards") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.coin_rain_rewards") }}
                </div>
              </div>
              <!-- 数据行 -->
              <div class="table-row">
                <div class="table-cell">
                  <div class="reward-item">
                    <div class="cover">
                      <img src="@/assets/images/activity/poster/party/rewards/1.png" /></img>
                    </div>
                    <div class="desc">x2</div>
                  </div>
                </div>
                <div class="table-cell">XXXX XXXXX</div>
                <div class="table-cell">
                  <div class="reward-item">
                    <div class="cover">
                      <img src="@/assets/images/activity/poster/party/rewards/1.png" /></img>
                    </div>
                    <div class="desc">x2</div>
                  </div>
                </div>
              </div>
              <div class="table-row">
                <div class="table-cell">XXXXXX</div>
                <div class="table-cell">XXXX XXXXX</div>
                <div class="table-cell">XXXXXX</div>
              </div>
              <div class="table-row">
                <div class="table-cell">XXXXXX</div>
                <div class="table-cell">XXXX XXXXX</div>
                <div class="table-cell">XXXXXX</div>
              </div>
              <div class="table-row">
                <div class="table-cell">XXXXXX</div>
                <div class="table-cell">XXXX XXXXX</div>
                <div class="table-cell">XXXXXX</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二个表格 - Raward 2 -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.reward_2')"
              >
                <div>{{ $t("party_poster.reward_2") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content table-content-merged">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">
                  {{ $t("party_poster.party_requirements") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.room_owner_rewards") }}
                </div>
                <div class="table-cell">
                  {{ $t("party_poster.top3_gift_recipients_rewards") }}
                </div>
              </div>
              <!-- 使用Grid布局实现合并单元格 -->
              <div class="merged-table-grid">
                <!-- 第一列合并单元格 -->
                <div class="merged-cell-1">
                  <div class="requirement-text">
                    *The number of users entering the room reaches XX
                  </div>
                  <div class="requirement-text">
                    *The number of gift-giving users reaches XX
                  </div>
                  <div class="requirement-text">
                    *The room heat value reaches XXXX
                  </div>
                </div>
                <!-- 第二列合并单元格 -->
                <div class="merged-cell-2">
                  <div class="reward-text">XXXXXX</div>
                  <div class="reward-text">XXXXXX</div>
                  <div class="reward-text">XXXXXX</div>
                </div>
                <!-- 第三列独立单元格 -->
                <div class="individual-cell">XXXXXX</div>
                <div class="individual-cell">XXXXXX</div>
                <div class="individual-cell">XXXXXX</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 注释文本 -->
      <div class="note-text">
        {{ $t("party_poster.note", [2]) }}
      </div>

      <!-- 第三个表格 - Extra reward -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <div class="table">
            <!-- 表头 -->
            <div class="table-header">
              <div
                class="header-title"
                :data-title="$t('party_poster.extra_reward')"
              >
                <div>{{ $t("party_poster.extra_reward") }}</div>
              </div>
            </div>
            <!-- 表格内容 -->
            <div class="table-content table-content-two-columns">
              <!-- 列标题行 -->
              <div class="table-row header-row">
                <div class="table-cell">Number of qualified parties</div>
                <div class="table-cell">Rewards for room owner</div>
              </div>
              <!-- 数据行 -->
              <div class="table-row">
                <div class="table-cell">XXXXXX</div>
                <div class="table-cell">XXXXXX</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 底部注释文本 -->
      <div class="note-text">
        *Note: If the party numbers that you hold meet the requirements of
        Reward 2 between July 14 and July 31, you can get an additional honorary
        title. The honorary title will be issued on August 1.
      </div>

      <!-- Party play guide 部分 -->
      <div class="box1 mt-16">
        <div class="box1-content">
          <!-- 表头 -->
          <div class="box4-header">
            <span>Party play guide</span>
          </div>
          <div class="guide-container">
            <!-- Talent Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>Talent Party</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">Microphone position</div>
                <div class="guide-text">
                  The host has a giant microphone position 1, and microphones
                  2-9 are used as contestants. Audience members can not obtain
                  the microphone.
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Competition process</div>
                <div class="guide-text">
                  Contestants sing one by one, 1-2 songs each. The host
                  introduces the contestants, and the whole audience is silent
                  during the singing to maintain order.
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Interactive voting</div>
                <div class="guide-text">
                  After the singing, the audience can vote on the public screen
                  or send gifts to add points for their favorite contestants.
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Promotion elimination</div>
                <div class="guide-text">
                  After the singing, the audience can vote on the public screen
                  or send gifts to add points for their favorite contestants.
                </div>
              </div>
            </div>

            <!-- PK Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>PK Party</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">Open PK mode</div>
                <div class="guide-text">Open microphone PK or team PK</div>
              </div>

              <div class="guide-item">
                <div class="guide-label">PK process</div>
                <div class="guide-text">
                  Host announces the start of PK, PK players conduct PK
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Result announcement</div>
                <div class="guide-text">
                  Countdown ends, the system automatically settles the PK score,
                  the host announces the winner and the punishment or reward
                  content (such as the winner sings a designated song, the loser
                  performs a talent show)
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Interesting interaction</div>
                <div class="guide-text">
                  The room can be set to accept interesting punishments to
                  enhance the entertainment atmosphere
                </div>
              </div>
            </div>

            <!-- Game Party 部分 -->
            <div class="party-section">
              <div class="party-title">
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
                <span>Game Party</span>
                <img
                  src="@/assets/images/activity/poster/party/star-2.png"
                  class="star-icon"
                  alt="star"
                />
              </div>

              <div class="guide-item">
                <div class="guide-label">Mini-games</div>
                <div class="guide-text">
                  Microphone No. 1 is the host, and microphones 2-9 are free to
                  participate in small games (such as your draw and I guess,
                  brain chain, and is the undercover, etc)
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Game process</div>
                <div class="guide-text">
                  The host introduces the rules of this round of games and
                  allocates corresponding roles. It is recommended that the
                  winners the question first will go to the microphone!
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Interactive participation</div>
                <div class="guide-text">
                  After the game, the outstanding performer retains the
                  microphone, and the loser leaves the microphone to let new
                  players join
                </div>
              </div>

              <div class="guide-item">
                <div class="guide-label">Interactive method</div>
                <div class="guide-text">
                  Audience can type or cheer for the players through the public
                  screen, or be invited to the microphone as an interactive
                  memory opponent in specific links
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup></script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.party-container {
  position: relative;
  min-height: 100vh;
  background-image: url("@/assets/images/activity/poster/party/bg-1.png");
  background-size: 100% px2rem(394);
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: px2rem(10);
    left: px2rem(-76);
    width: px2rem(488);
    height: px2rem(293);
    background-image: url("@/assets/images/activity/poster/party/bg-2.png");
    background-size: px2rem(488) px2rem(293);
    background-repeat: no-repeat;
  }

  .title {
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(34);
    font-style: italic;
    font-weight: 900;
    line-height: px2rem(34); /* 100% */
    letter-spacing: px2rem(-0.32);
    position: relative;
    z-index: 2;
    margin-top: px2rem(36);

    z-index: 0;
    div {
      color: #fff;
    }
    &::after {
      content: attr(data-title);
      text-shadow: px2rem(0) px2rem(4) px2rem(15.2) #f00;
      -webkit-text-stroke-width: px2rem(2);
      -webkit-text-stroke-color: #fb00ff;
      text-align: center;
      position: absolute;
      inset: 0;
      z-index: -1;
    }
  }

  .content {
    margin-top: px2rem(200);
    width: 100%;
    flex-shrink: 0;
    background: linear-gradient(
      180deg,
      rgba(183, 89, 220, 0) 0.75%,
      #b659db 3.23%,
      #d569ff 100%
    );
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: px2rem(52);

    .box1 {
      width: px2rem(363);
      flex-shrink: 0;
      border-radius: px2rem(17);
      border: px2rem(1) solid #fff;
      background: rgba(255, 255, 255, 0.25);
      padding: px2rem(5);
      .box1-content {
        border-radius: px2rem(12);
        background: #bc35f2;

        color: #fff;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        overflow: visible;

        .box1-content-text {
          padding: px2rem(13);
        }
      }
    }
  }
}
.mt-91 {
  margin-top: px2rem(91);
}
.mt-16 {
  margin-top: px2rem(16);
}
.table {
  border-radius: px2rem(12);
  border: px2rem(1) solid #fff;

  .table-header {
    width: 100%;
    height: px2rem(43);
    flex-shrink: 0;
    background: linear-gradient(90deg, #edd9fd 0%, #ff9af2 100%);
    border-radius: px2rem(11) px2rem(11) 0 0;
    border-bottom: px2rem(1) solid #fff;

    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(15);
    position: relative;

    .header-title {
      z-index: 2;
      position: relative;
      font-family: Gilroy;
      font-size: px2rem(24);
      font-style: italic;
      font-weight: 900;
      line-height: normal;

      z-index: 0;
      div {
        color: #bf29fb;
      }
      &::after {
        content: attr(data-title);
        -webkit-text-stroke-width: px2rem(2);
        -webkit-text-stroke-color: #fff;
        text-align: center;
        position: absolute;
        inset: 0;
        z-index: -1;
      }
    }

    &::before,
    &::after {
      position: absolute;
      content: "";
      background-image: url("@/assets/images/activity/poster/party/star.png");
      background-size: 100% 100%;
      z-index: 1;
    }
    &::before {
      width: px2rem(57);
      height: px2rem(63);
      top: px2rem(-10);
      right: px2rem(-14);
    }
    &::after {
      width: px2rem(82);
      height: px2rem(95);
      top: px2rem(-29);
      right: px2rem(5);
      transform: rotate(-10.051deg);
    }
  }

  .table-content {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      border-bottom: px2rem(1) solid #fff;

      &:last-child {
        border-bottom: none;
      }

      &.header-row {
        border-bottom: px2rem(1) solid #fff;

        .table-cell {
          font-weight: 700;
          font-size: px2rem(14);
          color: #fff;
          text-shadow: 0 px2rem(1) px2rem(2) rgba(0, 0, 0, 0.3);
        }
      }

      .table-cell {
        padding: px2rem(16) px2rem(12);
        color: rgba(255, 255, 255, 0.95);
        font-family: Gilroy;
        font-size: px2rem(12);
        font-weight: 500;
        line-height: 1.3;
        text-align: center;
        border-right: px2rem(1) solid #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: px2rem(50);

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  // 合并单元格Grid布局样式
  .table-content-merged {
    .table-row {
      grid-template-columns: 1fr 0.9fr 1fr;
    }
    .merged-table-grid {
      display: grid;
      grid-template-columns: 1fr 0.9fr 1fr;
      grid-template-rows: repeat(3, 1fr);
      min-height: px2rem(120);

      .merged-cell-1 {
        grid-column: 1;
        grid-row: 1 / 4;
        border-right: px2rem(1) solid #fff;
        padding: px2rem(15);
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .requirement-text {
          color: #fff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .merged-cell-2 {
        grid-column: 2;
        grid-row: 1 / 4;
        border-right: px2rem(1) solid #fff;
        padding: px2rem(15);
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .reward-text {
          color: #fff;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }

      .individual-cell {
        grid-column: 3;
        border-bottom: px2rem(1) solid #fff;
        padding: px2rem(15);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: normal;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .bottom-note {
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(11);
    font-weight: 400;
    line-height: 1.4;
    margin: px2rem(20) px2rem(20) px2rem(40);
    padding: px2rem(15);
    background: rgba(255, 255, 255, 0.1);
    border-radius: px2rem(10);
    border: px2rem(1) solid rgba(255, 255, 255, 0.2);
  }

  // 两列表格样式
  .table-content-two-columns {
    .table-row {
      display: grid;
      grid-template-columns: 1fr 1fr;

      .table-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
    }
  }
}
.note-text {
  color: #a133cd;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(13);
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  margin: px2rem(6) auto;
  width: px2rem(352);
}

// Party play guide 样式
.guide-container {
  padding: px2rem(12);
}

.guide-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: px2rem(20);

  .guide-title {
    color: #fff;
    font-size: px2rem(20);
    font-weight: bold;
    margin: 0 px2rem(10);
    font-family: Gilroy;
  }

  .star-icon {
    width: px2rem(26);
    height: px2rem(26);

    &.left {
      margin-right: px2rem(10);
    }

    &.right {
      margin-left: px2rem(10);
    }
  }
}

.party-section {
  margin-bottom: px2rem(30);

  .party-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: px2rem(15);

    span {
      color: #fff;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(18);
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 23.4px */
      padding: 0 px2rem(8);
    }

    .star-icon {
      width: px2rem(26);
      height: px2rem(26);
    }
  }

  .guide-item {
    margin-bottom: px2rem(12);

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    .guide-label {
      display: flex;
      padding: px2rem(3) px2rem(11);
      justify-content: center;
      align-items: center;
      color: #fff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(11);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      border-radius: px2rem(22);
      background: #ff871f;
    }

    .guide-text {
      color: rgba(255, 255, 255, 0.8);
      font-family: Gilroy;
      font-size: px2rem(12);
      text-align: left;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 15.6px */
      margin-top: px2rem(4);
    }
  }
}

.box4-header {
  width: 100%;
  height: px2rem(43);
  flex-shrink: 0;
  background: linear-gradient(90deg, #edd9fd 0%, #ff9af2 100%);
  border-radius: px2rem(11) px2rem(11) 0 0;
  border-bottom: px2rem(1) solid #fff;

  color: #bf29fb;
  -webkit-text-stroke-width: px2rem(1);
  -webkit-text-stroke-color: #fff;
  font-family: Gilroy;
  font-size: px2rem(24);
  font-style: italic;
  font-weight: 900;
  line-height: normal;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 px2rem(15);
  position: relative;

  span {
    z-index: 2;
    position: relative;
  }
}

.reward-item {
  display: flex;
  align-items: center;
  .cover {
    img {
      width: px2rem(20);
      height: px2rem(20);
    }
  }
  .desc {
    color: #fff;
  }
}
</style>
