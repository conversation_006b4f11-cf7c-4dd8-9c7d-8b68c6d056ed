<template>
  <div class="prize-process">
    <div class="prize-boxs">
      <div
        class="prize-box"
        @click="emit('preview', getReword('D'))"
        v-track:click
        trace-key="r_recharge_reward_click"
        :track-params="JSON.stringify({ reward_level: 'D' })"
      >
        <img src="@/assets/images/activity/recharge-ranking/box-1.png" />
      </div>
      <gap :gap="7"></gap>
      <div
        class="prize-box"
        @click="emit('preview', getReword('C'))"
        v-track:click
        trace-key="r_recharge_reward_click"
        :track-params="JSON.stringify({ reward_level: 'C' })"
      >
        <img src="@/assets/images/activity/recharge-ranking/box-2.png" />
      </div>
      <gap :gap="7"></gap>
      <div
        class="prize-box"
        @click="emit('preview', getReword('B'))"
        v-track:click
        trace-key="r_recharge_reward_click"
        :track-params="JSON.stringify({ reward_level: 'B' })"
      >
        <img src="@/assets/images/activity/recharge-ranking/box-3.png" />
      </div>
      <gap :gap="7"></gap>
      <div
        class="prize-box"
        @click="emit('preview', getReword('A'))"
        v-track:click
        trace-key="r_recharge_reward_click"
        :track-params="JSON.stringify({ reward_level: 'A' })"
      >
        <img src="@/assets/images/activity/recharge-ranking/box-4.png" />
      </div>
      <gap :gap="7"></gap>
      <div
        class="prize-box"
        @click="emit('preview', getReword('S'))"
        v-track:click
        trace-key="r_recharge_reward_click"
        :track-params="JSON.stringify({ reward_level: 'S' })"
      >
        <img src="@/assets/images/activity/recharge-ranking/box-5.png" />
      </div>
    </div>
    <div class="process-box">
      <div class="process">
        <div
          class="process-inner"
          :style="{
            width: `${process}px`,
          }"
        ></div>
      </div>
      <div class="process-steps">
        <div class="process-step" ref="processStepRef">
          <img :src="getStepSrc(0)" :draggable="false" />
        </div>
        <gap :gap="46"></gap>
        <div class="process-step" ref="processStepRef">
          <img :src="getStepSrc(1)" :draggable="false" />
        </div>
        <gap :gap="46"></gap>
        <div class="process-step" ref="processStepRef">
          <img :src="getStepSrc(2)" :draggable="false" />
        </div>
        <gap :gap="46"></gap>
        <div class="process-step" ref="processStepRef">
          <img :src="getStepSrc(3)" :draggable="false" />
        </div>
        <gap :gap="46"></gap>
        <div class="process-step" ref="processStepRef">
          <img :src="getStepSrc(4)" :draggable="false" />
        </div>
      </div>
    </div>
    <div class="coins">
      <div class="coin-box">
        <GenderCoin :size="13"></GenderCoin>
        <gap :gap="2"></gap>
        <div class="coin-number">{{ getReword("D")?.coin ?? 0 }}</div>
      </div>
      <gap :gap="7"></gap>
      <div class="coin-box">
        <GenderCoin :size="13"></GenderCoin>
        <gap :gap="2"></gap>
        <div class="coin-number">{{ getReword("C")?.coin ?? 0 }}</div>
      </div>
      <gap :gap="7"></gap>
      <div class="coin-box">
        <GenderCoin :size="13"></GenderCoin>
        <gap :gap="2"></gap>
        <div class="coin-number">{{ getReword("B")?.coin ?? 0 }}</div>
      </div>
      <gap :gap="7"></gap>
      <div class="coin-box">
        <GenderCoin :size="13"></GenderCoin>
        <gap :gap="2"></gap>
        <div class="coin-number">{{ getReword("A")?.coin ?? 0 }}</div>
      </div>
      <gap :gap="7"></gap>
      <div class="coin-box">
        <GenderCoin :size="13"></GenderCoin>
        <gap :gap="2"></gap>
        <div class="coin-number">{{ getReword("S")?.coin ?? 0 }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import GenderCoin from "@/components/gender-coin/index.vue";
import { computed, ref, watch } from "vue";
import Step1 from "@/assets/images/activity/recharge-ranking/step-1.png";
import Step2 from "@/assets/images/activity/recharge-ranking/step-2.png";
import Step3 from "@/assets/images/activity/recharge-ranking/step-3.png";
import { getLang } from "@/i18n/index.js";
const emit = defineEmits(["preview"]);
const props = defineProps({
  rewords: {
    type: Array,
    default: () => [],
  },
  currentProcess: {
    type: Number,
    default: 0,
  },
  isReadyed: {
    type: Boolean,
    default: false,
  },
});
const lang = getLang();
const processStepRef = ref();
const stepsCoins = computed(() => {
  return [
    getReword("D")?.coin,
    getReword("C")?.coin,
    getReword("B")?.coin,
    getReword("A")?.coin,
    getReword("S")?.coin,
  ];
});

const findStepIndex = (value) => {
  if (value >= stepsCoins.value[stepsCoins.value.length - 1]) {
    return stepsCoins.value.length - 1;
  }
  if (value < stepsCoins.value[0]) {
    return -1;
  }
  let cur = 0;
  for (let i = 1; i < stepsCoins.value.length; i++) {
    if (value >= stepsCoins.value[cur] && value < stepsCoins.value[i]) {
      return cur;
    } else {
      cur = i;
    }
  }
  return cur;
};

const getReword = (grade) => {
  return props.rewords.find((i) => i.grade === grade);
};

const getStepSrc = (index) => {
  if (!current.value || !props.isReadyed) {
    return Step1;
  }
  const currentStep = findStepIndex(current.value);
  if (index < currentStep) {
    return Step2;
  } else if (index === currentStep) {
    return Step3;
  } else {
    return Step1;
  }
};

const current = ref(0);
const process = ref(0);

const computedProcess = () => {
  if (!current.value || !props.isReadyed) {
    return 0;
  }
  const stepBox = document.querySelector(".process-steps");
  let stepDoms = Array.from(document.querySelectorAll(".process-step"));
  if (lang === "ar") {
    stepDoms = stepDoms.reverse();
  }
  const stepBoxRect = stepBox?.getBoundingClientRect();

  const currentStep = findStepIndex(current.value);
  if (currentStep === stepsCoins.value.length - 1) {
    return stepBoxRect?.width ?? 0;
  }
  if (currentStep === -1) {
    const rate = current.value / stepsCoins.value[0];
    const nextStepDomsRect = stepDoms[0]?.getBoundingClientRect();
    const stepDomsRect = stepDoms[0]?.getBoundingClientRect();
    return (
      (nextStepDomsRect.right - stepDomsRect?.width / 2 - stepBoxRect?.left) *
      rate
    );
  }
  const nextStep = currentStep + 1;
  let leftWidth = 0;
  const stepDomsRect = stepDoms[currentStep]?.getBoundingClientRect();
  const nextStepDomsRect = stepDoms[nextStep]?.getBoundingClientRect();
  leftWidth +=
    stepDomsRect?.right - stepDomsRect?.width / 2 - stepBoxRect?.left;
  const nextStepWidth = nextStepDomsRect?.left - (stepDomsRect?.left ?? 0);
  const rate =
    (current.value - (stepsCoins.value[currentStep] ?? 0)) /
    (stepsCoins.value[nextStep] - stepsCoins.value[currentStep]);
  const paddingWidth = nextStepWidth * rate;
  leftWidth += paddingWidth;
  return leftWidth;
};

watch(
  [() => props.currentProcess, () => props.isReadyed],
  ([value, readyed]) => {
    current.value = value;
    if (readyed === true) {
      process.value = computedProcess();
    }
  }
);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.prize-process {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .prize-boxs {
    display: flex;
    .prize-box {
      width: px2rem(58);
      height: px2rem(58);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      img {
        width: px2rem(47);
        height: px2rem(47);
        vertical-align: bottom;
      }
    }
  }
  .process-box {
    height: px2rem(20);
    margin-top: px2rem(8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    .process {
      width: px2rem(315);
      height: px2rem(6);
      flex-shrink: 0;
      border-radius: px2rem(4);
      background: #878787;
      position: relative;
      .process-inner {
        transition: all 1s;
        position: absolute;
        left: 0;
        top: 0;
        width: 0;
        height: 100%;
        flex-shrink: 0;
        border-radius: px2rem(4);
        background: linear-gradient(180deg, #ffe419 0%, #a35f00 100%);
      }
    }
    .process-steps {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      .process-step {
        width: px2rem(20);
        height: px2rem(20);
        position: relative;
        img {
          width: px2rem(20);
          height: px2rem(20);
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }
  }

  .coins {
    margin-top: px2rem(8);
    display: flex;
    .coin-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: px2rem(58);
      flex-shrink: 0;
      .coin-number {
        color: #ffda6d;
        font-family: "DIN Next W1G";
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: -0.5px;
      }
    }
  }
}
.rtl-html {
  .process-inner {
    left: unset;
    right: 0;
  }
}
</style>
