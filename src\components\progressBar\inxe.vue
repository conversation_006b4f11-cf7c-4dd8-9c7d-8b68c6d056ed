<template>
  <div class="progress-bar-container" :style="`height:${pxToRemPx(height)}px`">
    <!-- 进度条底色 -->
    <div class="progress-bar-background"></div>

    <!-- 进度条进度 -->
    <div class="progress-bar-fill" :style="progressStyle"></div>

    <!-- 进度数值 -->
    <div class="progress-text" v-if="showText">
      {{ current }}/{{ total }}
    </div>
  </div>
</template>

<script>
import { computed,getCurrentInstance } from 'vue';

export default {
  props: {
    current: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
    showText: {
      type: Boolean,
      default: true, 
    },
    height: {
      type: Number,
      default: 12,
    },
  },
  setup(props) {
    // 计算进度条的宽度
    const { proxy } = getCurrentInstance()
    const pxToRemPx = (value) => {
      return proxy.$pxToRemPx(value);
    };

    const progressStyle = computed(() => {
      const progress = (props.current / props.total) * 100;
      if (progress < 0.2) {
        return { width: '0.2%' };
      }
      return { width: `${progress}%` };
    });

    return {
      progressStyle,
      pxToRemPx
    };
  },
};
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.progress-bar-container {
  position: relative;
  width: 100%;
  background-color: #f0f0f0;
  border-radius: $radius10; /* 圆弧角度 */
  overflow: hidden;
}

.progress-bar-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #E5E5EF; /* 灰色底色 */
}

.progress-bar-fill {
  position: absolute;
  height: 100%;
  background-color: #7063FF; /* 蓝色进度 */
  border-radius: $radius10; /* 圆弧角度 */
  transition: width 0.3s ease; /* 平滑过渡 */
}

.progress-text {
  position: absolute;
  color: $white;
  top: 50%;
  left: px2rem(8);
  transform: translateY(-50%);
  font-size: px2rem(11);
  z-index: 1;
}
</style>