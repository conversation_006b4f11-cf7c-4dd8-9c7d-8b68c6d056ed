<template>
  <van-uploader
    v-model="fileList"
    :after-read="afterRead"
    :multiple="false"
    :max-count="1"
    :readonly="disabledUpload"
    @click-upload="clickUpload"
  >
    <div class="upload-cell">
      <img class="icon-plus" src="@/assets/images/icon/icon-plus.svg" alt="" />
    </div>
    <template #preview-delete>
      <img
        class="icon-close"
        src="@/assets/images/icon/icon-close-circle.svg"
        alt=""
      />
    </template>
  </van-uploader>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, onUnmounted, ref } from "vue";
import { uploadFile } from "@/utils/util.js";
import { i18n } from "@/i18n/index.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const emit = defineEmits(["update"]);

// 上传图片
const fileList = ref([]);
const afterRead = async (file, detail) => {
  showLoadingToast({
    forbidClick: true,
    duration: 0,
  });
  const response = await uploadFile(file);
  fileList.value.splice(detail.index, 1, {
    url: response,
    isImage: true,
  });
  emit("update", fileList.value);
  closeToast();
};

// 判断用户是否通话中
const userStatusConfig = ref({
  isCalling: 0,
  cameraAuth: false,
  photoAuth: false,
});
const disabledUpload = computed(() => {
  return (
    userStatusConfig.value.isCalling === 2 ||
    !userStatusConfig.value.cameraAuth ||
    !userStatusConfig.value.photoAuth
  );
});
const checkUserIsCalling = async () => {
  const getUserInfoResult = await proxy.$siyaApp("getUserInfo");
  if (getUserInfoResult && getUserInfoResult.data) {
    userStatusConfig.value = getUserInfoResult.data;
  }
};

const clickUpload = async () => {
  if (disabledUpload.value) {
    await checkUserIsCalling();
  }
  if (userStatusConfig.value.isCalling === 2) {
    proxy.$toast(t("help.feedback_is_calling_tips"));
  } else if (!userStatusConfig.value.cameraAuth) {
    proxy.$siyaApp("showSettingAuth", { type: 1 });
  } else if (!userStatusConfig.value.photoAuth) {
    proxy.$siyaApp("showSettingAuth", { type: 0 });
  }
};
const handleVisible = (e) => {
  if (e.target.visibilityState === "visible") {
    checkUserIsCalling();
  }
};

onMounted(() => {
  checkUserIsCalling();
  window.addEventListener("visibilitychange", handleVisible);
});

onUnmounted(() => {
  window.removeEventListener("visibilitychange", handleVisible);
});
</script>

<style>
.van-popup {
  max-height: 100vh;
}
</style>
<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.upload-cell {
  width: var(--van-uploader-size);
  height: var(--van-uploader-size);
  margin-bottom: $gap8;
  background-color: $white;
  border-radius: var(--van-uploader-border-radius);

  @include centerV;

  .icon-plus {
    width: px2rem(16);
    height: px2rem(16);
  }
}

.icon-close {
  position: absolute;
  top: px2rem(4);
  right: px2rem(4);
  width: px2rem(16);
  height: px2rem(16);
}

.rtl-html {
  .icon-close {
    transform: scaleX(-1);
    right: unset !important;
    left: -76px !important;
  }
}
</style>
