<template>
  <header-bar
    back-icon-show
    :show-title="true"
    :title="$t(`singer.title`)"
    close-type="close"
    :content-padding="true"
    is-scroll-change-bg
    :always-show-right-icon="true"
    @emit:scrollChangeBgShow="scrollChangeBgShow"
  >
    <template #right>
      <svg-icon
        class="icon-concat"
        :icon="showPageTitle ? 'contact-black' : 'contact-white'"
        :color="showPageTitle ? '#000' : '#fff'"
        @click="goFeedback()"
        v-track:click
        trace-key="singer_button_click"
        :track-params="JSON.stringify({ singer_button_name: 2 })"
      />
    </template>
  </header-bar>
  <div
    class="banner-title"
    :style="{
      backgroundImage: `url('${getImageUrl('banner-title.png')}')`,
    }"
  >
    <img :src="getImageUrl('banner-title.png')" draggable="false" />
  </div>
  <!-- 规则按钮 -->
  <RightButton></RightButton>
  <!-- 提交记录按钮 -->
  <SubmitListButton></SubmitListButton>
</template>
<script setup>
import { getLang } from "@/i18n/index.js";
import RightButton from "./RightButton.vue";
import SubmitListButton from "./SubmitListButton.vue";
import { ref } from "vue";
import { useRouter } from "vue-router";
const router = useRouter();
const lang = getLang();
function getImageUrl(name) {
  return new URL(
    `../../../../assets/images/activity/singer/${lang}/${name}`,
    import.meta.url
  ).href;
}

// 下滑展示标题处理
const showPageTitle = ref(false);
const scrollChangeBgShow = (show) => {
  showPageTitle.value = show;
};

const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.icon-concat {
  font-size: px2rem(28);
  color: unset;
}
.banner-title {
  width: px2rem(337);
  background-size: 100% 100%;
  margin: 0 auto;
  margin-top: px2rem(151);
  img {
    opacity: 0;
    width: px2rem(337);
    vertical-align: bottom;
  }
}
</style>
