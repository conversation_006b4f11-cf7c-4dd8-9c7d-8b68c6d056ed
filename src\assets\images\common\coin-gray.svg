<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_13093_27515)">
<g filter="url(#filter0_iii_13093_27515)">
<circle cx="12.0002" cy="12.5003" r="11.1429" fill="url(#paint0_radial_13093_27515)"/>
<circle cx="12.0002" cy="12.5003" r="11.1429" fill="url(#paint1_radial_13093_27515)"/>
</g>
<g filter="url(#filter1_di_13093_27515)">
<ellipse cx="12.0004" cy="12.5001" rx="8.57143" ry="8.57143" fill="url(#paint2_radial_13093_27515)"/>
<ellipse cx="12.0004" cy="12.5001" rx="8.57143" ry="8.57143" fill="url(#paint3_radial_13093_27515)"/>
<ellipse cx="12.0004" cy="12.5001" rx="8.57143" ry="8.57143" fill="url(#paint4_radial_13093_27515)"/>
</g>
<g filter="url(#filter2_di_13093_27515)">
<path d="M16.5082 11.5151C16.5082 14.1676 14.3886 16.2896 12.004 16.2896C9.61943 16.2896 7.49982 14.1676 7.49982 11.5151C7.49982 8.78711 10.4764 8.24748 11.9614 10.1336C11.9826 10.1605 12.0247 10.1598 12.0451 10.1324C13.5014 8.17766 16.5082 8.84087 16.5082 11.5151Z" fill="url(#paint5_linear_13093_27515)" shape-rendering="crispEdges"/>
</g>
</g>
<g style="mix-blend-mode:color">
<circle cx="11.9998" cy="12.5003" r="11.1429" fill="black"/>
</g>
<defs>
<filter id="filter0_iii_13093_27515" x="0.857361" y="0.961231" width="22.2857" height="23.0785" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.660318"/>
<feGaussianBlur stdDeviation="0.198095"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.945098 0 0 0 0 0.564706 0 0 0 0 0.0352941 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_13093_27515"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.660318"/>
<feGaussianBlur stdDeviation="0.198095"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_13093_27515" result="effect2_innerShadow_13093_27515"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.330159"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.868 0 0 0 0 0.4 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_13093_27515" result="effect3_innerShadow_13093_27515"/>
</filter>
<filter id="filter1_di_13093_27515" x="3.42896" y="3.92871" width="17.1429" height="17.7336" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.492488"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.76 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13093_27515"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13093_27515" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.590985"/>
<feGaussianBlur stdDeviation="0.738732"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.782479 0 0 0 0 0.443533 0 0 0 0 0.0456394 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_13093_27515"/>
</filter>
<filter id="filter2_di_13093_27515" x="5.60665" y="8.05732" width="12.7947" height="11.0725" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.946585"/>
<feGaussianBlur stdDeviation="0.946585"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.982078 0 0 0 0 0.62102 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13093_27515"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_13093_27515" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.473293"/>
<feGaussianBlur stdDeviation="1.41988"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_13093_27515"/>
</filter>
<radialGradient id="paint0_radial_13093_27515" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12.0002 12.5003) rotate(90) scale(11.1429)">
<stop offset="0.630208" stop-color="#FFE27C"/>
<stop offset="1" stop-color="#F9D46E"/>
</radialGradient>
<radialGradient id="paint1_radial_13093_27515" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(5.25835 3.05617) rotate(51.0278) scale(10.7194)">
<stop stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_13093_27515" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(12.0004 8.51959) rotate(89.9994) scale(11.9528 11.9528)">
<stop stop-color="#FFCA6E"/>
<stop offset="1" stop-color="#FFAC20"/>
</radialGradient>
<radialGradient id="paint3_radial_13093_27515" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(7.69406 5.06547) rotate(54.0903) scale(0.793706 2.66025)">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint4_radial_13093_27515" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16.428 19.696) rotate(61.8237) scale(4.43148 14.1662)">
<stop stop-color="white" stop-opacity="0.69"/>
<stop offset="0.280503" stop-color="white" stop-opacity="0.28"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint5_linear_13093_27515" x1="12.004" y1="9.00391" x2="12.004" y2="16.2896" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEFC5" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0.46"/>
</linearGradient>
<clipPath id="clip0_13093_27515">
<rect width="24" height="24" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
