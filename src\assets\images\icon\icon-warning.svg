<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_8012_21827)">
<circle cx="8" cy="8" r="7" fill="#F04C18"/>
<circle cx="8" cy="8" r="7" fill="url(#paint0_radial_8012_21827)" fill-opacity="0.8"/>
</g>
<g filter="url(#filter1_d_8012_21827)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.00237 3.25684C7.47141 3.25684 6.79001 3.36656 6.6157 4.00042C6.25074 5.32752 6.60702 6.58437 6.97884 7.89606C7.02462 8.05755 7.07063 8.21987 7.11556 8.38326C7.2297 8.79829 7.63634 8.85591 7.99981 8.85591C8.36329 8.85591 8.76993 8.79829 8.88407 8.38326C8.92903 8.21975 8.97508 8.0573 9.0209 7.89569C9.3927 6.58413 9.74896 5.32739 9.38404 4.00042C9.21187 3.37434 8.52673 3.25684 8.00237 3.25684ZM7.99979 12.4911C8.71776 12.4911 9.29979 11.9091 9.29979 11.1911C9.29979 10.4731 8.71776 9.89111 7.99979 9.89111C7.28182 9.89111 6.69979 10.4731 6.69979 11.1911C6.69979 11.9091 7.28182 12.4911 7.99979 12.4911Z" fill="url(#paint1_linear_8012_21827)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_ii_8012_21827" x="1" y="1" width="16" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.619709 0 0 0 0 0.405796 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_8012_21827"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.713726 0 0 0 0 0.385412 0 0 0 0 0 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_8012_21827" result="effect2_innerShadow_8012_21827"/>
</filter>
<filter id="filter1_d_8012_21827" x="4.72956" y="3.25684" width="6.54058" height="12.701" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.73333"/>
<feGaussianBlur stdDeviation="0.866667"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.867701 0 0 0 0 0.316654 0 0 0 0 0.0804912 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8012_21827"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8012_21827" result="shape"/>
</filter>
<radialGradient id="paint0_radial_8012_21827" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(11.1297 3.9947) rotate(90.2125) scale(15.7856 12.0789)">
<stop stop-color="white" stop-opacity="0.42"/>
<stop offset="0.368598" stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_8012_21827" x1="7.96548" y1="4.11992" x2="7.96548" y2="8.11021" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.7"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
