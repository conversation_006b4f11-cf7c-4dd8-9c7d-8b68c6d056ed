<template>
  <div class="app-container flex">
    <header>
      <header-bar :title="$t('dealer_coin.coin_dealer_instructions')">
      </header-bar>
    </header>
    <main>
      <div
        class="text"
        v-for="(text, idx) in $tm('dealer_coin.coin_dealer_instructions_list')"
        :key="idx"
      >
        {{ text.replace("__X__", "@") }}
      </div>
    </main>
  </div>
</template>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: $white;
  flex-direction: column;

  main {
    width: 100%;
    padding: px2rem(24);
    .text {
      font-size: $fontSize13;
      color: $fontColorB3;
      margin-bottom: px2rem(16);
    }
  }
}
</style>
