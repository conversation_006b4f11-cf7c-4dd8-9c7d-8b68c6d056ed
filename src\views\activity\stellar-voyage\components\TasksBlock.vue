<template>
  <div class="tasks-block">
    <GoldBox>
      <div class="box-title" :data-title="$t('pk_king.daily_tasks')">
        <div>{{ $t("pk_king.daily_tasks") }}</div>
      </div>
      <div class="box-desc">{{ $t("pk_king.daily_task_frequency") }}</div>
      <div class="box-count-down">
        <CountDown
          :left-time="
            (activityInfo?.dail_countdown ?? 0) - activityInfo?.requestTime
          "
        ></CountDown>
      </div>
      <div class="task-list">
        <div class="task" v-for="(item, index) in dayTasks" :key="index">
          <div class="cover">
            <PrizeBox :rewards="item.rewards"></PrizeBox>
          </div>
          <gap :gap="8"></gap>
          <div class="detail">
            <div class="detail-desc">{{ item.desc }}</div>
            <div class="process">
              <Process :current="item.current" :max="item.max"></Process>
            </div>
            <div class="process-desc">
              ({{ formatCount(item.current) }}/{{ formatCount(item.max) }})
            </div>
          </div>
          <gap :gap="8"></gap>
          <div class="action">
            <div
              class="action-button"
              v-if="item.status === 0"
              @click="handleGo(item.go)"
              v-track:click
              trace-key="pk_activity_button_click"
              :track-params="
                JSON.stringify({
                  pk_activity_button_name: '3',
                  pk_activity_status: '1',
                })
              "
            >
              {{ $t("pk_king.go") }}
            </div>
            <div
              class="action-button claim"
              v-else-if="item.status === 1"
              @click="handleReceive(item.receiveType)"
              v-track:click
              trace-key="pk_activity_button_click"
              :track-params="
                JSON.stringify({
                  pk_activity_button_name: '3',
                  pk_activity_status: '2',
                })
              "
            >
              {{ $t("pk_king.claim") }}
            </div>
            <div
              class="action-button claimed"
              v-else-if="item.status === 2"
              v-track:click
              trace-key="pk_activity_button_click"
              :track-params="
                JSON.stringify({
                  pk_activity_button_name: '3',
                  pk_activity_status: '3',
                })
              "
            >
              {{ $t("pk_king.claimed") }}
            </div>
          </div>
        </div>
      </div>
    </GoldBox>
    <GoldBox class="box2">
      <div class="box-title">{{ $t("pk_king.weekly_tasks") }}</div>
      <div class="box-desc">{{ $t("pk_king.weekly_task_frequency") }}</div>
      <div class="box-count-down">
        <CountDown
          :left-time="
            (activityInfo?.week_countdown ?? 0) - activityInfo?.requestTime
          "
        ></CountDown>
      </div>
      <div class="task-list">
        <div class="task" v-for="(item, index) in weekTasks" :key="index">
          <div class="cover">
            <PrizeBox :rewards="item.rewards"></PrizeBox>
          </div>
          <gap :gap="8"></gap>
          <div class="detail">
            <div class="detail-desc">{{ item.desc }}</div>
            <div class="process">
              <Process :current="item.current" :max="item.max"></Process>
            </div>
            <div class="process-desc">
              ({{ formatCount(item.current) }}/{{ formatCount(item.max) }})
            </div>
          </div>
          <gap :gap="8"></gap>
          <div class="action">
            <div
              class="action-button"
              v-if="item.status === 0"
              @click="handleGo(item.go)"
            >
              {{ $t("pk_king.go") }}
            </div>
            <div
              class="action-button claim"
              v-else-if="item.status === 1"
              @click="handleReceive(item.receiveType)"
            >
              {{ $t("pk_king.claim") }}
            </div>
            <div class="action-button claimed" v-else-if="item.status === 2">
              {{ $t("pk_king.claimed") }}
            </div>
          </div>
        </div>
      </div>
    </GoldBox>
    <RewordDialog ref="rewordDialogRef"></RewordDialog>
  </div>
</template>
<script setup>
import GoldBox from "./GoldBox.vue";
import CountDown from "./CountDown.vue";
import PrizeBox from "./PrizeBox.vue";
import Process from "./Process.vue";
import { formatCount } from "@/utils/util.js";
import { computed, getCurrentInstance, ref } from "vue";
import RewordDialog from "./RewordDialog.vue";
import activityApi from "@/api/activity.js";

const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits("refresh");
const { proxy } = getCurrentInstance();
const rewordDialogRef = ref();
const goRoomTabUrl = `siya://siya.com/app?method=goRoomTab`;
const goCreateRoomUrl = `siya://siya.com/app?method=goCreateRoom`;
// 1. 个人PK值达到1000
// 2. 获得一场PK胜利
// 3. PK贡献值达到1000
// 4. 我的房间开启PK10分钟
// 5.我的房间PK值达到5000
// 6. 我的房间PK人数>=5人
// 7. 个人PK值累计达到50K
// 8. 个人PK值累计达到100K
// 9. 个人PK值累计达到200K
// 10. 房间PK值累计达到100K
// 11. 房间PK值累计达到200K
// 12. 房间PK值累计达到500K
// 13.PK贡献值累计达到50K
// 14.PK贡献值累计达到100K
// 15.PK贡献值累计达到200K
const dayTasks = computed(() => {
  return [
    {
      rewards: props.rewordInfo.pk1000,
      desc: proxy.$t("pk_king.task_personal_pk_1000"),
      current: props.activityInfo?.dail_task?.pk1000?.current_value ?? 0,
      max: props.activityInfo?.dail_task?.pk1000?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk1000?.status ?? 0,
      go: goRoomTabUrl,
      receiveType: 1,
    },
    {
      rewards: props.rewordInfo.pk_win,
      desc: proxy.$t("pk_king.task_win_pk_1000"),
      current: props.activityInfo?.dail_task?.pk_win?.current_value ?? 0,
      max: props.activityInfo?.dail_task?.pk_win?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk_win?.status ?? 0,
      go: goRoomTabUrl,
      receiveType: 2,
    },
    {
      rewards: props.rewordInfo?.pk_contribution1000,
      desc: proxy.$t("pk_king.task_pk_contribution_1000"),
      current:
        props.activityInfo?.dail_task?.pk_contribution1000?.current_value ?? 0,
      max:
        props.activityInfo?.dail_task?.pk_contribution1000?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk_contribution1000?.status ?? 0,
      go: goRoomTabUrl,
      receiveType: 3,
    },
    {
      rewards: props.rewordInfo.pk_room_live10,
      desc: proxy.$t("pk_king.task_room_pk_10min"),
      current:
        props.activityInfo?.dail_task?.pk_room_live10?.current_value ?? 0,
      max: props.activityInfo?.dail_task?.pk_room_live10?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk_room_live10?.status ?? 0,
      go: goCreateRoomUrl,
      receiveType: 4,
    },
    {
      rewards: props.rewordInfo.pk_room5000,
      desc: proxy.$t("pk_king.task_room_pk_value_5000"),
      current: props.activityInfo?.dail_task?.pk_room5000?.current_value ?? 0,
      max: props.activityInfo?.dail_task?.pk_room5000?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk_room5000?.status ?? 0,
      go: goRoomTabUrl,
      receiveType: 5,
    },
    {
      rewards: props.rewordInfo.pk_room_online_num5,
      desc: proxy.$t("pk_king.task_room_pk_users_5"),
      current:
        props.activityInfo?.dail_task?.pk_room_online_num5?.current_value ?? 0,
      max:
        props.activityInfo?.dail_task?.pk_room_online_num5?.target_value ?? 0,
      status: props.activityInfo?.dail_task?.pk_room_online_num5?.status ?? 0,
      go: goCreateRoomUrl,
      receiveType: 6,
    },
  ];
});

const weekTasks = computed(() => {
  return [
    {
      rewards: props.rewordInfo.pk50k,
      desc: proxy.$t("pk_king.task_personal_pk_50k"),
      current: Math.min(props.activityInfo?.week_task?.pk_value ?? 0, 50000),
      max: 50000,
      status: props.activityInfo?.week_task?.pk50k ?? 0,
      go: goRoomTabUrl,
      receiveType: 7,
    },
    {
      rewards: props.rewordInfo.pk100k,
      desc: proxy.$t("pk_king.task_personal_pk_100k"),
      current: Math.min(props.activityInfo?.week_task?.pk_value ?? 0, 100000),
      max: 100000,
      status: props.activityInfo?.week_task?.pk100k ?? 0,
      go: goRoomTabUrl,
      receiveType: 7,
    },
    {
      rewards: props.rewordInfo.pk200k,
      desc: proxy.$t("pk_king.task_personal_pk_200k"),
      current: Math.min(props.activityInfo?.week_task?.pk_value ?? 0, 200000),
      max: 200000,
      status: props.activityInfo?.week_task?.pk200k ?? 0,
      go: goRoomTabUrl,
      receiveType: 9,
    },

    {
      rewards: props.rewordInfo.room_pk100k,
      desc: proxy.$t("pk_king.task_room_pk_100k"),
      current: Math.min(
        props.activityInfo?.week_task?.room_pk_value ?? 0,
        50000
      ),
      max: 100000,
      status: props.activityInfo?.week_task?.room_pk100k ?? 0,
      go: goCreateRoomUrl,
      receiveType: 10,
    },
    {
      rewards: props.rewordInfo.room_pk200k,
      desc: proxy.$t("pk_king.task_room_pk_200k"),
      current: Math.min(
        props.activityInfo?.week_task?.room_pk_value ?? 0,
        100000
      ),
      max: 200000,
      status: props.activityInfo?.week_task?.room_pk200k ?? 0,
      go: goCreateRoomUrl,
      receiveType: 11,
    },
    {
      rewards: props.rewordInfo.room_pk500k,
      desc: proxy.$t("pk_king.task_room_pk_500k"),
      current: Math.min(
        props.activityInfo?.week_task?.room_pk_value ?? 0,
        200000
      ),
      max: 500000,
      status: props.activityInfo?.week_task?.room_pk500k ?? 0,
      go: goCreateRoomUrl,
      receiveType: 12,
    },

    {
      rewards: props.rewordInfo.contribution50k,
      desc: proxy.$t("pk_king.task_pk_contribution_50k"),
      current: Math.min(
        props.activityInfo?.week_task?.contribution_value ?? 0,
        50000
      ),
      max: 50000,
      status: props.activityInfo?.week_task?.contribution50k ?? 0,
      go: goRoomTabUrl,
      receiveType: 13,
    },
    {
      rewards: props.rewordInfo.contribution100k,
      desc: proxy.$t("pk_king.task_pk_contribution_100k"),
      current: Math.min(
        props.activityInfo?.week_task?.contribution_value ?? 0,
        100000
      ),
      max: 100000,
      status: props.activityInfo?.week_task?.contribution100k ?? 0,
      go: goRoomTabUrl,
      receiveType: 14,
    },
    {
      rewards: props.rewordInfo.contribution200k,
      desc: proxy.$t("pk_king.task_pk_contribution_200k"),
      current: Math.min(
        props.activityInfo?.week_task?.contribution_value ?? 0,
        200000
      ),
      max: 200000,
      status: props.activityInfo?.week_task?.contribution200k ?? 0,
      go: goRoomTabUrl,
      receiveType: 15,
    },
  ];
});

const handleGo = (url) => {
  proxy.$siyaApp("openSiyaUrl", {
    url: url,
  });
};

const getRewardsByType = (type) => {
  return [...dayTasks.value, ...weekTasks.value].find(
    (i) => i.receiveType === type
  )?.rewards;
};

const isGetPrizeLoading = ref(false);
const handleReceive = async (type) => {
  if (isGetPrizeLoading.value === true) {
    return;
  }
  isGetPrizeLoading.value = true;
  try {
    const res = await activityApi.pk_task_receive({
      type,
    });
    if (res.code === 200) {
      const rewards = getRewardsByType(type);
      rewordDialogRef.value?.open(rewards);
    }
    emit("refresh");
    isGetPrizeLoading.value = false;
  } catch (e) {
    console.log(e);
    isGetPrizeLoading.value = false;
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.tasks-block {
  margin-top: px2rem(44);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 px2rem(10);
  padding-bottom: px2rem(60);
  .box2 {
    margin-top: px2rem(41);
  }
  .box-title {
    margin-top: px2rem(41);
    text-align: center;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #5f0297;
    font-family: Gilroy;
    font-size: px2rem(26);
    font-style: italic;
    font-weight: 900;
    line-height: normal;

    background: linear-gradient(180deg, #fff 0%, #ffba24 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 0 px2rem(5);
  }

  .box-desc {
    margin-top: px2rem(2);
    color: rgba(235, 157, 255, 0.65);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .box-count-down {
    margin-top: px2rem(22);
  }

  .task-list {
    width: 100%;
    padding: 0 px2rem(17);
    padding-bottom: px2rem(30);
    margin-top: px2rem(30);
    position: relative;
    z-index: 2;
    .task {
      display: flex;
      align-items: center;
      border-radius: px2rem(10);
      border: 1px solid #feecaa;
      background: #9023b7;
      margin-bottom: px2rem(10);
      padding: px2rem(10);
      .cover {
        flex-shrink: 0;
        width: px2rem(55);
        height: px2rem(55);
      }
      .detail {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        .detail-desc {
          color: #fae8ff;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin-bottom: px2rem(6);
        }
        .process {
          width: 100%;
          margin-bottom: px2rem(2);
        }
        .process-desc {
          color: rgba(235, 157, 255, 0.6);
          font-family: Gilroy;
          font-size: px2rem(10);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }
      .action {
        flex-shrink: 0;
        .action-button {
          min-width: px2rem(56);
          height: px2rem(24);
          color: #ffe3e3;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 700;
          line-height: normal;

          border-radius: px2rem(50);
          border: 1px solid #ffa2a2;
          background: linear-gradient(
            180deg,
            #ffad9e 0%,
            #af1af2 47.12%,
            #56009e 89.9%
          );
          box-shadow: 0px px2rem(3) px2rem(3) 0px rgba(255, 255, 255, 0.26)
            inset;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .action-button.claim {
          color: #a609df;
          background: linear-gradient(180deg, #fff052 1.44%, #ff7c27 100%);
        }
        .action-button.claimed {
          color: #cb8ffe;
          background: linear-gradient(180deg, #5d0ca0 14.08%, #390081 70.67%);
        }
      }
    }
  }
}

.lang-zh_cn,
.lang-zh_tw {
  .box-title {
    -webkit-text-stroke-width: 0 !important;
  }
}
</style>
