<template>
  <div class="block-prize">
    <block-box :title="$t(`promoter_star.more_rewards`)" :icon="Icon">
      <div
        class="prize-tip"
        v-html="
          $t(`promoter_star.choose_platform_to_publish_video`, [
            formatDate(starInfo?.submit_start_time),
            formatDate(starInfo?.submit_start_time),
            formatDate(starInfo?.submit_end_time),
          ])
        "
      ></div>
      <div class="tag-box">
        <div>
          <div class="tag-block h-80">
            <div class="tag">Top1</div>
            <div class="content text-48">15%</div>
          </div>
        </div>
        <div class="flex">
          <div class="tag-block h-80">
            <div class="tag">Top2</div>
            <div class="content">10%</div>
          </div>
          <div class="tag-block h-80 ml-12">
            <div class="tag">Top3</div>
            <div class="content text-36">5%</div>
          </div>
        </div>
        <div class="flex">
          <div class="tag-block h-70">
            <div class="tag">Top4-10</div>
            <div class="content text-24 pt-12">3%</div>
          </div>
          <div class="tag-block h-70 ml-12">
            <div class="tag">Top11-20</div>
            <div class="content text-24 pt-12">2%</div>
          </div>
          <div class="tag-block h-70 ml-12">
            <div class="tag">Top21-50</div>
            <div class="content text-24 pt-12">1%</div>
          </div>
        </div>
      </div>
      <div class="video-specs">
        <specs-tab v-model="specsTabsSelected" :options="specsTabs"></specs-tab>
        <div>
          <div class="title">
            {{ $t(`promoter_star.after_passing_review`) }}
          </div>
          <div class="gifs">
            <div class="gif">
              <div class="icon gift-1"></div>
              <div class="name">
                {{ $t(`promoter_star.avatar_frame_30_days`) }}
              </div>
            </div>
            <div class="gif" v-if="specsTabsSelected === TAB_2">
              <div
                class="icon"
                :style="`background: url(${getImageUrl(
                  'gift-2.png'
                )}) 100% / 100% no-repeat;`"
              ></div>
              <div class="name">{{ $t(`promoter_star.title_30_days`) }}</div>
            </div>
          </div>
          <div class="video-specs-table">
            <div class="row font-700">
              <div class="cell cell-left">
                {{ $t(`promoter_star.platform`) }}
              </div>
              <div class="cell cell-right">
                {{ $t(`promoter_star.data_requirements`) }}
              </div>
            </div>
            <div
              class="row"
              v-for="item in specs[specsTabsSelected]"
              :key="item.platform"
            >
              <div class="cell cell-left">
                <div class="platform">
                  <img :src="item.icon" />
                  <gap :gap="4"></gap>
                  <span>{{ item.platform }}</span>
                </div>
              </div>
              <div class="cell cell-right">
                {{ item.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </block-box>
  </div>
</template>
<script setup>
import BlockBox from "./BlockBox.vue";
import SpecsTab from "./SpecsTab.vue";
import Icon from "@/assets/images/promoter-star/icon-1.png";
import IconTiktok from "@/assets/images/promoter-star/icon-tiktok.svg";
import IconKwai from "@/assets/images/promoter-star/icon-kwai.svg";
import IconFacebook from "@/assets/images/promoter-star/icon-facebook.svg";
import IconInstangram from "@/assets/images/promoter-star/icon-instagram.svg";
import { ref } from "vue";
import { i18n } from "@/i18n/index.js";
import { checkLang } from "@/i18n/index.js";

const props = defineProps({
  starInfo: {
    defult: () => ({}),
  },
});

const lang = checkLang();
const t = i18n.global.t;
const TAB_1 = 1,
  TAB_2 = 2;
const specsTabsSelected = ref(TAB_1);
const specsTabs = ref([
  {
    label: t(`promoter_star.primary`),
    value: TAB_1,
    traceKey: "star_ambassador_beginner_tab_click",
  },
  {
    label: t(`promoter_star.advanced`),
    value: TAB_2,
    traceKey: "star_ambassador_advanced_tab_click",
  },
]);
const specs = ref({
  [TAB_1]: [
    {
      platform: "Tiktok",
      icon: IconTiktok,
      content: t(`promoter_star.post_video_on_tiktok`),
    },
    {
      platform: "Kwai",
      icon: IconKwai,
      content: t(`promoter_star.post_video_on_youtube`),
    },
    {
      platform: "Instagram",
      icon: IconInstangram,
      content: t(`promoter_star.post_video_on_instagram`),
    },
    {
      platform: "Facebook",
      icon: IconFacebook,
      content: t(`promoter_star.post_video_on_facebook`),
    },
  ],
  [TAB_2]: [
    {
      platform: "Tiktok",
      icon: IconTiktok,
      content: t(`promoter_star.post_video_on_tiktok_advanced`),
    },
    {
      platform: "Kwai",
      icon: IconKwai,
      content: t(`promoter_star.post_video_on_youtube_advanced`),
    },
    {
      platform: "Instagram",
      icon: IconInstangram,
      content: t(`promoter_star.post_video_on_instagram_advanced`),
    },
    {
      platform: "Facebook",
      icon: IconFacebook,
      content: t(`promoter_star.post_video_on_facebook_advanced`),
    },
  ],
});
function getImageUrl(name) {
  return new URL(
    `../../../../assets/images/promoter-star/${lang}/${name}`,
    import.meta.url
  ).href;
}
const formatDate = (unixTimestamp) => {
  try {
    const date = new Date(unixTimestamp * 1000);
    if (isNaN(Date.parse(date))) {
      return "";
    }
    return date.toLocaleDateString();
  } catch (e) {
    return "";
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.ml-12 {
  margin-left: px2rem(12);
}
.text-48 {
  font-size: px2rem(48);
}
.text-36 {
  font-size: px2rem(36);
}
.text-24 {
  font-size: px2rem(24);
}
.h-80 {
  height: px2rem(80);
}
.h-70 {
  height: px2rem(70);
}
.pt-12 {
  padding-top: px2rem(12);
}
.font-700 {
  font-weight: 700;
}
.block-prize {
  padding: 0 px2rem(12);
  padding-top: px2rem(24);
  .prize-tip {
    color: #145057;
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 400;
    line-height: 132%;
    :deep(span) {
      font-weight: bold;
    }
  }
  .tag-box {
    margin-top: px2rem(16);
    & > div {
      margin-bottom: px2rem(12);
    }
    .tag-block {
      border-radius: px2rem(12);
      background: #f9f9f9;
      position: relative;
      overflow: hidden;
      color: #7c0001;
      text-align: center;
      font-family: Gilroy;
      font-style: normal;
      font-weight: 900;
      line-height: normal;
      padding: px2rem(10) 0;
      flex-grow: 1;

      .tag {
        border-radius: 0px 0px px2rem(8) 0px;
        background: #f64648;
        color: var(---White, #fff);

        /* T15/B */
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        display: inline-block;
        padding: 0 px2rem(8);
        position: absolute;
        top: 0;
        left: 0;
      }
      .content {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .video-specs {
    width: 100%;
    padding: 0 px2rem(12) px2rem(8) px2rem(12);
    border-radius: px2rem(12);
    overflow: auto;
    background: #e2f7ff;
    & > div {
      margin-top: px2rem(16);
    }
    .title {
      color: #083c44;
      text-align: center;

      /* T15/B */
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
    .gifs {
      margin-top: px2rem(9);
      display: flex;
      justify-content: center;
      align-items: center;
      .gif {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex-grow: 1;
        flex-shrink: 0;
        .icon {
          width: px2rem(130);
          height: px2rem(130);
          flex-shrink: 0;
        }
        .gift-1 {
          background: url("@/assets/images/promoter-star/gift-1.png") 50% /
            cover no-repeat;
        }
        .gift-2 {
          background: url("@/assets/images/promoter-star/gift-2.png") 100% /
            100% no-repeat;
        }
        .name {
          margin-top: px2rem(8);
          width: 100%;
          display: flex;
          padding: px2rem(4) px2rem(12);
          justify-content: center;
          align-items: center;
          border-top: 1px solid rgba(206, 210, 255);
          border-bottom: 1px solid rgba(206, 210, 255);
          background: linear-gradient(
            90deg,
            rgba(234, 239, 255, 0) 0%,
            #ebf1ff 50%,
            rgba(234, 239, 255, 0) 100%
          );
          color: #145057;
          text-align: center;

          /* T13/R */
          font-family: Gilroy;
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 124%; /* 16.12px */
          mask: linear-gradient(
            90deg,
            transparent 0%,
            #fff 40%,
            #fff 60%,
            transparent 100%
          );
        }
      }
    }
    .video-specs-table {
      margin-top: px2rem(36);
      width: 100%;
      border-radius: px2rem(8);
      border: 1px solid #9abecb;
      color: #083c44;

      /* T15/B */
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      line-height: normal;
      .row {
        min-height: px2rem(48);
        display: flex;
        border-bottom: 1px solid #9abecb;
        .cell {
          border-right: 1px solid #9abecb;
          display: flex;
          align-items: center;
          padding: px2rem(8);
          &:last-child {
            border-right: none;
          }
          .platform {
            display: flex;
            align-items: center;
            & :first-child {
              width: px2rem(24);
              height: px2rem(24);
              border-radius: px2rem(8);
            }
          }
        }
        &:last-child {
          border-bottom: none;
        }
        .cell-left {
          width: px2rem(120);
          flex-shrink: 0;
        }
        .cell-right {
          flex-grow: 1;
        }
      }
    }
  }
}
.rtl-html {
  .cell {
    border-right: none !important;
  }
  .cell-left {
    border-left: 1px solid #9abecb;
    border-right: none !important;
  }
}
</style>
