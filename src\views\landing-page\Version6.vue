<template>
  <div
    class="app-container flex"
    @click="downloadOrAdjust"
    v-track:click
    trace-key="fb_share_h5_click"
    :track-params="JSON.stringify(pointData('BR-2', 1, 'pt'))"
  >
    <img
      src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version6/title.png"
      alt=""
      class="more-img"
      v-track:exposure
      trace-key="fb_share_h5_expo"
      :track-params="JSON.stringify(pointData('BR-2', 1, 'pt'))"
    />
    <main>
      <section v-for="(item, idx) in pictureList" :key="idx">
        <div class="info flex">
          <span>{{ item.distance }}</span>
          <img
            src="@/assets/images/landing-page/version2/location.png"
            alt=""
          />
          <gap :gap="2"></gap>
          <span>{{ item.age }}</span>
          <gap :gap="2"></gap>
          <img src="@/assets/images/common/female.png" alt="" class="gender" />
        </div>
        <van-swipe
          class="picture-wrap"
          :autoplay="currentIndex === idx ? 3000 : 0"
          :touchable="false"
        >
          <van-swipe-item>
            <img
              :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/avatars/${item.pictrue[0]}.jpg`"
              alt=""
              class="picture"
            />
          </van-swipe-item>
        </van-swipe>
        <img
          src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/call.png"
          alt=""
          class="free-btn"
        />
      </section>
    </main>
    <footer>
      <div class="download-btn">
        <span>descarregar</span>
        <gap :gap="4"></gap>
        <img
          src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version5/download.png"
          alt=""
        />
      </div>
    </footer>
  </div>
</template>

<script setup>
import { downloadOrAdjust, pointData } from "./utils.js";

const pictureList = [
  {
    pictrue: ["16"],
    distance: "2.8km",
    age: "19",
  },
  {
    pictrue: ["10"],
    distance: "3.2km",
    age: "18",
  },
  {
    pictrue: ["11"],
    distance: "850m",
    age: "19",
  },
  {
    pictrue: ["12"],
    distance: "1.2km",
    age: "23",
  },
  {
    pictrue: ["13"],
    distance: "4.1km",
    age: "22",
  },
  {
    pictrue: ["14"],
    distance: "1.1km",
    age: "19",
  },
];
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  position: relative;
  width: 100vw;
  background: url("https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version6/bg.png")
    no-repeat;
  background-color: #c28bf5;
  background-size: 100% auto;
  padding: px2rem(630) 0 px2rem(70) 0;
  .more-img {
    width: px2rem(306);
    height: px2rem(60);
    margin: px2rem(10) auto px2rem(12);
  }
  main {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 0 px2rem(22) px2rem(34);
    section {
      position: relative;
      margin-bottom: px2rem(40);
      .info {
        position: absolute;
        top: px2rem(12);
        right: px2rem(8);
        width: 100%;
        z-index: 100;
        justify-content: flex-end;
        padding: px2rem(8);
        font-size: $fontSize15;
        color: $white;
        height: px2rem(21);
        @include centerL;
        padding: 0 px2rem(4);
        img {
          width: px2rem(12);
          height: px2rem(12);
        }
      }
      .picture-wrap {
        width: px2rem(164);
        height: px2rem(208);
        border-radius: px2rem(16);
        overflow: hidden;
        transform: scaleX(-1);
        .picture {
          width: 100%;
          object-fit: fill;
        }
      }
      .free-btn {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 50%);
        width: px2rem(120);
        height: px2rem(42);
      }
    }
  }
  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 101;
    padding: px2rem(60) px2rem(16) px2rem(30);
    background: linear-gradient(
      0deg,
      rgba(211, 81, 255, 0.64) 0%,
      rgba(255, 90, 219, 0.32) 50%,
      rgba(255, 90, 214, 0) 100%
    );

    .download-btn {
      width: 100%;
      height: px2rem(72);
      line-height: px2rem(72);
      border-radius: px2rem(72);
      font-weight: $fontWeightBold;
      text-align: center;
      background: linear-gradient(90deg, #9091fe 0%, #fc98fe 100%);
      font-size: px2rem(28);
      color: #fff;
      animation: spin 0.8s ease-in-out infinite;
      box-shadow: 0px 0px 11px 2px #ffffff inset;
      @include center;
      img {
        width: px2rem(24);
        height: px2rem(24);
      }
    }
  }
}
@keyframes spin {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
</style>
