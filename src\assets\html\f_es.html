<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Tu amigo te invita a Siya" />
    <meta property="og:description" content="Descarga y chatea para ganar dinero. Ya he ganado 50 $ a través de SIYA~ Descarga a través del enlace y rellena el código de invitación para recibir los beneficios para principiantes~
" />
    <meta property="og:image"
        content="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/invite/share-female.png" />
    <script async>
        try {
            const envMap = {
                1: 'https://server.siyatest.com/i/f',
                2: 'https://server-gray.siyachat.com/i/f',
                3: 'https://siyachat.com/i/f',
            }
            const url = location.href;
            const regex = /\?(.*)/;
            const match = url.match(regex);

            const urlParams = new URLSearchParams(window.location.search);
            const env = urlParams.get("t").substring(0, 1);
            const host = envMap[env];
            if (match && host) {
                const queryString = match[1];
                window.location.href = `${host}?${queryString}`
            }
        } catch (error) {
            console.error(error)
        }
    </script>
</head>

</html>