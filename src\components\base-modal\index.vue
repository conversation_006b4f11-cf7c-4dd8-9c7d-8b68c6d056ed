<template>
  <van-overlay :show="dialogShow" @click="handleCancel">
    <div class="base-modal-wrapper" @click.stop>
      <div class="wrapper-bg"></div>
      <div class="wrapper-header flex">
        <div class="wrapper-title">
          <slot name="title"></slot>
        </div>
        <div class="wrapper-content">
          <slot name="content"></slot>
        </div>
      </div>
      <div class="wrapper-button-wrap">
        <div class="wrapper-button active" v-if="showConform" @click="conform">
          {{ conformText }}
        </div>
        <div class="wrapper-button" v-if="showCancel" @click="handleCancel">
          {{ cancelText }}
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
import { ref, watch } from "vue";
import { i18n } from "@/i18n/index.js";

const emit = defineEmits(["update:show", "confirm"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  showConform: {
    type: Boolean,
    default: true,
  },
  conformText: {
    type: String,
    default: () => i18n.global.t("common.common_ok"),
  },
  showCancel: {
    type: Boolean,
    default: false,
  },
  cancelText: {
    type: String,
    default: () => i18n.global.t("common.common_cancel"),
  },
});

const dialogShow = ref(false);

watch(
  () => props.show,
  (val) => {
    dialogShow.value = !!val;
  },
  { immediate: true }
);

const handleCancel = () => {
  emit("update:show", false);
};

const conform = () => {
  emit("confirm");
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.base-modal-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  overflow: hidden;
  width: px2rem(300);
  padding: px2rem(32) $gap16 $gap16;
  background: #ffffff;
  border-radius: px2rem(26);
  transform: translate(-50%, -50%);

  .wrapper-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: px2rem(100);
    background: linear-gradient(
      175.89deg,
      rgba(95, 86, 237, 0.16) 3.35%,
      rgba(95, 86, 237, 0) 96.66%
    );
  }

  .wrapper-header {
    flex-direction: column;
    margin-bottom: px2rem(22);
  }

  .wrapper-title {
    margin-bottom: $gap8;
    font-size: px2rem(17);
    font-weight: $fontWeightBold;
    line-height: px2rem(24);
    text-align: center;
    color: $fontColorB0;
  }

  .wrapper-content {
    font-size: px2rem(13);
    text-align: center;
    color: $fontColorB3;
  }

  .wrapper-button {
    flex: 1;
    height: px2rem(34);
    margin-top: px2rem(12);
    font-size: px2rem(17);
    line-height: px2rem(34);
    color: $fontColorB3;
    text-align: center;
    border-radius: $radius16;

    &.active {
      height: px2rem(50);
      line-height: px2rem(50);
      color: $white;
      background-color: $mainColor;
    }
  }
}
</style>
