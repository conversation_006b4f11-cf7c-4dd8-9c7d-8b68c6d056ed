<template>
  <div class="app-container">
    <div class="reason-title">{{reason.name}}</div>
    <van-field
        class="reason-detail"
        v-model="describe"
        rows="3"
        autosize
        type="textarea"
        maxlength="200"
        :formatter="formatter"
        format-trigger="onBlur"
        :placeholder="$t('help.report_describe_detail')"
        show-word-limit
    />
    <div class="reason-upload">
      <upload-cell @update="handleUpdateImages" />
      <div class="upload-tips">{{ $t('help.report_photo_limit') }}</div>
    </div>

    <van-button class="submit-button"
                :disabled="buttonDisabled"
                block type="primary"
                @click="handleSubmit"
    >
      {{ $t('common.common_submit') }}
    </van-button>
  </div>
</template>

<script setup>
import { getCurrentInstance, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import helpApi from '@/api/help.js'
import uploadCell from '@/views/help/feedback/components/uploadCell.vue'
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t
const route = useRoute()
const { proxy } = getCurrentInstance()

let reason = route.query.reason || '{}'
reason = JSON.parse(reason)

const describe = ref('')
const buttonDisabled = ref(true)
// 过滤输入的空格
const formatter = (value) => value.trim();
watch(() => describe.value, (val) => {
  // 大于5个字后按钮可点击
  buttonDisabled.value = val.length < 5
})

// 上传图片
const fileList = ref([])
const handleUpdateImages = (file) => {
  fileList.value = file
}

// 提交
const handleSubmit = async () => {
  if (buttonDisabled.value) return

  const params = {
    user_id: reason.userId,
    type: reason.type,
    content: describe.value,
    pic: fileList.value.map(i => i.url),
    report_id: reason.reportId,
    id: reason.id,
    channel: reason.channel,
    ver: proxy.$appVersionName,
  }
  const response = await helpApi.report_submit(params)
  if (response.code === 200) {
    proxy.$closeAfterToast(`${t('help.report_success')}`)
  }
}
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding: px2rem(16);

  --van-uploader-border-radius: #{$radius16};
}

.reason-title {
  padding: px2rem(17) px2rem(12);
  margin-bottom: $gap8;
  font-size: px2rem(15);
  color: $fontColorB0;
  background-color: $white;
  border-radius: $radius16;
}

.reason-detail {
  margin-bottom: $gap8;
  font-size: px2rem(15);
  border-radius: $radius16;

}

.reason-upload {

  .upload-tips {
    font-size: px2rem(13);
    color: $fontColorB4;
  }
}

.submit-button {
  margin-top: $gap16;
}
</style>
