<template>
  <div class="gb-outer-border" :style="style">
    <div v-if="showDecoration" class="r-1"></div>
    <div v-if="showDecoration" class="r-2"></div>
    <div v-if="showDecoration" class="r-3"></div>
    <div class="gb-inside-border">
      <div class="gb-inner" :class="showBg ? 'show-bg' : ''">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  showBg: {
    type: Boolean,
    default: true,
  },
  showDecoration: {
    type: Boolean,
    default: true,
  },
  borderRadius: {
    type: Number,
    default: [40, 40, 40, 40],
  },
});

const style = computed(() => {
  return {
    "--border-radius": `${props.borderRadius
      .map((i) => `${proxy.$pxToRemPx(i)}px`)
      .join(" ")}`,
  };
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.gb-outer-border {
  width: 100%;
  height: fit-content;
  padding: px2rem(3);
  border-radius: var(--border-radius);
  background: linear-gradient(135deg, #ffecaa 0%, #ffecaa 50%, #f36e25 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  .r-3 {
    content: "";
    position: absolute;
    z-index: 5;
    top: px2rem(-23);
    margin: 0 auto;
    width: 85%;
    aspect-ratio: 308.88/59;
    background-image: url("@/assets/images/activity/pk-king/bg3.png");
    background-size: cover;
  }
  .r-1,
  .r-2 {
    position: absolute;
    z-index: 2;
    left: px2rem(4);
    top: px2rem(4);
    height: px2rem(72);
    width: calc(100% - px2rem(8));
    &::before,
    &::after {
      content: "";
      position: absolute;
      background-image: url("@/assets/images/activity/pk-king/rad.png");
      background-size: px2rem(68) px2rem(72);
      background-repeat: no-repeat;
      background-position: top left;
      width: px2rem(68);
      height: px2rem(72);
    }
    &::after {
      right: 0;
      transform: scaleX(-1);
    }
  }
  .r-2 {
    top: unset;
    bottom: px2rem(4);
    transform: scaleY(-1);
  }
  .gb-inside-border {
    border-radius: var(--border-radius);
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #e66c23 0%, #e87f2d 50%, #ffdcad 100%);
    padding: px2rem(3);
    .gb-inner {
      position: relative;
      background: #6a0091;
      border-radius: var(--border-radius);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
    }
    .gb-inner.show-bg {
      &::after {
        pointer-events: none;
        content: "";
        position: absolute;
        inset: 0;
        background-image: url("@/assets/images/activity/egypt-national-day/box-inner.png");
        background-size: px2rem(407) px2rem(353);
        border-radius: var(--border-radius);
        opacity: 0.05;
      }
    }
  }
}

.rtl-html {
  .r-1,
  .r-2 {
    &::before {
      left: 0;
    }
  }
}
</style>
