<template>
  <div
    class="btn-vote"
    :class="userItem?.is_vote ? '' : 'vote-actived'"
    @click="handleVote"
    v-track:click
    trace-key="singer_button_click"
    :track-params="
      JSON.stringify({
        singer_button_name: 10,
        singer_button_click_source: source,
      })
    "
  >
    {{
      userItem?.is_vote
        ? $t(`singer.audition_round_vote_success`)
        : $t(`singer.audition_round_vote`)
    }}
  </div>
</template>
<script setup>
import singer<PERSON><PERSON> from "@/api/activity.js";
import { t } from "@/i18n/index.js";
const emit = defineEmits(["vote"]);
const props = defineProps({
  userItem: {
    type: Object,
    default: null,
  },
  // 按钮来源：3.搜索 4.热门榜 5.随机列表 6.我的好友列表
  source: {
    type: Number,
    default: 3,
  },
});

const handleVote = async () => {
  if (!props.userItem) {
    return;
  }
  if (props.userItem.is_vote) {
    showToast(t("singer.audition_round_cannot_vote_repeatedly"));
    return;
  }
  const res = await singerApi.singer_poll({
    account: props.userItem?.account,
  });
  switch (res.code) {
    case 200:
      showToast(t("singer.audition_round_vote_success"));
      emit("vote", props.userItem);
      break;
    case 20017:
      showToast(t("singer.audition_round_all_votes_used"));
      break;
    case 20018:
      showToast(t("singer.audition_round_cannot_vote_repeatedly"));
      break;
  }
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.btn-vote {
  width: px2rem(74);
  height: px2rem(25);
  flex-shrink: 0;
  border-radius: px2rem(15);
  border: px2rem(0.8) solid #ffc2f9;
  background: #5c00ba;
  box-shadow: 0px -3px 4px 0px rgba(0, 0, 0, 0.25) inset,
    0px 2px 4px 0px rgba(255, 255, 255, 0.29) inset;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ad5eff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(10);
  font-style: normal;
  font-weight: 700;
  line-height: 80%; /* 8px */
}

.vote-actived {
  color: #fff;
  border: px2rem(0.8) solid #edb5ff;
  background: linear-gradient(180deg, #ff3d68 0%, #ad50ff 100%);
  text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.4);
}
</style>
