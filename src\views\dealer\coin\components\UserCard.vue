<template>
  <div class="card-wrap flex">
    <img :src="data.avatar" alt="" class="avatar" />
    <gap :gap="8" />
    <div class="card-right">
      <div class="card-name flex">
        <span class="nickname ellipsis">{{ data.nickname }}</span>
        <gap :gap="4" />
        <div
          class="gender-wrap flex"
          v-if="gender"
          :style="
            data.gender === 1
              ? { background: ' #D3D1FFCC', color: '#5F56ED' }
              : {}
          "
        >
          <img :src="getImageUrl(data.gender === 1 ? 'man' : 'woman')" alt="" />
        </div>
      </div>
      <div class="card-ID flex">
        <img src="@/assets/images/dealer-coin/ID.png" alt="" />
        <gap :gap="2" />
        {{ data.account }}
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Object,
  },
  border: {
    type: Boolean,
    default: false,
  },
  gender: {
    type: Boolean,
    default: true,
  },
});

const getImageUrl = (name) => {
  return new URL(
    `../../../../assets/images/dealer-coin/${name}.png`,
    import.meta.url
  ).href;
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.card-wrap {
  padding: px2rem(10) 0;
  background: #fff;
  border-bottom: v-bind("border?'0.5px solid #f2f2f2':''");
  flex-wrap: nowrap !important;
  .avatar {
    width: px2rem(40);
    height: px2rem(40);
    border-radius: 50%;
  }
  .card-right {
    flex: 1;
    flex-wrap: nowrap;
    .card-name {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: px2rem(4);
      .nickname {
        max-width: calc(100vw - px2rem(120));
      }
      .gender-wrap {
        height: px2rem(16);
        border-radius: px2rem(16);
        background: #ffd5dfcc;
        @include fontSize11;
        color: #ff4771;
        padding: 0 px2rem(6) 0 px2rem(4);
        img {
          width: px2rem(13);
          height: px2rem(13);
        }
      }
    }
    .card-ID {
      font-size: 12px;
      color: #999;
      img {
        width: px2rem(12);
        height: px2rem(12);
      }
    }
  }
}
</style>
