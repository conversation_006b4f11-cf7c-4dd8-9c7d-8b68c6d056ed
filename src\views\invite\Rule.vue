<template>
  <div class="container">
    <header-bar
      :title="$t('invite.rules_title')"
      :is-scroll-change-bg="false"
      fixed-bg-color="#fff"
    />
    <div class="inner">
      <div class="title">{{ $t("invite.rules_title") }}</div>
      <div class="content">
        <p class="mb-16">
          {{
            gender === "1"
              ? $t("invite.gold_rule_1")
              : $t("invite.diamond_rule_1")
          }}
        </p>
        <p class="mb-16">
          {{
            gender === "1"
              ? $t("invite.gold_rule_2")
              : $t("invite.diamond_rule_2")
          }}
        </p>
        <p class="mb-24">
          {{
            gender === "1"
              ? $t("invite.gold_rule_3")
              : $t("invite.diamond_rule_3")
          }}
        </p>
        <div class="sub-title mb-16">{{ $t("invite.reward_tiers_note") }}</div>
        <div class="table mb-24">
          <div class="row header">
            <div class="cell">
              {{ $t("invite.gold_male_recharge_amount") }}
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              {{
                gender === "1"
                  ? $t("invite.gold_male_reward_amount")
                  : $t("invite.diamond_reward_amount")
              }}
            </div>
          </div>
          <div class="row" v-for="item in data?.task_reward_recharge || []">
            <div class="cell">
              <div class="cell-content">
                <div class="color-1">{{ item.progress_target }}</div>
                <Coin :gender="1"></Coin>
              </div>
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              <div class="cell-content">
                <div class="color-2">{{ item.progress_reward }}</div>
                <Coin :gender="+gender"></Coin>
              </div>
            </div>
          </div>
        </div>
        <div class="table mb-16">
          <div class="row header">
            <div class="cell">
              {{ $t("invite.gold_female_diamond_amount") }}
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              {{
                gender === "1"
                  ? $t("invite.gold_male_reward_amount")
                  : $t("invite.diamond_reward_amount")
              }}
            </div>
          </div>
          <div class="row" v-for="item in data?.task_reward_income || []">
            <div class="cell">
              <div class="cell-content">
                <div class="color-1">{{ item.progress_target }}</div>
                <Coin :gender="11"></Coin>
              </div>
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              <div class="cell-content">
                <div class="color-2">{{ item.progress_reward }}</div>
                <Coin :gender="+gender"></Coin>
              </div>
            </div>
          </div>
        </div>
        <p class="mb-16">
          {{ $t("invite.rule_4") }}
        </p>
        <div class="table mb-16">
          <div class="row header">
            <div class="cell">
              {{ $t("invite.gold_monthly_invite_num") }}
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              {{
                gender === "1"
                  ? $t("invite.gold_male_reward_amount")
                  : $t("invite.diamond_reward_amount")
              }}
            </div>
          </div>
          <div class="row" v-for="item in data?.task_reward_register || []">
            <div class="cell">
              <div class="cell-content">
                <div class="color-1 font-13">
                  {{
                    item.is_first_invite === 1
                      ? $t("invite.gold_first_invite_success")
                      : $t("invite.gold_invite_x_users", [item.progress_target])
                  }}
                </div>
              </div>
            </div>
            <div class="table-gap"></div>
            <div class="cell">
              <div class="cell-content">
                <div class="color-2">{{ item.progress_reward }}</div>
                <Coin :gender="+gender"></Coin>
              </div>
            </div>
          </div>
        </div>
        <p class="mb-16">
          {{ $t("invite.rule_5") }}
        </p>
        <p class="mb-16">
          {{ $t("invite.rule_6") }}
        </p>
        <p class="mb-16">
          {{ $t("invite.rule_7") }}
        </p>
        <p class="mb-16">
          {{ $t("invite.rule_8") }}
        </p>
        <p class="mb-16">
          {{ $t("invite.rule_9") }}
        </p>
        <p class="mb-16">
          {{ $t("invite.rule_10") }}
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>
import Coin from "./components/Coin.vue";
import { useRoute } from "vue-router";
import { computed, ref, onMounted, nextTick } from "vue";
import inviteApi from "@/api/invite.js";

const route = useRoute();
const gender = computed(() => `${route.query?.gender}` || "1");

const data = ref(null);

const fetchData = async () => {
  const res = await inviteApi.task_reward_desc();
  if (res.code === 200) {
    data.value = res.data;
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.mb-16 {
  margin-bottom: px2rem(16);
}
.mb-24 {
  margin-bottom: px2rem(24);
}
.color-1 {
  color: #642628;
}
.color-2 {
  color: #ff4771;
}
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ffcfc9 0%, #f5dcf7 100%);
  overflow: hidden;
  padding-bottom: px2rem(36);
  .inner {
    margin: px2rem(12) px2rem(17);
    border-radius: px2rem(24);
    border: px2rem(3) solid #ed9d9d;
    background: #fff1f1;
    min-height: 100vh;

    .title {
      width: px2rem(240);
      height: px2rem(38);
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      background-image: url("@/assets/images/invite/rulr-title-bg.png");
      background-size: 100% 100%;
      margin-top: px2rem(-4);

      color: #fff3bc;
      text-align: center;
      font-family: "Alibaba PuHuiTi 3.0";
      font-size: px2rem(17);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      gap: px2rem(4);

      &::before,
      &::after {
        content: "";
        width: px2rem(14);
        height: px2rem(14);
        background-image: url("@/assets/images/invite/star.svg");
        background-size: 100% 100%;
      }
    }

    .content {
      padding: 0 px2rem(20);
      padding-top: px2rem(12);
      padding-bottom: px2rem(24);
      p {
        color: #642628;

        /* T15/R */
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 500;
        line-height: 128%; /* 19.2px */
      }

      .sub-title {
        color: #642628;
        font-family: "Alibaba PuHuiTi 3.0";
        font-size: px2rem(17);
        font-style: normal;
        font-weight: 700;
        line-height: 128%; /* 21.76px */
      }

      .table {
        border-radius: px2rem(12);
        border: px2rem(1) solid #9b7b7c;
        overflow: hidden;
        .row {
          display: flex;
          border-bottom: px2rem(1) solid #9b7b7c;
          min-height: px2rem(56);
          &:last-child {
            border-bottom: none;
          }
        }
        .header {
          background: #ffe7e7;
          .cell {
            color: #642628;
            text-align: center;

            /* T15/B */
            font-family: Gilroy;
            font-size: px2rem(15);
            font-style: normal;
            font-weight: 700;
            line-height: normal;
          }
        }
        .cell {
          padding: px2rem(8);
          flex-grow: 1;
          flex-shrink: 0;
          width: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          .cell-content {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: px2rem(2);
            min-height: px2rem(48);
            flex-grow: 1;
            width: 100%;
            & > div {
              font-family: Gilroy;
              font-size: px2rem(22);
              font-style: normal;
              font-weight: 900;
              line-height: normal;
            }
            .font-13 {
              font-size: px2rem(13) !important;
              line-height: normal;
            }
          }
        }
        .table-gap {
          position: relative;
          flex-shrink: 0;
          z-index: 3;
          height: auto;
          width: px2rem(1);
          background-color: #9b7b7c;
        }
      }
    }
  }
}
</style>
