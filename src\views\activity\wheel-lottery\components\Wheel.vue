<template>
  <div class="wheel-bg">
    <div class="wheel-light"></div>
    <div
      class="wheel-inner"
      :style="{
        transform: `rotate(${angle % 360}deg)`,
      }"
    >
      <div
        class="is-prize"
        :class="{
          'is-prize-animation': isPrizeAnimation,
        }"
        :style="{
          transform: `rotate(${-angle % 360}deg)`,
        }"
      ></div>
      <div
        class="prize"
        v-for="(prize, index) in prizes"
        :style="{
          transform: `rotate(${index * 45}deg)`,
        }"
        @click="handlePrizeClick(prize)"
        v-track:click
        trace-key="prize_wheel_reward_click"
      >
        <template v-if="prize.reward_type === 1">
          <img
            src="@/assets/images/wheel-lottery/common/wheel/prize-coin.png"
          />
          <div>
            <span class="flex"><span>x</span>{{ prize.num }}</span>
          </div>
        </template>
        <template v-else>
          <img :src="prize.reward_img" />
          <div>
            <span class="flex" v-if="prize.reward_type !== 5"
              ><span>x</span
              >{{
                $t(
                  prize.duration > 1
                    ? "common.common_days"
                    : "common.common_day",
                  [prize.duration]
                )
              }}</span
            >
            <span class="flex" v-else><span>x</span>{{ prize.num }}</span>
          </div>
        </template>
      </div>
    </div>
    <div class="wheel-button">
      <canvas
        :id="pag1"
        :class="{
          'tap-animation': running,
        }"
      ></canvas>
      <canvas :id="pag2"></canvas>
      <div
        class="button-text"
        :class="{
          'tap-animation': running,
        }"
      >
        <div
          :style="{
            backgroundImage: `url('${getImageUrl('wheel-btn.png')}')`,
          }"
        ></div>
        <div>{{ useCount }}/{{ drawCount }}</div>
      </div>
    </div>
    <div class="click-area" @click="handleStart"></div>
  </div>
</template>
<script setup>
import { AnimationPAGInitLocal } from "@/utils/util.js";
import { onMounted, ref, computed, onUnmounted } from "vue";
import { checkLang } from "@/i18n/index.js";
import { useRoute } from "vue-router";
import musicManager from "../utils/musicManager";
const route = useRoute();
const theme = computed(() => route.params.theme || "normal");

let animationObj1 = null,
  animationObj2 = null,
  pag1 = "pag-1",
  pag2 = "pag-2";

const emits = defineEmits(["result", "prizeClick"]);
const props = defineProps({
  prizes: {
    type: Array,
  },
  useCount: {
    type: Number,
  },
  drawCount: {
    type: Number,
  },
  getResultApi: {
    type: Function,
  },
});

const running = ref(false);
const isPrizeAnimation = ref(false);

let startAngle = 0; // 起始角度
let endAngle = 0; // 结束角度
const duration = 6000; // 总时长（ms）
const accelTime = 3000; // 加速时间（ms）
const totalCircles = 6; // 至少旋转圈数

const angle = ref(startAngle);
const lang = checkLang();
function getImageUrl(name) {
  return new URL(
    `../../../../assets/images/wheel-lottery/common/${lang}/${name}`,
    import.meta.url
  ).href;
}
const init = async () => {
  animationObj1 = await AnimationPAGInitLocal(
    `wheel-pag-${theme.value}-1.pag`,
    `#${pag1}`
  );
  animationObj1.setRepeatCount(0);
  animationObj2 = await AnimationPAGInitLocal(
    `wheel-pag-${theme.value}-2.pag`,
    `#${pag2}`
  );
  animationObj2.setRepeatCount(0);
  animationObj1.play();
};

const handlePrizeClick = (prize) => {
  emits("prizeClick", prize);
};

const handleStart = async () => {
  if (running.value === true) {
    return;
  }
  musicManager.playClick();
  running.value = true;
  const result = await props.getResultApi();
  if (result) {
    const index = props.prizes.findIndex(
      (i) => i.reward_packet_id === result.reward_packet_id
    );
    if (index === -1) {
      running.value = false;
      return;
    }
    endAngle = index * 45 * -1;
    setTimeout(() => {
      musicManager.playRound();
      setTimeout(() => {
        animationObj2.play();
        setTimeout(() => {
          animationObj2.stop();
        }, 4000);
      }, 1000);
      startAnimation();
      setTimeout(() => {
        musicManager.stopRound();
        musicManager.playTarget();
        isPrizeAnimation.value = true;
        setTimeout(() => {
          musicManager.stopTarget();
          musicManager.playPrize();
          running.value = false;
          isPrizeAnimation.value = false;
          emits("result", result);
        }, 1000);
      }, 6000);
    }, 800);
  } else {
    running.value = false;
  }
};

// 开始旋转
const startAnimation = () => {
  const t1 = accelTime / 1000; // 加速时间 (秒)
  const t2 = (duration - accelTime) / 1000; // 减速时间 (秒)

  const extraAngle = (endAngle - startAngle + 360) % 360;
  const totalAngle = totalCircles * 360 + extraAngle;

  const coeff = 0.5 * t1 * t1 + t1 * t2 - 0.5 * t2 * t2;
  const a = totalAngle / coeff; // 反推加速度 (°/s²)

  const accelAngle = 0.5 * a * t1 * t1;
  const vMax = a * t1;

  const start = performance.now();

  function animate(now) {
    const elapsed = (now - start) / 1000;
    let currentAngle = 0;

    if (elapsed < t1) {
      // 加速阶段
      currentAngle = 0.5 * a * elapsed * elapsed;
    } else if (elapsed < t1 + t2) {
      // 减速阶段
      const t = elapsed - t1;
      currentAngle = accelAngle + vMax * t - 0.5 * a * t * t;
    } else {
      // 动画结束，精确设定最终角度
      angle.value = startAngle + totalAngle;
      startAngle = angle.value;
      return;
    }

    angle.value = startAngle + currentAngle;
    requestAnimationFrame(animate);
  }

  requestAnimationFrame(animate);
};

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
@import "../theme.scss";
@keyframes light {
  0%,
  50% {
    background-image: url("@/assets/images/wheel-lottery/common/wheel/light-1.png");
  }
  50.1%,
  100% {
    background-image: url("@/assets/images/wheel-lottery/common/wheel/light-2.png");
  }
}
@keyframes is-prize {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes tap-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
.flex {
  display: flex;
}
.wheel-bg {
  height: px2rem(496);
  width: px2rem(390);
  background-image: var(--wheel-bg);
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  .wheel-light {
    position: absolute;
    left: 0;
    z-index: 9;
    height: px2rem(390);
    width: px2rem(390);
    background-image: url("@/assets/images/wheel-lottery/common/wheel/light-1.png");
    background-size: 100% 100%;
    animation: light 2s infinite;
  }
  .wheel-inner {
    height: px2rem(390);
    width: px2rem(390);
    background-image: var(--wheel-inner);
    background-size: 100% 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;
    .prize {
      position: absolute;
      height: 50%;
      top: 0;
      padding-top: px2rem(46);
      transform-origin: center bottom;
      z-index: 9;
      img {
        height: px2rem(48);
        width: px2rem(48);
        vertical-align: bottom;
      }
      div {
        color: #000;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-top: px2rem(2);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
  .is-prize {
    position: absolute;
    left: 0;
    z-index: 8;
    height: px2rem(390);
    width: px2rem(390);
    background-image: url("@/assets/images/wheel-lottery/common/wheel/is-prize.png");
    background-size: 100% 100%;
    opacity: 0;
  }
  .is-prize-animation {
    animation: is-prize 0.2s infinite;
  }
  .wheel-button {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    height: px2rem(188);
    width: px2rem(188);
    z-index: 99;
    pointer-events: none;
    canvas {
      position: absolute;
    }
    #pag-1 {
      z-index: 4;
      height: px2rem(188) !important;
      width: px2rem(376) !important;
    }
    #pag-2 {
      z-index: 3;
      height: px2rem(188) !important;
      width: px2rem(376) !important;
    }
    .button-text {
      color: #fff3e0;
      text-align: center;
      font-family: Gilroy;
      font-weight: 900;
      line-height: normal;
      z-index: 5;
      :first-child {
        height: px2rem(36);
        width: px2rem(76);
        background-size: 100% 100%;
      }
      :last-child {
        font-size: px2rem(17);
        margin-top: px2rem(-2);
      }
    }
    .tap-animation {
      animation: tap-animation 0.3s 1;
    }
  }
  .click-area {
    position: absolute;
    width: 80px;
    height: 80px;
    left: 50%;
    top: 50%;
    z-index: 99;
    transform: translateX(-50%) translateY(-50%);
  }
}
</style>
