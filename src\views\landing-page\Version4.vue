<template>
  <div
    class="app-container flex"
    @click="downloadOrAdjust"
    v-track:click
    trace-key="fb_share_h5_click"
    :track-params="JSON.stringify(pointData('中东落地页2', 1, 'ar'))"
  >
    <img
      src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/bg.jpg"
      alt=""
      class="top-img"
      v-track:exposure
      trace-key="fb_share_h5_expo"
      :track-params="JSON.stringify(pointData('中东落地页2', 1, 'ar'))"
    />
    <img
      src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/more-title.png"
      alt=""
      class="more-img"
    />
    <div class="picture-wrap">
      <img
        :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/picture${i}.png`"
        alt=""
        class="picture"
        v-for="i in 6"
        :key="i"
      />
      <img
        src="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/intersperse2.png"
        alt=""
        class="intersperse"
      />
    </div>
    <footer>
      <div class="download-btn">
        <span>تحميل</span>
        <gap :gap="4"></gap>
        <svg-icon class="download-icon" icon="download" color="#7c0001" />
      </div>
    </footer>
  </div>
</template>

<script setup>
import { onMounted, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import { downloadOrAdjust, pointData } from "./utils.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();

onMounted(() => {});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  position: relative;
  width: 100vw;
  background-color: #ff5a94;
  background-size: 100% auto;
  .top-img {
    width: 100%;
    height: px2rem(684);
  }
  .more-img {
    width: px2rem(306);
    height: px2rem(60);
    margin: px2rem(40) auto px2rem(12);
  }
  .picture-wrap {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 0 0 px2rem(110);
    position: relative;
    margin-top: px2rem(-10);
    background: url("https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/Version4/intersperse1.png")
      no-repeat;
    background-size: px2rem(118) px2rem(103);
    background-position: left px2rem(30);
    .picture {
      width: px2rem(205);
      margin-bottom: px2rem(-20);
      &:nth-child(2n) {
        margin-left: px2rem(-20);
      }
    }
    .intersperse {
      position: absolute;
      right: 0;
      top: px2rem(-20);
      height: px2rem(53);
      width: px2rem(43);
    }
  }
  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 101;
    padding: px2rem(60) px2rem(16) px2rem(30);
    background: linear-gradient(
      0deg,
      rgba(255, 90, 149, 0.64) 0%,
      rgba(255, 90, 149, 0.32) 50%,
      rgba(255, 90, 149, 0) 100%
    );

    .download-btn {
      width: 100%;
      height: px2rem(72);
      line-height: px2rem(72);
      border-radius: px2rem(72);
      color: #fff;
      font-weight: $fontWeightBold;
      text-align: center;
      background: #ffd42f;
      font-size: px2rem(28);
      box-shadow: 0px 0px px2rem(50.23) 0px #ffffff inset;
      color: #7c0001;
      animation: spin 0.8s ease-in-out infinite;
      @include center;
      .download-icon {
        font-size: px2rem(28);
        transform: translateY(px2rem(-2));
      }
    }
  }
}
@keyframes spin {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
</style>
