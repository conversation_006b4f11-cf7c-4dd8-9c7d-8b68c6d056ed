#!/usr/bin/env sh

# 发生错误时终止
set -e

rm -rf siya

# 构建
npm run build:stage

echo -e "\033[33;42m ========================== \033[0m"
echo -e "\033[33;42m === 当前为【STAGE】环境 === \033[0m"
echo -e "\033[33;42m === 当前为【STAGE】环境 === \033[0m"
echo -e "\033[33;42m === 当前为【STAGE】环境 === \033[0m"
echo -e "\033[33;42m === 当前为【STAGE】环境 === \033[0m"
echo -e "\033[33;42m === 当前为【STAGE】环境 === \033[0m"
echo -e "\033[33;42m ========================== \033[0m"

scp -r siya root@47.236.85.166:/usr/local/nginx/html/
