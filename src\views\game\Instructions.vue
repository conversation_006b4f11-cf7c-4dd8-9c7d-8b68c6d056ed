<template>
  <div class="app-container flex instructions-container">
    <header-bar
      :statusBarPadding="0.01"
      :title="$t('game.game_instructions_title')"
      fontColor="#fff"
      is-scroll-change-bg
      @emit:scrollChangeBgShow="scrollChangeBgShow"
      observer-root-margin="1px"
      fixed-bg-color="#ffffff10"
      :opacity-rate="100"
    >
      <template #right>
        <img
          class="close"
          @click="handleClose"
          src="@/assets/images/game/close.png"
          alt=""
      /></template>
    </header-bar>
    <main>
      <div
        class="text"
        v-for="(item, idx) in $tm('game.game_instructions_desc')"
        :key="idx"
      >
        {{ item }}
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";

const t = i18n.global.t;
const router = useRouter();
const { proxy } = getCurrentInstance();

const list = ref([
  "1.Lucky games can only use paid coins, and experience coins cannot be used in the game",
  "2.Room game list (1) The room/family game list only counts the game consumption and rewards of game players in the room (2) The statistical time of the room game list is: UTC+X",
  "Daily game tasks (1) The daily reset time of game tasks is: UTC+X 00:00:00 (2) When participating in game tasks, rewards will be automatically issued after completing the tasks",
  "If you encounter any problems in your game that need help, please contact the official customer service to help solve them. We will answer your questions as soon as possible. Wish you a happy game",
]);

const scrollChangeBgShow = (show) => {
  const targetBgEl = document.querySelector(
    ".instructions-container .header-bar-bg"
  );
  targetBgEl.style["backdrop-filter"] = "blur(10px)";
  targetBgEl.style["-webkit-backdrop-filter"] = "blur(10px)";
};

const handleClose = () => {
  proxy.$siyaApp("closeWebview");
};
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: url("@/assets/images/game/task-bg.jpg") top left no-repeat;
  background-size: 100%;
  background-color: #a8a4df;
  .close {
    width: px2rem(24);
    height: px2rem(24);
  }
  main {
    width: 100%;
    flex: 1;
    padding: px2rem(12) px2rem(24) px2rem(50);
    .text {
      font-size: $fontSize13;
      color: $white;
      margin-bottom: px2rem(10);
    }
  }
}
.app-container::v-deep(.title) {
  text-align: center !important;
}
.app-container::v-deep(.icon-back) {
  display: none;
}
</style>
