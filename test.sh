#!/usr/bin/env sh

# 发生错误时终止
set -e

rm -rf siya

# 构建
npm run build:test

echo -e "\033[41;36m ========================== \033[0m"
echo -e "\033[41;36m === 当前为【test】环境 === \033[0m"
echo -e "\033[41;36m === 当前为【test】环境 === \033[0m"
echo -e "\033[41;36m === 当前为【test】环境 === \033[0m"
echo -e "\033[41;36m === 当前为【test】环境 === \033[0m"
echo -e "\033[41;36m === 当前为【test】环境 === \033[0m"
echo -e "\033[41;36m ========================== \033[0m"

scp -r siya root@121.41.115.227:/usr/local/nginx/html/
