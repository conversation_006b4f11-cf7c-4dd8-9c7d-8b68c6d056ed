<template>
  <div class="rule-container">
    <div :style="{ height: `${headerHeight}px` }"></div>
    <header-bar
      :title="$t('recharge_ranking.rules_title')"
      :is-scroll-change-bg="true"
      :padding="false"
      fixed-bg-color="#fff"
      :backIcon="backIcon"
    >
      <template #title>
        <div class="title">{{ $t("recharge_ranking.rules_title") }}</div>
      </template>
    </header-bar>
    <right-button
      :text="$t('recharge_ranking.feedback')"
      @click="goFeedback()"
      :top="48"
    ></right-button>
    <div class="content-box">
      <div class="content-inner">
        <p v-for="text in $tm('recharge_ranking.rules')">
          {{ text }}
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>
import RightButton from "./components/RightButton.vue";
import { useRouter } from "vue-router";
import { computed, getCurrentInstance, onMounted } from "vue";
import backIcon from "@/assets/images/activity/recharge-ranking/arrow-left.svg";
const { proxy } = getCurrentInstance();
const router = useRouter();
const headerHeight = computed(() => Math.max(proxy.$appNavBarHeight, 44) + 46);
const goFeedback = () => {
  router.push({
    name: "Feedback",
    query: {
      type: 15,
    },
  });
};

onMounted(() => {});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule-container {
  min-height: 100vh;
  height: fit-content;
  background: linear-gradient(180deg, #551005 0%, #d01d00 100%);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.icon-back) {
    color: #ffe8aa;
  }

  &::before,
  &::after {
    opacity: 0.5;
    content: "";
    width: px2rem(75);
    height: 100%;
    position: absolute;
    top: px2rem(-100);
    background-size: px2rem(75) px2rem(496);
    background-repeat: no-repeat;
    background-position: 0 0;
  }
  &::before {
    left: px2rem(-21);
    transform: scaleX(-1);
    background-image: url("@/assets/images/activity/recharge-ranking/bg-1.png");
  }
  &::after {
    right: px2rem(-21);
    background-image: url("@/assets/images/activity/recharge-ranking/bg-1.png");
  }

  .title {
    text-align: left;
    background: linear-gradient(180deg, #fff 13.35%, #f9b700 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-family: Gilroy;
    font-size: px2rem(26);
    font-style: italic;
    font-weight: 900;
    line-height: normal;
    height: 100%;
    display: flex;
    align-items: center;
  }

  .content-box {
    border-radius: px2rem(18) px2rem(18) 0 0;
    border: px2rem(2) solid #ffe8aa;
    padding: px2rem(8);
    margin: 0 px2rem(24);
    margin-bottom: 0;
    margin-top: px2rem(19);
    border-bottom: unset;
    position: relative;
    flex-grow: 1;
    z-index: 2;
    height: fit-content;
    overflow: hidden;
    display: flex;
    .content-inner {
      inset: px2rem(8);
      margin-bottom: px2rem(-14);
      border-radius: px2rem(12) px2rem(12) 0 0;
      border: 1px solid #ffe8aa;
      background: #521111;
      padding: px2rem(39) px2rem(18) px2rem(39) px2rem(18);
      min-height: 100%;
      flex-grow: 1;
      p {
        color: rgba(255, 240, 130, 0.6);
        font-family: Gilroy;
        font-size: px2rem(15);
        font-style: normal;
        font-weight: 500;
        line-height: 128%; /* 19.2px */
        margin-bottom: px2rem(16);
      }
    }
  }
}
</style>
