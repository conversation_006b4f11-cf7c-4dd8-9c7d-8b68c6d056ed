@import "config/dimens";

.rtl-html {
  input,
  textarea {
    direction: rtl;
    text-align: right;
  }

  .rotate-icon {
    transform: rotateY(180deg);
  }

  .header-bar {
    .header-bar-fixed {
      .icon-back {
        left: unset !important;
        right: $gap16;
      }

      .right-icon {
        right: unset !important;
        left: $gap16;
      }

      .header-content {
        .title {
          text-align: right;
        }
      }
    }
  }

  .page-level {
    .level-img {
      right: unset !important;
      left: $gap16;
    }

    .progress-wrap {
      .progress {
        left: unset !important;
        right: 0;
      }
    }

    .rule-wrap {
      .title::before {
        left: unset !important;
        right: 0;
        transform: rotateY(180deg);
      }
    }
  }

  .page-guild-home {
    .page-bg {
      .header-img {
        right: unset !important;
        left: 0;
        transform: rotateY(180deg);
      }
    }

    .invite-button {
      margin-right: auto;
      margin-left: 0 !important;
    }

    .tab-list {
      .tab {
        &:not(:last-child) {
          margin-left: $gap8;
          margin-right: 0;
        }
      }
    }

    .member-list {
      .cell-right {
        margin-right: auto;
        margin-left: 0 !important;
      }
    }
  }

  .page-anchor-week {
    .card-wrap .card-title-wrap .title-img {
      right: unset !important;
      left: 0;
      transform: rotateY(180deg);
    }

    header .rule {
      right: unset !important;
      left: 0;
      padding: $gap4 $gap12 $gap4 $gap8 !important;
      border-radius: 0 $radius12 $radius12 0 !important;
    }
  }

  .coiner.explain-text {
    text-align: right !important;
  }

  //任务中心规则
  .page-task-center-rule {
    .img-box {
      right: unset !important;
      left: px2rem(16);
    }
  }

  //收益中心
  .page-income {
    .tabs-wrap .tab:first-child {
      margin-right: unset !important;
      margin-left: $gap20;
    }
  }

  // 常驻榜单
  .daily-ranking {
    .cell-wrap {
      border-left: 0.5px solid #ffffff1f !important;
      &:first-child {
        border-right: none !important;
      }
    }
    .front-dot,
    .back-dot {
      transform: rotate(180deg);
    }
  }
  .policy-card .card-content .card-content-top .card-item-gray1::after  {
    right: px2rem(110) !important;
  }
  .policy-card .party-content .party-content-item:first-of-type::after {
      right: px2rem(178) !important;
  }
  .opacity-48 {
    background-position: left center !important; 
  }
  .icon-arrow-right {
    transform: rotateY(180deg);
  }
  .card-item-gray1 {
    border-top-right-radius: $radius12;
    border-bottom-right-radius: $radius12; 
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
  }
  .card-item-gray2 {
    border-top-left-radius: $radius12;
    border-bottom-left-radius: $radius12;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important; 
  }
  .progress-text {
    right: px2rem(8);
    left: unset!important;
  }
  .van-field__left-icon {
    margin-left: px2rem(4);
  }
  // 政策表格的圆角
  .pop table th:first-child {
    border-top-left-radius: 0 !important;
    border-top-right-radius: px2rem(12);
  }
  .pop table th:last-child {
    border-top-right-radius: 0!important;
    border-top-left-radius: px2rem(12);
  }
  .pop table tr:last-child td:first-child {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: px2rem(12);
  }
  .pop table tr:last-child td:last-child {
    border-bottom-right-radius: 0 !important;
    border-bottom-left-radius: px2rem(12);
  }
  .tab-header .tab-name {
    margin-left: px2rem(20);
    margin-right: 0!important;
  }
  .sticky-search .van-search__content {
    padding-left: 0!important;
  }
  .count-down-wrap {
    padding-right: 0 !important;
    padding-left: $gap8;
    .date {
      border-radius: 0 px2rem(20) px2rem(20) 0 !important;
    }
  }
  .lucky-gift-ranking .ranking-content .rank-content .rank-content1 .tab .tab-item {
    &:first-child {
      transform: scaleX(-1); 
      &>p {
        transform: scaleX(-1);
      }
    }
    &:last-child {
      transform: scaleX(1) !important; 
      &>p {
        transform: scaleX(1) !important;
      }
    }
  }
  .count-down-wrap {
    padding-right: 0 !important;
    padding-left: $gap8;
    .date {
      border-radius: 0 px2rem(20) px2rem(20) 0 !important;
    }
  }
  .rank-top-content .rank-top-item .username {
    transform: translateX(50%) !important;
    right: 50%;
    left: unset !important;
  }
}
