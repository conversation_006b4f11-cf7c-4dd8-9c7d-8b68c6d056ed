<template>
  <div class="app-container flex">
    <header>
      <header-bar :showTitle="false" :contentPadding="false"> </header-bar>
      <van-search
        v-model="currentUser.account"
        show-action
        ref="searchRef"
        inputmode="search"
        confirm-button
        clearable
        class="input-container"
        autofocus
        :placeholder="$t('dealer_coin.coin_dealer_search_placeholder')"
        :action-text="$t('common.common_cancel')"
        @touchstart.stop="show = true"
        @search="search"
      >
        <template #action>
          <span @click="cancel">{{ $t("common.common_cancel") }}</span>
          <!-- <span class="analog-text" ref="inputTextRef">{{
            currentUser.account
          }}</span> -->
        </template>
      </van-search>
      <van-number-keyboard
        v-model="currentUser.account"
        theme="custom"
        :close-button-text="$t('common.common_confirm')"
        :extra-key="[' ']"
        :show="false"
        @close="search"
      >
      </van-number-keyboard>
    </header>
    <main>
      <template v-if="list.length > 0">
        <user-card
          v-for="item in list"
          :key="item.id"
          :data="{ ...item.user_info }"
          @click="confirm(item.user_info, 1)"
        ></user-card
      ></template>
      <Empty v-else class="empty" :tips="emptyTip"></Empty>
    </main>
  </div>
</template>

<script setup>
import { nextTick, onMounted, ref, unref, watch } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter, useRoute } from "vue-router";
import dealerApi from "@/api/dealer.js";
import UserCard from "./components/UserCard.vue";

const t = i18n.global.t;
const router = useRouter();
const route = useRoute();
const currentUser = ref({});
const show = ref(true);
const inputTextRef = ref(null);
const searchRef = ref(null);
const inputWidth = ref(0);
const emptyTip = ref("");
const list = ref([]);
const search = async (isCloseKeyboard = true) => {
  if (!unref(currentUser).account) return;
  const resResult = await dealerApi.transfer_userinfo({
    account: unref(currentUser).account,
  });
  if (resResult.code === 200) {
    list.value = resResult.data.list;
    if (resResult.data.list.length === 0)
      emptyTip.value = t("dealer_coin.coin_dealer_no_marching_result");
    if (resResult.data.list.length > 0 && isCloseKeyboard) show.value = false;
  }
};

const confirm = ({ account, avatar, nickname }, isPopupTransfer = false) => {
  currentUser.value = {
    account,
    avatar,
    nickname,
  };
  if (isPopupTransfer) localStorage.setItem("isCheck", 1);
  router.replace({
    name: "DealerCoin",
    query: unref(currentUser.value),
  });
};

const cancel = () => {
  router.replace({
    name: "DealerCoin",
  });
};

const onRouteQuery = () => {
  if (route.query?.account) {
    currentUser.value = route.query;
    search(false);
  }
};

function updateCursorPosition() {
  if (!inputTextRef?.value) return false;
  nextTick(() => {
    inputWidth.value = inputTextRef.value.offsetWidth;
  });
}

watch(
  () => currentUser.value.account,
  () => {
    updateCursorPosition();
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  nextTick(() => {
    searchRef.value.focus();
  });
  onRouteQuery();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: $white;
  flex-direction: column;
  header {
    width: 100%;
  }
  main {
    width: 100%;
    padding: 0 px2rem(16);
  }
}

.analog-text {
  position: absolute;
  z-index: -1000;
  margin-left: -100000px;
}
// ::v-deep(.input-container .van-field__body::after) {
//   content: "";
//   position: absolute;
//   top: 50%;
//   left: v-bind("inputWidth + 'px'");
//   width: 2px;
//   height: 16px;
//   background-color: $fontColorB1;
//   transform: translateY(-50%);
//   animation: cursorBlink 1s steps(1) infinite;
//   display: v-bind("show ? 'block' : 'none'");
// }

// @keyframes cursorBlink {
//   0% {
//     opacity: 1;
//   }

//   50% {
//     opacity: 0;
//   }

//   100% {
//     opacity: 1;
//   }
// }
::v-deep(.van-search) {
  padding-left: px2rem(16);
  padding-right: px2rem(16);
}
::v-deep(.van-search__action) {
  padding: 0 px2rem(16) 0 px2rem(8);
}
::v-deep(.van-search__content) {
  border-radius: px2rem(12);
  background: $bgColorB6;
  .van-search__field {
    height: px2rem(44);
  }
}
</style>
