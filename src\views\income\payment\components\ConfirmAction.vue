<template>
  <van-action-sheet v-bind="$attrs" :close-on-click-overlay="false">
    <div class="content-wrap flex">
      <div class="line"></div>
      <div class="title">{{ $t("income.withdraw_confirm_payment") }}</div>
      <div class="desc">
        {{ $t("income.withdraw_confirm_payment_desc") }}
      </div>
      <van-button type="primary" block @click="confirm">
        {{ $t("income.withdraw_confirm_payment") }}
      </van-button>
      <div class="text-btn" @click="cancel">
        {{ $t("common.common_cancel") }}
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup lang="ts">
import { onMounted } from "vue";

const emit = defineEmits(["update:show", "confirm"]);

const cancel = () => {
  emit("update:show", false);
};

const confirm = () => {
  emit("confirm");
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
@import "../assets/payment.scss";

.content-wrap {
  flex-direction: column;
  align-items: center;
  padding: px2rem(10) px2rem(16) $iphoneXBottomHeight;
  text-align: center;
  .line {
    background-color: #c5c5c7;
    width: px2rem(36);
    height: px2rem(5);
    border-radius: px2rem(5);
  }
  .title {
    font-size: px2rem(20);
    color: $fontColorB0;
    margin: px2rem(42) 0 px2rem(8);
  }
  .desc {
    color: $fontColorB3;
    font-size: $fontSize13;
    margin: px2rem(10) 0 px2rem(48);
  }
  .text-btn {
    color: $fontColorB3;
    font-size: $fontSize17;
    padding: px2rem(14) 0;
    margin-top: px2rem(4);
  }
}
</style>
