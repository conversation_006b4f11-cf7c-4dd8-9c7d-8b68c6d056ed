<template>
  <div class="custom-tabs">
    <div class="tabs-nav" ref="navRef">
      <div
        v-for="(tab, index) in tabs"
        :key="tab.name"
        class="tab-item"
        :class="{ active: activeTab === index }"
        @click="handleTabClick(index)"
        v-track:click
        :trace-key="tab.traceKey"
      >
        {{ tab.title }}
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  getCurrentInstance,
} from "vue";
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;

const props = defineProps({
  initialTab: {
    type: Number,
    default: 0,
  },
  tabItems: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["tab-change"]);

const activeTab = ref(props.initialTab);
const navRef = ref(null);
const tabWidths = ref([]);
const tabOffsets = ref([]);

const tabs = ref(props.tabItems.length > 0 ? props.tabItems : []);

const handleTabClick = (index) => {
  activeTab.value = index;
  emit("tab-change", {
    index,
    tab: tabs.value[index],
  });
};

watch(
  () => props.initialTab,
  (newValue) => {
    activeTab.value = newValue;
  }
);

watch(
  () => props.tabItems,
  (newItems) => {
    if (newItems.length > 0) {
      tabs.value = newItems;
    }
  },
  { deep: true }
);
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.custom-tabs {
  width: 100%;
  overflow-x: auto;
  .tabs-nav {
    position: relative;
    display: flex;
    height: px2rem(40);
    background-color: #fff;

    .tab-item {
      flex: 1;
      display: flex;
      flex-shrink: 0;
      flex-wrap: nowrap;
      white-space: nowrap;
      align-items: center;
      justify-content: center;
      color: #999;

      color: #666;
      /* T15/R */
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 500;
      line-height: 128%; /* 19.2px */

      &.active {
        color: #000;
        font-weight: 700;
      }
    }

    .tab-line {
      position: absolute;
      bottom: 0;
      left: 0;
      height: px2rem(3);
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      &::after {
        content: "";
        height: px2rem(3);
        border-radius: px2rem(3);
        width: 50%;
        background-color: #000;
      }
    }
  }

  .tabs-content {
    position: relative;

    .tab-pane {
      display: none;

      &.active {
        display: block;
      }
    }
  }
}
</style>
