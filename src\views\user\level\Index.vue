<template>
  <div
    class="app-container flex page-level"
    :class="{ wealth: currentTabIdx === 0, charm: currentTabIdx === 1, monthly: currentTabIdx === 2 }"
  >
    <header-bar font-color="#fff" close-type="close">
      <div :class="{'tab-wrap':true, 'flex':true, tab2:tabList.length==2,tab3:tabList.length==3}" ref="tabContainer">
        <div
          class="tab"
          :class="{
            active: currentTabIdx === idx,
            tab0: idx === 0,
            tab1: idx === 1,
            tab2: idx === 2
          }"
          v-for="(tab, idx) in tabList"
          :key="idx"
          @click.stop="onChangeTab(idx)" 
          :ref="el => tabRefs[idx] = el"
        >
          {{ tab }}
          <div
            :class="{
              'icon-active-line': true,
              'charm-active': idx === 1,
              'wealth-active': idx === 0,
              'monthly-active': idx === 2,
            }"
          ></div>
        </div>
      </div>
    </header-bar>
    <swiper
      class="main"
      :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
      :modules="modules"
      slides-per-view="auto"
      :space-between="0"
      :navigation="navigation"
      :resistanceRatio="0"
      :auto-height="true"
      @swiper="setControlledSwiper"
      @slideChange="onSlideChange"
    >
      <swiper-slide
        class="swipe-item"
        v-for="(n, idx) in pageConfig"
        :key="idx"
      >
        <div class="level-card">
          <div class="level-bg rotate-icon"></div>
          <img
            class="level-img"
            v-if="n.user.level_icon"
            :src="n.user.level_icon"
            ref="observerRef"
            alt=""
            :hook:mounted="webResTrack()"
            @load="onImageLoad"
          />
          <template v-if="!n.user.level_icon">
            <div class="level-name" v-if="n.user.level !== undefined && n.user.level !== null">
              Lv.{{ n.user.level }}
            </div>
          </template>
          <div class="level-container">
            <div :class="{'user-info':true, 'flex':true, mt4:idx == 2}">
              <avatar
                class="avatar"
                :url="n.user.avatar"
                :size="48"
                border-color="#FFFFFFE0"
              />
              <gap :gap="8"></gap>
              <div class="user-level">
                <div class="level-num gradient-text" v-if="idx == 2">{{ n.user.nickname }}</div>
                <div class="level-num gradient-text" v-else>Lv.{{ n.user.level }}</div>
                <div class="level-text" v-if="idx == 2">
                  <div class="level-text">
                    <i18n-t
                      class="progress-text"
                      keypath="base_total.setting_price_charm_value"
                      tag="div"
                    >
                      <span>{{ n.user.exp }}</span>
                    </i18n-t>
                  </div>
                </div>
                <div class="level-text" v-else>
                  {{ $t("base_total.user_level_current_level") }}
                </div>
              </div>
            </div>
            <div class="level-progress-wrap">
              <i18n-t
                class="progress-text"
                :class="{ show: n.user.lastNextLevelExp !== -1 }"
                :keypath="`base_total.user_level_${apiNameMap[idx]}_progress_text`"
                tag="div"
                v-if="idx != 2"
              >
                <span>{{ n.user.lastNextLevelExp }}</span>
              </i18n-t>
              <div class="progress-text" :class="{ show: n.user.progress < 100 }" v-else v-html="updatetoLevel('user_level_monthly_progress_text')">
              </div>
              <div class="progress-wrap">
                <div
                  class="progress"
                  :style="{ width: `${n.user.progress}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="rule-wrap" v-if="idx!=2">
          <div class="rule-bg"></div>
          <div class="rule-cell">
            <div class="title">
              {{ $t(`base_total.user_level_${apiNameMap[idx]}_rule1_title`) }}
            </div>
            <div class="desc">
              {{ $t(`base_total.user_level_${apiNameMap[idx]}_rule1_desc`) }}
            </div>
          </div>
          <div class="line"></div>
          <div class="rule-cell">
            <div class="title">
              {{ $t(`base_total.user_level_${apiNameMap[idx]}_rule2_title`) }}
            </div>
            <div>
              <div
                class="box flex"
                v-for="(box, idx) in n.config.actions"
                :key="idx"
              >
                <img class="box-icon" :src="box.icon" alt="" />
                <gap :gap="8"></gap>
                <div class="box-right">
                  <div class="box-title">{{ box.action }}</div>
                  <div class="box-desc">{{ box.desc }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="note-box" v-if="currentTabIdx == 0">
            <div class="note-title">
              <img
                class="note-icon"
                src="@/assets/images/level/note.png"
                alt=""
              />
              <span>{{ $t("income.msg_exchange_success_title") }}</span>
            </div>
            <div class="note-content">
              {{ contentVal }}
            </div>
          </div>
          <div class="line"></div>
          <div class="rule-cell">
            <div class="title">
              {{ $t(`base_total.user_level_${apiNameMap[idx]}_rule3_title`) }}
            </div>
            <div class="desc">
              {{ $t(`base_total.user_level_${apiNameMap[idx]}_rule3_desc`) }}
            </div>
            <div class="table">
              <div class="table-title flex">
                <div
                  v-for="(title, idx) in $tm(
                    'base_total.user_level_table_title'
                  )"
                  :key="idx"
                >
                  {{ title }}
                </div>
              </div>
              <div v-if="n.config.level_icons">
                <div
                  class="table-cell flex"
                  v-for="(cell, idx) in n.config.level_icons"
                  :key="idx"
                >
                  <div>{{ cell.min_level }} ~ {{ cell.max_level }}</div>
                  <div>
                    <div class="level-icon-wrap">
                      <img class="level-icon-img" :src="cell.icon" alt="" />
                      <span>{{ cell.min_level }}</span>
                    </div>
                  </div>
                  <div>
                    <img class="cell-icon2" :src="cell.small_icon" alt="" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rule-wrap" v-else>
          <div class="rule-bg"></div>
          <div class="rule-cell">
            <div class="title">
              {{ $t("base_total.setting_price_charm_rules") }}
            </div>
            <div class="rule-sub-title">
              {{ $t("base_total.setting_price_upgrade_howto") }}
            </div>
            <div class="desc mt4">
              {{ $t(`base_total.setting_price_charm_receive`) }}
            </div>
            <div class="rule-sub-title">
              {{ $t("base_total.setting_price_update_monthly") }}
            </div>
            <div class="desc mt4">
              {{ $t(`base_total.setting_price_update_tip`) }}
            </div>
          </div>
          <div class="line"></div>
          <div class="rule-cell">
            <div class="title">
              {{ $t(`base_total.setting_price_level_benefits`) }}
            </div>
            <div class="table">
              <div class="table-title1 flex">
                <div> {{ $t(`base_total.setting_price_level`) }} </div>
                <div> {{ $t(`base_total.setting_price_level_target`) }} </div>
                <div v-html="$t(`base_total.setting_price_max_setting`)"></div>
              </div>
              <div
                class="table-cell1 flex"
                v-for="(cell, idx) in n.config"
                :key="idx"
              >
                <div>{{ cell.level }}</div>
                <div>{{ cell.targetExp || '-' }}</div>
                <div>
                  <div class="chat-icon-wrap">
                    <svg-icon icon="chat" :size="22"></svg-icon>
                    <gap :gap="2" />
                    <span>{{ cell.servicePrices?.chatPrice }}/msg</span>
                  </div>
                    <div class="chat-icon-wrap">
                    <svg-icon icon="video" :size="22"></svg-icon>
                    <gap :gap="2" />
                    <span>{{ cell.servicePrices?.videoPrice }}/{{ $t('common.common_mins') }}</span>
                  </div>
                    <div class="chat-icon-wrap">
                    <svg-icon icon="phone" :size="22"></svg-icon>
                    <gap :gap="2" />
                    <span>{{ cell.servicePrices?.voicePrice }}/{{ $t('common.common_mins') }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { onMounted, ref, nextTick } from "vue";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Controller, Navigation } from "swiper/modules";
import levelApi from "@/api/level.js";
import { i18n } from "@/i18n/index.js";
import { useRoute } from "vue-router";
import ta from "@/utils/thinkingdata";

const t = i18n.global.t;
const route = useRoute();

const apiNameMap = {
  0: "wealth",
  1: "charm",
  2: "monthly"
};
const tabRefs = ref([]);
const tabContainer = ref(null);

const contentVal = ref("");
const tabList = ref([
  t("base_total.user_level_tabList_wealth"),
  t("base_total.user_level_tabList_charming"),
]);
const currentTabIdx = ref(null);
const navigation = ref({
  prevEl: ".tab0",
  nextEl: ".tab1",
  nextEl: ".tab2",
});
const modules = [Navigation, Controller];
const controlledSwiper = ref(null);

const showLevelName = ref(false);
const imageLoaded = ref(false);

// 添加图片加载处理函数
const onImageLoad = () => {
  imageLoaded.value = true;
  showLevelName.value = false; // 图片加载成功，不显示level-name
};
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === currentTabIdx.value) return;
  onChangeSwipe(idx);
};
const onChangeSwipe = async (n) => {
  currentTabIdx.value = n;
  await getConfig();
  await nextTick();
  if (tabRefs.value[n] && tabContainer.value) {
    const tabElement = tabRefs.value[n];
    const container = tabContainer.value;
    // 计算居中滚动位置
    const containerWidth = container.clientWidth;
    const tabLeft = tabElement.offsetLeft;
    const tabWidth = tabElement.clientWidth;
    // 计算滚动位置 (tab中心点 - 容器中心点)
    const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);
    container.scrollTo({
      left: scrollPosition,
      behavior: 'smooth'
    });
  }
};
const onChangeTab = async (idx) => {
   if (controlledSwiper.value) {
    controlledSwiper.value.slideTo(idx);
    currentTabIdx.value = idx;
    await nextTick();
    if (tabRefs.value[idx] && tabContainer.value) {
      tabRefs.value[idx].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center'
      });
    }
  }
};

const pageConfig = ref({
  0: {
    config: {},
    user: {},
  },
  1: {
    config: {},
    user: {},
  },
});
const setLevelProgress = (userInfo) => {
  let lastNextLevelExp;
  if (userInfo.next_exp === -1) lastNextLevelExp = -1;
  else lastNextLevelExp = userInfo.next_exp - (userInfo.exp || 0);

  let progress;
  if (lastNextLevelExp === -1) progress = 100;
  else progress = (1 - lastNextLevelExp / userInfo.next_exp) * 100;

  userInfo.lastNextLevelExp = lastNextLevelExp;
  userInfo.progress = progress;
  return userInfo;
};
const getConfig = async () => {
  if (pageConfig.value[currentTabIdx.value]?.user?.avatar) return;
  const response = await levelApi[apiNameMap[currentTabIdx.value]]();
  if (response.code === 200) {
    if(currentTabIdx.value === 2) {
      pageConfig.value[2] = {
        config: pageConfig.value[2]?.config || [],
        user: {
          avatar: response.data.avatar,
          nickname: response.data.nickname,
          level: response.data.level,
          next_exp: response.data.next_exp,
          exp: response.data.exp,
        },
      };
      
      pageConfig.value[2].user = {
        ...setLevelProgress(pageConfig.value[2].user),
      }
    } else {
      // 判断是否是初次进入第一个tab且没有level_icon
      if (currentTabIdx.value === 0 && !response.data.user.level_icon) {
        showLevelName.value = true;
      }
      pageConfig.value[currentTabIdx.value] = {
        config: response.data,
        user: {
          ...setLevelProgress(response.data.user),
        },
      };
    }
  }
};
const updatetoLevel = (key) => {
  const str = t(`base_total.${key}`);
  const currentLevel = pageConfig.value[2]?.user?.level || 0;
  const nextLevel = currentLevel + 1;
  const neededExp = pageConfig.value[2]?.user?.lastNextLevelExp || 0;
  
  let result = str;
  let firstReplacementDone = false;
  
  result = result.replace(/__x__/g, (match) => {
    if (!firstReplacementDone) {
      firstReplacementDone = true;
      return `<span style="font-weight: bold; font-size: 15px;">${neededExp}</span>`;
    } else {
      return `<span>Lv.${nextLevel}</span>`;
    }
  });
  
  return result;
}
const getMonthTable = async () => {
  const response = await levelApi.getMonthlyTable();
   if (response.code === 200) {
       pageConfig.value = {
      ...pageConfig.value,
      2: {
        ...pageConfig.value[2],
        config: response.data.items
      }
    };
    }
};
const getNoteContent = async () => {
  const response = await levelApi.getNote();
  if (response.code === 200) {
    contentVal.value = response.data;
  }
};

const webResTrack = () => {
  nextTick(() => {
    const observer = new IntersectionObserver(
      function (entries, observer) {
        const enterTime = localStorage.getItem("enter_time");
        const now = Date.now();
        const diff = now - parseInt(enterTime);
        ta.track("domain_response", {
          interface_type: 5,
          domain: window.location.host,
          response_time: diff,
          is_success: true,
        });
      },
      {
        rootMargin: "10px",
        threshold: 1,
      }
    );
    const observerEl = document.querySelector(".level-card .level-img");
    observer.observe(observerEl);
  });
};
const getUserInfo = async () => {
  const response = await levelApi.getUserInfo();
  // audit为true 第三个tab不展示   audit为false时只有当gender为11才展示第三个tab
  if (!response.data.audit) {
    tabList.value = [
      t("base_total.user_level_tabList_wealth"),
      t("base_total.user_level_tabList_charming"),
      ...(response.data.gender === 11 ? [t("base_total.setting_price_charm_level")] : [])
    ];
  }
  if(tabList.value.length > 2) {
    pageConfig.value = {
      0: {
        config: {},
        user: {},
      },
      1: {
        config: {},
        user: {},
      },
      2: {
        config: [],
        user: {},
      },
    };
    getMonthTable()
  } else {
    navigation.value = {
      prevEl: ".tab0",
      nextEl: ".tab1",
    };
  }
  
};

onMounted(async () => {
  await getUserInfo();
  // type:  0 财富   1 魅力
  const type = Number(route.query.type) || 0;
  currentTabIdx.value = type;
  await getConfig();
  // 如果是第三个tab，直接跳转而不需要滑动效果
  if (type === 2) {
    await getConfig();
    await nextTick();
    // 直接设置swiper到指定slide，不使用过渡动画
    if (controlledSwiper.value) {
      controlledSwiper.value.slideTo(type, 0); 
    }
    // 处理tab标签的定位
    if (tabRefs.value[type] && tabContainer.value) {
      const tabElement = tabRefs.value[type];
      const container = tabContainer.value;
      // 计算居中滚动位置
      const containerWidth = container.clientWidth;
      const tabLeft = tabElement.offsetLeft;
      const tabWidth = tabElement.clientWidth;
      // 计算滚动位置 (tab中心点 - 容器中心点)
      const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);
      // 直接设置滚动位置，不使用平滑滚动
      container.scrollTo({
        left: scrollPosition
      });
    }
  } else {
    // 其他情况不变
    onSlideChange({ activeIndex: type });
    onChangeTab(type);
  }
  getNoteContent();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

$pageBgColor: #161723;

.app-container {
  flex-direction: column;
  height: 100vh;

  &.wealth {
    background: url("@/assets/images/level/wealth/bg.svg") top/contain no-repeat,
      $pageBgColor;
  }

  &.charm {
    background: url("@/assets/images/level/charm/bg.svg") top/contain no-repeat,
      $pageBgColor;
  }

  &.monthly {
    background: url("@/assets/images/level/monthly/bg.png") top/contain no-repeat,
      $pageBgColor;
  }
}

.main {
  flex: 1;
  width: 100vw;
  overflow-y: scroll;
  padding: $gap16 0;
}

::v-deep(.header-bar .header-bar-fixed .header-content) {
  padding-right: px2rem(16);
}
:deep(.bold) {
  color: #FF4771;
  font-size: px2rem(13);
  font-weight: 700;
}

.tab-wrap {
  justify-content: space-between;
  height: 100%;
  padding: 0 px2rem(50);
  display: flex;
  flex-wrap: nowrap;
  overflow-x: scroll;
  overflow-y: hidden;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  &.tab2 {
    padding: 0 px2rem(50);
  }
  &.tab3 {
    padding: 0 px2rem(6);
  }
  
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;  // IE 和 Edge
  scrollbar-width: none;  // Firefox

  .tab {
    position: relative;
    height: 100%;
    font-size: px2rem(19);
    font-weight: $fontWeightBold;
    color: $fontColorB3;
    white-space: nowrap;
    margin-right: px2rem(12);
    pointer-events: auto;

    &::after {
      content: '';
      position: absolute;
      top: -10px;
      bottom: -10px;
      left: -10px;
      right: -10px;
      z-index: 1;
    }

    @include centerV;

    &.active {
      color: $white;

      .icon-active-line {
        position: absolute;
        left: 50%;
        bottom: px2rem(-2);
        width: px2rem(34);
        height: px2rem(30);
        transform: translateX(-50%);
        &.wealth-active {
          background: url("@/assets/images/level/wealth/tab-active.svg") 100% /
            cover no-repeat;
          z-index: -1;
        }
        &.charm-active {
          background: url("@/assets/images/level/charm/tab-active.svg") 100% /
            cover no-repeat;
          z-index: -1;
        }
        &.monthly-active {
            background: url("@/assets/images/level/monthly/tab-active.svg") 100% /
            cover no-repeat;
          z-index: -1;
        }
      }
    }
  }
}

.swipe-item {
  &:nth-child(1) {
    .icon-active-line {
      background: url("@/assets/images/level/wealth/tab-active.svg") 100% /
        cover no-repeat;
    }

    .level-card {
      .level-bg {
        border-image-source: url("@/assets/images/level/wealth/card-bg.png");
      }

      .level-num {
        background-image: linear-gradient(118.75deg, #638dff 0%, #206aca 100%);
      }

      .level-text {
        color: #3985e8;
      }
    }

    .level-progress-wrap {
      .progress-text {
        color: #206aca;
      }

      .progress {
        background-color: #206aca;
      }
    }

    .rule-wrap {
      .title {
        &::before {
          background: url("@/assets/images/level/wealth/star.svg") 100% / cover
            no-repeat;
        }
      }
    }
  }

  &:nth-child(2) {
    .icon-active-line {
      background: url("@/assets/images/level/charm/tab-active.svg") 100% / cover
        no-repeat;
    }

    .level-card {
      .level-bg {
        border-image-source: url("@/assets/images/level/charm/card-bg.png");
      }

      .level-num {
        background-image: linear-gradient(118.75deg, #ff74d3 0%, #ec29b8 100%);
      }

      .level-text {
        color: #ff5bd1;
      }
    }

    .level-progress-wrap {
      .progress-text {
        color: #f638c1;
      }

      .progress {
        background-color: #f638c1;
      }
    }

    .rule-wrap {
      .title {
        &::before {
          background: url("@/assets/images/level/charm/star.svg") 100% / cover
            no-repeat;
        }
      }
    }
  }
   &:nth-child(3) {
    .icon-active-line {
      background: url("@/assets/images/level/monthly/tab-active.svg") 100% / cover
        no-repeat;
    }

    .level-card {
      .level-bg {
        border-image-source: url("@/assets/images/level/monthly/card-bg.png");
      }

      .level-num {
        font-size: 20px;
        font-weight: 900;
        line-height: normal;
        background: linear-gradient(149deg, #C187FF 8.36%, #8829EC 58.53%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding-right: px2rem(4);
      }

      .level-text {
        color: #AA5BFF;
      }
    }

    .level-progress-wrap {
      .progress-text {
        color: #9438F6;
      }

      .progress {
        background-color: #9438F6;
      }
    }

    .rule-wrap {
      .title {
        &::before {
          background: url("@/assets/images/level/monthly/star.svg") 100% / cover
            no-repeat;
        }
      }
    }
  }
}

.level-card {
  position: relative;
  width: px2rem(390);
  min-height: px2rem(234);
  padding: px2rem(15) $gap32 px2rem(110);
  margin: 0 auto;

  .level-bg {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    border-image-slice: 239 0 210 0 fill;
    border-image-width: px2rem(145) 0 px2rem(92) 0;
    border-image-outset: 0 0 0 0;
    border-image-repeat: stretch stretch;
    border-style: solid;
  }

  .level-container {
    position: relative;
  }

  .level-img {
    position: absolute;
    top: px2rem(-24);
    right: $gap16;
    width: px2rem(112);
    height: px2rem(112);
  }
  .level-name {
    position: absolute;
    top: px2rem(24);
    right: px2rem(28);
    background: linear-gradient(119deg, #B774FF 0%, #8829EC 100%);
    background-clip: text;
    text-align: right;
    font-family: Gilroy;
    font-size: px2rem(32);
    font-weight: $fontWeightHeavy;
    font-weight: 900;
    line-height: 124%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding-right: px2rem(4);
  }

  .user-info {
    align-items: center;
    margin-bottom: px2rem(16);

    .level-num {
      padding: 0 px2rem(1);
      font-size: px2rem(32);
      font-weight: $fontWeightHeavy;
      //font-style: italic;
      line-height: px2rem(40);
    }

    .level-text {
      padding: 0 px2rem(1);
      font-size: px2rem(11);
      line-height: px2rem(13);
    }
  }

  .level-progress-wrap {
    .progress-text {
      min-height: px2rem(18);
      margin-bottom: px2rem(8);
      font-size: px2rem(13);
      opacity: 0;

      &.show {
        opacity: 1;
      }

      span {
        margin: 0 px2rem(4);
        font-size: px2rem(15);
        font-weight: $fontWeightBold;
      }
    }

    .progress-wrap {
      position: relative;
      overflow: hidden;
      height: px2rem(6);
      border-radius: px2rem(100);
      background-color: rgba($white, 0.32);

      .progress {
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        border-radius: px2rem(100);
      }
    }
  }
}

.rule-wrap {
  position: relative;
  margin-top: px2rem(-62);
  padding: 0 $gap16 1px;

  .rule-bg {
    position: absolute;
    top: px2rem(62);
    left: 0;
    bottom: 0;
    width: 100vw;
    background-color: $pageBgColor;
  }

  .rule-cell {
    position: relative;
  }
  .rule-sub-title {
    color: #ccc;
    font-size: px2rem(15);
    font-weight: 700;
    margin-top: px2rem(12);
  }
  .title {
    position: relative;
    padding: px2rem(7) px2rem(16) 0;
    font-size: px2rem(17);
    font-weight: $fontWeightBold;
    color: $white;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: px2rem(24);
      height: px2rem(24);
    }
  }

  .desc {
    margin-top: px2rem(12);
    font-size: px2rem(13);
    line-height: px2rem(16);
    color: $fontColorB3;
  }
  .mt4 {
    margin-top: px2rem(4);
  }

  .line {
    position: relative;
    margin: $gap24 0;
    border-bottom: 0.5px solid #ffffff14;
  }

  .box {
    flex-wrap: nowrap;

    &:first-child {
      margin-top: px2rem(20);
    }

    &:not(:last-child) {
      margin-bottom: $gap24;
    }

    .box-icon {
      width: px2rem(48);
      height: px2rem(48);
    }

    .box-title {
      margin-bottom: px2rem(2);
      font-size: px2rem(15);
      font-weight: $fontWeightBold;
      line-height: px2rem(18);
      color: $white;
    }

    .box-desc {
      font-size: px2rem(13);
      line-height: px2rem(16);
      color: $fontColorB3;
    }
  }

  .table {
    margin-top: px2rem(20);
    margin-bottom: px2rem(32);
    font-size: px2rem(13);
    text-align: center;
    color: $white;
    background-color: #282934;
    border: 0.5px solid #282934;
    border-radius: $gap8;
    overflow: hidden;

    .table-title > div {
      height: px2rem(40);
      background-color: #1e1f2c;
    }

    .table-cell > div {
      height: px2rem(40);
      background-color: #21222c;
    }
    .table-cell1> div {
      height: px2rem(80);
      background-color: #21222c;
    }
    .table-title > div,
    .table-cell > div {
      flex: 1;
      margin: 0.5px;

      @include centerV;
    }
    .table-title1 > div {
      height: px2rem(40);
      background-color: #1e1f2c;
      flex: 1;
      margin: 0.5px;

      @include centerV;
      &:nth-child(3) {
        flex: 1.3; 
      }
    }
    .table-cell1 > div {
      flex: 1;
      margin: 0.5px;

      @include centerV;
      display: flex;
      &:nth-child(3) {
        padding-left: px2rem(30);
        align-items: start;
      }
    }
  }
  .chat-icon-wrap {
    display: flex;
    align-items: center;
    padding: px2rem(4);
  }


  .level-icon-wrap {
    position: relative;
    font-size: 0;

    .level-icon-img {
      height: px2rem(15);
      object-fit: contain;
    }

    span {
      position: absolute;
      top: 50%;
      right: px2rem(4);
      min-width: px2rem(10);
      text-align: center;
      font-weight: $fontWeightBold;
      transform: translateY(-50%);

      @include fontSize11;
    }
  }

  .cell-icon2 {
    width: px2rem(28);
    height: px2rem(28);
  }
}
.note-box {
  color: $fontColorB3;
  font-size: $fontSize11;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: px2rem(8);
  position: relative;
  margin-top: px2rem(20);
  padding: px2rem(8) px2rem(10);
  .note-title {
    color: $fontColorB4;
    font-size: $fontSize13;
    display: flex;
    align-items: center;
    margin-bottom: px2rem(2);
    .note-icon {
      width: px2rem(12);
      height: px2rem(12);
      margin-right: px2rem(2);
    }
  }
}
.rtl-html {
  .level-name {
    left: px2rem(28);
    right: unset;
  }
}
html.rtl-html .app-container :deep(.header-bar .header-bar-fixed .header-content) {
  padding-left: px2rem(16);
  padding-right: px2rem(50);
}
.mt4 {
  margin-top: px2rem(4);
}
</style>
