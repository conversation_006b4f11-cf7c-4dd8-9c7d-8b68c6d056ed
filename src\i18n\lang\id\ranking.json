{"ranking_weekly_star_receiving": "<PERSON>tan<PERSON>", "ranking_weekly_star_receiving_desc": "Peringkat Bintang Penerima Hadiah ditentukan berdasarkan jumlah hadiah yang diterima pengguna dari tiga hadiah mingguan bintang berikut:", "ranking_weekly_star_send": "Bintang <PERSON>", "ranking_weekly_star_send_desc": "Peringkat Bintang Pelindung Hadiah ditentukan berdasarkan nilai bintang dari tiga hadiah mingguan berikut yang dikirimkan oleh pengguna. 1 {0} = 1 {1}", "ranking_weekly_star_ranking": "<PERSON><PERSON><PERSON>", "ranking_weekly_star_rewards": "<PERSON><PERSON>", "ranking_weekly_star_rules": "<PERSON><PERSON><PERSON>", "ranking_weekly_star_rewards_title": "<PERSON><PERSON> untuk Bin<PERSON>g <PERSON>", "ranking_weekly_star_rules_title": "<PERSON><PERSON> untuk Bintang Pelindung Hadiah", "ranking_weekly_star_extra_rewards": "<PERSON><PERSON> tambahan untuk Bintang Pelindung Hadiah peringkat TOP1", "ranking_weekly_star_extra_desc": "Bintang Pelindung Hadiah yang meraih peringkat TOP1 akan mendapatkan Lencana Utama Permanen\\*1. Se<PERSON><PERSON> sering meraih TOP1, semakin besar peluang untuk naik ke lencana tingkat lanjut.", "ranking_weekly_star_top_obtained": "<PERSON><PERSON><PERSON>han TOP1", "ranking_weekly_star_badge_rewards": "<PERSON><PERSON>", "ranking_weekly_star_milestone_title": "Hadiah & Aturan", "ranking_weekly_star_milestone_rule1": "1. W<PERSON>tu Aktivitas: <PERSON><PERSON><PERSON> hari <PERSON> pukul 00:00:00 hing<PERSON> pukul 23:59:59 ({0})", "ranking_weekly_star_milestone_rule2": "2. Bintang Penerima Hadiah: Terdapat tiga peringkat penerima hadiah berdasarkan hadiah bintang mingguan. Setiap peringkat dihitung berdasarkan jumlah hadiah bintang mingguan yang diterima pengguna. Pengguna peringkat TOP1–3 akan mendapatkan hadiah, dan hadiah berlian hanya diberikan jika pengguna menerima jumlah hadiah sesuai ketentuan berikut:", "ranking_weekly_star_milestone_rule3": "3. <PERSON>tan<PERSON> Hadiah: Peringkat ini dihitung berdasarkan nilai bintang dari tiga hadiah bintang mingguan yang dikirim oleh pengguna, 1{0} = 1{1}. Pengguna peringkat TOP1–3 akan mendapatkan hadiah, dan pengguna TOP1 akan mendapatkan tambahan hadiah lencana. Semakin sering meraih peringkat TOP1, semakin besar peluang untuk naik ke lencana tingkat lanjut.", "ranking_weekly_star_milestone_rule4": "4. <PERSON><PERSON> aktivitas akan dikirim ke ransel kamu dalam waktu 24 jam setelah siklus mingguan berakhir.", "ranking_weekly_star_milestone_rule5": "5. Hall of Fame: Tiga pengguna TOP1 dari peringkat Bintang Penerima Hadiah mingguan akan ditampilkan di Hall of Fame sebagai bentuk pengakuan atas prestasi mereka!", "ranking_weekly_star_unit_day": "{0} hari", "ranking_500": "500x", "ranking_superbig": "Super Jackpot", "ranking_Lottery": "<PERSON>il Real Time", "ranking_rule": "<PERSON><PERSON><PERSON>", "ranking_rule_list": "1. <PERSON><PERSON> yang Diberikan Tidak Dapat Digunakan untuk Bermain Hadiah <PERSON>beruntungan.\n2. <PERSON><PERSON><PERSON>, <PERSON><PERSON> Secara Acak Memicu Bonus 1-500x, Pengguna yang Memicu Bonus Akan Langsung Menerima Hadiah <PERSON> Bonus.\n3. Semakin Banyak Hadiah yang <PERSON>, <PERSON><PERSON><PERSON>ggi Peluang Memenangkan Hadiah Besar.\n4. Saat Mengirim Had<PERSON>, Pengguna Memiliki Kesempatan untuk Merebut Sebagian dari Kolam Jackpot.\n5. Papan Peringkat Juara Keberuntungan: 10 Pengguna dengan Perolehan Koin Terbanyak Setiap Hari/Minggu Akan Tercantum di Papan Juara Keberuntungan.\n6. <PERSON><PERSON>: 10 Pengguna dengan Pengeluaran Terbesar dalam Permainan Hadiah Keberuntungan Setiap Hari/Minggu Akan Tercantum di Papan Juara Sultan.\n7. Tiga Pengguna dengan Jumlah Hadiah Jackpot Tertinggi Setiap Minggu Akan Tercantum di Papan Pemenang <PERSON> (Papan Mingguan Diperbarui Setiap Senin Pukul 00:00).\n8. Setiap Kali <PERSON>, Host yang Menerima <PERSON>kan Mendapatkan Berlian dengan Persentase Tertentu\n9. Hadiah Keberuntungan meningkatkan kekayaan sebesar 1 setiap 10 koin emas yang dikonsumsi", "ranking_jack_detail": "<PERSON><PERSON><PERSON><PERSON>", "ranking_jack_list": "1. <PERSON><PERSON> Penghitungan Papan Peringkat: Data Dihitung dari <PERSON>in Pukul 00:00 hingga Minggu Pukul 24:00\n2. Waktu Pembaruan Papan Peringkat: Papan Peringkat Mingguan Diperbarui Setiap Senin Pukul 00:00 Waktu Setempat.\n3. <PERSON><PERSON> Papan Peringkat: Tiga Teratas di Setiap Papan Peringkat Selama Periode Akan Mendapatkan Hadiah Border Avatar, yang Akan Diberikan Antara Pukul 00:00 hingga 03:00 <PERSON>ia<PERSON>.", "ranking_bonus": "<PERSON><PERSON>", "ranking_ranking": "<PERSON><PERSON><PERSON>", "ranking_detail": "<PERSON><PERSON><PERSON><PERSON>", "ranking_cycle": "<PERSON><PERSON>", "ranking_cycle_detail": "(1) Papan <PERSON>: Menghitung Data dari <PERSON>ukul 00:00 hingga 24:00 Setiap Hari \n (2) Papan Mingguan: Menghitung Data dari <PERSON>l 00:00 hingga <PERSON>gu Pukul 24:00", "ranking_reward": "<PERSON><PERSON>:", "ranking_reward_detail": "10 Teratas di Papan <PERSON> dan <PERSON> \"<PERSON>n <PERSON>\" dan \"<PERSON><PERSON>\" <PERSON><PERSON>, yang <PERSON> ke Akun dalam Waktu 1-3 Jam Setelah Periode Penghitungan.", "ranking_title1": "<PERSON><PERSON>:", "ranking_title2": "<PERSON><PERSON>:", "ranking_congratulations": "Selamat __NAME__ Mengirimkan __GIFT__ __ICON__ dan <PERSON> __COIN__", "ranking_ranking1": "<PERSON><PERSON>", "ranking_jackpot": "Pemenang Jackpot", "ranking_ou": "Papan <PERSON> Juara Keberuntung<PERSON>", "ranking_tu": "<PERSON><PERSON>", "ranking_day": "<PERSON><PERSON>", "ranking_week": "<PERSON><PERSON>", "ranking_gift_xu": "Posisi Tersedia"}