<template>
  <div class="mb-60 rule-container">
    <div class="mt-27">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="19"
        icon-size="large"
        :title="$t(`singer.rules_activity_time_title`)"
      ></BlockTitle>
    </div>
    <div class="activity-time mt-34">
      <div class="time-cell">
        <div class="rate"></div>
        <gap :gap="37"></gap>
        <div class="time">
          {{ $t(`singer.rules_activity_time_registration`) }}
        </div>
      </div>
      <div class="time-cell">
        <div class="rate"></div>
        <gap :gap="37"></gap>
        <div class="time">
          {{ $t(`singer.rules_activity_time_official_review`) }}
        </div>
      </div>
      <div class="time-cell">
        <div class="rate"></div>
        <gap :gap="37"></gap>
        <div class="time">
          {{ $t(`singer.rules_activity_time_audition_round`) }}
        </div>
      </div>
      <div class="time-cell">
        <div class="rate"></div>
        <gap :gap="37"></gap>
        <div class="time">
          {{ $t(`singer.rules_activity_time_qualifying_round`) }}
        </div>
      </div>
      <div class="time-cell">
        <div class="rate"></div>
        <gap :gap="37"></gap>
        <div class="time">
          {{ $t(`singer.rules_activity_time_final_round`) }}
        </div>
      </div>
    </div>
    <div class="mt-63">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="19"
        icon-size="large"
        :title="$t(`singer.audition_round_title`)"
      ></BlockTitle>
    </div>
    <div class="time-desc mt-6">
      <span>
        {{ formatDate(activityInfo?.stage1_time?.start_time, "M.D") }}
      </span>
      <template
        v-if="
          !isSameDate(
            activityInfo?.stage1_time?.start_time,
            activityInfo?.stage1_time?.end_time
          )
        "
      >
        <span>-</span>
        <span>
          {{ formatDate(activityInfo?.stage1_time?.end_time, "M.D") }}
        </span>
      </template>
    </div>
    <div class="box-text mt-20">
      <div class="text-item">
        <span class="index">1</span>
        <p class="text-content">
          {{ $t(`singer.rules_audition_round_details_items_0`) }}
        </p>
      </div>
      <div class="text-item">
        <span class="index">2</span>
        <p class="text-content">
          {{ $t(`singer.rules_audition_round_details_items_1`) }}
        </p>
      </div>
    </div>
    <div class="mt-58">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="19"
        icon-size="large"
        :title="$t(`singer.qualifying_round_title`)"
      ></BlockTitle>
    </div>
    <div class="time-desc mt-6">
      <span>
        {{ formatDate(activityInfo?.stage3_time?.start_time, "M.D") }}
      </span>
      <template
        v-if="
          !isSameDate(
            activityInfo?.stage3_time?.start_time,
            activityInfo?.stage3_time?.end_time
          )
        "
      >
        <span>-</span>
        <span>
          {{ formatDate(activityInfo?.stage3_time?.end_time, "M.D") }}
        </span>
      </template>
    </div>
    <div class="box-text mt-20">
      <div class="text-item">
        <div class="cell">
          <div class="label">
            {{ $t(`singer.qualifying_round_competition_room_room_id`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            {{ activityInfo?.room_info?.room_no || "-" }}
          </div>
        </div>
        <div class="cell">
          <div class="label">
            {{
              $t(`singer.qualifying_round_competition_room_competition_time`)
            }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            <span>
              {{
                formatDate(
                  activityInfo?.stage3_time?.start_time,
                  "M.D HH:mm:ss"
                )
              }}
            </span>
            <span>-</span>
            <span>
              {{
                formatDate(
                  activityInfo?.stage3_time?.end_time,
                  isSameDate(
                    activityInfo?.stage3_time?.start_time,
                    activityInfo?.stage3_time?.end_time
                  )
                    ? "HH:mm:ss"
                    : "M.D HH:mm:ss"
                )
              }}
            </span>
            <span v-if="activityInfo?.timezone"
              >({{ activityInfo?.timezone }})</span
            >
          </div>
        </div>
      </div>
      <div class="text-item">
        <div class="cell">
          <div class="content">
            {{ $t(`singer.rules_qualifying_round_details_items_1`) }}
          </div>
        </div>
      </div>
      <div class="text-item">
        <div class="cell">
          <div class="label">
            {{ $t(`singer.judges_scores_title`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content mt-6">
            {{ $t(`singer.judges_scores_description`) }}
          </div>
        </div>
        <div class="cell">
          <div class="label">
            {{ $t(`singer.gift_points_title`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content mt-6">
            {{ $t(`singer.gift_points_description`) }}
          </div>
        </div>
        <div class="cell">
          <div class="label">
            {{ $t(`singer.total_score_title`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            {{ $t(`singer.total_score_description`) }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-55">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="19"
        icon-size="large"
        :title="$t(`singer.rules_final_round_details_title`)"
      ></BlockTitle>
    </div>
    <div class="time-desc mt-6">
      <span>
        {{ formatDate(activityInfo?.stage4_time?.start_time, "M.D") }}
      </span>
      <template
        v-if="
          !isSameDate(
            activityInfo?.stage4_time?.start_time,
            activityInfo?.stage4_time?.end_time
          )
        "
      >
        <span>-</span>
        <span>
          {{ formatDate(activityInfo?.stage4_time?.end_time, "M.D") }}
        </span>
      </template>
    </div>
    <div class="box-text mt-20">
      <div class="text-item">
        <div class="cell">
          <div class="label">
            {{ $t(`singer.qualifying_round_competition_room_room_id`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            {{ activityInfo?.room_info?.room_no || "-" }}
          </div>
        </div>
        <div class="cell">
          <div class="label">
            {{
              $t(`singer.qualifying_round_competition_room_competition_time`)
            }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            <span>
              {{
                formatDate(
                  activityInfo?.stage4_time?.start_time,
                  "M.D HH:mm:ss"
                )
              }}
            </span>
            <span>-</span>
            <span>
              {{
                formatDate(
                  activityInfo?.stage4_time?.end_time,
                  isSameDate(
                    activityInfo?.stage4_time?.start_time,
                    activityInfo?.stage4_time?.end_time
                  )
                    ? "HH:mm:ss"
                    : "M.D HH:mm:ss"
                )
              }}
            </span>
            <span v-if="activityInfo?.timezone"
              >({{ activityInfo?.timezone }})</span
            >
          </div>
        </div>
      </div>
      <div class="text-item">
        <div class="cell">
          <div class="content">
            {{ $t(`singer.rules_final_round_details_items_1`) }}
          </div>
        </div>
      </div>
      <div class="text-item">
        <div class="cell">
          <div class="label">
            {{ $t(`singer.rules_final_round_details_items_2`) }}
          </div>
          <gap :gap="7"></gap>
          <div class="content">
            {{ $t(`singer.rules_final_round_details_items_3`) }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-62">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="19"
        icon-size="large"
        :title="$t(`singer.rules_rankings_title`)"
      ></BlockTitle>
    </div>
    <div class="time-desc mt-6">7.12-7.18</div>
    <div class="box-text mt-20">
      <div class="text-item">
        <div class="cell">
          <div class="label">{{ $t(`singer.popularity_ranking_title`) }}</div>
          <gap :gap="7"></gap>
          <div class="content mt-6">
            {{ $t(`singer.rules_rankings_popularity_ranking`) }}
          </div>
        </div>
        <div class="cell">
          <div class="label">{{ $t(`singer.support_ranking_title`) }}</div>
          <gap :gap="7"></gap>
          <div class="content mt-6">
            {{ $t(`singer.rules_rankings_support_ranking`) }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-57">
      <BlockTitle
        :show-line="true"
        :font-size="22"
        :icon-gap="0"
        icon-size="large"
        :title="$t(`singer.rules_reward_distribution_title`)"
      ></BlockTitle>
    </div>
    <div class="box-text mt-20">
      <div class="text-item">
        <span class="index">1</span>
        <p class="text-content">
          {{ $t(`singer.rules_reward_distribution_items_0`) }}
        </p>
      </div>
      <div class="text-item">
        <span class="index">2</span>
        <p class="text-content">
          {{ $t(`singer.rules_reward_distribution_items_1`) }}
        </p>
      </div>
    </div>
  </div>
</template>
<script setup>
import BlockTitle from "./BlockTitle.vue";
import singerApi from "@/api/activity.js";
import { onMounted, ref } from "vue";
import { formatDate } from "@/utils/util";
const activityInfo = ref(null);

const fetchData = async () => {
  const res = await singerApi.singer_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
  }
};

const isSameDate = (date1, date2) => {
  return formatDate(date1, "M.D") === formatDate(date2, "M.D");
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule-container {
  background-image: url("@/assets/images/activity/singer/bg.png"),
    url("@/assets/images/activity/singer/bg.png"),
    url("@/assets/images/activity/singer/bg.png");
  background-size: 100% px2rem(434);
  background-repeat: no-repeat;
  background-position: 0 px2rem(0), 0 px2rem(940), 0 px2rem(1457);
}
.activity-time {
  margin: 0 auto;
  width: px2rem(238);
  .time-cell {
    display: flex;
    align-items: center;
    margin-bottom: px2rem(16);
    .rate {
      width: px2rem(12);
      height: px2rem(12);
      background: url("@/assets/images/activity/singer/ball-light.png")
        no-repeat center/100% 100%;
      flex-shrink: 0;
    }
    .time {
      color: #ede0ff;
      font-family: Gilroy;
      font-size: px2rem(16);
      font-style: normal;
      font-weight: 700;
      line-height: 100%; /* 16px */
    }
    .desc {
      color: #d8bdff;
      font-family: Gilroy;
      font-size: px2rem(16);
      font-style: normal;
      font-weight: 500;
      line-height: 100%; /* 16px */
    }
  }
}

.time-desc {
  color: #d8bdff;
  text-align: center;
  font-family: Gilroy;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 100%; /* 14px */
}

.box-text {
  padding: 0 px2rem(27);
  .text-item {
    margin-bottom: px2rem(14);
    padding: px2rem(10);
    border-radius: px2rem(8);
    border: px2rem(1) solid #a772ea;
    background: linear-gradient(
      180deg,
      rgba(202, 158, 255, 0.22) 0%,
      rgba(136, 57, 237, 0.22) 72.96%
    );
    .index {
      width: px2rem(16);
      height: px2rem(16);
      line-height: px2rem(16);
      border-radius: px2rem(16);
      display: block;
      text-align: center;
      flex-shrink: 0;
      background: linear-gradient(
        180deg,
        #ff9ebb 0%,
        #d433e8 50.2%,
        #6100e8 100%
      );
      color: #fff;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 700;
      float: left;
      margin-top: px2rem(-3);
      margin-right: px2rem(8);
    }
    .text-content {
      color: #d8bdff;
      font-family: Gilroy;
      font-size: px2rem(14);
      font-style: normal;
      font-weight: 500;
      line-height: 100%;
    }

    .cell {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: px2rem(12);
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        display: inline-flex;
        padding: px2rem(4) px2rem(7);
        justify-content: center;
        align-items: center;
        color: #f1d8ff;
        font-family: Gilroy;
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 700;
        line-height: 100%; /* 12px */
        border-radius: px2rem(12);
        background: #9346ff;
      }
      .content {
        color: #d8bdff;
        font-family: Gilroy;
        font-size: px2rem(12);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 12px */
      }
    }
  }
}
.mb-60 {
  margin-bottom: px2rem(60);
}
.rtl-html {
  .text-item {
    .index {
      float: right;
      margin-right: unset;
      margin-left: px2rem(8);
    }
  }
}
@for $i from 1 through 9 {
  $values: 27, 34, 63, 6, 20, 58, 55, 62, 57;
  $mt: nth($values, $i);
  .mt-#{$mt} {
    margin-top: px2rem($mt);
  }
}
</style>
