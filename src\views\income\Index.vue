<template>
  <div
    class="app-container flex page-income"
    v-track:exposure
    trace-key="withdraw_popup_exposure"
  >
    <header>
      <header-bar
        :close-type="closeType"
        :right-icon="iconMenu"
        @emit:right-icon="jumpPage(4)"
      />
      <van-notice-bar
        class="notice-bar"
        v-if="isShowCloseNoticeBar"
        speed="30"
        :class="{ 'rtl-notice-bar': isRtl }"
      >
        <div class="notice-text" :class="{ 'rtl-notice-text': isRtl }">
          {{ config?.lamp.content }}
        </div>
        <template #left-icon>
          <img
            src="@/assets/images/withdraw/broadcast-icon.png"
            alt=""
            class="notice-icon"
        /></template>
        <template #right-icon>
          <img
            src="@/assets/images/withdraw/close-icon.png"
            alt=""
            @click="closeNoticeBar(true)"
            class="notice-icon"
          />
        </template>
      </van-notice-bar>
      <!-- 男 -->
      <div class="man-content-wrap" v-if="config.gender === 1">
        <div class="balance-wrap flex">
          <div class="balance-title">{{ $t("income.balance_title") }}</div>
          <div class="balance-amount flex">
            <img
              class="icon-diamond"
              src="@/assets/images/common/diamond.png"
              alt=""
            />
            <gap :gap="5"></gap>
            <span>{{ numberWithCommas(config.balance) }}</span>
          </div>
        </div>
        <div class="header-padding"></div>
      </div>
      <!-- 女 -->
      <div class="content-wrap" v-else>
        <div class="balance-wrap flex">
          <div class="balance-title">{{ $t("income.balance_title") }}</div>
          <div class="balance-amount flex">
            <gap :gap="8"></gap>
            <img
              class="icon-diamond"
              src="@/assets/images/common/diamond.png"
              alt=""
            />
            <gap :gap="2"></gap>
            <span>{{ numberWithCommas(config.balance) }}</span>
          </div>
        </div>
        <div class="exchange-wrap">
          <div class="value-text">{{ $t("income.withdraw_value") }} ≈</div>
          <div class="local-value">
            {{ numberWithCommas(config.balance_local) }}
            <span>{{ config.currency }}</span>
            <img
              src="@/assets/images/common/question-mark.png"
              alt=""
              @click="tipDialogShow = true"
            />
          </div>
        </div>
      </div>
    </header>
    <div class="tabs-wrap flex">
      <div class="tabs-title flex">
        <div
          class="tab"
          :class="{ active: currentTabIdx === idx }"
          v-for="(tab, idx) in computedTabsList"
          :key="idx"
          @click="changeTabIdx(idx)"
        >
          {{ tab }}
        </div>
      </div>
      <div
        class="icon-history"
        v-if="!currentIsCoin && isShowWithdraw"
        @click="jumpPage(7)"
      >
        <img src="@/assets/images/icon/icon-history.svg" alt="" />
        <div class="dot" v-if="config.red_point"></div>
      </div>
    </div>
    <div class="list-wrap">
      <div
        class="auth-notice"
        v-if="!currentIsCoin && !config.auth"
        @click="jumpPage(6)"
      >
        <van-notice-bar
          class="notice-bar"
          color="#FFAD33"
          background="#FFF3DF"
          :text="$t('income.no_verified_tips')"
        >
          <template #left-icon>
            <img
              class="notice-left-icon"
              src="@/assets/images/icon/icon-warning.svg"
              alt=""
            />
            <gap :gap="4"></gap>
          </template>
          <template #right-icon>
            <gap :gap="4"></gap>
            <img
              class="notice-right-icon"
              src="@/assets/images/withdraw/icon-arrow-right.png"
              alt=""
            />
          </template>
        </van-notice-bar>
      </div>
      <div class="list-bg">
        <!-- 隐藏按钮，用于测量最长文本的宽度 -->
        <div ref="hiddenButton" class="cell-button flex hidden">
          <div class="cell-icon-diamond"></div>
          <gap :gap="2"></gap>
          <span>{{ longestText }}</span>
        </div>
        <template v-if="!isEmpty">
          <div
            class="cell flex"
            :class="{ disabled: cell.disabled }"
            v-for="(cell, idx) in config.items"
            :key="idx"
          >
            <div class="cell-left">
              <div class="flex" v-if="currentIsCoin">
                <img
                  class="icon-dollar"
                  v-if="!cell.disabled"
                  src="@/assets/images/common/coin.svg"
                  alt=""
                />
                <img
                  class="icon-dollar"
                  v-else
                  src="@/assets/images/common/coin-gray.svg"
                  alt=""
                />
                <gap :gap="4"></gap>
                <span class="money-text">{{ cell.coin }}</span>
              </div>
              <template v-else>
                <div class="flex">
                  <img
                    class="icon-dollar"
                    v-if="!cell.disabled"
                    src="@/assets/images/common/dollar.png"
                    alt=""
                  />
                  <img
                    class="icon-dollar"
                    v-else
                    src="@/assets/images/common/dollar-gray.png"
                    alt=""
                  />
                  <gap :gap="4"></gap>
                  <span class="money-text"
                    >{{ cell.price }} {{ cell.currency }}</span
                  >
                </div>
                <div class="money-bonus-tip" v-if="cell.corner_mark">
                  {{ cell.corner_mark }}
                </div>
              </template>
            </div>
            <div
              class="cell-button flex"
              :style="{ width: computedButtonWidth + 'px' }"
              @click="
                currentIsCoin ? checkExchange(cell) : checkWithdrawal(cell)
              "
            >
              <div class="cell-icon-diamond"></div>
              <gap :gap="2"></gap>
              <span>{{ cell.diamond }}</span>
            </div>
          </div>
        </template>
        <Empty
          v-else
          class="empty"
          :tips="$t('common.common_content_yet')"
        ></Empty>
      </div>
    </div>
    <footer class="flex">
      <div class="box-item flex" @click="jumpPage(1)">
        <img
          class="box-icon"
          src="@/assets/images/icon/icon-question.svg"
          alt=""
        />
        <gap :gap="4"></gap>
        <span>{{ $t("common.common_feedback") }}</span>
      </div>
      <div class="box-item flex" @click="jumpPage(2)">
        <img
          class="box-icon"
          src="@/assets/images/icon/icon-contact.svg"
          alt=""
        />
        <gap :gap="4"></gap>
        <span>{{ $t("common.common_contact_us") }}</span>
      </div>
    </footer>
  </div>

  <!--  兑换确认弹窗  -->
  <van-overlay :show="exchangeDialogShow" @click="toggleExchangeDialog(false)">
    <div class="check-exchange" @click.stop>
      <div class="check-bg"></div>
      <div class="check-header flex">
        <img
          class="check-img"
          src="@/assets/images/withdraw/exchange-coin.png"
          alt=""
        />
        <div class="check-title">{{ $t("income.exchange_dialog_title") }}</div>
        <div class="check-content">
          {{
            $t("income.exchange_dialog_hint", [
              numberWithCommas(currentChooseItem.diamond),
              currentChooseItem.coin,
            ])
          }}
        </div>
      </div>
      <div class="check-button-wrap flex">
        <div class="check-button" @click="toggleExchangeDialog(false)">
          {{ $t("common.common_cancel") }}
        </div>
        <div class="check-button active" @click="exchangePost">
          {{ $t("income.exchange_dialog_exchange") }}
        </div>
      </div>
    </div>
  </van-overlay>

  <!--  性别认证弹窗  -->
  <base-modal
    :show="verifyDialogShow"
    :conform-text="$t('common.common_verify')"
    show-cancel
    @confirm="currentIsCoin ? jumpPage(3) : jumpPage(6)"
    @update:show="toggleVerifyDialog"
  >
    <template #title v-if="currentIsCoin">{{
      $t("income.coin_no_verified_tips")
    }}</template>
    <template #title v-else>{{ $t("income.no_verified_tips") }}</template>
  </base-modal>

  <!--  其他提示弹窗  -->
  <base-modal
    :show="hintDialogShow"
    @confirm="toggleHintDialog(false)"
    @update:show="toggleHintDialog"
  >
    <template #title>{{ hintDialogTitle }}</template>
    <template #content>{{ hintDialogMsg }}</template>
  </base-modal>

  <!--  联系客服弹窗  -->
  <base-modal
    :show="contactDialogShow"
    @confirm="toggleContactDialog(false)"
    @update:show="toggleContactDialog"
  >
    <template #title>
      <div v-html="officialEmail"></div>
      <!-- <div><EMAIL></div> -->
    </template>
  </base-modal>

  <!--  tip弹窗  -->
  <base-modal :show="tipDialogShow" @confirm="tipDialogShow = false">
    <template #content>{{ $t("income.withdraw_modal_tip") }}</template>
  </base-modal>
</template>

<script setup>
import {
  ref,
  onMounted,
  getCurrentInstance,
  onUnmounted,
  computed,
  onActivated,
  watch,
  nextTick,
} from "vue";
import walletApi from "@/api/wallet.js";
import withdrawApi from "@/api/withdraw.js";
import { numberWithCommas } from "@/utils/util.js";
import { useRouter } from "vue-router";
import { i18n } from "@/i18n/index.js";
import { useWithdrawStore } from "@/store/index";
import iconMenu from "@/assets/images/icon/icon-menu.svg";
import { pick } from "lodash";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const router = useRouter();
const withdrawStore = useWithdrawStore();
const closeType = ref("close");

// 处理提交请求后直接跳转至记录页
router.beforeEach((to, from) => {
  if (from.name === "WithdrawalInformation") {
    if (to.name === "IncomeList") {
      router.push({
        name: "WithdrawalRecord",
        query: { timestamp: new Date().getTime() },
      });
    }
  }
});

const config = ref({
  authSwitch: false,
  genderAuth: false,

  auth: true,
  auth_type: 0,
  red_point: false,

  balance: 0,
  balance_local: 0,
  currency: "",
  items: [],

  lamp: {},
  gender: 0,
});
const isEmpty = ref(false);
const tipDialogShow = ref(false);
const officialEmail = computed(() => {
  let str = t("common.common_official_email");
  str = str.replace("__x__", "<EMAIL>");
  return str.replace(/\n/g, "<br>");
});
const getConfig = async () => {
  let response;
  if (currentTabIdx.value === 0) {
    response = await walletApi.exchange_coin_list();
  } else {
    response = await withdrawApi.withdraw_list();
  }
  if (response.code === 200) {
    config.value = {
      ...config.value,
      ...response.data,
    };
    config.value.items.forEach((item) => {
      item.disabled = computedButtonDisabled(item);
    });
    isEmpty.value = !config.value.items || config.value.items.length === 0;
  }
};

// 计算最长按钮文本
const hiddenButton = ref(null);
const computedButtonWidth = ref(0);
const longestText = computed(() => {
  return config.value.items.reduce((max, item) => {
    return item.diamond > max ? item.diamond : max;
  }, "");
});

watch(
  longestText,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        computedButtonWidth.value = Math.ceil(
          hiddenButton.value.getBoundingClientRect().width + 5
        );
      });
    }
  },
  {
    immediate: true,
  }
);

const isRtl = computed(() => {
  return ["ar"].includes(proxy.$languageFile);
});

const computedButtonDisabled = (cell) => {
  return !!(cell.exchange_num <= 0 || cell.gray);
};

let timer = null;
const isShowCloseNoticeBar = ref(false);

const closeNoticeBar = (isDel = false) => {
  isShowCloseNoticeBar.value = false;
  if (isDel) {
    proxy.$siyaApp("saveCacheToApp", {
      key: "closeTimeId",
      value: config.value.lamp.id,
    });
  }
  console.log({
    key: "closeTimeId",
    value: config.value.lamp.id,
  });
  clearTimeout(timer);
  timer = null;
};

const setNoticDuration = async () => {
  try {
    const result = await proxy.$siyaApp("getCacheFromApp", {
      key: "closeTimeId",
    });
    isShowCloseNoticeBar.value =
      result.data?.value !== config.value.lamp.id.toString();
  } catch (error) {}

  timer = setTimeout(() => {
    closeNoticeBar();
  }, config.value.lamp.duration * 60 * 1000);
};
// 如果用户当前国家有配置且启用的收款渠道则显示
const isShowWithdraw = ref(false);
const checkShowWithdraw = async () => {
  const response = await withdrawApi.withdraw_list();
  if (response.code === 200) {
    config.value = {
      ...config.value,
      ...pick(response.data, ["balance_local", "currency", "lamp", "gender"]),
    };
    setNoticDuration();
    isShowWithdraw.value = response.data.items.length > 0;
  }
};

const checkExchange = (cell) => {
  // 超过兑换上限
  if (cell.exchange_num <= 0) {
    proxy.$toast(t("income.msg_count_limit"));
    return;
  }
  // 余额不足
  if (cell.diamond > config.value.balance) {
    proxy.$toast(t("income.msg_insufficient_balance"));
    return;
  }
  // 未认证
  if (config.value.authSwitch && !config.value.genderAuth) {
    toggleVerifyDialog(true);
    return;
  }
  currentChooseItem.value = cell;
  toggleExchangeDialog(true);
};

const currentChooseItem = ref({});
const exchangeLock = ref(false);
const exchangePost = async () => {
  if (exchangeLock.value) return;
  exchangeLock.value = true;
  const params = {
    id: currentChooseItem.value.id,
  };
  const response = await walletApi.exchange_coin(params);
  if (response.code === 200) {
    toggleExchangeDialog(false);
    hintDialogTitle.value = t("income.msg_exchange_success_title");
    hintDialogMsg.value = t("income.msg_exchange_success_content", [
      currentChooseItem.value.coin,
    ]);
    toggleHintDialog(true);
    getConfig();
  } else {
    toggleExchangeDialog(false);
    // 未认证
    if (response.code === 1001) {
      toggleVerifyDialog(true);
      // 当前地区不支持提现
    } else if (response.code === 1002) {
      hintDialogTitle.value = t("income.msg_nationality");
      toggleHintDialog(true);
    } else {
      proxy.$toast(response.msg);
    }
  }
  exchangeLock.value = false;
};

const jumpPage = (type) => {
  switch (type) {
    // 意见反馈
    case 1:
      router.push({
        name: "Feedback",
        query: { type: 3 },
      });
      break;
    // 联系我们
    case 2:
      toggleContactDialog(true);
      break;
    // 未认证 性别
    case 3:
      toggleVerifyDialog(false);
      proxy.$siyaApp("idenAuthentication", { type: 1 }); // type: 1 性别  2 头像
      break;
    // 钻石记录
    case 4:
      router.push({ name: "IncomeRecord" });
      break;
    // 提现收款方式列表
    case 5:
      router.push({ name: "WithdrawalList" });
      break;
    // 未认证 提现
    case 6:
      toggleVerifyDialog(false);
      proxy.$siyaApp("idenAuthentication", { type: config.value.auth_type }); // type: 1 性别  2 头像
      break;
    // 提现记录
    case 7:
      router.push({
        name: "WithdrawalRecord",
        query: { timestamp: new Date().getTime() },
      });
  }
};

const exchangeDialogShow = ref(false);
const toggleExchangeDialog = (bool) => {
  exchangeDialogShow.value = bool;
};

const verifyDialogShow = ref(false);
const toggleVerifyDialog = (bool) => {
  verifyDialogShow.value = bool;
};

const hintDialogShow = ref(false);
const hintDialogTitle = ref("");
const hintDialogMsg = ref("");
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool;
  if (!bool) {
    setTimeout(() => {
      hintDialogTitle.value = "";
      hintDialogMsg.value = "";
    }, 1000);
  }
};

const contactDialogShow = ref(false);
const toggleContactDialog = (bool) => {
  contactDialogShow.value = bool;
};

const handleVisible = (e) => {
  if (e.target.visibilityState === "visible") {
    getConfig();
  }
};

const tabsList = [t("income.exchange_coin"), t("income.exchange_diamonds")];
const computedTabsList = computed(() => {
  if (isShowWithdraw.value) return tabsList;
  return [tabsList[0]];
});
const currentTabIdx = ref(0);
const currentIsCoin = computed(() => {
  return currentTabIdx.value === 0;
});
const changeTabIdx = (idx) => {
  if (currentTabIdx.value === idx) return;
  currentTabIdx.value = idx;
  config.value.items = [];
  getConfig();
};

/*    ======== 提现 ========    */
const checkWithdrawal = (cell) => {
  // 未认证
  if (!config.value.auth) {
    toggleVerifyDialog(true);
    return;
  }
  // 余额不足
  if (cell.diamond > config.value.balance) {
    proxy.$toast(t("income.msg_insufficient_balance"));
    return;
  }
  // 提现限制
  if (cell.gray) {
    if ([1].includes(cell.astrict_type))
      proxy.$toast(t("income.withdraw_item_msg_pending"));
    else if ([2, 3].includes(cell.astrict_type))
      proxy.$toast(t("income.withdraw_item_msg_submitted"));
    return;
  }

  withdrawStore.$patch({
    currentProductId: cell.id,
    countryCode: cell.country_code,
  });

  jumpPage(5);
};

onMounted(() => {
  checkShowWithdraw();
  window.addEventListener("visibilitychange", handleVisible);
  const fromRoute = router.options.history.state.back;
  if (fromRoute?.includes("anchor-center")) {
    closeType.value = "back";
  } else {
    closeType.value = "close";
  }
});

onActivated(() => {
  getConfig();
});

onUnmounted(() => {
  window.removeEventListener("visibilitychange", handleVisible);
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  height: 100vh;
}

header {
  position: relative;
  width: 100vw;
  background: url("@/assets/images/withdraw/bg.png") bottom/cover no-repeat;
  .notice-bar {
    height: px2rem(30);
    width: px2rem(358);
    margin: 0 auto px2rem(16);
    background: #ffffffb2;
    border-radius: px2rem(7);
    padding: 0 px2rem(8);
    .rtl-notice-text {
      transform: scaleX(-1);
      direction: ltr;
      unicode-bidi: isolate;
    }
    .notice-text {
      font-size: $fontSize13;
      color: #999;
      padding: 0 px2rem(4);
    }
    .notice-icon {
      height: px2rem(16);
      width: px2rem(16);
    }
  }
  .rtl-notice-bar {
    transform: scaleX(-1);
    direction: ltr;
    unicode-bidi: isolate;
  }
  .content-wrap {
    width: px2rem(358);
    height: px2rem(110);
    padding: px2rem(16) px2rem(24);
    margin: 0 auto;
    background: url("@/assets/images/withdraw/bg-border.png") no-repeat;
    background-size: 100%;
    color: rgba(0, 0, 0, 0.4);
    .balance-wrap {
      margin-bottom: px2rem(17);
      .balance-title {
        font-size: px2rem(13);
        line-height: px2rem(16);
        text-align: center;
      }
      .balance-amount {
        font-size: px2rem(13);
        font-weight: $fontWeightBold;
        // color: rgba(0, 0, 0, 0.8);
      }
      .icon-diamond {
        width: px2rem(16);
        height: px2rem(16);
      }
    }
    .exchange-wrap {
      font-weight: $fontWeightBold;
      .value-text {
        font-size: $fontSize13;
        margin-bottom: px2rem(4);
      }
      .local-value {
        font-size: px2rem(24);
        color: #000000cc;
        span {
          font-size: $fontSize15;
        }
        img {
          width: px2rem(14);
          height: px2rem(14);
          margin: 0 px2rem(4);
          transform: translateY(px2rem(2));
        }
      }
    }
  }
  .man-content-wrap {
    width: px2rem(358);
    height: px2rem(100);
    padding: px2rem(16) px2rem(24);
    margin: 0 auto;
    background: url("@/assets/images/withdraw/bg-border.png") no-repeat;
    background-size: 100%;
    color: rgba(0, 0, 0, 0.4);
    .header-padding {
      height: px2rem(100);
      margin-top: px2rem(5);
    }
    .balance-wrap {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      width: px2rem(358);
      height: px2rem(100);
      padding: 0 px2rem(24);
      margin: 0 auto;
    }

    .balance-title {
      font-size: px2rem(13);
      line-height: px2rem(16);
      text-align: center;
      color: rgba(0, 0, 0, 0.4);
    }

    .balance-amount {
      font-size: px2rem(24);
      font-weight: $fontWeightBold;
      color: rgba(0, 0, 0, 0.8);
    }

    .icon-diamond {
      width: px2rem(24);
      height: px2rem(24);
    }
  }
}

.tabs-wrap {
  justify-content: space-between;
  width: 100vw;
  padding: px2rem(13) px2rem(16) px2rem(13) px2rem(24);
  margin-top: px2rem(10);

  .tab {
    font-size: px2rem(13);
    line-height: px2rem(24);
    color: $fontColorB3;

    &.active {
      font-size: px2rem(15);
      font-weight: $fontWeightBold;
      color: $fontColorB0;
    }

    &:first-child {
      margin-right: $gap20;
    }
  }

  .icon-history {
    width: px2rem(24);
    height: px2rem(24);
    font-size: 0;
    position: relative;
    .dot {
      position: absolute;
      top: px2rem(-1);
      right: px2rem(-1);
      width: px2rem(8);
      height: px2rem(8);
      border-radius: 50%;
      background-color: #ff4771;
    }
  }
}

.auth-notice {
  position: relative;
  margin: 0 $gap16;

  &::before {
    content: "";
    position: absolute;
    bottom: -$gap16;
    left: 0;
    right: 0;
    height: $gap16;
    background-color: #fff3df;
  }

  .notice-bar {
    height: px2rem(32);
    padding: $gap8 $gap8 $gap8 $gap16;
    border-radius: $radius16 $radius16 0 0;

    @include fontSize13;
  }

  .notice-left-icon {
    width: px2rem(16);
    height: px2rem(16);
  }

  .notice-right-icon {
    width: px2rem(24);
    height: px2rem(24);
  }
}

.list-wrap {
  flex: 1;
  width: 100vw;
  overflow-y: scroll;
  padding-bottom: px2rem(20);

  .list-bg {
    position: relative;
    z-index: 1;
    padding: $gap8 0;
    margin: 0 $gap16;
    background-color: $white;
    border-radius: $radius16;
  }

  .cell {
    flex-wrap: nowrap;
    justify-content: space-between;
    padding: px2rem(17) 0;
    margin: 0 $gap16;

    &:not(:last-child) {
      border-bottom: 0.5px solid $bgColorB6;
    }

    &.disabled {
      .cell-left {
        color: $fontColorB4;
      }

      .money-bonus-tip {
        color: $white;
        background: $fontColorB4;
      }

      .cell-button {
        color: $fontColorB4;
        background: $bgColorB7;

        .cell-icon-diamond {
          background: url("@/assets/images/common/diamond-gray.png") 100% /
            cover no-repeat;
        }
      }
    }
  }

  .cell-left {
    font-size: 0;

    .icon-dollar {
      width: px2rem(24);
      height: px2rem(24);
    }

    .money-text {
      @include fontSize17;
      font-weight: $fontWeightBold;
    }

    .money-bonus-tip {
      display: inline-block;
      padding: px2rem(1) $gap6;
      margin-top: $gap4;
      color: $white;
      background: linear-gradient(
        104.08deg,
        #ffd2ba 0%,
        #ff4771 12.64%,
        #7970fe 66.01%
      );
      border-radius: $radius16;

      @include fontSize10;
    }
  }

  .cell-button {
    flex: none;
    flex-wrap: nowrap;
    min-width: px2rem(103);
    height: px2rem(36);
    padding: 0 px2rem(8);
    margin-left: $gap16;
    font-size: px2rem(15);
    color: $mainColor;
    background: #5f56ed1a;
    border-radius: px2rem(25);

    &.hidden {
      position: absolute;
      top: -10000;
      opacity: 0;
    }

    .cell-icon-diamond {
      width: px2rem(26);
      height: px2rem(26);
      background: url("@/assets/images/common/diamond.png") 100% / cover
        no-repeat;
    }
  }
}

.empty {
  flex: 1;
}

footer {
  width: 100vw;
  padding: $gap8 0;

  .box-item {
    flex: 1;
    justify-content: center;
    height: px2rem(24);
    font-size: px2rem(13);
    color: $fontColorB2;
  }

  .box-icon {
    width: px2rem(20);
    height: px2rem(20);
  }
}

.check-exchange {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top-left-radius: px2rem(26);
  border-top-right-radius: px2rem(26);
  overflow: hidden;

  .check-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: px2rem(100);
    background: linear-gradient(
      175.89deg,
      rgba(95, 86, 237, 0.16) 3.35%,
      rgba(95, 86, 237, 0) 96.66%
    );
  }

  .check-header {
    flex-direction: column;
    margin-bottom: px2rem(22);
  }

  .check-img {
    width: px2rem(100);
    height: px2rem(100);
  }

  .check-title {
    margin-bottom: $gap8;
    font-size: px2rem(20);
    font-weight: $fontWeightBold;
    line-height: px2rem(24);
    color: $fontColorB0;
  }

  .check-content {
    font-size: px2rem(13);
    color: $fontColorB3;
  }

  .check-button-wrap {
    padding: $gap16;
  }

  .check-button {
    flex: 1;
    height: px2rem(50);
    font-size: px2rem(17);
    line-height: px2rem(50);
    color: $mainColor;
    text-align: center;
    background-color: #f8f7ff;
    border-radius: $radius16;

    &.active {
      margin-left: px2rem(10);
      color: $white;
      background-color: $mainColor;
    }
  }
}
</style>
