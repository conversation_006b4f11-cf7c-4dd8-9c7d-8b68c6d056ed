<template>
  <div class="container">
    <right-button
      :text="$t('egypt.rules')"
      @click="toRulePage()"
      v-track:click
      trace-key="egypt_national_button_name"
      :track-params="JSON.stringify({ active_button_name: '规则按钮点击' })"
    ></right-button>
    <div
      class="bg-box"
      v-track:exposure
      trace-key="egypt_national_expo"
      :track-params="JSON.stringify({ active_expo_source: from })"
    >
      <header-bar
        back-icon-show
        :padding="false"
        :contentPadding="false"
        :show-title="false"
        close-type="close"
      />
      <div
        class="banner-title"
        :style="{
          backgroundImage: `url('${getImageUrl('title.png')}')`,
        }"
      >
        <img :src="getImageUrl('title.png')" draggable="false" />
      </div>
      <div class="count-down">
        <CountDown :left-time="activityInfo?.remain_times || 0"></CountDown>
      </div>
    </div>
    <div class="tabs">
      <div
        class="tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("egypt.tasks") }}</span>
      </div>
      <gap :gap="6"></gap>
      <div
        class="tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        <span>{{ $t("egypt.rankings") }}</span>
      </div>
    </div>
    <div v-if="current === 0" class="tasks">
      <div class="box-1">
        <GoldBox>
          <div class="title">
            <BlockTitle :title="$t('egypt.activity_gifts')"></BlockTitle>
          </div>
          <div class="desc">
            {{ $t("egypt.tasks_and_rankings_info") }}
          </div>
          <div class="rewards">
            <div class="reward" v-for="reward in activityInfo?.gifts || []">
              <div class="cover">
                <PrizeBox :rewards="reward" :showTag="false"></PrizeBox>
              </div>
              <div class="coin-box">
                <GenderCoin :size="18"></GenderCoin>
                <gap :gap="3"></gap>
                <div class="coin-number">
                  {{ formatCount(reward?.coin ?? 0) }}
                </div>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <!-- 每日任务 -->
      <div class="box-2">
        <GoldBox>
          <div class="title">
            <BlockTitle :title="$t('egypt.daily_tasks')"></BlockTitle>
          </div>
          <div class="desc">{{ $t("egypt.daily_task_frequency") }}</div>
          <div class="task-list">
            <div class="task">
              <div class="cover">
                <PrizeBox :rewards="rewardsInfo?.task1 || []"></PrizeBox>
              </div>
              <gap :gap="8"></gap>
              <div class="detail">
                <div class="detail-desc">{{ $t("egypt.sign_in") }}</div>
              </div>
              <gap :gap="8"></gap>
              <div class="action">
                <div
                  class="action-button claim"
                  v-if="activityInfo?.sign === false"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 2 })"
                  @click="handleReceive(1)"
                >
                  {{ $t("egypt.claim") }}
                </div>
                <div
                  class="action-button claimed"
                  v-else-if="activityInfo?.sign === true"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 3 })"
                >
                  {{ $t("egypt.claimed") }}
                </div>
              </div>
            </div>
            <div class="task">
              <div class="cover">
                <PrizeBox :rewards="rewardsInfo?.task4 || []"></PrizeBox>
              </div>
              <gap :gap="8"></gap>
              <div class="detail">
                <div class="detail-desc">
                  {{ $t("egypt.broadcast_2_hours") }}
                </div>
                <div class="process">
                  <Process
                    :current="activityInfo?.live_time?.progress || 0"
                    :max="activityInfo?.live_time?.total || 0"
                  ></Process>
                </div>
                <div class="process-desc">
                  ({{ formatMin(activityInfo?.live_time?.progress || 0) }}/{{
                    formatMin(activityInfo?.live_time?.total || 0)
                  }})
                </div>
              </div>
              <gap :gap="8"></gap>
              <div class="action">
                <div
                  class="action-button"
                  v-if="activityInfo?.live_time?.status === 0"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 1 })"
                  @click="handleGo(4)"
                >
                  {{ $t("egypt.go") }}
                </div>
                <div
                  class="action-button claim"
                  v-else-if="activityInfo?.live_time?.status === 1"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 2 })"
                  @click="handleReceive(4)"
                >
                  {{ $t("egypt.claim") }}
                </div>
                <div
                  class="action-button claimed"
                  v-else-if="activityInfo?.live_time?.status === 2"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 3 })"
                >
                  {{ $t("egypt.claimed") }}
                </div>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <!-- 限量任务 -->
      <div class="box-3">
        <GoldBox>
          <div class="title">
            <BlockTitle :title="$t('egypt.limited_tasks')"></BlockTitle>
          </div>
          <div class="desc">{{ $t("egypt.limited_task_frequency") }}</div>
          <div class="task-list">
            <div class="task">
              <div class="cover">
                <PrizeBox :rewards="rewardsInfo?.task2 || []"></PrizeBox>
              </div>
              <gap :gap="8"></gap>
              <div class="detail">
                <div class="detail-desc">
                  {{ $t("egypt.give_20000_coins_gift") }}
                </div>
                <div class="process">
                  <Process
                    :current="activityInfo?.send?.progress || 0"
                    :max="activityInfo?.send?.total || 0"
                  ></Process>
                </div>
                <div class="process-desc">
                  ({{ formatCount(activityInfo?.send?.progress || 0) }}/{{
                    formatCount(activityInfo?.send?.total || 0)
                  }})
                </div>
              </div>
              <gap :gap="8"></gap>
              <div class="action">
                <div
                  class="action-button"
                  v-if="activityInfo?.send?.status === 0"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 1 })"
                  @click="handleGo(2)"
                >
                  {{ $t("egypt.go") }}
                </div>
                <div
                  class="action-button claim"
                  v-else-if="activityInfo?.send?.status === 1"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 2 })"
                  @click="handleReceive(2)"
                >
                  {{ $t("egypt.claim") }}
                </div>
                <div
                  class="action-button claimed"
                  v-else-if="activityInfo?.send?.status === 2"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 3 })"
                >
                  {{ $t("egypt.claimed") }}
                </div>
              </div>
            </div>
            <div class="task">
              <div class="cover">
                <PrizeBox :rewards="rewardsInfo?.task3 || []"></PrizeBox>
              </div>
              <gap :gap="8"></gap>
              <div class="detail">
                <div class="detail-desc">
                  {{ $t("egypt.receive_50000_coins_gift") }}
                </div>
                <div class="process">
                  <Process
                    :current="activityInfo?.receive?.progress || 0"
                    :max="activityInfo?.receive?.total || 0"
                  ></Process>
                </div>
                <div class="process-desc">
                  ({{ formatCount(activityInfo?.receive?.progress || 0) }}/{{
                    formatCount(activityInfo?.receive?.total || 0)
                  }})
                </div>
              </div>
              <gap :gap="8"></gap>
              <div class="action">
                <div
                  class="action-button"
                  v-if="activityInfo?.receive?.status === 0"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 1 })"
                  @click="handleGo(3)"
                >
                  {{ $t("egypt.go") }}
                </div>
                <div
                  class="action-button claim"
                  v-else-if="activityInfo?.receive?.status === 1"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 2 })"
                  @click="handleReceive(3)"
                >
                  {{ $t("egypt.claim") }}
                </div>
                <div
                  class="action-button claimed"
                  v-else-if="activityInfo?.receive?.status === 2"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 3 })"
                >
                  {{ $t("egypt.claimed") }}
                </div>
              </div>
            </div>
            <div class="task">
              <div class="cover">
                <PrizeBox :rewards="rewardsInfo?.task5 || []"></PrizeBox>
              </div>
              <gap :gap="8"></gap>
              <div class="detail">
                <div class="detail-desc">
                  {{ $t("egypt.room_gift_flow_100000") }}
                </div>
                <div class="process">
                  <Process
                    :current="activityInfo?.room_flow?.progress || 0"
                    :max="activityInfo?.room_flow?.total || 0"
                  ></Process>
                </div>
                <div class="process-desc">
                  ({{ formatCount(activityInfo?.room_flow?.progress || 0) }}/{{
                    formatCount(activityInfo?.room_flow?.total || 0)
                  }})
                </div>
              </div>
              <gap :gap="8"></gap>
              <div class="action">
                <div
                  class="action-button"
                  v-if="activityInfo?.room_flow?.status === 0"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 1 })"
                  @click="handleGo(5)"
                >
                  {{ $t("egypt.go") }}
                </div>
                <div
                  class="action-button claim"
                  v-else-if="activityInfo?.room_flow?.status === 1"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 2 })"
                  @click="handleReceive(5)"
                >
                  {{ $t("egypt.claim") }}
                </div>
                <div
                  class="action-button claimed"
                  v-else-if="activityInfo?.room_flow?.status === 2"
                  v-track:click
                  trace-key="egypt_national_button_name"
                  :track-params="JSON.stringify({ receive_button_status: 3 })"
                >
                  {{ $t("egypt.claimed") }}
                </div>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
    </div>
    <div v-if="current === 1" class="ranking">
      <div class="box-4">
        <GoldBox>
          <div class="title">
            <BlockTitle :title="$t('egypt.activity_gifts')"></BlockTitle>
          </div>
          <div class="desc">
            {{ $t("egypt.tasks_and_rankings_info") }}
          </div>
          <div class="rewards">
            <div class="reward" v-for="reward in activityInfo?.gifts || []">
              <div class="cover">
                <PrizeBox :rewards="reward" :showTag="false"></PrizeBox>
              </div>
              <div class="coin-box">
                <GenderCoin :size="18"></GenderCoin>
                <gap :gap="3"></gap>
                <div class="coin-number">
                  {{ reward?.coin ?? 0 }}
                </div>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <div class="sub-tabs">
        <div
          class="sub-tab"
          :class="rankingTab === 0 ? 'active' : ''"
          @click="rankingTab = 0"
          v-track:click
          trace-key="egypt_national_button_name"
          :track-params="JSON.stringify({ egypt_national_tab_name: 1 })"
        >
          <span>{{ $t("egypt.gift_receiving_ranking") }}</span>
        </div>
        <gap :gap="6"></gap>
        <div
          class="sub-tab"
          :class="rankingTab === 1 ? 'active' : ''"
          @click="rankingTab = 1"
          v-track:click
          trace-key="egypt_national_button_name"
          :track-params="JSON.stringify({ egypt_national_tab_name: 2 })"
        >
          <span>{{ $t("egypt.gift_giving_ranking") }}</span>
        </div>
        <gap :gap="6"></gap>
        <div
          class="sub-tab"
          :class="rankingTab === 2 ? 'active' : ''"
          @click="rankingTab = 2"
          v-track:click
          trace-key="egypt_national_button_name"
          :track-params="JSON.stringify({ egypt_national_tab_name: 3 })"
        >
          <span>{{ $t("egypt.room_gift_ranking") }}</span>
        </div>
      </div>
      <Ranking
        v-if="rankingTab === 0"
        :type="2"
        :desc="$t('egypt.gift_receiving_ranking_info')"
      ></Ranking>
      <Ranking
        v-if="rankingTab === 1"
        :type="1"
        :desc="$t('egypt.gift_giving_ranking_info')"
      ></Ranking>
      <Ranking
        v-if="rankingTab === 2"
        :type="3"
        :desc="$t('egypt.room_gift_ranking_info')"
      ></Ranking>
    </div>
  </div>
  <RewordDialog ref="rewordDialogRef"></RewordDialog>
</template>
<script setup>
import { ref, getCurrentInstance } from "vue";
import { getLang } from "@/i18n/index.js";
import RightButton from "./components/RightButton.vue";
import CountDown from "./components/CountDown.vue";
import GoldBox from "./components/GoldBox.vue";
import BlockTitle from "./components/BlockTitle.vue";
import PrizeBox from "./components/PrizeBox.vue";
import Process from "./components/Process.vue";
import Ranking from "./components/Ranking.vue";
import GenderCoin from "@/components/gender-coin/index.vue";
import RewordDialog from "./components/RewordDialog.vue";
import { useRouter, useRoute } from "vue-router";
import activityApi from "@/api/activity.js";
import { formatCount } from "@/utils/util.js";

const router = useRouter();
const route = useRoute();
const { from } = route.query;
const rewordDialogRef = ref();
const current = ref(0);
const rankingTab = ref(0);
const lang = getLang();
const activityInfo = ref();
const rewardsInfo = ref();
const { proxy } = getCurrentInstance();

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/activity/egypt-national-day/${lang}/${name}`,
    import.meta.url
  ).href;
}
const toRulePage = () => {
  router.push({
    name: "EgyptNationalDayRule",
  });
};

const formatMin = (number = 0) => {
  const rtl = document.querySelector("html")?.getAttribute("dir") === "rtl";
  const min = Math.floor(number / 60);
  return !!rtl ? `min${min}` : `${min}min`;
};

const fetchData = async () => {
  const res = await activityApi.egypt_info();
  if (res.code === 200) {
    activityInfo.value = res.data;
  }
  const res2 = await activityApi.egypt_rewards();
  if (res2.code === 200) {
    rewardsInfo.value = res2.data;
  }
};

const getRewardsByType = (type) => {
  switch (type) {
    case 1:
      return rewardsInfo.value?.task1 || [];
    case 2:
      return rewardsInfo.value?.task2 || [];
    case 3:
      return rewardsInfo.value?.task3 || [];
    case 4:
      return rewardsInfo.value?.task4 || [];
    case 5:
      return rewardsInfo.value?.task5 || [];
    default:
      return [];
  }
};

const isGetPrizeLoading = ref(false);
const handleReceive = async (type) => {
  if (isGetPrizeLoading.value === true) {
    return;
  }
  isGetPrizeLoading.value = true;
  try {
    const res = await activityApi.egypt_task_receive({
      type,
    });
    const rewards = getRewardsByType(type);
    if (res.code === 200) {
      rewordDialogRef.value?.open(rewards);
    }
    fetchData();
    isGetPrizeLoading.value = false;
  } catch (e) {
    isGetPrizeLoading.value = false;
  }
};

const handleGo = (type) => {
  switch (type) {
    case 2:
    case 3:
      proxy.$siyaApp("openSiyaUrl", {
        url: `siya://siya.com/app?method=goRoomTab`,
      });
      break;
    case 4:
    case 5:
      proxy.$siyaApp("openSiyaUrl", {
        url: `siya://siya.com/app?method=goCreateRoom`,
      });
      break;
    default:
      break;
  }
};

siya: fetchData();
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  min-height: 100vh;
  width: 100%;
  background-color: #5b1303;
  overflow: hidden;
  .bg-box {
    background-image: url("@/assets/images/activity/egypt-national-day/bg-1.jpg");
    background-size: 100% px2rem(476);
    width: 100%;
    height: px2rem(476);
    background-repeat: no-repeat;
    overflow: hidden;
    .banner-title {
      width: px2rem(223);
      background-size: 100% 100%;
      margin: 0 auto;
      margin-top: px2rem(149);
      img {
        opacity: 0;
        width: px2rem(223);
        vertical-align: bottom;
      }
    }
    .count-down {
      margin-top: px2rem(122);
    }
  }
  .tabs,
  .sub-tabs {
    display: flex;
    width: px2rem(342);
    height: px2rem(52);
    display: flex;
    padding: px2rem(4);
    align-items: center;
    border-radius: px2rem(31);
    border: 1px solid #ffdb8d;
    background: #cc3a34;
    margin: 0 auto;
    margin-top: px2rem(16);

    .tab,
    .sub-tab {
      width: px2rem(164);
      height: px2rem(44);
      flex-shrink: 0;
      border-radius: px2rem(50);
      border: px2rem(2) solid #ff8781;
      background: linear-gradient(180deg, #8f1914 0%, #250200 100%);

      color: #ffa19d;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .sub-tab {
      width: px2rem(108);
      font-size: px2rem(11);
    }

    .sub-tab.active {
      span {
        font-size: px2rem(11);
        color: #ffe1a2;
        text-align: center;
        text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
        font-family: Gilroy;
        font-style: normal;
        font-weight: 700;
      }
    }

    .tab.active {
      span {
        text-align: center;
        text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        font-family: Gilroy;
        font-size: px2rem(20);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        background: linear-gradient(180deg, #fff 28.26%, #ffae00 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .active {
      border: px2rem(1.5) solid #fff;
      background: linear-gradient(
        180deg,
        #ffad9e 0%,
        #d90000 47.12%,
        #8d0000 85.58%
      );
      box-shadow: 0px -4px 4px 0px rgba(44, 2, 104, 0.3) inset,
        0px 3px 3px 0px rgba(255, 255, 255, 0.66) inset;
    }
  }
  .sub-tabs {
    width: px2rem(344);
  }
  .tasks {
    width: 100%;
    margin-top: px2rem(28);
    padding: 0 px2rem(18);
    padding-bottom: px2rem(48);
  }
}

.box-1,
.box-2,
.box-3,
.box-4 {
  width: px2rem(355);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  .title {
    margin-top: px2rem(16);
  }
  .desc {
    margin-top: px2rem(6);
    color: #c8534e;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    width: px2rem(217);
  }
  .rewards {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: px2rem(27) 0;
    .reward {
      display: flex;
      align-items: center;
      flex-direction: column;
      width: px2rem(87);
      .cover {
        width: px2rem(70);
        height: px2rem(70);
      }
      .name {
        margin-top: px2rem(12);
        width: px2rem(87);
        color: #ffa19d;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(14);
        font-style: normal;
        font-weight: 500;
        line-height: 100%; /* 14px */
      }
    }
  }
  .task-list {
    width: 100%;
    padding: 0 px2rem(17);
    padding-bottom: px2rem(30);
    margin-top: px2rem(15);
    position: relative;
    z-index: 2;
    .task {
      display: flex;
      align-items: center;
      border-radius: px2rem(10);
      border: 1px solid #fccf8c;
      background: #9b2e29;
      margin-bottom: px2rem(10);
      padding: px2rem(10);
      .cover {
        flex-shrink: 0;
        width: px2rem(55);
        height: px2rem(55);
      }
      .detail {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        .detail-desc {
          color: #ffa19d;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
          margin-bottom: px2rem(6);
        }
        .process {
          width: 100%;
          margin-bottom: px2rem(2);
        }
        .process-desc {
          color: #c8534e;
          font-family: Gilroy;
          font-size: px2rem(10);
          font-style: normal;
          font-weight: 500;
          line-height: normal;
        }
      }
      .action {
        flex-shrink: 0;
        .action-button {
          min-width: px2rem(56);
          height: px2rem(24);
          color: #ffe3e3;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(11);
          font-style: normal;
          font-weight: 700;
          line-height: normal;

          border-radius: px2rem(50);
          border: 1px solid #ffa2a2;
          background: linear-gradient(
            180deg,
            #ffad9e 0%,
            #d90000 47.12%,
            #8f0000 89.9%
          );
          box-shadow: 0px px2rem(3) px2rem(3) 0px rgba(255, 255, 255, 0.26)
            inset;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .action-button.claim {
          color: #ab0000;
          background: linear-gradient(180deg, #fff052 1.44%, #ff7c27 100%);
        }
        .action-button.claimed {
          color: #ffa19d;
          background: linear-gradient(180deg, #8f0500 14.08%, #350300 70.67%);
        }
      }
    }
  }
}
.box-2,
.box-3 {
  margin-top: px2rem(37);
}
.box-4 {
  margin-top: px2rem(28);
}
.coin-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: px2rem(11);
  .coin-number {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffa19d;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 14px */
  }
}
</style>
