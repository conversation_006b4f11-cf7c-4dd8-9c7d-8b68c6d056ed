<template>
  <div class="good-voice">
    <div class="steps">
      <div class="process-bar">
        <div
          class="steps-bg"
          :style="{
            '--steps-bg-width':
              step === 0 ? '25%' : step === 1 ? '50%' : '100%',
          }"
        ></div>
        <div class="balls">
          <div
            class="step-ball-1"
            :class="step >= 0 ? 'step-ball-active' : ''"
            @click="handleStepClick(0)"
            v-track:click
            trace-key="singer_button_click"
            :track-params="JSON.stringify({ singer_button_name: 5 })"
          ></div>
          <gap :gap="70"></gap>
          <div
            class="step-ball-2"
            :class="step >= 1 ? 'step-ball-active' : ''"
            @click="handleStepClick(1)"
            v-track:click
            trace-key="singer_button_click"
            :track-params="JSON.stringify({ singer_button_name: 6 })"
          ></div>
          <gap :gap="70"></gap>
          <div
            class="step-ball-3"
            :class="step >= 2 ? 'step-ball-active' : ''"
            @click="handleStepClick(2)"
            v-track:click
            trace-key="singer_button_click"
            :track-params="JSON.stringify({ singer_button_name: 7 })"
          ></div>
        </div>
      </div>
      <div class="steps-texts">
        <div
          class="steps-text-item"
          :class="step === 0 ? 'actived' : ''"
          @click="handleStepClick(0)"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 5 })"
        >
          <div>{{ $t("singer.audition_round_title") }}</div>
          <div>
            <span>
              {{ formatDate(activityInfo?.stage2_time?.start_time, "M.D") }}
            </span>
            <span>-</span>
            <span>
              {{ formatDate(activityInfo?.stage2_time?.end_time, "M.D") }}
            </span>
          </div>
        </div>
        <gap :gap="40"></gap>
        <div
          class="steps-text-item"
          :class="step === 1 ? 'actived' : ''"
          @click="handleStepClick(1)"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 6 })"
        >
          <div>{{ $t("singer.qualifying_round_title") }}</div>
          <div>
            {{ formatDate(activityInfo?.stage3_time?.start_time, "M.D") }}
          </div>
        </div>
        <gap :gap="40"></gap>
        <div
          class="steps-text-item"
          :class="step === 2 ? 'actived' : ''"
          @click="handleStepClick(2)"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 7 })"
        >
          <div>{{ $t("singer.final_round_title") }}</div>
          <div>
            {{ formatDate(activityInfo?.stage4_time?.start_time, "M.D") }}
          </div>
        </div>
      </div>
    </div>
    <!-- 海选赛 -->
    <template v-if="showStep === 0">
      <div class="search" v-if="activityInfo?.stage <= 2">
        <VoteSearch
          :show-vote-btn="activityInfo?.stage === 2"
          :source="1"
        ></VoteSearch>
      </div>
      <div class="tabs" v-if="activityInfo?.stage <= 2">
        <div
          class="tab"
          :class="tabIndex === 0 ? 'tab-actived' : ''"
          @click="tabIndex = 0"
        >
          {{ $t("singer.popular_ranking") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="tab"
          :class="tabIndex === 1 ? 'tab-actived' : ''"
          @click="tabIndex = 1"
          v-track:click
          trace-key="singer_button_click"
          :track-params="JSON.stringify({ singer_button_name: 11 })"
        >
          {{ $t("singer.random_list") }}
        </div>
      </div>
      <div class="popular" v-if="tabIndex === 0 || activityInfo?.stage > 2">
        <div class="desc">
          {{ $t("singer.audition_to_qualifying") }}
        </div>
        <!-- 海选排行 -->
        <Ranking
          :rank-type="2"
          :show-vote-btn="activityInfo?.stage === 2"
          :show-audio-btn="activityInfo?.stage === 2"
        ></Ranking>
      </div>
      <RandomRanking
        v-else-if="tabIndex === 1"
        :show-vote-btn="activityInfo?.stage === 2"
        :show-audio-btn="activityInfo?.stage === 2"
      ></RandomRanking>
    </template>
    <!-- 晋级赛 -->
    <template v-if="showStep === 1">
      <RoomBlock
        :roomInfo="activityInfo?.room_info"
        :is-subscribe="activityInfo?.subscribe_stage3"
        :timezone="activityInfo?.timezone"
        :stage="1"
        :step="activityInfo?.stage"
        @subscribe="activityInfo.subscribe_stage3 = true"
      ></RoomBlock>
      <!-- 参赛列表 -->
      <template v-if="activityInfo?.stage < 5">
        <div class="t-title">
          <BlockTitle
            :show-line="true"
            :font-size="22"
            :lineWidth="30"
            :iconGap="8"
            :lineGap="3"
            icon-size="large"
            :title="$t(`singer.qualifying_round_participants_list`)"
          ></BlockTitle>
        </div>
        <div class="t-desc">
          {{ $t(`singer.qualifying_round_to_final`) }}
        </div>
        <!-- 参赛名单，海选结束，晋级赛未开始 -->
        <CompetitionList
          v-if="activityInfo?.stage === 4"
          :stage="1"
        ></CompetitionList>
        <div class="not-start" v-if="activityInfo?.stage < 4">
          {{ $t(`singer.qualifying_round_not_started`) }}
        </div>
      </template>
      <!-- 晋级赛排行 -->
      <template v-else>
        <div class="t-title">
          <BlockTitle
            :show-line="true"
            :font-size="22"
            :lineWidth="30"
            :iconGap="8"
            :lineGap="3"
            icon-size="large"
            :title="$t(`singer.qualifying_round_ranking_title`)"
          ></BlockTitle>
        </div>
        <div class="t-desc">
          {{ $t(`singer.qualifying_round_ranking_scoring_method`) }}
        </div>
        <Ranking
          class="mt-33"
          :rank-type="1"
          :is-score="true"
          :rank-data-type="2"
        ></Ranking>
      </template>
    </template>
    <!-- 决赛 -->
    <template v-if="showStep === 2">
      <RoomBlock
        :roomInfo="activityInfo?.room_info"
        :is-subscribe="activityInfo?.subscribe_stage4"
        :timezone="activityInfo?.timezone"
        :stage="2"
        :step="activityInfo?.stage"
        @subscribe="activityInfo.subscribe_stage4 = true"
      ></RoomBlock>
      <!-- 参赛列表 -->
      <template v-if="activityInfo?.stage <= 7">
        <div class="t-title">
          <BlockTitle
            :show-line="true"
            :font-size="22"
            :lineWidth="30"
            :iconGap="8"
            :lineGap="3"
            icon-size="large"
            :title="$t(`singer.final_round_participants_list`)"
          ></BlockTitle>
        </div>
        <div class="t-desc">
          {{ $t(`singer.final_round_rewards`) }}
        </div>
        <CompetitionList
          v-if="activityInfo?.stage === 7"
          :stage="2"
        ></CompetitionList>
        <div class="not-start" v-if="activityInfo?.stage < 7">
          {{ $t(`singer.qualifying_round_not_started`) }}
        </div>
      </template>
      <!-- 决赛排行 -->
      <template v-else>
        <div class="t-title">
          <BlockTitle
            :show-line="true"
            :font-size="22"
            :lineWidth="30"
            :iconGap="0"
            :lineGap="3"
            icon-size="large"
            :title="$t(`singer.final_round_ranking`)"
          ></BlockTitle>
        </div>
        <div class="t-desc">
          {{ $t(`singer.qualifying_round_ranking_scoring_method`) }}
        </div>
        <Ranking
          class="mt-33"
          :rank-type="2"
          :is-score="true"
          :rank-data-type="2"
        ></Ranking>
      </template>
    </template>
  </div>
</template>
<script setup>
import VoteSearch from "./VoteSearch.vue";
import Ranking from "./Ranking.vue";
import RandomRanking from "./RandomRanking.vue";
import CompetitionList from "./CompetitionList.vue";
import RoomBlock from "./RoomBlock.vue";
import BlockTitle from "./BlockTitle.vue";
import { inject, ref, watch } from "vue";
import { formatDate } from "@/utils/util";
const step = ref(0);
const showStep = ref(0);
const tabIndex = ref(0);
const activityInfo = inject("activityInfo", null);
const maps = [
  [0, 1, 2, 3],
  [4, 5, 6],
  [7, 8, 9],
];
const handleStepClick = (index) => {
  const allowClickMap = maps.slice(0, index + 1).flat();
  if (allowClickMap.includes(activityInfo.value.stage)) {
    showStep.value = index;
  }
};

const initStep = (stage) => {
  if (stage <= 3) {
    return 0;
  } else if (stage <= 6) {
    return 1;
  } else if (stage <= 9) {
    return 2;
  } else {
    return 2;
  }
};

watch(
  () => activityInfo.value,
  (info) => {
    // stage 0 报名 1.审核 2.海选 3.海选结束， 4.晋级赛当天零点-晋级赛开始时间 5晋级赛 6晋级赛结束 7.决赛当天零点-决赛开始时间 8决赛 9决赛结束 10 活动结束
    step.value = initStep(info.stage);
    showStep.value = initStep(info.stage);
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.good-voice {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.steps {
  margin-top: px2rem(21);

  .process-bar {
    position: relative;
    overflow: auto;
    height: px2rem(20);

    .steps-bg {
      margin: 0 auto;
      margin-top: px2rem(6);
      width: px2rem(340);
      height: px2rem(8);
      flex-shrink: 0;
      border-radius: px2rem(10);
      border: px2rem(1) solid #de69ce;
      background: linear-gradient(90deg, #480074 0%, #8700da 100%);
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &::after {
        content: "";
        width: var(--steps-bg-width);
        height: 100%;
        border-radius: 10px;
        background: linear-gradient(90deg, #fbff79 0%, #ff9d00 100%);
      }
    }

    .balls {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      width: 100%;

      .step-ball-1,
      .step-ball-2,
      .step-ball-3 {
        width: px2rem(20);
        height: px2rem(20);
        background: url("@/assets/images/activity/singer/ball-nomal.png")
          no-repeat center/100% 100%;
      }
      .step-ball-active {
        background-image: url("@/assets/images/activity/singer/ball-light.png");
      }
    }
  }

  .steps-texts {
    margin-top: px2rem(11);
    display: flex;
    justify-content: center;

    .steps-text-item {
      color: rgba(255, 255, 255, 0.6);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(10);
      font-weight: 500;
      line-height: 1;
      width: px2rem(62);
      display: flex;
      flex-direction: column;
      align-items: center;

      & :first-child {
        padding: 0 5px;
        height: px2rem(20);
      }

      & :last-child {
        margin-top: px2rem(4);
        width: 100%;
        height: px2rem(11);
        line-height: px2rem(11);
      }
    }

    .actived {
      color: rgba(255, 240, 156, 0.7);
      & > :last-child {
        color: #f2d300;
        font-weight: 700;
        text-align: center;
        background: linear-gradient(
          270deg,
          rgba(255, 250, 229, 0) 0%,
          rgba(255, 223, 0, 0.4) 51.86%,
          rgba(255, 255, 242, 0) 100%
        );
      }
    }
  }
}
.search {
  margin-top: px2rem(21);
}
.tabs {
  margin-top: px2rem(24);
  display: flex;
  padding: px2rem(4);
  align-items: center;
  border-radius: px2rem(30);
  background: #2e0c33;
  width: px2rem(280);
  height: px2rem(38);

  .tab {
    width: px2rem(138);
    height: px2rem(30);
    color: #fff;
    text-align: center;
    text-shadow: 0 px2rem(2) px2rem(2.9) rgba(0, 0, 0, 0.34);
    font-family: Gilroy;
    font-size: px2rem(11);
    font-weight: 500;
    line-height: 1.24;
    border-radius: px2rem(50);
    border: px2rem(1.5) solid #86868699;
    background: #2e0c33;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tab-actived {
    border: px2rem(1.5) solid #f4a9ff;
    background: linear-gradient(180deg, #ff61f0 13.41%, #9430ff 77.63%);
  }
}

.popular {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: px2rem(17);
  .desc {
    display: inline-flex;
    padding: px2rem(10);
    margin-bottom: px2rem(18);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(6);
    background: rgba(255, 255, 255, 0.12);
    color: rgba(216, 189, 255, 0.55);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 12px */
    width: px2rem(245);
  }
}

.mt-33 {
  margin-top: px2rem(33);
}

.t-title {
  margin-top: px2rem(45);
  width: px2rem(351);
}
.t-desc {
  display: inline-flex;
  padding: px2rem(10);
  justify-content: center;
  align-items: center;
  gap: px2rem(10);
  width: px2rem(294);
  margin-top: px2rem(28);
  border-radius: px2rem(6);
  background: rgba(255, 255, 255, 0.12);
  color: #d5b8ff;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(13);
  font-style: normal;
  font-weight: 500;
  line-height: 100%; /* px2rem(13) */
}

.not-start {
  display: inline-flex;
  padding: px2rem(14) px2rem(12);
  justify-content: center;
  align-items: center;
  border-radius: px2rem(6);
  border: 1px solid #513776;
  color: #513776;
  text-align: center;
  font-family: Gilroy;
  font-size: px2rem(18);
  font-style: normal;
  font-weight: 500;
  line-height: 100%; /* 18px */
  margin: px2rem(45) 0;
}
</style>
