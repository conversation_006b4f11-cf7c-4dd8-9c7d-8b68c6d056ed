<template>
  <div class="rule">
    <header-bar back-icon-show :show-title="false" close-type="back" />
    <div class="tabs">
      <div
        class="tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        {{ $t("singer.rules_rewards_title") }}
      </div>
      <gap :gap="6"></gap>
      <div
        class="tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        {{ $t("singer.rules_title") }}
      </div>
    </div>

    <RuleRewards v-show="current === 0"></RuleRewards>
    <RuleContent v-show="current === 1"></RuleContent>
  </div>
</template>
<script setup>
import { ref } from "vue";
import RuleRewards from "./components/RuleRewards.vue";
import RuleContent from "./components/RuleContent.vue";

const current = ref(0);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule {
  min-height: 100vh;
  background: #230253;
  display: flex;
  flex-direction: column;
  align-items: center;

  .tabs {
    display: flex;
    width: px2rem(342);
    height: px2rem(52);
    display: inline-flex;
    padding: px2rem(4);
    align-items: center;
    border-radius: px2rem(31);
    background: rgba(255, 255, 255, 0.2);

    .tab {
      width: px2rem(164);
      height: px2rem(44);
      flex-shrink: 0;
      border-radius: px2rem(50);
      border: px2rem(2) solid #574671;
      background: linear-gradient(
        180deg,
        #39008b 0%,
        #39008b 34.07%,
        #18003b 100%
      );

      color: #e3d0ff;
      text-align: center;
      text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
      font-family: Gilroy;
      font-size: px2rem(20);
      font-style: normal;
      font-weight: 700;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      transition: all 0.2s linear;
    }

    .active {
      border: px2rem(2) solid #fff;
      background: linear-gradient(
        180deg,
        #ffa798 0%,
        #cd1ff6 47.12%,
        #6600f5 83.17%
      );
      box-shadow: 0px px2rem(-4) px2rem(4) 0px rgba(44, 2, 104, 0.64) inset,
        0px px2rem(3) px2rem(3) 0px rgba(255, 255, 255, 0.66) inset;
    }
  }
}
</style>
