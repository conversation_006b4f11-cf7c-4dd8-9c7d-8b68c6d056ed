<template>
  <header-bar is-scroll-change-bg
   :title="$t('common.common_poster')"
    closeType="close"
    v-if="lang == 'en'"
   />
  <div class="content">
    <img :src="imgSrc" alt="" />
  </div>
</template>

<script setup>
import { getCurrentInstance,ref,onMounted } from 'vue'
import pt from "@/assets/images/activity/pt-poster.png"
import en from "@/assets/images/activity/en-poster.png"
import tr from "@/assets/images/activity/tr-poster.png"
import ar from "@/assets/images/activity/ar-poster.png"
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t
const { proxy } = getCurrentInstance()

const lang = proxy.$languageFile
const imgSrc = ref(en)

onMounted(() => {
  if(lang === 'pt') imgSrc.value = pt
  else if(lang === 'tr') imgSrc.value = tr
  else if(lang === 'ar') imgSrc.value = ar 
  else imgSrc.value = en
})

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';
.content {
  img {
    width: 100vw;
    display: block;
  }
}
</style>
