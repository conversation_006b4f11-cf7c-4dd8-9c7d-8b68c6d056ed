{"event_name": "Raja PK", "event_time": "<PERSON><PERSON> 29 <PERSON><PERSON> pukul 00:00:00 hing<PERSON> 11 <PERSON><PERSON><PERSON> pukul 23:59:59 ({0})", "first_cycle": "29 Juli 00:00:00 – 4 <PERSON>gustus 23:59:59 ({0})", "second_cycle": "5 Agustus 00:00:00 – 11 Agustus 23:59:59 ({0})", "task": "Tugas", "daily_tasks": "<PERSON><PERSON>", "daily_task_frequency": "Dapat diselesaikan sekali per hari", "weekly_tasks": "Tugas <PERSON>", "weekly_task_frequency": "Hanya bisa diselesaikan sekali per siklus", "task_personal_pk_1000": "<PERSON>lai PK kamu mencapai 1000", "task_win_pk_1000": "Menang PK (total nilai PK ≥ 1000)", "task_pk_contribution_1000": "<PERSON>lai kontribusi PK mencapai 1000", "task_room_pk_10min": "<PERSON><PERSON>mu<PERSON> (durasi PK ≥ 10 menit, nilai PK total > 0)", "task_room_pk_value_5000": "<PERSON><PERSON> PK ruanganmu mencapai 5000", "task_room_pk_users_5": "<PERSON><PERSON><PERSON> pen<PERSON> dalam PK ruanganmu ≥ 5", "task_personal_pk_50k": "<PERSON>lai PK kamu mencapai 50.000", "task_personal_pk_100k": "<PERSON>lai PK kamu mencapai 100.000", "task_personal_pk_200k": "Nilai PK kamu mencapai 200.000", "task_room_pk_100k": "Nilai PK ruanganmu mencapai 100.000", "task_room_pk_200k": "Nilai PK ruanganmu mencapai 200.000", "task_room_pk_500k": "Nilai PK ruanganmu mencapai 500.000", "task_pk_contribution_50k": "<PERSON>lai kontribusi PK mencapai 50.000", "task_pk_contribution_100k": "Nilai kontribusi PK mencapai 100.000", "task_pk_contribution_200k": "Nilai kontribusi PK mencapai 200.000", "go": "<PERSON><PERSON>", "claim": "<PERSON><PERSON><PERSON>", "claimed": "<PERSON><PERSON>", "congratulations": "Selamat!", "task_reward_sent": "<PERSON><PERSON> tugas telah dikirim ke Ransel kamu", "ok": "OK", "user_ranking": "Peringkat Pengguna", "pk_value_ranking": "<PERSON><PERSON><PERSON>", "pk_value_ranking_desc": "Peringkat ini menghitung total nilai PK pengguna dalam setiap periode. 10 pengguna teratas berkesempatan mendapatkan hadiah", "weekly_rewards": "<PERSON><PERSON>", "top_1_3_share": "Pengguna TOP1–10 akan berbagi hadiah berlian secara proporsional", "top_1": "TOP1", "top_2": "TOP2", "top_3": "TOP3", "pk_contribution_ranking": "Peringkat Kontribusi PK", "pk_contribution_ranking_desc": "Peringkat ini menghitung total nilai PK yang dikontribusikan oleh pengguna dalam setiap periode dan 10 kontributor teratas akan menerima hadiah", "room_ranking": "<PERSON><PERSON><PERSON>", "room_ranking_desc": "Peringkat ini menghitung total nilai PK dari ruangan Anda dalam setiap siklus dan 10 pemilik ruangan teratas akan mendapatkan hadiah", "room_top_1_3_share": "Pemilik ruangan TOP1–10 akan berbagi hadiah secara proporsional", "rules": "<PERSON><PERSON><PERSON>", "rewards": "<PERSON><PERSON>", "task_rewards": "<PERSON><PERSON>", "task_rewards_desc": "<PERSON><PERSON><PERSON> men<PERSON><PERSON> tug<PERSON>, <PERSON><PERSON> harus kembali ke halaman Event untuk meng<PERSON><PERSON> hadiah", "event_rules_label": "Waktu Event", "first_cycle_label": "<PERSON><PERSON><PERSON>", "second_cycle_label": "Sik<PERSON>", "task_rewards_rule": "2. <PERSON><PERSON>: <PERSON><PERSON><PERSON><PERSON> tug<PERSON>, <PERSON><PERSON> ha<PERSON> kembali ke halaman Event untuk mengklaim hadiah.\nTugas Harian: Dapat diselesaikan sekali per hari.\nTugas Mingguan: <PERSON><PERSON> dapat diselesaikan sekali per siklus", "ranking_rewards": "<PERSON><PERSON>", "ranking_rewards_desc": "<PERSON><PERSON> per<PERSON> akan didistribusikan ke ransel pengguna pemenang setelah Event berakhir.", "feedback": "Jika Anda memiliki masukan event ini, silakan klik tombol di bagian atas halaman untuk memberikan saran berharga <PERSON>a", "prize_pool_description_title": "<PERSON><PERSON><PERSON><PERSON>", "prize_pool_description_content_1": "Persentase tertentu dari nilai PK semua pengguna akan masuk ke dalam kumpulan hadiah berlian. Semakin tinggi nilai <PERSON>, semakin besar pula kumpulan hadiahnya. Pengguna TOP1–10 akan berbagi hadiah berlian secara proporsional", "prize_pool_description_content_2": "Persentase tertentu dari semua nilai PK kamar akan masuk ke dalam kumpulan hadiah berlian. Se<PERSON><PERSON> tinggi nilai <PERSON>, semakin besar kumpulan hadiahnya. Pemilik ruangan TOP1–10 akan berbagi hadiah secara proporsional."}