<template>
  <div class="user-ranking-block">
    <div class="tabs">
      <div
        class="tab"
        :class="current === 0 ? 'active' : ''"
        @click="current = 0"
      >
        <span>{{ $t("pk_king.pk_value_ranking") }}</span>
      </div>
      <gap :gap="4"></gap>
      <div
        class="tab"
        :class="current === 1 ? 'active' : ''"
        @click="current = 1"
      >
        <span>{{ $t("pk_king.pk_contribution_ranking") }}</span>
      </div>
    </div>
    <div class="desc">
      {{
        current === 0
          ? $t("pk_king.pk_value_ranking_desc")
          : $t("pk_king.pk_contribution_ranking_desc")
      }}
    </div>

    <div class="rank-count-down">
      <CountDown
        :left-time="
          (activityInfo?.week_countdown ?? 0) - activityInfo?.requestTime
        "
      ></CountDown>
    </div>

    <template v-if="current === 0">
      <div class="reward-box">
        <GoldBox>
          <div class="box-title">{{ $t("pk_king.weekly_rewards") }}</div>
          <div class="box-desc">
            {{ $t("pk_king.top_1_3_share") }}
          </div>
          <div class="box-number" @click="handleRuleDialog">
            <div class="coin">
              <gender-coin :gender="11" :size="34"></gender-coin>
            </div>
            <gap :gap="2"></gap>
            <div>{{ activityInfo?.pk_pool }}</div>
            <gap :gap="4"></gap>
            <div class="qa"></div>
          </div>
          <div class="rewards">
            <div class="rewards-top-1">
              <div class="top1-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top1 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-2">
              <div class="top2-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top2 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-3">
              <div class="top3-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top3 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-4">
              <div class="top4-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.pk_top?.top410 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <div class="rank">
        <Ranking
          :type="1"
          :desc="$t('egypt.gift_receiving_ranking_info')"
        ></Ranking>
      </div>
    </template>
    <template v-if="current === 1">
      <div class="reward-box">
        <GoldBox>
          <div class="box-title">{{ $t("pk_king.weekly_rewards") }}</div>
          <div class="rewards">
            <div class="rewards-top-1">
              <div class="top1-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top1 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-2">
              <div class="top2-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top2 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-3">
              <div class="top3-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top?.top3 ||
                  []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>

            <div class="rewards-top-4">
              <div class="top4-tag"></div>
              <div class="rewards-list">
                <template
                  v-for="(item, index) in rewordInfo?.contribution_top
                    ?.top410 || []"
                >
                  <div class="r">
                    <div class="r-cover">
                      <PrizeBox :rewards="item"></PrizeBox>
                    </div>
                    <div class="r-name">{{ item.reward_name }}</div>
                  </div>
                  <gap v-if="index > 0" :gap="2"></gap>
                </template>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
      <div class="rank">
        <Ranking
          :type="2"
          :desc="$t('egypt.gift_giving_ranking_info')"
        ></Ranking>
      </div>
    </template>
    <RuleDialog ref="ruleDialogRef"></RuleDialog>
  </div>
</template>
<script setup>
import { ref } from "vue";
import CountDown from "./CountDown.vue";
import Ranking from "./Ranking.vue";
import GoldBox from "./GoldBox.vue";
import PrizeBox from "./PrizeBox.vue";
import RuleDialog from "./RuleDialog.vue";
import { t } from "@/i18n/index.js";
const props = defineProps({
  activityInfo: {
    type: Object,
    default: () => ({}),
  },
  rewordInfo: {
    type: Object,
    default: () => ({}),
  },
});
const ruleDialogRef = ref();
const current = ref(0);

const handleRuleDialog = () => {
  ruleDialogRef.value?.open(
    t("pk_king.prize_pool_description_title"),
    t("pk_king.prize_pool_description_content_1")
  );
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.user-ranking-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: px2rem(8);
  padding: 0 px2rem(10);

  .tabs {
    display: flex;
    width: 100%;
    padding: 0 px2rem(4);
    display: flex;
    align-items: center;
    justify-content: center;

    .tab {
      width: px2rem(124);
      height: px2rem(36);
      padding: 0 px2rem(10);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/pk-king/button22.png");
      background-repeat: no-repeat;
      background-size: cover;

      color: #deb8ff;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(11);
      font-style: normal;
      font-weight: 500;
    }

    .tab.active {
      background-image: url("@/assets/images/activity/pk-king/button2.png");
      span {
        color: #ffe2a3;
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(11);
        font-style: normal;
        font-weight: 700;
      }
    }
  }

  .desc {
    display: inline-flex;
    padding: px2rem(10);
    margin-top: px2rem(12);
    width: px2rem(314);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(8);
    border: 0.5px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.16);
    color: #cb8ffe;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .rank-count-down {
    margin-top: px2rem(37);
  }

  .reward-box {
    width: 100%;
    margin-top: px2rem(52);
  }

  .box-title {
    margin-top: px2rem(41);
    text-align: center;
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: #5f0297;
    font-family: Gilroy;
    font-size: px2rem(26);
    font-style: italic;
    font-weight: 900;
    line-height: normal;

    background: linear-gradient(180deg, #fff 0%, #ffba24 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 0 px2rem(5);
  }

  .box-desc {
    width: px2rem(262);
    margin-top: px2rem(2);
    color: rgba(235, 157, 255, 0.65);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .box-number {
    margin-top: px2rem(4);
    text-align: center;
    text-shadow: 0px 0px px2rem(13) rgba(255, 195, 0, 0.7);
    font-family: Gilroy;
    font-size: px2rem(36);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    background: linear-gradient(
      91deg,
      #ffc300 33.4%,
      #fff 42.75%,
      #fc0 50.8%,
      #fff 58.39%,
      #ffae00 66.64%
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    .qa {
      width: px2rem(16);
      height: px2rem(16);
      margin-top: px2rem(-8);
      background-image: url("@/assets/images/activity/pk-king/qa.png");
      background-size: 100% 100%;
    }
  }

  .rewards {
    margin-top: px2rem(17);

    .rewards-top-1,
    .rewards-top-2,
    .rewards-top-3,
    .rewards-top-4 {
      width: px2rem(332);
      min-height: px2rem(118);
      padding-top: px2rem(12);
      margin-bottom: px2rem(16);
      flex-shrink: 0;
      border-radius: px2rem(8);
      border: 0.6px solid #ff0;
      background: rgba(255, 255, 255, 0.1);
      /* 边框1 */
      box-shadow: 0px 0px px2rem(24) 0px #ffe100 inset;

      display: flex;
      align-items: flex-start;

      .top1-tag {
        &::before {
          background-image: url("@/assets/images/activity/pk-king/top1-tag.png");
        }
        &::after {
          content: "TOP1";
          background: linear-gradient(
            96deg,
            #fb0 2.46%,
            #ffe6a3 31.48%,
            #fabb01 63.11%,
            #ffe9ad 98.59%
          );
        }
      }

      .top2-tag {
        &::before {
          background-image: url("@/assets/images/activity/pk-king/top2-tag.png");
        }
        &::after {
          content: "TOP2";
          background: linear-gradient(
            97deg,
            #729fff 1.66%,
            #c5d8ff 36.34%,
            #709cfb 66.74%,
            #aec8ff 100.47%
          );
        }
      }

      .top3-tag {
        &::before {
          background-image: url("@/assets/images/activity/pk-king/top3-tag.png");
        }
        &::after {
          content: "TOP3";
          background: linear-gradient(
            97deg,
            #c25e00 4.24%,
            #ff9e43 35.08%,
            #c15d01 64.57%,
            #ff830f 100.08%
          );
        }
      }

      .top1-tag,
      .top2-tag,
      .top3-tag {
        width: px2rem(54);
        height: px2rem(70);
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &::before {
          content: "";
          width: px2rem(32);
          height: px2rem(22);
          background-size: 100% 100%;
        }
        &::after {
          text-align: center;
          -webkit-text-stroke-width: 1px;
          -webkit-text-stroke-color: #3c0051;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 900;
          line-height: 100%; /* 14px */
          margin-top: px2rem(-5);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .rewards-list {
        display: flex;
        flex-wrap: wrap;
        flex-grow: 1;
      }

      .r {
        width: px2rem(87);
        padding-bottom: px2rem(12);
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-shrink: 0;
        .r-cover {
          width: px2rem(70);
          height: px2rem(70);
        }
        .r-name {
          margin-top: px2rem(6);
          color: #eb9dff;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(14);
          font-style: normal;
          font-weight: 500;
          line-height: 100%; /* 14px */
        }
      }
    }

    .rewards-top-2 {
      border: 0.6px solid #729fff;
      box-shadow: 0px 4px 24px 0px #729fff inset;
    }

    .rewards-top-3 {
      border: 0.6px solid #c25e00;
      box-shadow: 0px 4px 24px 0px #c25e00 inset;
    }

    .rewards-top-4 {
      border: 1px solid rgba(255, 255, 255, 0.7);
      box-shadow: unset;
      margin-bottom: px2rem(22);

      .top4-tag {
        width: px2rem(42);
        height: px2rem(42);
        background-image: url("@/assets/images/activity/pk-king/top4-10-tag.png");
        background-size: 100% 100%;
        margin-top: px2rem(14);
        margin-left: px2rem(13);
      }
    }
  }

  .rank {
    width: 100%;
    margin-top: px2rem(37);
  }
}

.rtl-html {
  .top4-tag {
    margin-right: px2rem(13) !important;
    margin-left: px2rem(0) !important;
  }
}

.lang-zh_cn,
.lang-zh_tw {
  .box-title,
  .top1-tag::after,
  .top2-tag::after,
  .top3-tag::after {
    -webkit-text-stroke-width: 0 !important;
  }
}
</style>
