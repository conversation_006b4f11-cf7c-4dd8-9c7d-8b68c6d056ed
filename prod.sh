#!/usr/bin/env sh

# 发生错误时终止
set -e

rm -rf siya

# 构建
npm run build

echo -e "\033[33;45m ========================== \033[0m"
echo -e "\033[33;45m === 当前为【PROD】环境 === \033[0m"
echo -e "\033[33;45m === 当前为【PROD】环境 === \033[0m"
echo -e "\033[33;45m === 当前为【PROD】环境 === \033[0m"
echo -e "\033[33;45m === 当前为【PROD】环境 === \033[0m"
echo -e "\033[33;45m === 当前为【PROD】环境 === \033[0m"
echo -e "\033[33;45m ========================== \033[0m"

scp -r siya root@8.219.202.212:/usr/local/nginx/html/
