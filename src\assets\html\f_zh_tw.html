<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="你的好友邀請你來Siya" />
    <meta property="og:description" content="下載聊天軟體就可以賺錢，我已經透過 SIYA 賺到了 50 美元啦～點擊連結下載並填寫邀請碼，就能領取新手福利喲～" />
    <meta property="og:image"
        content="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/invite/share-female.png" />
    <script async>
        try {
            const envMap = {
                1: 'https://server.siyatest.com/i/f',
                2: 'https://server-gray.siyachat.com/i/f',
                3: 'https://siyachat.com/i/f',
            }
            const url = location.href;
            const regex = /\?(.*)/;
            const match = url.match(regex);

            const urlParams = new URLSearchParams(window.location.search);
            const env = urlParams.get("t").substring(0, 1);
            const host = envMap[env];
            if (match && host) {
                const queryString = match[1];
                window.location.href = `${host}?${queryString}`
            }
        } catch (error) {
            console.error(error)
        }
    </script>
</head>

</html>