<template>
  <div class="pop-title">{{$t('guild.guild_date_picker')}}</div>
  <div class="tab-content">
    <div :class="{'tab-item':true,active:currentTabIdx===index && isStart===0}" v-for="(item,index) in tabList" :key="index" @click="chooseTabTime(index)">
      {{item.title}}
    </div>
  </div>
  <div class="pop-title" :style="`text-align: ${['ar'].includes(i18n.global.locale) ? 'right' : 'left'}`">{{$t('guild.guild_custom_date')}}</div>
  <div class="customize">
    <div :class="{'customize-item':true,active:isStart!=0&&(isStart==1 || (startTimeStr&&endTimeStr))}" @click="clickToSelectDate(1)">{{startTimeStr || $t('guild.common_start')}}</div>
    <div class="divider"></div>
    <div :class="{'customize-item':true,active:isStart!=0&&(isStart==2 || (startTimeStr&&endTimeStr))}" @click="clickToSelectDate(2)">{{endTimeStr || $t('guild.common_end')}}</div>
  </div>
  <van-button class="okbtn" type="primary" @click="checkDate">{{$t('common.common_comfirm')}}</van-button>
  <van-button class="resetBtn" type="text" @click="resetDate">{{$t('common.common_reset')}}</van-button>
  <van-popup position="bottom" v-model:show="showStartTimePicker" class="pop">
    <van-date-picker
      v-show="isStart===1"
      v-model="startTime"
      :show-toolbar="false"
      :max-date="new Date()" 
      type="date"
    />
    <van-date-picker
      v-show="isStart===2"
      v-model="endTime"
      type="date"
      :show-toolbar="false"
      :min-date="new Date(startTime?.join('-'))"
      :max-date="new Date()"  
    />
    <van-button class="okbtn" type="primary" @click="comfirmDate">{{$t('common.common_comfirm')}}</van-button>
  </van-popup>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick,computed,reactive } from 'vue'
import { i18n } from '@/i18n/index.js'
import {getTimeRange} from '@/utils/util.js'

const { proxy } = getCurrentInstance()
const t = i18n.global.t
const props = defineProps({
  
})
// 1今日2本周、3本月、4上周、5上月  6前30天
const tabList = [
  { title: t('guild.common_today'), value: 1 },
  { title: t('guild.common_this_week'), value: 2 },
  { title: t('guild.common_this_month'), value: 3 },
  { title: t('guild.common_last_week'), value: 4 },
  { title: t('guild.common_last_month'), value: 5 },
  { title: t('guild.common_last_30_day'), value: 6 },
]
const params = reactive({
  startTs: "",
  endTs: "",
})
const currentTabIdx = ref(5)

const showStartTimePicker = ref(false)
const now = new Date()
const year = now.getFullYear()
const month = String(now.getMonth() + 1).padStart(2, '0')
const day = String(now.getDate()).padStart(2, '0')
const startTime = ref( [year, month, day])
const endTime = ref( [year, month, day])
const startTimeStr = ref('')
const endTimeStr = ref('')
const isStart = ref(0)   // 0代表选择上面的tab, 1代表选择自定义的开始时间, 2代表选择自定义的结束时间
const dateStr = ref(`${t('guild.common_last_30_day')}`)
const emit = defineEmits(["confirm"]);

const chooseTabTime = (idx) => {
  currentTabIdx.value = idx
  isStart.value = 0
  startTimeStr.value = ''
  endTimeStr.value = ''
}
const clickToSelectDate = (val) => {   // 点击自定义选择的开始按钮或结束按钮
  showStartTimePicker.value = true
  isStart.value = val
}
const comfirmDate = () => {
  showStartTimePicker.value = false
  if(isStart.value === 1)startTimeStr.value = startTime.value.join('-')
  if(isStart.value === 2) endTimeStr.value = endTime.value.join('-')
}
const resetDate = () => {
  isStart.value = 0
  currentTabIdx.value = 5
  // checkDate()
}
const checkDate = () => {
  if(isStart.value === 0) {
    dateStr.value = tabList[currentTabIdx.value].title
    let value = tabList[currentTabIdx.value].value
    // 1今日2本周、3本月、4上周、5上月  6前30天
    let parameter = 0
    if(value === 1) {
      parameter = 1
    } else if(value === 2) {
      parameter = 3
    } else if(value === 3) {
      parameter = 5
    } else if(value === 4) {
      parameter = 4
    } else if(value === 5) {
      parameter = 6
    } else if(value === 6) {
      parameter = 8
    }
    params.timeType = parameter
  }
  else {
    if(!endTimeStr.value || !startTimeStr.value) {
      proxy.$toast(t('guild.guild_select_time'))
      return
    }else {
      dateStr.value = startTimeStr.value + ' ~ ' + endTimeStr.value
      params.startStr = startTimeStr.value
      params.endStr = endTimeStr.value
      params.timeType = 11
    }
  }
  emit('confirm',params,dateStr.value)
}

</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding-bottom: $gap20;
  background-color: $white;
}

main {
  position: relative;
  padding: 0 $gap16;
  .pop {
    padding: px2rem(16);
    border-radius: px2rem(26) px2rem(26) 0 0;
    padding-bottom: px2rem(32);
    .pop-title {
      font-size: $fontSize17;
      font-weight: 700;
      text-align: center;
      margin: px2rem(16) 0;
    }
    .tab-content {
      margin-top: px2rem(16);
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .tab-item {
        width: 49%;
        font-size: px2rem(16);
        color: $fontColorB4;
        background-color: $bgColorB6;
        font-weight: normal;
        margin-bottom: px2rem(10);
        text-align: center;
        padding: px2rem(10) 0;
        border-radius: px2rem(8);
        box-sizing: border-box;
        border: 1px solid transparent;
      }
    }
    .customize {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .customize-item {
        width: px2rem(159);
        padding: px2rem(10) 0;
        background-color: $bgColorB6;
        color: $fontColorB4;
        font-size: px2rem(16);
        text-align: center;
        border-radius: px2rem(8);
        box-sizing: border-box;
      }
      .divider {
        width: px2rem(16);
        height: px2rem(1);
        background-color: #D9D9D9;
      }
    }
    .okbtn {
      width: 100%;
      margin-top: px2rem(16);
      font-weight: $fontWeightBold;
    }
    .resetBtn {
      width: 100%;
      margin-top: px2rem(12);
      font-weight: $fontWeightBold;
      background-color: #F8F7FF;
      color: $mainColor;
    }
  }
}
.active {
  border: 1px solid $mainColor !important;
  color: $mainColor !important;
  background-color: #F8F7FF!important;
  box-sizing: border-box;
}

</style>
