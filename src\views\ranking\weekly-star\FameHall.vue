<template>
  <div class="app-container flex">
    <header>
      <header-bar fontColor="#fff"> </header-bar>
      <img
        :src="getImageUrl('head-title.png')"
        alt=""
        class="weekly-star-img"
      />
      <img :src="getImageUrl('hall.png')" alt="" class="fame-hall-tag" />
    </header>
    <main>
      <div class="content">
        {{
          $t("ranking.ranking_weekly_star_milestone_rule5").replace("5.", "")
        }}
      </div>
      <div class="section" v-for="(item, idx) in resData" :key="idx">
        <div class="time">{{ item.start_time }} - {{ item.end_time }}</div>
        <div
          :class="`rank rank${rankIdx + 1}`"
          v-for="(rank, rankIdx) in item.user_top"
          :index="rankIdx"
        >
          <div class="avatar-wrap">
            <img
              :src="rank.avatar_frame"
              v-if="rank.avatar_frame"
              alt=""
              class="border-img"
            />
            <img :src="rank.avatar" alt="" class="avatar-img" />
          </div>
          <div class="name ellipsis">{{ rank.nickname }}</div>
          <div class="award-gift-wrap flex">
            <img :src="rank.gift_icon" alt="" />
            <gap :gap="2"></gap>
            {{ rank.num }}
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import rankingApi from "@/api/ranking.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const lang = proxy.$languageFile;

const resData = ref();
const fetchData = async () => {
  const result = await rankingApi.week_star_history();
  resData.value = result.data;
};

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/ranking/weekly-star/${lang}/${name}`,
    import.meta.url
  ).href;
}

fetchData();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: $white;
  flex-direction: column;
  background: url("@/assets/images/ranking/weekly-star/bg-head.jpg") top left
      no-repeat,
    url("@/assets/images/ranking/weekly-star/bg-common.png") top left repeat;
  background-color: #190a33;
  background-size: 100% auto;
  header {
    width: 100%;
    .weekly-star-img {
      width: px2rem(250);
      display: block;
      margin: 0 auto;
    }
    .fame-hall-tag {
      width: px2rem(140);
      display: block;
      margin: px2rem(-10) auto 0;
    }
  }
  main {
    width: 100%;
    padding: px2rem(30) px2rem(16);
    .content {
      // font-size: $fontSize13;
      // color: #fff;
      // width: px2rem(310);
      // margin: px2rem(10) auto;
      border-radius: px2rem(6);
      padding: px2rem(6);
      border: 0.5px solid #ccaeff4d;
      color: #c7a9ffb2;
      font-size: $fontSize10;
      margin: px2rem(12) auto;
      justify-content: flex-start;
      width: px2rem(314);
    }

    .section {
      height: px2rem(200);
      width: px2rem(350);
      color: $white;
      font-size: $fontSize13;
      background: url("@/assets/images/ranking/weekly-star/fame-hall-border.png")
        top left no-repeat;
      background-size: 100% auto;
      position: relative;
      margin-bottom: px2rem(20);
      .time {
        position: absolute;
        top: px2rem(27);
        left: 50%;
        transform: translateX(-50%);
        text-shadow: 0px 0px 6px #dd00ff;
      }
      .rank {
        position: absolute;
        width: px2rem(110);
        height: px2rem(161);
        z-index: 100;
        top: px2rem(50);
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;

        .avatar-wrap {
          width: px2rem(70);
          height: px2rem(70);
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          .border-img {
            position: absolute;
            top: 0;
            left: 0;
            width: px2rem(70);
            height: px2rem(70);
            z-index: 100;
          }
          .avatar-img {
            width: px2rem(50);
            height: px2rem(50);
            border-radius: 50%;
            object-fit: cover;
          }
        }
        .name {
          font-weight: 700;
          margin-top: px2rem(-5);
          max-width: px2rem(90);
        }
        .award-gift-wrap {
          font-weight: $fontWeightBold;
          text-shadow: 0px 0px 6px #dd00ff;
          & > img {
            width: px2rem(18);
            height: px2rem(18);
          }
        }
      }
      .rank1 {
        padding-top: px2rem(40);
        top: px2rem(10);
        left: 50%;
        z-index: 1000;
        transform: translateX(-50%);
      }
      .rank2 {
        left: px2rem(30);
      }
      .rank3 {
        right: px2rem(30);
      }
    }
  }
}
</style>
