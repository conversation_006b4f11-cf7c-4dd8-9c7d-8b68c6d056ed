<template>
  <div
    class="app-container flex"
    @click="downloadOrAdjust"
    v-track:click
    trace-key="fb_share_h5_click"
    :track-params="JSON.stringify(pointData('PH_落地页2'))"
  >
    <img
      src="@/assets/images/landing-page/version2/title.png"
      alt=""
      class="title"
      v-track:exposure
      trace-key="fb_share_h5_expo"
      :track-params="JSON.stringify(pointData('PH_落地页2'))"
    />
    <main>
      <section v-for="(item, idx) in list" :key="idx">
        <div class="info flex">
          <div class="distance-wrap">
            <img
              src="@/assets/images/landing-page/version2/location.png"
              alt=""
            />
            <gap :gap="1"></gap>
            <span>{{ item.distance }}</span>
          </div>
          <div class="dot"></div>
        </div>
        <van-swipe
          class="picture-wrap isolated"
          :autoplay="currentIndex === idx ? 3000 : 0"
          :touchable="false"
        >
          <van-swipe-item
            v-for="(subItem, subIdx) in item.pictrue"
            :key="subIdx"
          >
            <img
              :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/landing-page/version2/${subItem}.png`"
              alt=""
              class="picture"
            />
          </van-swipe-item>
        </van-swipe>
        <img
          src="@/assets/images/landing-page/version2/call-btn.png"
          alt=""
          class="free-btn"
        />
      </section>
    </main>
    <footer>
      <div class="download-btn">Download</div>
    </footer>
  </div>
</template>

<script setup>
import { onUnmounted, ref } from "vue";
import { downloadOrAdjust, pointData } from "./utils.js";

const currentIndex = ref(0);
let timer = setInterval(() => {
  if (currentIndex.value >= 3) {
    currentIndex.value = 0;
  } else {
    currentIndex.value++;
  }
}, 9000);

onUnmounted(() => {
  clearInterval(timer);
  timer = null;
});

function getImageUrl(name) {
  return new URL(
    `../../assets/images/landing-page/version2/${name}`,
    import.meta.url
  ).href;
}

const list = ref([
  {
    pictrue: ["picture1", "picture2", "picture3"],
    distance: "2.8km",
  },
  {
    pictrue: ["picture4", "picture5", "picture6"],
    distance: "3.2km",
  },
  {
    pictrue: ["picture7", "picture8", "picture9"],
    distance: "850m",
  },
  {
    pictrue: ["picture10", "picture11", "picture12"],
    distance: "1.2km",
  },
  {
    pictrue: ["picture13"],
    distance: "4.1km",
  },
  {
    pictrue: ["picture14"],
    distance: "1.1km",
  },
]);
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: url("@/assets/images/landing-page/version2/bg.jpg") top left
    no-repeat;
  background-color: #fc4773;
  background-size: 100% auto;

  padding: px2rem(37) 0 px2rem(70);
  flex-direction: column;
  flex-wrap: nowrap;
  .title {
    height: px2rem(90);
  }
  main {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: px2rem(34) px2rem(22);
    section {
      position: relative;
      margin-bottom: px2rem(40);
      .info {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 100;
        justify-content: space-between;
        padding: px2rem(8);
        .distance-wrap {
          border-radius: px2rem(4);
          background: #00000080;
          font-size: $fontSize15;
          color: $white;
          height: px2rem(21);
          @include centerL;
          padding: 0 px2rem(4);
          img {
            width: px2rem(12);
            height: px2rem(12);
          }
        }
        .dot {
          width: px2rem(10);
          height: px2rem(10);
          border-radius: px2rem(10);
          background: #09ff00;
        }
      }
      .picture-wrap {
        width: px2rem(164);
        height: px2rem(208);
        border-radius: px2rem(16);
        overflow: hidden;
        .picture {
          width: 100%;
          object-fit: cover;
        }
      }
      .free-btn {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translate(-50%, 50%);
        width: px2rem(120);
        height: px2rem(42);
      }
    }
  }
  footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 101;
    padding: px2rem(60) px2rem(16) px2rem(30);
    background: linear-gradient(
      180deg,
      rgba(255, 71, 113, 0) 0%,
      #ff4771 40.25%
    );

    .download-btn {
      width: 100%;
      height: px2rem(54);
      line-height: px2rem(54);
      border-radius: px2rem(54);
      color: #fff;
      font-size: px2rem(20);
      font-weight: $fontWeightBold;
      text-align: center;
      background: linear-gradient(90deg, #5f56ed 0%, #d900ff 100%);
      animation: spin 0.8s ease-in-out infinite;
    }
  }
}
@keyframes spin {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
</style>
