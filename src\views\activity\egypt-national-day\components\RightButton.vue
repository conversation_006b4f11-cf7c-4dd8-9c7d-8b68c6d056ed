<template>
  <div
    class="right-button"
    :style="{
      top: `${props.top}px`,
    }"
  >
    <div class="right-button-bg"></div>
    <div class="right-button-text">{{ text }}</div>
  </div>
</template>
<script setup>
import { computed } from "vue";
import { getCurrentInstance } from "vue";

const { proxy } = getCurrentInstance();
const props = defineProps({
  text: {
    type: String,
  },
  top: {
    type: Number,
    default: 128,
  },
});

const fixedTop = computed(() => proxy.$pxToRemPx(props.top));
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.right-button {
  height: px2rem(30);
  width: fit-content;
  position: fixed;
  right: 0;
  z-index: 9999;
  .right-button-bg {
    display: flex;
    align-items: center;
    position: absolute;
    inset: 0;
    width: initial;
    &::before {
      content: "";
      height: px2rem(30);
      width: px2rem(44);
      background-image: url("@/assets/images/activity/recharge-ranking/right-btn-bg.png");
      background-size: px2rem(44) px2rem(30);
      background-repeat: no-repeat;
      flex-shrink: 0;
    }
    &::after {
      content: "";
      height: px2rem(30);
      background-image: url("@/assets/images/activity/recharge-ranking/right-btn-fill.png");
      background-size: px2rem(44) px2rem(30);
      background-repeat: repeat-x;
      flex-shrink: 0;
      transform: scaleX(-1);
      margin-left: -1px;
      margin-right: -1px;
      flex-grow: 1;
    }
  }
  .right-button-text {
    background-image: url("@/assets/images/activity/recharge-ranking/right-btn-fill.png");
    background-size: px2rem(12) px2rem(30);
    background-repeat: repeat;
    position: relative;
    font-family: Gilroy;
    font-size: px2rem(11);
    height: px2rem(30);
    font-style: normal;
    font-weight: 700;
    text-align: center;
    padding: 0 px2rem(4) 0 px2rem(16);
    line-height: px2rem(30);
    letter-spacing: -0.583px;
    background: linear-gradient(180deg, #fff 19.85%, #ed9a00 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: 0;
    .right-button-bg {
      &::before {
        transform: scaleX(-1);
      }
    }
    .right-button-text {
      padding: 0 px2rem(16) 0 px2rem(4);
    }
  }
}
</style>
