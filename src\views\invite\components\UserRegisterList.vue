<template>
  <div class="list">
    <div class="item" v-for="(item, index) in dataList" :key="index">
      <user-cell
        :nickname="item.user_info?.nickname ?? '-'"
        :avatar="item.user_info?.avatar"
        :gender="item.user_info?.gender ?? 1"
        :userId="item.user_info?.account ?? '-'"
        :age="item.user_info?.age ?? 0"
        :desc="item.register_time ?? '-'"
        :show-coin="false"
      ></user-cell>
    </div>
    <load-more v-if="dataList.length > 0" @loadMore="load()"></load-more>
    <empty
      v-if="dataList.length === 0 && loadState === STATE_NOMORE"
      :tips="$t('common.common_content_yet')"
    ></empty>
  </div>
</template>
<script setup>
import { ref } from "vue";
import UserCell from "./UserCell.vue";
import LoadMore from "./LoadMore.vue";
import inviteApi from "@/api/invite.js";
const emits = defineEmits(["load"]);
const props = defineProps({
  startDate: {
    type: String,
  },
});
const STATE_MORE = "more",
  STATE_LOADING = "loading",
  STATE_NOMORE = "nomore";
const pageIndex = ref(1);
const pageSize = ref(20);
const loadState = ref(STATE_MORE); // more loading nomore
const dataList = ref([]);
const load = async (refreh) => {
  if (loadState.value === STATE_LOADING) {
    return;
  }
  if (refreh === true) {
    pageIndex.value = 1;
    dataList.value = [];
    loadState.value = STATE_MORE;
  }
  if (loadState.value === STATE_NOMORE) {
    return;
  }
  try {
    const res = await inviteApi.user_register_list({
      page: pageIndex.value,
      size: pageSize.value,
      start_time: props.startDate,
    });
    if (res.code === 200) {
      const data = res.data?.list || [];
      const count = res.data?.count || 0;
      dataList.value = [...dataList.value, ...data];
      pageIndex.value++;
      if (count < pageSize.value) {
        loadState.value = STATE_NOMORE;
      } else {
        loadState.value = STATE_MORE;
      }
    } else {
      loadState.value = STATE_NOMORE;
    }
    emits("load", props.startDate);
  } catch (e) {
    loadState.value = STATE_NOMORE;
    emits("load", props.startDate);
  }
};

defineExpose({
  load,
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.list {
  min-height: px2rem(350);
  padding-top: px2rem(16);
  .item {
    margin-bottom: px2rem(20);
  }
}
</style>
