<template>
  <div class="app-container flex">
    <main>
      <div class="ban-title">
        {{ $t('base_total.banning_detail_title', [config.result]) }}
      </div>
      <div class="ban-detail">
        <div class="ban-cell">
          <div class="cell-title">
            {{ $t('base_total.banning_detail_banning_reason') }}
          </div>
          <div class="cell-desc">
            {{ config.reason }}
          </div>
        </div>
        <div class="ban-cell">
          <div class="cell-title">
            {{ $t('base_total.banning_detail_banning_time') }}
          </div>
          <div class="cell-desc">{{ config.start_time }}</div>
        </div>
        <div class="ban-cell">
          <div class="cell-title">
            {{ $t('base_total.banning_detail_banning_end_time') }}
          </div>
          <div class="cell-desc">{{ config.end_time }}</div>
        </div>
      </div>
    </main>
<!--    <div class="tips">-->
<!--      Questions about the ticket <span>File a complaint</span>-->
<!--    </div>-->
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { onMounted, ref } from 'vue'
import baseForAppApi from '@/api/baseforapp.js'

const route = useRoute()
const { type = 0, userId = 0, deviceId = '' } = route.query;

const config = ref({})
const getDetail = async () => {
  const response = await baseForAppApi.ban_type({
    type,
    user_id: userId,
    device_id: deviceId,
  })
  if (response.code === 200) {
    config.value = response.data
  }
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  flex-direction: column;
  height: 100vh;
}

main {
  width: 100%;
  padding: $gap12 $gap16;
}

.ban-title {
  padding: $gap8;
  margin-bottom: px2rem(11);
  font-size: px2rem(13);
  font-weight: $fontWeightBold;
  line-height: px2rem(16);
  color: $subColorRed;
  background: #FFF2F5;
  border-radius: $radius4;
}

.ban-detail {
  padding: 0 $gap12;
  background-color: $white;
  border-radius: $gap16;

  .ban-cell {
    padding: $gap12 0;

    &:not(:last-child) {
      border-bottom: 0.5px solid #E6E6E6;
    }
  }

  .cell-title {
    font-size: px2rem(13);
    font-weight: $fontWeightBold;
    line-height: px2rem(16);
    margin-bottom: $gap8;
  }

  .cell-desc {
    font-size: px2rem(13);
    line-height: px2rem(16);
    color: $fontColorB2;
  }
}

.tips {
  padding: px2rem(10) 0;
  margin-top: auto;
  font-size: px2rem(13);
  line-height: px2rem(16);
  color: $fontColorB4;
  text-align: center;

  span {
    color: $subColorBlue;
    text-decoration: underline;
  }
}
</style>
