<template>
  <div class="app-container flex daily-ranking">
    <header>
      <header-bar
        :title="$t('ranking.ranking_detail')"
        font-color="#fff"
        close-type="close"
        fixedBgColor="#190f2c"
        closeType="back"
      />
    </header>
    <div class="content">
      <div class="rule-title flex">
        <img
          class="front-dot"
          src="@/assets/images/daily-ranking/dot-title-left.svg"
        />
        <span>{{ $t("ranking.ranking_cycle") }}</span>
        <img
          class="back-dot"
          src="@/assets/images/daily-ranking/dot-title-right.svg"
        />
      </div>
      <div class="rule">
        <div
          class="rule-item"
          v-for="(item, index) in $t('ranking.ranking_cycle_detail').split(
            '\n'
          )"
          :key="index"
        >
          <span>·</span>
          <gap :gap="4" />
          <span>{{ item.slice(3) }}</span>
        </div>
      </div>
      <div class="rule-title flex">
        <img
          class="front-dot"
          src="@/assets/images/daily-ranking/dot-title-left.svg"
        />
        <span>{{ $t("ranking.ranking_reward") }}</span>
        <img
          class="back-dot"
          src="@/assets/images/daily-ranking/dot-title-right.svg"
        />
      </div>
      <div class="rule">
        <div class="rule-item">{{ $t("ranking.ranking_reward_detail") }}</div>
      </div>
      <div class="rule-title flex">
        <img
          class="front-dot"
          src="@/assets/images/daily-ranking/dot-title-left.svg"
        />
        <span>{{ $t("ranking.ranking_title1") }}</span>
        <img
          class="back-dot"
          src="@/assets/images/daily-ranking/dot-title-right.svg"
        />
      </div>
      <div class="reward-wrap">
        <div class="reward-item flex">
          <div class="cell-wrap sort flex">
            {{ $t("ranking.ranking_ranking") }}
          </div>
          <div class="cell-wrap title flex">{{ $t("ranking.ranking_ou") }}</div>
          <div class="cell-wrap title flex">{{ $t("ranking.ranking_tu") }}</div>
        </div>
        <div
          class="reward-item flex"
          v-for="(item, index) in rewardList.day.lucky_reward"
          :key="index"
        >
          <div
            class="cell-wrap sort flex"
            :style="{ color: levelColor(index + 1) }"
          >
            {{ item.rank }}
          </div>
          <div class="cell-wrap flex">
            <img :src="rewardList.day.lucky_reward[index]?.icon" alt="" />
            <div class="reward-time">
              {{ rewardList.day.lucky_reward[index]?.duration }}
            </div>
          </div>
          <div class="cell-wrap flex">
            <img :src="rewardList.day.rick_reward[index]?.icon" alt="" />
            <div class="reward-time">
              {{ rewardList.day.rick_reward[index]?.duration }}
            </div>
          </div>
        </div>
      </div>
      <div class="rule-title flex">
        <img
          class="front-dot"
          src="@/assets/images/daily-ranking/dot-title-left.svg"
        />
        <span>{{ $t("ranking.ranking_title2") }}</span>
        <img
          class="back-dot"
          src="@/assets/images/daily-ranking/dot-title-right.svg"
        />
      </div>
      <div class="reward-wrap">
        <div class="reward-item flex">
          <div class="cell-wrap sort flex">
            {{ $t("ranking.ranking_ranking") }}
          </div>
          <div class="cell-wrap title flex">{{ $t("ranking.ranking_ou") }}</div>
          <div class="cell-wrap title flex">{{ $t("ranking.ranking_tu") }}</div>
        </div>
        <div
          class="reward-item flex"
          v-for="(item, index) in rewardList.week.lucky_reward"
          :key="index"
        >
          <div
            class="cell-wrap sort flex"
            :style="{ color: levelColor(index + 1) }"
          >
            {{ item.rank }}
          </div>
          <div class="cell-wrap flex">
            <img :src="rewardList.week.lucky_reward[index]?.icon" alt="" />
            <div class="reward-time">
              {{ rewardList.week.lucky_reward[index]?.duration }}
            </div>
          </div>
          <div class="cell-wrap flex">
            <img :src="rewardList.week.rick_reward[index]?.icon" alt="" />
            <div class="reward-time">
              {{ rewardList.week.rick_reward[index]?.duration }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { i18n } from "@/i18n/index.js";
import rankingApi from "@/api/ranking.js";

const t = i18n.global.t;
const rewardList = ref({
  day: { lucky_reward: [], rick_reward: [] },
  week: { lucky_reward: [], rick_reward: [] },
});

const levelColor = (val) => {
  if (val === 1) return "#FFC26A";
  if (val === 2) return "#ff8b00";
  if (val === 3) return "#968881";
};

const getReward = async () => {
  const res = await rankingApi.rank_reward();
  let list = res.data.rank || {};
  // 处理 day 数据
  const dayData = list.day || {};
  rewardList.value.day.lucky_reward = dayData.lucky_reward || [];
  rewardList.value.day.rick_reward = dayData.rick_reward || [];

  // 处理 week 数据
  const weekData = list.week || {};
  rewardList.value.week.lucky_reward = weekData.lucky_reward || [];
  rewardList.value.week.rick_reward = weekData.rick_reward || [];
};
onMounted(() => {
  getReward();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

$pageBgColor: #190f2c;

$borderStyle: 0.5px solid #ffffff1f;
header {
  height: 100px;
}
.app-container {
  flex-direction: column;
  background-color: $pageBgColor;
  .content {
    width: 100vw;
    color: #fff;
    font-size: $fontSize13;
    padding: 0 px2rem(20);
    .rule {
      margin-bottom: 16px;
      .rule-item {
        display: flex;
        font-family: Gilroy;
        font-style: normal;
        font-weight: 500;
      }
    }
    .rule-title {
      justify-content: center;
      margin-bottom: $gap4;
      font-weight: $fontWeightBold;
      text-align: center;

      @include fontSize17;

      .front-dot,
      .back-dot {
        width: px2rem(34);
        height: px2rem(8);
      }
      span {
        padding: 0 px2rem(16);
        max-width: 80%;
        display: inline-block;
      }
    }
    .reward-wrap {
      border-radius: px2rem(8);
      border: $borderStyle;
      margin: px2rem(16) 0 px2rem(32) 0;

      .reward-item {
        @include fontSize11;
        border-bottom: $borderStyle;
        font-weight: 500;
        .cell-wrap {
          flex: 1;
          height: px2rem(100);
          flex-direction: column;
          justify-content: center;
          border-left: $borderStyle;
          min-width: 0;
          img {
            width: px2rem(54);
            height: px2rem(54);
          }
          .reward-name {
            padding: px2rem(2) px2rem(4);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
          }
          .reward-time {
            color: #ffffff80;
            margin-top: px2rem(4);
          }
          &:first-child {
            border-left: none;
          }
        }
        .title {
          font-size: px2rem(14);
          font-weight: 700;
          width: 36%;
        }
        .sort {
          width: 28%;
          font-size: px2rem(14);
          flex: none;
          font-weight: 700;
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
