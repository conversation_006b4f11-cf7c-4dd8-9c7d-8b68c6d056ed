<template>
  <div dir="ltr" :id="'guide' + timeId" class="svga"></div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import SVGA from "svgaplayerweb";
const props = defineProps({
  url: {
    type: String,
  },
});
const timeId = ref(Math.floor(new Date().getTime() * Math.random())); // 使该图表保持唯一id
const guideFn = () => {
  // 获取id的dom元素
  let player = new SVGA.Player(`#guide${timeId.value}`);
  let parser = new SVGA.Parser();

  parser.load(props.url, (videoItem) => {
    player.setVideoItem(videoItem);
    player.startAnimation();
  });
};
onMounted(() => {
  guideFn();
});
</script>
<style lang="scss" scoped>
.svga {
  width: 100%;
  height: 100%;
}
</style>
