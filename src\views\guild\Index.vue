<template>
  <div class="app-container page-guild-home">
    <div class="page-bg">
      <img class="header-img" src="@/assets/images/guild/bg-icon.png" alt="">
    </div>
    <header-bar is-scroll-change-bg close-type="close"/>
    <main>
      <!--      公会信息      -->
      <div class="guild-info flex">
        <avatar :size="72" :url="config.avatar" border-color="#EFEFFF"/>
        <gap :gap="8"/>
        <div class="detail">
          <div class="nickname ellipsis">{{ config.nickname }}</div>
          <div class="number flex">
            <span>{{ $t('guild.agency_ID') }}: {{ config.guild_account }}</span>
            <span class="line"></span>
            <span>{{ $t('guild.members') }}: {{ config.member_cnt }}</span>
          </div>
        </div>
      </div>
      <!--      邀请码      -->
      <div class="panel-wrap">
        <div class="panel-title">{{ $t('guild.invitation_code') }}</div>
        <div class="invite-code-card flex"
             @click="copyOne($event)"
        >
          <div class="invite-card-left">
            <div class="invite-code">{{ config.invite_code }}</div>
            <div class="invite-tips">
              {{ $t('guild.send_code_tips') }}
            </div>
          </div>
          <gap :gap="35"/>
          <div class="invite-button">{{ $t('guild.copy') }}</div>
        </div>
      </div>
      <!--      数据中心      -->
      <div class="panel-wrap data-center">
        <div class="panel-title">{{ $t('guild.data_center') }}</div>
        <div class="tab-list flex" ref="tabsContainer">
          <div class="tab"
              :class="{active: currentTabIdx === idx}"
              v-for="(tab, idx) in tabList" :key="idx"
              @click="handleChangeTab(idx)"
          >
           {{tab.title}}
          </div>
        </div>
        <div class="data-card-wrap flex">
          <div class="data-card">
            <div class="card-top flex">
              <coin-number class="card-count" :num="memberConfig.incr_cnt" :unit-size="24" plus />
              <gap :gap="2"/>
              <img class="card-icon" src="@/assets/images/icon/icon-person.svg" alt="">
            </div>
            <div class="data-card-title">{{ $t('guild.new_member') }}</div>
          </div>
          <gap :gap="12"/>
          <div class="data-card">
            <div class="card-top flex">
              <coin-number class="card-count" :num="memberConfig.income_total" :unit-size="24" plus />
              <gap :gap="2"/>
              <img class="card-icon" src="@/assets/images/common/diamond.png" alt="">
            </div>
            <div class="data-card-title">{{ $t('guild.member_income') }}</div>
          </div>
        </div>
        <div class="member-list">
          <div class="member-title-wrap flex">
            <div class="member-title">{{ $t('guild.my_members') }}</div>
            <div class="member-sort flex" @click="handleSortList">
              <span>{{ $t('guild.income') }}</span>
              <img class="card-icon" v-if="sort === 1" src="@/assets/images/icon/icon-sort-1.svg" alt="">
              <img class="card-icon" v-else src="@/assets/images/icon/icon-sort-2.svg" alt="">
            </div>
          </div>

          <van-list
              v-if="!isEmpty"
              class="list-wrap"
              v-model:loading="isLoading"
              :finished="finished"
              finished-text=""
              loading-text="loading..."
              :immediate-check="false"
              @load="getList"
          >
            <div>
              <div class="member-cell flex"
                   v-for="(cell, idx) in resultList" :key="idx"
                   @click="goUserDetail(cell.user_id)"
              >
                <div class="cell-left flex">
                  <avatar :size="56" :url="cell.avatar"/>
                  <gap :gap="6"/>
                  <div class="member-info">
                    <div class="member-name ellipsis">
                      <flag :flag="cell.flag" :size="12"></flag>
                      {{ cell.nickname }}
                    </div>
                    <div class="member-id">ID {{ cell.user_account }}</div>
                  </div>
                </div>
                <div class="cell-right flex">
                  <img class="card-icon" src="@/assets/images/common/diamond.png" alt="">
                  <gap :gap="2"/>
                  <coin-number class="card-number" :num="cell.income" :decimal="2" plus />
                </div>
              </div>
            </div>
          </van-list>
          <div class="empty flex" v-else>
            <img class="empty-img" src="@/assets/images/common/empty.svg" alt="">
            <div class="empty-tips">{{ $t('guild.list_empty_tips') }}</div>
            <div class="invite-code">{{ config.invite_code }}</div>
            <div class="copy-button" @click="copyOne($event)">{{ $t('guild.list_empty_button') }}</div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick } from 'vue'
import clipboard from "@/utils/clipboard";
import guildApi from '@/api/guild.js'
import { i18n } from '@/i18n/index.js'

const { proxy } = getCurrentInstance()
const t = i18n.global.t

const config = ref({
  avatar: '',
  nickname: '',
  guild_account: '',
  member_cnt: '',
  invite_code: '',
})
const getConfig = async () => {
  const response = await guildApi.my_guild()
  if (response.code === 200) {
    config.value = response.data || {}
  }
}


// 1今日2昨日3这周4最近30天
const tabList = [
  { title: t('guild.today'), value: 1 },
  { title: t('guild.yesterday'), value: 2 },
  { title: t('guild.this_week'), value: 3 },
  { title: t('guild.last_30_days'), value: 4 },
]
const currentTabIdx = ref(0)
const handleChangeTab = (idx) => {
  if (currentTabIdx.value === idx) return
  currentTabIdx.value = idx
  centerActiveTab(idx)
  resetList()
  getList()
}

const memberConfig = ref({
  incr_cnt: 0,
  income_total: 0,
  // member_list: [],
})
const resultList = ref([])
const curPage = ref(1)
const pageSize = 20
const isLoading = ref(false)
const isEmpty = ref(false)
const finished = ref(false)
// 1正序2倒序
const sort = ref(2)
const handleSortList = () => {
  resetList()
  sort.value = unref(sort) === 1 ? 2 : 1
  getList()
}

const getList = async () => {
  isLoading.value = true

  const params = {
    day: tabList[unref(currentTabIdx)].value,
    sort: unref(sort),
    page: unref(curPage),
    size: pageSize,
  }
  const response = await guildApi.member_list(params)
  if (response.code === 200) {
    memberConfig.value = response.data
    let list = response.data.member_list || []

    list = list.filter(i => resultList.value.findIndex(re => re.user_id === i.user_id))
    resultList.value = resultList.value.concat(list)

    finished.value = list.length < pageSize
    curPage.value += 1
    isEmpty.value = resultList.value.length === 0
  } else {
    finished.value = true
  }

  // 加载状态结束
  isLoading.value = false
}

const resetList = () => {
  curPage.value = 1
  finished.value = false
  isEmpty.value = false
  resultList.value = []
}

const copyOne = (event) => {
  const text = unref(config).invite_code
  const msg = t('guild.copy_success')
  clipboard(text, event, msg)
}

const goUserDetail = (userId) => {
  if (!userId) return
  proxy.$siyaApp('goUserDetail', { userId })
}

const tabsContainer = ref()
const centerActiveTab = (index) => {
  nextTick(() => {
    const tabElements = tabsContainer.value.children;
    if (tabElements[index]) {
      tabElements[index].scrollIntoView({
        behavior: 'smooth',
        inline: 'center'
      });
    }
  });
}

onMounted(() => {
  getConfig()
  getList()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.page-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: px2rem(390);
  height: px2rem(232);
  background: linear-gradient(0deg, rgba(130, 132, 236, 0) 10%, #8284EC 100%);

  .header-img {
    position: absolute;
    top: px2rem(60);
    right: 0;
    width: px2rem(101);
    height: px2rem(144);
  }
}

main {
  position: relative;
  padding: 0 $gap16;
}

.guild-info {
  margin-top: $gap12;
  margin-bottom: px2rem(20);

  .detail {
    flex: 1;

    .nickname {
      width: px2rem(270);
      margin-bottom: px2rem(4);
      font-size: px2rem(17);
      font-weight: $fontWeightBold;
      line-height: px2rem(21);
      color: $fontColorB0;
    }

    .number {
      font-size: $fontSize13;
      line-height: px2rem(16);
      color: $fontColorB2;

      .line {
        width: px2rem(1);
        height: px2rem(8);
        margin: 0 $gap8;
        background-color: $fontColorB3;
      }
    }
  }
}

.panel-wrap {
  padding: $gap12;
  margin-bottom: px2rem(14);
  border-radius: $radius12;
  background-color: $white;

  .panel-title {
    font-size: px2rem(17);
    font-weight: $fontWeightBold;
    line-height: px2rem(21);
    color: $fontColorB0;
  }
}

.invite-code-card {
  flex-wrap: nowrap;
  padding: $gap12;
  margin-top: $gap16;
  background-color: #F2F2F2;
  border-radius: $radius12;

  .invite-code {
    margin-bottom: px2rem(2);
    font-size: px2rem(24);
    font-weight: $fontWeightBold;
    line-height: px2rem(29);
    color: $subColorRed;
  }

  .invite-tips {
    font-size: $fontSize13;
    line-height: px2rem(16);
    color: $fontColorB2;
  }

  .invite-button {
    height: px2rem(36);
    padding: 0 $gap16;
    margin-left: auto;
    font-size: $fontSize13;
    font-weight: $fontWeightBold;
    line-height: px2rem(36);
    color: $white;
    background-color: $mainColor;
    border-radius: $radius10;
  }
}

.data-center {
  padding-left: 0;
  padding-right: 0;

  .panel-title,
  .data-card-wrap,
  .member-list {
    padding: 0 $gap12;
  }
}

.tab-list {
  flex-wrap: nowrap;
  overflow-x: scroll;
  padding: 0 $gap12;
  margin-top: $gap12;
  margin-bottom: $gap16;
  scroll-behavior: smooth;

  &::-webkit-scrollbar {
    display: none;
  }

  .tab {
    flex: 0 0 auto;
    min-width: px2rem(76);
    height: px2rem(30);
    padding: 0 $gap8;
    font-size: $fontSize13;
    line-height: px2rem(30);
    color: $fontColorB3;
    text-align: center;
    border: 1px solid #E6E6E6;
    border-radius: $radius8;

    &:not(:last-child) {
      margin-right: $gap8;
    }

    &.active {
      font-weight: $fontWeightBold;
      color: $mainColor;
      background: rgba($mainColor, .1);
      border: 1px solid rgba($mainColor, .2);
    }
  }
}

.data-card-wrap {
  align-items: stretch;
  margin-bottom: $gap16;

  .data-card {
    flex: 1;
    padding: $gap12 $gap4;
    background-color: $bgColorB6;
    border-radius: $gap8;
  }

  .card-top {
    flex-wrap: nowrap;
    justify-content: center;
    font-size: px2rem(32);
    font-weight: $fontWeightBold;
    line-height: px2rem(39);
    color: #1B1A1F;
  }

  .card-icon {
    width: px2rem(20);
    height: px2rem(20);
  }

  .data-card-title {
    font-size: $fontSize13;
    line-height: px2rem(16);
    text-align: center;
    color: $fontColorB3;
  }
}

.member-list {
  .member-title-wrap {
    justify-content: space-between;
    margin-bottom: $gap12;
  }

  .member-title {
    font-size: $fontSize15;
    font-weight: $fontWeightBold;
    color: $fontColorB0;
  }

  .member-sort {
    font-size: $fontSize13;
    line-height: px2rem(16);
    color: $mainColor;
  }

  .card-icon {
    width: px2rem(16);
    height: px2rem(16);
  }

  .member-cell {
    flex-wrap: nowrap;

    &:not(:last-child) {
      padding-bottom: $gap12;
      margin-bottom: $gap12;
      border-bottom: 0.5px solid #F2F2F2;
    }
  }

  .cell-left {
    flex-wrap: nowrap;
    margin-right: $gap4;
  }

  .member-name {
    max-width: px2rem(200);
    margin-bottom: $gap4;
    font-size: $fontSize15;
    font-weight: $fontWeightBold;
    color: $fontColorB0;
  }

  .member-id {
    font-size: px2rem(13);
    line-height: px2rem(16);
    color: $fontColorB3;
  }

  .cell-right {
    flex-wrap: nowrap;
    margin-left: auto;
  }

  .card-number {
    font-size: $fontSize15;
    font-weight: $fontWeightBold;
    line-height: px2rem(18);
    color: $fontColorB2;
    word-break: break-all;
  }
}

.empty {
  flex-direction: column;
  padding: px2rem(80) px2rem(17);

  .empty-img {
    width: px2rem(120);
    height: px2rem(120);
    margin-bottom: $gap8;
  }

  .empty-tips {
    margin-bottom: $gap20;
    font-size: $fontSize15;
    line-height: px2rem(20);
    text-align: center;
    color: $fontColorB2;
  }

  .invite-code {
    margin-bottom: $gap8;
    font-size: px2rem(24);
    font-weight: $fontWeightBold;
    line-height: px2rem(29);
    color: $subColorRed;
  }

  .copy-button {
    padding: $gap10 $gap12;
    font-size: $fontSize13;
    line-height: px2rem(16);
    font-weight: $fontWeightBold;
    color: $white;
    background-color: $mainColor;
    border-radius: $radius10;
  }
}
</style>
