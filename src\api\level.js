import request from '@/utils/request'

export default {
  wealth(query) {
    return request({
      url: '/client_web/user_level/wealth',
      method: 'get',
      params: query
    })
  },
  charm(query) {
    return request({
      url: '/client_web/user_level/charm',
      method: 'get',
      params: query
    })
  },
  monthly(query) {
    return request({
      url: '/client_web/charm_month/level',
      method: 'get',
      params: query
    })
  },
  // monthly  table
  getMonthlyTable(query) {
    return request({
      url: '/client_web/charm_month/prices',
      method: 'get',
      params: query
    })
  },
  // 获取note
  getNote(query) {
    return request({
      url: '/client_web/wc/wc_audit',
      method: 'get',
      params: query
    })  
  },
  // 获取用户信息
  getUserInfo(query) {
    return request({
      url: '/user/info',
      method: 'get',
      params: query
    })
  },
}
