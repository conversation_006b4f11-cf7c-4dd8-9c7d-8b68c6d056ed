<template>
  <div class="flex-bc">
    <div class="sub-title">
      {{ props.title }}
    </div>
    <slot></slot>
  </div>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.flex-bc {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sub-title {
  position: relative;
  background-image: url("@/assets/images/wheel-lottery/common/sub-title-bg.svg");
  background-position: left bottom;
  background-repeat: no-repeat;
  background-size: px2rem(77) px2rem(16);
  color: #000;
  font-family: <PERSON><PERSON>;
  font-size: px2rem(20);
  font-style: normal;
  font-weight: 700;
  line-height: normal;
  line-height: px2rem(20);
  height: px2rem(20);
  z-index: 3;
  text-align: left;
}
.rtl-html {
  .sub-title {
    background-position: right bottom;
  }
}
</style>
