<template>
  <div class="app-container page-anchor-center">
    <div class="page-bg">
      <img
        class="header-img"
        src="@/assets/images/anchor-center/bg.png"
        alt=""
      />
      <header-bar is-scroll-change-bg close-type="close" title="Anchor" />
      <div class="user-info">
        <img class="avatar" :src="userInfo.avatar || avatar"  />
        <div class="user-info-text">
          <div class="user-name">
            {{ userInfo.nickname }}
          </div>
          <div class="user-id">
            <img class="auth" src="@/assets/images/anchor-center/auth.png" />
            <gap :gap="4" />
            <div class="auth-text">
              <img class="heart" src="@/assets/images/anchor-center/heart.png" />
              <span>{{ userInfo.charm_level }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="data">
        <div class="tab-header">
          <div class="tab-left">
            <span
              :class="{ 'tab-name': true, active: active == index }"
              v-for="(item, index) in tabList"
              :key="index"
              @click="onChangeTab(index)"
              >{{ item }}</span
            >
          </div>
          <div class="more" @click="toDetail">
            <span class="more-text">{{ $t("anchor.anchor_more") }}</span>
            <img src="@/assets/images/anchor-center/arrow-icon.png" />
          </div>
        </div>
        <swiper
          v-if="userInfo.income_data.length"
          class="swiper"
          :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
          :modules="modules"
          slides-per-view="auto"
          :space-between="0"
          @swiper="setControlledSwiper"
          @slideChange="onSlideChange"
        >
          <swiper-slide
            v-for="item in userInfo.income_data"
            :key="item"
            class="swipe-item"
          >
            <div class="box" v-for="(jtem, index) in swipeList" :key="index">
              <div class="box-num">
                {{ userInfo.income_data[active][index] }}
              </div>
              <div class="box-text">{{ jtem }}</div>
            </div>
          </swiper-slide>
        </swiper>
      </div>
    </div>
    <div class="content">
      <div class="tool">
        <div class="tool-title">{{ t('anchor.anchor_common_tools') }}</div>
        <div class="tool-content">
          <div
            class="tool-box"
            v-for="(item, index) in toolList"
            :key="index"
            @click="item.function"
          >
            <img class="tool-img" :src="item.icon" alt="" />
            <div class="tool-text">{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div class="tool" v-if="userInfo.start_center?.length">
        <div class="tool-title">{{ t('anchor.anchor_learning_center') }}</div>
        <div class="study-item" v-for="(item,index) in userInfo.start_center" :key="index" @click="jumpLink(item.url)">
          <img :src="item.background" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, unref, getCurrentInstance, nextTick } from "vue";
import { i18n } from "@/i18n/index.js";
import { Controller } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/vue";
import anchorApi from "@/api/anchor.js";
import icon1 from "@/assets/images/anchor-center/icon-1.png";
import icon2 from "@/assets/images/anchor-center/icon-2.png";
import icon3 from "@/assets/images/anchor-center/icon-3.png";
import icon4 from "@/assets/images/anchor-center/icon-4.png";
import avatar from "@/assets/images/common/avatar.jpg";
import { useRoute, useRouter } from "vue-router";

const { proxy } = getCurrentInstance();
const router = useRouter();

const modules = [Controller];
const t = i18n.global.t;
const tm = i18n.global.tm;
const active = ref(0);
const tabList = ref([
  t("anchor.anchor_today"),
  t("anchor.anchor_week"),
  t("anchor.anchor_month"),
]);
const swipeList = ref([
  t("anchor.anchor_total_diamonds"),
  t("anchor.anchor_room_income"),
  t("anchor.anchor_room_chat_money"),
  t("anchor.anchor_room_platform"),
]);
const controlledSwiper = ref(null);
const userInfo = ref({ income_data: [], start_center: [] });
const toolList = ref([
  {
    name: t("anchor.anchor_task"),
    icon: icon1,
    function: () => {
      proxy.$siyaApp("openSiyaUrl", {
        url: "siya://siya.com/app?method=taskCenter",
      });
    },
  },
  {
    name: t("anchor.anchor_my_room"),
    icon: icon2,
    function: () => {
      proxy.$siyaApp("openSiyaUrl", {
        url: "siya://siya.com/app?method=goCreateRoom",
      });
    },
  },
  {
    name: t("anchor.anchor_verification_center"),
    icon: icon3,
    function: () => {
      proxy.$siyaApp("openSiyaUrl", {
        url: "siya://siya.com/app?method=authCenter",
      });
    },
  },
  {
    name: t("anchor.anchor_withdraw"),
    icon: icon4,
    function: () => {
      router.push({ path: "/income/list", closeType: "back" });
    },
  },
]);

const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};
const onSlideChange = (swiper) => {
  const idx = swiper.activeIndex;
  if (idx === active.value) return;
  changeTab(idx);
};
const onChangeTab = (idx) => {
  controlledSwiper.value.slideTo(idx);
};
const changeTab = (index) => {
  active.value = index;
};
const toDetail = () => {
  router.push({ path: "/anchor-center/detail" });
};
const jumpLink = (url) => {
  if(url.includes('siya://')) return proxy.$siyaApp('openSiyaUrl', { url })
  else window.location.href = url
};
const getData = () => {
  anchorApi.anchor_home().then((res) => {
    if (res.code === 200) {
      userInfo.value = res.data;
    }
  });
};
onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";
.page-anchor-center {
  background-color: $bgColorB7;
}
.page-bg {
  position: relative;
  top: 0;
  width: 100vw;

  .header-img {
    position: absolute;
    width: 100%;
  }
  .user-info {
    width: 100%;
    padding: px2rem(12) px2rem(16);
    display: flex;
    .avatar {
      width: px2rem(72);
      height: px2rem(72);
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #e099d3;
    }
    .user-name {
      font-size: $fontSize17;
      font-weight: 700;
      color: $fontColorB0;
      margin-bottom: px2rem(4);
    }
    .user-info-text {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-left: px2rem(8);
    }
    .user-id {
      height: px2rem(15);
      display: flex;
      align-items: center;
      .auth {
        width: px2rem(13);
        height: px2rem(13);
        object-fit: cover;
      }
      .auth-text {
        height: px2rem(15);
        font-weight: 700;
        background: 
          /* 横向渐变（从左到右） */ linear-gradient(
          to right,
          #ffdbe1,
          #ffa6a6
        );
        font-size: $fontSize11;
        color: $white;
        border-radius: px2rem(30);
        padding: 0 px2rem(4) 0 px2rem(1);
        display: flex;
        align-items: center;
        .heart {
          width: px2rem(15);
          height: px2rem(15);
        }
      }
    }
  }
  .data {
    margin: px2rem(4) px2rem(16);
    border-radius: px2rem(12);
    background-color: $white;
    padding: 0 px2rem(12) px2rem(12) px2rem(12);
    position: relative;
    z-index: 999;
    .tab-header {
      display: flex;
      align-items: center;
      .tab-left {
        padding-top: px2rem(12);
        flex: 1;
        display: flex;
        margin-right: px2rem(6);
        .tab-name {
          font-size: $fontSize15;
          font-weight: 500;
          color: $fontColorB3;
          margin-right: px2rem(14);
          line-height: px2rem(20);
          &.active {
            font-weight: 700;
            color: $fontColorB0;
            font-size: $fontSize17;
          }
        }
      }
      .more {
        display: flex;
        align-items: center;
        height: 100%;
        padding-top: px2rem(12);
        img {
          width: px2rem(12);
        }
        .more-text {
          font-size: $fontSize13;
          background: linear-gradient(90deg, #ffbad5 5%, #3dbaff 100%);
          -webkit-background-clip: text; /* 兼容 WebKit/Blink 浏览器 */
          background-clip: text;
          color: transparent; /* 让文字透明，显示背景渐变 */
          margin-right: px2rem(2);
        }
      }
    }
    .swiper {
      margin-top: px2rem(12);
      border: 1px solid #ffe5ec;
      border-radius: px2rem(12);
      padding: px2rem(12) 0;
      background: linear-gradient(
        90deg,
        #fff3f6 0%,
        #f2f2ff 49.76%,
        #eefaff 100%
      );
    }
    .swipe-item {
      display: flex;
      flex-wrap: wrap;
      .box {
        width: 50%;
        text-align: center;
        position: relative;
        padding: 0 px2rem(8);
        .box-num {
          color: #0a2a3b;
          font-size: px2rem(20);
          font-weight: 900;
        }
        .box-text {
          color: $fontColorB2;
          font-size: $fontSize13;
          margin-top: px2rem(2);
        }
        &::after {
          content: "";
          width: px2rem(1);
          height: px2rem(32);
          background-color: rgba(0, 0, 0, 0.04);
          position: absolute;
          right: -1px;
          top: 50%;
          transform: translateY(-50%);
        }
        &:nth-child(2),
        &:nth-child(4) {
          &::after {
            display: none;
          }
        }
        &:nth-child(1),
        &:nth-child(2) {
          margin-bottom: px2rem(8);
        }
      }
    }
  }
}
.content {
  padding: 0 px2rem(16);
  .tool {
    margin-top: px2rem(12);
    padding: px2rem(12);
    border-radius: px2rem(12);
    background-color: $white;
    .tool-title {
      font-size: px2rem(20);
      font-weight: 700;
      color: $fontColorB0;
      position: relative;
      z-index: 1;
      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: px2rem(-4);
        width: px2rem(77);
        height: px2rem(13);
        background: url("@/assets/images/anchor-center/decorate.png")
          bottom/contain no-repeat;
        z-index: -1; // 让背景图位于文字下方
      }
    }
    .tool-content {
      display: flex;
      margin-top: px2rem(12);
      background-color: $bgColorB7;
      border-radius: px2rem(8);
      padding: px2rem(16) px2rem(4);
      .tool-box {
        width: 25%;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        .tool-img {
          display: block;
          width: px2rem(40);
        }
        .tool-text {
          font-size: $fontSize11;
          color: $fontColorB0;
          margin-top: px2rem(4);
        }
      }
    }
    .study-item {
      width: 100%;
      height: px2rem(84);
      margin-top: px2rem(12);
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: px2rem(12);
      }
    }
  }
}
.rtl-html {
  .page-bg .data .tab-header .more img {
    transform: scaleX(-1);
  }
  .page-bg .data .tab-header .more .more-text {
    background: linear-gradient(90deg, #3dbaff 5%,#ffbad5 100% );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
  .content .tool .tool-title::after {
     right: 0;
     left: auto;
  }
  .page-bg .user-info .user-id .auth-text {
    padding: 0 px2rem(1) 0 px2rem(4);
  }
}
</style>
