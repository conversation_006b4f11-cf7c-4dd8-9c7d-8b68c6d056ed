import { createRouter, createWebHistory } from "vue-router";
import { setPageTitle18n } from "@/i18n/index.js";
import { siyaApp } from "@/utils/renderPage.js";
import app from "../main.js";
import walletRouter from "./wallet.js";
import landingPagepRouter from "./landing-page.js";
import helpRouter from "./help.js";
import baseForAppRouter from "./baseforapp.js";
import userRouter from "./user.js";
import incomeRouter from "./income.js";
import guildRouter from "./guild.js";
import gameRouter from "./game.js";
import rechargeRouter from "./recharge.js";
import activityRouter from "./activity.js";
import dealerRouter from "./dealer.js";
import rankingRouter from "./ranking.js";
import rules from "./rules.js";
import inviteRouter from "./invite.js";
import anchorCenter from "./anchorCenter.js";

const agreementPage =
  process.env.VITE_COMPANY === "PUHUA"
    ? () => import(/*webChunkName: "Agreement"*/ "../views/agreement/Ios.vue")
    : () =>
        import(/*webChunkName: "Agreement"*/ "../views/agreement/Index.vue");

const routes = [
  ...walletRouter,
  ...landingPagepRouter,
  ...helpRouter,
  ...baseForAppRouter,
  ...userRouter,
  ...incomeRouter,
  ...guildRouter,
  ...gameRouter,
  ...rechargeRouter,
  ...activityRouter,
  ...dealerRouter,
  ...rankingRouter,
  ...rules,
  ...inviteRouter,
  ...anchorCenter,
  {
    path: "/",
    name: "Home",
    component: () =>
      import(/*webChunkName: "Index"*/ "../views/home/<USER>"),
  },
  {
    path: "/agreement",
    name: "Agreement",
    component: agreementPage,
  },
  {
    path: "/agreement-ios",
    name: "AgreementIos",
    component: () =>
      import(/*webChunkName: "AgreementIos"*/ "../views/agreement/Ios.vue"),
  },
  {
    path: "/404",
    name: "NotFound",
    component: () =>
      import(/* webpackChunkName: "NotFound" */ "../views/NotFound.vue"),
  },
];

const router = createRouter({
  history: createWebHistory("/"),
  routes,
  scrollBehavior() {
    return { top: 0 };
  },
});

const setIsHalf = (route) => {
  // 根据第一次进入带的isHalf判断是否是半屏,，后续路由间跳转不再判断
  const curScreen = app.config.globalProperties.$isHalf;
  if (curScreen) return false;
  app.config.globalProperties.$isHalf = route.query?.isHalf === "true";
};

router.beforeEach((to, from, next) => {
  // siyaApp('setAppFullScreen', {fullScreen: true})
  setIsHalf(to);
  if (!to.name) {
    next("/404");
  } else {
    next();
  }
});

router.afterEach((to, from) => {
  setPageTitle18n(to.name);

  siyaApp("setAppFullScreen", { fullScreen: !!to.meta.fullScreen }).catch(
    (e) => e
  );
});

export default router;
