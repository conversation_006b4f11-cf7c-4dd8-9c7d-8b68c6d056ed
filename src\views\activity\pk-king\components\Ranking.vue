<template>
  <GoldBox :showBg="false">
    <div class="ranking">
      <div class="top3">
        <div class="top-2">
          <div v-if="type === 3" class="room-cover">
            <img v-if="top2" :src="top2?.cover" />
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div v-else class="avatar">
            <frame-avatar
              v-if="top2"
              :avatar="top2?.cover"
              :frame="top2?.frame"
              :size="40"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top2?.name || "-" }}</div>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(top2?.coin ?? 0) }}</div>
          </div>
        </div>
        <div class="top-1">
          <div v-if="type === 3" class="room-cover">
            <img v-if="top1" :src="top1?.cover" />
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div v-else class="avatar">
            <frame-avatar
              v-if="top1"
              :avatar="top1?.cover"
              :frame="top1?.frame"
              :size="48"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top1?.name || "-" }}</div>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(top1?.coin ?? 0) }}</div>
          </div>
        </div>
        <div class="top-3">
          <div v-if="type === 3" class="room-cover">
            <img v-if="top3" :src="top3?.cover" />
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div v-else class="avatar">
            <frame-avatar
              v-if="top3"
              :avatar="top3?.cover"
              :frame="top3?.frame"
              :size="40"
            ></frame-avatar>
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div class="nickname">{{ top3?.name || "-" }}</div>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(top3?.coin ?? 0) }}</div>
          </div>
        </div>
      </div>
      <div class="ranking-list">
        <div
          class="rank-item"
          v-for="(item, index) in rankAfter3List"
          :key="index"
        >
          <div class="index">{{ item.rank }}</div>
          <gap :gap="8"></gap>
          <div v-if="type === 3" class="room-cover">
            <img v-if="item" :src="item?.cover" />
            <div v-else class="empty-avatar">
              <svg-icon icon="vector" :size="20"></svg-icon>
            </div>
          </div>
          <div v-else class="avatar">
            <frame-avatar
              :avatar="item?.cover"
              :frame="item.frame"
              :size="36"
            ></frame-avatar>
          </div>
          <gap :gap="8"></gap>
          <div class="nickname">{{ item.name || "-" }}</div>
          <gap :gap="8"></gap>
          <div class="coin-box">
            <div class="pk-value-icon"></div>
            <gap :gap="3"></gap>
            <div class="coin-number">{{ formatCount(item?.coin ?? 0) }}</div>
          </div>
          <gap :gap="16"></gap>
        </div>
      </div>
      <div class="bottom-box">
        <GoldBox
          :showBg="false"
          :showDecoration="false"
          :border-radius="[24, 24, 0, 0]"
        >
          <div class="bottom-box-inner">
            <div class="rank-item">
              <div class="index">
                {{
                  currentRank?.rank === -1
                    ? "-"
                    : (currentRank?.rank ?? 0) > 99
                    ? "99+"
                    : currentRank?.rank || "-"
                }}
              </div>
              <gap :gap="8"></gap>
              <div v-if="type === 3" class="room-cover">
                <img v-if="currentRank?.cover" :src="currentRank?.cover" />
                <div v-else class="empty-avatar">
                  <svg-icon icon="vector" :size="20"></svg-icon>
                </div>
              </div>
              <div v-else class="avatar">
                <frame-avatar
                  :avatar="currentRank?.cover"
                  :frame="currentRank?.frame"
                  :size="36"
                ></frame-avatar>
              </div>
              <gap :gap="8"></gap>
              <div class="nickname">{{ currentRank?.name || "-" }}</div>
              <div class="coin-box">
                <div class="pk-value-icon"></div>
                <gap :gap="3"></gap>
                <div class="coin-number">{{ currentRank?.coin ?? 0 }}</div>
              </div>
            </div>
          </div>
        </GoldBox>
      </div>
    </div>
  </GoldBox>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import GoldBox from "./GoldBox.vue";
import activityApi from "@/api/activity.js";
import { formatCount } from "@/utils/util.js";
const props = defineProps({
  type: {
    type: Number,
    default: 3, // 1 送礼榜，2 收礼榜，3 房间榜
  },
});
const rankList = ref([]);
const top1 = computed(() => {
  return rankList.value.find((i) => i.rank === 1);
});
const top2 = computed(() => {
  return rankList.value.find((i) => i.rank === 2);
});
const top3 = computed(() => {
  return rankList.value.find((i) => i.rank === 3);
});
const rankAfter3List = computed(() =>
  rankList.value.filter((i) => i.rank && i.rank > 3)
);
const currentRank = ref(null);
const hasMe = computed(
  () => currentRank.value && currentRank.value?.rank !== -1
);
const fetchData = async () => {
  if (props.type === 3) {
    const res = await activityApi.pk_room_rank();
    if (res.code === 200) {
      rankList.value = (res.data?.items || []).map((item) => {
        return {
          name: item.room_name || "-",
          cover: item.cover,
          coin: item.coin || 0,
          rank: item.rank,
          frame: null,
        };
      });
      currentRank.value = {
        name: res.data?.current?.room_name || "-",
        cover: res.data?.current?.cover,
        coin: res.data?.current?.coin || 0,
        rank: res.data?.current?.rank || -1,
        frame: null,
      };
    }
  } else {
    const res = await activityApi.pk_rank({ type: props.type });
    if (res.code === 200) {
      rankList.value = (res.data?.items || []).map((item) => {
        return {
          name: item.nickname || "-",
          cover: item.avatar,
          coin: item.count || 0,
          rank: item.rank,
          frame: item.frame,
        };
      });
      currentRank.value = {
        name: res.data?.current?.nickname || "-",
        cover: res.data?.current?.avatar,
        coin: res.data?.current?.count || 0,
        rank: res.data?.current?.rank || -1,
        frame: res.data?.current?.frame || null,
      };
    }
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.ranking {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #71009e;
}

.coin-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  .pk-value-icon {
    width: px2rem(19);
    height: px2rem(19);
    background-image: url("@/assets/images/activity/pk-king/value.png");
    background-size: 100% 100%;
  }
  .coin-number {
    color: #dd5c00;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.top3 {
  width: 100%;
  height: px2rem(540);
  background-image: url("@/assets/images/activity/pk-king/bg2.png");
  background-size: 100% 100%;
  background-position: -1px 0;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    width: px2rem(454);
    height: px2rem(224);
    top: px2rem(123);
    left: px2rem(-50);
    background-image: url("@/assets/images/activity/pk-king/light.png");
    background-size: 100% 100%;
  }
  .top-1,
  .top-2,
  .top-3 {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    &::after {
      content: "";
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
      position: absolute;
      inset: 0;
      z-index: 0;
    }
    .avatar {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(65);
      z-index: 1;
    }
    .room-cover {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(65);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      z-index: 10;
      position: relative;
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(38);
      color: #dd5c00;
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(15);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      width: px2rem(90);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      z-index: 10;
      position: relative;
    }
    .coin-box {
      margin-top: px2rem(4);
      z-index: 10;
      position: relative;
    }
  }
  .top-1 {
    width: px2rem(172);
    height: px2rem(225);
    left: 50%;
    top: px2rem(61);
    transform: translateX(-50%);
    &::before {
      position: absolute;
      content: "";
      width: 132px;
      height: 251px;
      flex-shrink: 0;
      background: #ffd200;
      filter: blur(36.45000076293945px);
      z-index: -999;
    }
    &::after {
      background-image: url("@/assets/images/activity/pk-king/top1.png");
    }
  }
  .top-2 {
    width: px2rem(160);
    height: px2rem(209);
    left: px2rem(9);
    top: px2rem(253);
    z-index: 1;
    &::after {
      background-image: url("@/assets/images/activity/pk-king/top2.png");
    }
    .avatar {
      width: px2rem(40);
      height: px2rem(40);
      margin-top: px2rem(64);
    }
    .room-cover {
      width: px2rem(42);
      height: px2rem(42);
      border-radius: px2rem(6);
      margin-top: px2rem(64);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(36);
      color: #284799;
    }
    .coin-box {
      .coin-number {
        color: #284799;
      }
    }
  }
  .top-3 {
    width: px2rem(160);
    height: px2rem(209);
    right: px2rem(6);
    top: px2rem(253);
    z-index: 1;
    &::after {
      background-image: url("@/assets/images/activity/pk-king/top3.png");
    }
    .avatar {
      width: px2rem(40);
      height: px2rem(40);
      margin-top: px2rem(64);
    }
    .room-cover {
      width: px2rem(42);
      height: px2rem(42);
      border-radius: px2rem(6);
      margin-top: px2rem(64);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      margin-top: px2rem(36);
      color: #8c5532;
    }
    .coin-box {
      .coin-number {
        color: #8c5532;
      }
    }
  }
}
.ranking-list {
  width: 100%;
  padding: 0 px2rem(5);
  margin-top: px2rem(-42);
  padding-bottom: px2rem(98);
  z-index: 3;
  .rank-item {
    display: flex;
    height: px2rem(60);
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: px2rem(8);
    border: 1px solid #ffd630;
    background: linear-gradient(
      180deg,
      rgba(112, 8, 119, 0.8) 0%,
      rgba(216, 0, 223, 0.8) 51.86%,
      rgba(108, 10, 116, 0.8) 100%
    );
    margin-bottom: px2rem(6);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
      flex-shrink: 0;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(46);
      height: px2rem(46);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }

    .nickname {
      color: var(---White, #fff);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }
    .coin-number {
      color: #ffda6d;
      font-family: "DIN Next W1G";
      font-size: px2rem(16);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }
}
.pb103 {
  padding-bottom: px2rem(103);
}
.rtl-html {
  .rank-item {
    .index {
      text-align: left;
    }
  }
}

.bottom-box {
  width: px2rem(391);
  height: fit-content;
  flex-shrink: 0;
  position: fixed;
  left: 0;
  bottom: px2rem(-1);
  z-index: 99;
  .bottom-box-inner {
    background: linear-gradient(180deg, #c800cc 0%, #770982 100%);
    width: 100%;
    height: px2rem(91);
  }
  .rank-item {
    display: flex;
    align-items: center;
    margin-left: px2rem(16);
    margin-right: px2rem(33);
    margin-top: px2rem(11);
    .index {
      width: px2rem(28);
      color: #fff;
      text-align: right;
      font-family: "DIN Next W1G";
      font-size: px2rem(17);
      font-style: italic;
      font-weight: 750;
      line-height: normal;
    }
    .avatar {
      display: flex;
      width: px2rem(46);
      height: px2rem(46);
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
    }
    .room-cover {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      position: relative;
      width: px2rem(46);
      height: px2rem(46);
      border: px2rem(2) solid rgba(255, 255, 255, 0.2);
      border-radius: px2rem(6);
      img {
        width: 100%;
        height: 100%;
        border-radius: px2rem(6);
        object-fit: cover;
      }
    }
    .nickname {
      color: var(---White, #fff);

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      /* T13/R */
      font-family: Gilroy;
      font-size: px2rem(13);
      font-style: normal;
      font-weight: 500;
      line-height: 124%; /* 16.12px */
      flex-grow: 1;
    }

    .coin-box {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-top: px2rem(-9);
      .coin-number {
        color: #ffda6d;
        font-family: "DIN Next W1G";
        font-size: px2rem(16);
        font-style: normal;
        font-weight: 700;
        line-height: 1.2;
        letter-spacing: -0.5px;
      }
    }
  }
  .bottom-desc {
    display: flex;
    height: px2rem(28);
    flex-direction: column;
    justify-content: center;

    position: absolute;
    right: px2rem(18);
    top: px2rem(43);
    text-align: right;
    font-family: Gilroy;
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 10px */
    & > div:first-child {
      color: #ff9791;
    }
    & > div:last-child {
      color: #cc5c55;
      margin-top: px2rem(8);
    }
  }
}

.empty-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
