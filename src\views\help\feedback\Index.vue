<template>
  <div class="app-container">
    <div class="cell-title">{{ $t('help.feedback_title_type') }}</div>
    <div class="reason-title flex" @click="canChangeAction ? toggleActionVisible(true) : ''">
      <span>{{feedbackItem.actions.name}}</span>
      <img v-if="canChangeAction" class="icon-down" src="@/assets/images/icon/icon-arrow-down.svg" alt="">
    </div>

    <div class="cell-title"><span>*</span>{{ $t('help.feedback_title_describe') }}</div>
    <van-field
        class="reason-detail"
        v-model="feedbackItem.describe"
        rows="3"
        autosize
        type="textarea"
        maxlength="500"
        :formatter="formatter"
        format-trigger="onBlur"
        :placeholder="$t('help.feedback_describe_detail')"
        show-word-limit
        :border="false"
    />
    <div class="reason-upload">
      <upload-cell @update="handleUpdateImages" />
      <div class="upload-tips">{{ $t('help.feedback_photo_limit') }}</div>
    </div>

    <div class="cell-title"><span>*</span>{{ $t('help.feedback_title_email') }}</div>
    <van-field class="reason-detail reason-email" v-model="feedbackItem.email"
               :placeholder="$t('help.feedback_email_detail')"
               maxlength="60"
               :border="false"
    />

    <van-button class="submit-button"
                type="primary"
                block
                :disabled="buttonDisabled"
                @click="handleSubmit"
    >
      {{ $t('common.common_submit') }}
    </van-button>

    <van-action-sheet
        class-name="action-sheet"
        v-model:show="actionVisible"
        :actions="reasonList"
        @select="onSelectReason"
        close-on-click-action
    />
  </div>
</template>

<script setup>
import { onMounted, ref, watch, getCurrentInstance, computed } from 'vue'
import { isEmail } from '@/utils/util.js'
import helpApi from '@/api/help.js'
import { useRoute } from 'vue-router'
import { i18n } from '@/i18n/index.js'
import uploadCell from '@/views/help/feedback/components/uploadCell.vue'

const t = i18n.global.t
const route = useRoute()
const { proxy } = getCurrentInstance()

// 1 登录问题 2 收不到验证码 3 功能建议 8 认证问题
const reasonType = Number(route.query.type || 0)

const feedbackItem = ref({
  actions: {
    name: '',
    id: 0,
  },
  describe: undefined,
  email: undefined,
})

// 按钮禁用状态
const buttonDisabled = ref(true)
// 过滤输入的空格
const formatter = (value) => value.trim();
watch(() => feedbackItem.value, (val) => {
  buttonDisabled.value = !(val.actions.name && val.describe && val.email)
}, { deep: true })

// 原因列表
const reasonList = ref([])
const getList = async () => {
  const response = await helpApi.feedback_cfg()
  if (response.code === 200) {
    reasonList.value = response.data
    if (reasonType) feedbackItem.value.actions = response.data.find(i => i.id === reasonType)
    else feedbackItem.value.actions = response.data[0]
  }
}

// 选择原因
const actionVisible = ref(false)
const canChangeAction = computed(() => {
  // 1 2 8 不可修改类型
  return ![1, 2, 8].includes(reasonType)
})
const toggleActionVisible = (bool) => {
  actionVisible.value = bool
}
const onSelectReason = (item) => {
  feedbackItem.value.actions = item
}


// 上传图片
const fileList = ref([])
const handleUpdateImages = (file) => {
  fileList.value = file
}

// 提交
const handleSubmit = async () => {
  if (buttonDisabled.value) return

  if (!isEmail(feedbackItem.value.email)) {
    proxy.$toast(`${t('help.feedback_email_incorrect')}`)
    return
  }

  const params = {
    question_id: feedbackItem.value.actions.id,
    question: feedbackItem.value.describe,
    pic: fileList.value.map(i => i.url),
    email: feedbackItem.value.email,
  }
  const response = await helpApi.feedback_submit(params)
  if (response.code === 200) {
    proxy.$closeAfterToast(`${t('help.feedback_success')}`)
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding: $gap16;

  --van-uploader-border-radius: #{$radius16};
}

.cell-title {
  padding-top: px2rem(8);
  padding-left: px2rem(12);
  margin-bottom: $gap8;
  font-size: px2rem(15);
  font-weight: $fontWeightBold;
  line-height: px2rem(24);

  span {
    color: $subColorRed;
    font-size: px2rem(17);
  }
}

.reason-title {
  justify-content: space-between;
  padding: px2rem(13) px2rem(12);
  margin-bottom: $gap8;
  font-size: px2rem(15);
  color: $fontColorB0;
  background-color: $white;
  border-radius: $radius16;
}

.icon-down {
  width: px2rem(24);
  height: px2rem(24);
}

.reason-detail {
  margin-bottom: $gap8;
  font-size: px2rem(15);
  border-radius: $radius16;
}

.reason-email {
  padding: px2rem(13) px2rem(12);
}

.reason-upload {
  margin-bottom: $gap8;

  .upload-tips {
    font-size: px2rem(13);
    color: $fontColorB4;
  }
}

.submit-button {
  margin-top: $gap16;
}

:deep(.van-action-sheet__item) {
  font-weight: $fontWeightBold;
}
</style>
