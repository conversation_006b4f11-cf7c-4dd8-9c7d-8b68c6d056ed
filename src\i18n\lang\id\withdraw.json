{"WALLET_accountNo_placeholder_ID": "Contoh: ***********", "WALLET_accountNo_placeholder_MY": "Contoh: **********", "WALLET_accountNo_placeholder_TH": "Contoh: **********", "WALLET_accountNo_placeholder_VN": "Contoh: ***********", "WALLET_accountNo_hint_ID": "<PERSON><PERSON><PERSON> masukkan nomor akun yang terhubung ke dompet: 9 hingga 14 digit, dimulai dengan 08", "WALLET_accountNo_hint_MY": "<PERSON><PERSON><PERSON> masukkan nomor akun yang terhubung ke dompet: 10 atau 11 digit, dimulai dengan 0", "WALLET_accountNo_hint_TH": "<PERSON><PERSON><PERSON> masukkan nomor akun yang terhubung ke dompet: 10 digit, dimulai dengan 0", "WALLET_accountNo_hint_VN": "<PERSON><PERSON><PERSON> masukkan nomor akun yang terhubung ke dompet: 11 digit, dimula<PERSON> dengan 84", "WALLET_fullName_placeholder": "contoh: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "BANK_TRANSFER_accountNo_title": "Rekening Bank", "BANK_TRANSFER_accountNo_placeholder_MY": "Contoh: **********", "BANK_TRANSFER_accountNo_placeholder_TH": "Contoh: *********", "BANK_TRANSFER_accountNo_placeholder_ID": "Contoh: ***********", "BANK_TRANSFER_accountNo_hint_MY": "<PERSON><PERSON><PERSON><PERSON> nomor rekening dengan maksimal 35 karakter", "BANK_TRANSFER_accountNo_hint_TH": "<PERSON><PERSON><PERSON> masukkan nomor rekening yang terdiri dari 10–18 digit", "BANK_TRANSFER_accountNo_hint_ID": "<PERSON><PERSON><PERSON><PERSON> nomor rekening dalam bentuk angka", "BANK_TRANSFER_bankCode_title_MY": "Kode Bank (Kode SWIFT)", "BANK_TRANSFER_bankCode_placeholder_MY": "contoh: CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "<PERSON><PERSON><PERSON>n kombinasi huruf dan angka sebanyak 8–11 karakter", "withdrawal_rule_title": "<PERSON><PERSON><PERSON><PERSON>", "withdrawal_rule_detail": ["<PERSON><PERSON><PERSON> permohonan penarikan ber<PERSON><PERSON>, dana akan diproses dan diharapkan masuk ke akun Anda dalam 1-7 hari kerja. <PERSON><PERSON> melebihi waktu yang diharapkan namun belum diterima, pastikan informasi akun penerima Anda benar dan harap bersabar atau hubungi layanan pelanggan.", "Biaya penarikan akan dikenakan sesuai dengan jumlah penarikan. <PERSON><PERSON><PERSON> biaya yang dikenakan akan ditampilkan secara akurat saat dana diterima.", "<PERSON><PERSON>a menutup akun atau akun Anda di<PERSON>lo<PERSON>r karena pelanggaran selama proses penarikan, penarikan <PERSON>a akan di<PERSON>ukan."], "withdrawal_select_methods_tips": "Penarikan membutuhkan pengikatan metode pembayaran Anda, silakan pilih metode pembayaran Anda dan pastikan informasi pembayaran Anda valid dan akurat.", "service_fee": "Biaya Penarikan: {0}", "withdrawal_methods": "<PERSON><PERSON>", "withdrawal_information": "Informasi Penarikan", "withdrawal_info_check": "Harap konfirmasi informasi penarikan Anda", "estimated_withdrawal_amount": "<PERSON><PERSON><PERSON><PERSON>", "estimated_amount_received": "<PERSON><PERSON><PERSON><PERSON>", "estimated_service_fee": "<PERSON><PERSON><PERSON><PERSON>", "current_selected": "<PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON>", "need_complete": "<PERSON><PERSON>", "withdrawal_amount": "<PERSON><PERSON><PERSON>", "total_withdrawal_amount": "Total Jumlah <PERSON>arikan", "withdrawal_pending": "<PERSON><PERSON>", "withdrawal_success": "<PERSON><PERSON><PERSON>", "withdrawal_fail": "Penarikan <PERSON>", "fail_reason": "Alasan <PERSON>", "empty": "Tidak Ada Riwayat Penarikan", "BANK_TRANSFER_receive_bank": "Bank Penerima", "BANK_TRANSFER_receive_bank_placeholder": "Silakan Pilih Bank Penerima", "BANK_TRANSFER_fill_info_tips": "Harap Isi Informasi Penerima untuk Transfer Bank", "payeeInfo_documentId_title": "NPWP", "payeeInfo_documentId_placeholder": "Contoh: ***********", "payeeInfo_documentId_hint": "Masukkan CPF/CNPJ (Nomor Pajak Pribadi/Perusahaan)", "payeeInfo_address_title": "<PERSON><PERSON><PERSON>", "payeeInfo_email_title": "Email", "payeeInfo_email_placeholder": "Contoh: 1234566{0}gmail.com", "payeeInfo_email_hint": "Ma<PERSON>kkan Email dengan Format yang Valid", "payeeInfo_phone_title": "<PERSON><PERSON>", "WALLET_accountNo_title": "{0} <PERSON><PERSON><PERSON>", "WALLET_accountType_title": "{0} <PERSON><PERSON> ya<PERSON>", "WALLET_accountNo_placeholder_SA": "Contoh: ************", "WALLET_accountNo_placeholder_EG": "Contoh: **********4", "WALLET_accountNo_placeholder_AE": "Contoh: +971-*********", "WALLET_accountNo_placeholder_TR": "Contoh: **********", "WALLET_accountNo_placeholder_PH": "Contoh: ***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "Contoh: mercadopago\\_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "Contoh: ***********", "WALLET_accountType_placeholder_BR_PIX": "<PERSON><PERSON><PERSON> yang <PERSON>", "WALLET_accountNo_placeholder_BR_PagBank": "Contoh: ************{0}", "WALLET_accountNo_hint_SA": "<PERSON><PERSON><PERSON><PERSON> Nomor Ponsel yang Terhubung dengan E-Wallet, 12 <PERSON><PERSON> Dimulai <PERSON>gan 9665", "WALLET_accountNo_hint_EG": "<PERSON><PERSON><PERSON><PERSON> Nomor Ponsel yang Terhubung dengan E-Wallet, 11 <PERSON>git Dimulai dengan 01", "WALLET_accountNo_hint_EG_2": "Ma<PERSON>kkan Nomor Ponsel yang Terhubung dengan E-Wallet, 11 Digit Dimulai dengan 01 atau Kode Negara diikuti 10 Digit", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "Nomor Ponsel Penerima - <PERSON><PERSON><PERSON> - Nomor Ponsel Terhubung dengan E-Wallet, 11 Digit Dimulai dengan 01 atau 12 Digit Dimulai dengan 201", "WALLET_accountNo_hint_AE": "Ma<PERSON>kkan <PERSON> Ponsel yang Terhubung dengan E-Wallet, Format Harus +Kode Negara-Nomor Ponsel", "WALLET_accountNo_hint_TR": "Ma<PERSON>kkan Nomor Ponsel yang Terhubung dengan E-Wallet, 10 Digit atau PL diikuti 10 Digit", "WALLET_accountNo_hint_PH": "<PERSON><PERSON><PERSON>n Nomor Ponsel yang Terhubung dengan E-Wallet, 11 <PERSON>git Dimulai dengan 09", "WALLET_accountNo_hint_PH_LAZADAPH": "<PERSON><PERSON><PERSON><PERSON> Nomor Ponsel yang Terhubung dengan E-Wallet, 11 <PERSON>git Dimulai dengan 09 atau Email", "WALLET_accountNo_hint_BR_MERCADOPAGO": "<PERSON><PERSON><PERSON><PERSON> yang Terhubung dengan E-Wallet (Email atau ID)", "WALLET_accountNo_hint_BR_PIX": "<PERSON><PERSON><PERSON><PERSON>n yang <PERSON>rhubung dengan E-Wallet", "WALLET_accountNo_hint_BR_PagBank": "<PERSON><PERSON><PERSON><PERSON> Email Terhu<PERSON>ng dengan E-Wallet, Maksimal 60 Karakter", "WALLET_fullName_title": "<PERSON><PERSON>", "WALLET_fullName_placeholder_AE": "Contoh: <PERSON>", "WALLET_fullName_placeholder_PH": "Contoh: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "Contoh: <PERSON>", "WALLET_fullName_hint": "<PERSON><PERSON><PERSON><PERSON>lam Bahasa Inggris", "WALLET_lastName_title": "<PERSON><PERSON>", "WALLET_lastName_placeholder_EG": "Contoh: <PERSON><PERSON><PERSON>", "WALLET_lastName_hint": "<PERSON><PERSON><PERSON><PERSON>", "WALLET_accountType_option_E_PIX": "Email", "WALLET_accountType_option_P_PIX": "Telepon", "WALLET_accountType_option_C_PIX": "CPF/CNPJ", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "Contoh: 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "Kode SWIFT", "SWIFT_accountNo_placeholder_SA": "Contoh: SA44200000**********1234", "SWIFT_accountNo_placeholder_AE": "Contoh: AE46009000000**********", "SWIFT_accountNo_placeholder_KW": "Contoh: ******************************", "SWIFT_accountNo_placeholder_QA": "Contoh: *****************************", "SWIFT_accountNo_placeholder_TR": "Contoh: TR3200100099999**********0", "SWIFT_accountNo_placeholder_BH": "Contoh: **********************", "SWIFT_accountNo_placeholder_YE": "Contoh: **********", "SWIFT_accountNo_placeholder_IQ": "Contoh: ***********************", "SWIFT_accountNo_placeholder_IL": "Contoh: ***********************", "SWIFT_accountNo_placeholder_PS": "Contoh: **********", "SWIFT_accountNo_placeholder_AZ": "Contoh: AZ77VTBA000000000**********0", "SWIFT_accountNo_placeholder_LB": "Contoh: ****************************", "SWIFT_accountNo_placeholder_MA": "Contoh: **********", "SWIFT_accountNo_placeholder_PH": "Contoh: **********", "SWIFT_accountNo_placeholder_BR": "Contoh: *****************************", "SWIFT_accountNo_placeholder_EG": "Contoh: *****************************", "SWIFT_accountNo_placeholder_JO": "Contoh: JO71CBJO00000000000**********0", "SWIFT_accountNo_hint_SA": "<PERSON><PERSON><PERSON>n Nomor Akun SWIFT, 24 Karakter Di<PERSON><PERSON> dengan SA", "SWIFT_accountNo_hint_AE": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 23 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>gan A<PERSON>", "SWIFT_accountNo_hint_KW": "<PERSON><PERSON><PERSON><PERSON> No<PERSON> Akun SWIFT, 30 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_QA": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 29 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_TR": "<PERSON><PERSON><PERSON><PERSON> Nomor Akun SWIFT, 26 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> TR", "SWIFT_accountNo_hint_BH": "<PERSON><PERSON><PERSON><PERSON> Nomor Akun SWIFT, 22 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> B<PERSON>", "SWIFT_accountNo_hint_YE": "Ma<PERSON>kkan <PERSON> Akun SWIFT, Maksimal 34 Karakter Be<PERSON>n <PERSON>", "SWIFT_accountNo_hint_IQ": "<PERSON><PERSON><PERSON><PERSON>kun SWIFT, 23 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_IL": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 23 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_PS": "Ma<PERSON>kkan <PERSON> Akun SWIFT, Maksimal 34 Karakter Be<PERSON>n <PERSON>", "SWIFT_accountNo_hint_AZ": "<PERSON><PERSON><PERSON><PERSON> Nomor Akun SWIFT, 28 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>gan A<PERSON>", "SWIFT_accountNo_hint_LB": "<PERSON><PERSON><PERSON><PERSON> Nomor Akun SWIFT, 28 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>gan LB", "SWIFT_accountNo_hint_MA": "Ma<PERSON>kkan <PERSON> Akun SWIFT, Maksimal 34 Karakter Be<PERSON>n <PERSON>", "SWIFT_accountNo_hint_PH": "Ma<PERSON>kkan <PERSON> Akun SWIFT, Maksimal 34 Karakter Be<PERSON>n <PERSON>", "SWIFT_accountNo_hint_BR": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 29 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_EG": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 29 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_accountNo_hint_JO": "<PERSON><PERSON><PERSON><PERSON> Akun SWIFT, 30 <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SWIFT_fullName_title": "<PERSON><PERSON>", "SWIFT_fullName_placeholder": "Contoh: <PERSON>", "SWIFT_fullName_hint": "<PERSON><PERSON><PERSON><PERSON>", "SWIFT_bankCode_title": "Kode Bank (SWIFT Code)", "SWIFT_bankCode_placeholder": "Contoh: SCBLHKHH", "SWIFT_bankCode_hint": "Masukkan Kode Bank, 8 atau 11 Karakter Berupa Huruf Be<PERSON>n <PERSON>", "CARD_accountNo_title": "Nomor Rekening Bank", "CARD_accountNo_placeholder_EG": "Contoh: ***********", "CARD_accountNo_placeholder_TR": "Contoh: ****************", "CARD_accountNo_hint_EG": "Masukkan Nomor Rekening Bank, Minimal 8 Digit", "CARD_accountNo_hint_TR": "Masukkan Nomor Kartu Debit, 16 Digit", "CARD_fullName_title": "<PERSON><PERSON>", "CARD_fullName_placeholder": "Contoh: <PERSON>", "CARD_fullName_hint": "<PERSON><PERSON><PERSON><PERSON>lam Bahasa Inggris", "CARRIER_BILLING_accountNo_title": "<PERSON><PERSON>", "CARRIER_BILLING_accountNo_placeholder": "Contoh: 63**********", "CARRIER_BILLING_accountNo_hint": "Masukkan Nomor Telepon", "CASH_accountNo_title": "{0} <PERSON><PERSON><PERSON>", "CASH_accountNo_placeholder": "Contoh: ***********", "CASH_accountNo_hint": "<PERSON><PERSON><PERSON><PERSON> {0} <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 0 dan <PERSON><PERSON><PERSON> 11 Digit", "BANK_TRANSFER_accountNo_title_SA": "Nomor Rekening Bank (IBAN)", "BANK_TRANSFER_accountNo_title_AE": "Nomor Rekening Bank (IBAN)", "BANK_TRANSFER_accountNo_title_TR": "Nomor Rekening Bank (IBAN)", "BANK_TRANSFER_accountNo_title_EG": "Nomor Rekening Bank", "BANK_TRANSFER_accountNo_title_KW": "Nomor Rekening Bank", "BANK_TRANSFER_accountNo_title_QA": "Nomor Rekening Bank", "BANK_TRANSFER_accountNo_title_MA": "Nomor Rekening Bank (RIB)", "BANK_TRANSFER_accountNo_title_PH": "Nomor Rekening Bank", "BANK_TRANSFER_accountNo_title_BR": "Nomor Rekening Bank", "BANK_TRANSFER_accountNo_placeholder_SA": "Contoh: ************************", "BANK_TRANSFER_accountNo_placeholder_EG": "Contoh: ************", "BANK_TRANSFER_accountNo_placeholder_AE": "Contoh: ***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "Contoh: ******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "Contoh: *****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "Contoh: **************************", "BANK_TRANSFER_accountNo_placeholder_MA": "Contoh: 123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "Contoh: ************", "BANK_TRANSFER_accountNo_placeholder_BR": "Contoh: ***********", "BANK_TRANSFER_accountNo_placeholder_JO": "Contoh: ******************************", "BANK_TRANSFER_accountNo_hint_SA": "Masukkan kombinasi SA + 22 huruf dan angka.", "BANK_TRANSFER_accountNo_hint_EG": "Masukkan IBAN dan <PERSON>kening Bank Lokal, IBAN; 29 <PERSON><PERSON><PERSON> (EG+27 Angka)", "BANK_TRANSFER_accountNo_hint_AE": "Masukkan 21 digit angka yang dimulai dengan <PERSON> (harus huruf besar)", "BANK_TRANSFER_accountNo_hint_KW": "Masukkan IBAN dengan Panjang Maksimal 50 Karakter", "BANK_TRANSFER_accountNo_hint_QA": "Masukkan IBAN dengan Panjang Maksimal 50 Karakter", "BANK_TRANSFER_accountNo_hint_TR": "Masukkan Nomor IBAN Di<PERSON>lai dengan TR dan 24 Angka", "BANK_TRANSFER_accountNo_hint_MA": "Masukkan kode akun RIB dengan 24 digit angka.", "BANK_TRANSFER_accountNo_hint_PH": "Masukkan 6-128 Digit Angka", "BANK_TRANSFER_accountNo_hint_BR": "Masukkan 4-15 Digit Angka", "BANK_TRANSFER_accountNo_hint_JO": "Masukkan Nomor IBAN Di<PERSON>lai <PERSON> JO dan 30 Karakter Berupa Huruf dan <PERSON>", "BANK_TRANSFER_bankBranch_title": "Kode Cabang Bank", "BANK_TRANSFER_bankBranch_placeholder_BR": "Contoh: 1234", "BANK_TRANSFER_bankBranch_hint_BR": "Masukkan Kode Cabang Bank, 4-6 Digit Angka", "BANK_TRANSFER_address_placeholder_SA": "Contoh: aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "Contoh: ALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "Contoh: kuwait xx street", "BANK_TRANSFER_address_hint_SA": "<PERSON><PERSON><PERSON><PERSON>, 10-200 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "BANK_TRANSFER_address_hint_AE": "<PERSON><PERSON><PERSON><PERSON>, 10-200 <PERSON><PERSON><PERSON>, 1-200 <PERSON><PERSON><PERSON>, Bank Akan Melakukan Verifikasi Keamanan", "BANK_TRANSFER_address_hint_KW": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 255 <PERSON>", "BANK_TRANSFER_fullName_placeholder_TR": "Contoh: <PERSON>", "BANK_TRANSFER_fullName_placeholder_PH": "Contoh: <PERSON>", "BANK_TRANSFER_fullName_placeholder_BR": "Contoh: <PERSON>", "BANK_TRANSFER_fullName_placeholder_MA": "Contoh: <PERSON>", "BANK_TRANSFER_fullName_placeholder_KW": "Contoh: <PERSON>", "BANK_TRANSFER_phone_placeholder_PH": "Contoh: ***********", "BANK_TRANSFER_phone_placeholder_BR": "Contoh: *************", "BANK_TRANSFER_phone_hint_PH": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 09 dan Berisi 11 Digit (<PERSON><PERSON> Diawali dengan 0)", "BANK_TRANSFER_phone_hint_BR": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> 55 dan <PERSON><PERSON><PERSON> 12-13 <PERSON><PERSON>", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT code", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "Please enter a combination of 8 or 11 uppercase letters and numbers", "BANK_TRANSFER_city_title_SA": "City", "BANK_TRANSFER_city_placeholder_SA": "For example: New York", "BANK_TRANSFER_city_hint_SA": "Please enter the city name, up to 35 characters", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "For example: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "Please enter a combination of 24 digits", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "Date of birth", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "例如********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "For example: ********", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "Document type", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "Please select the type of identification", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "Identification number", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "For example: NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "For example: ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "Please enter your identification number", "AGENT_accountNo_Whatsapp_title_ALL": "<PERSON>mor <PERSON>pp", "AGENT_accountNo_Whatsapp_placeholder_ALL": "Contoh: +62 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "<PERSON><PERSON><PERSON><PERSON>", "AGENT_accountNo_vodafone_title_EG": "Akun Vodafone", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "Nama Blockchain", "USDT_address_title_ALL": "<PERSON><PERSON><PERSON>", "USDT_address_placeholder_ALL": "Contoh: 11111111111111111111111111", "USDT_address_hint_ALL": "<PERSON><PERSON><PERSON><PERSON> 1, 3, atau bc", "USDT_address_hint_common": "Masukkan Alamat USDT Anda"}