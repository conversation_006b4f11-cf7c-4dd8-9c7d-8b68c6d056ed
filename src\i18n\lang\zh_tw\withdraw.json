{"withdrawal_rule_title": "提現說明", "withdrawal_rule_detail": ["申請提現成功後，預計1-7個工作日到賬，如已過預計到賬時間未到賬請確認您的收款賬戶無誤，並耐心等待或聯系客服", "根據提現金額需支付不同金額的提現手續費，具體以實際到賬為准", "若您在提現期間注銷賬號或因違規被封號，我們將凍結您的提現訂單"], "withdrawal_select_methods_tips": "提現需綁定您的收款方式，請選擇您的收款方式並確保你的收款信息真實有效", "service_fee": "手續費: {0}", "withdrawal_methods": "收款方式", "withdrawal_information": "提現信息", "withdrawal_info_check": "請確認您的提現信息", "estimated_withdrawal_amount": "預計提現金額", "estimated_amount_received": "預計到賬金額", "estimated_service_fee": "預計支付手續費", "current_selected": "當前選擇", "completed": "已填寫", "need_complete": "待完善", "withdrawal_amount": "提現金額", "total_withdrawal_amount": "累計提現金額", "withdrawal_pending": "提現中", "withdrawal_success": "提現成功", "withdrawal_fail": "提現失敗", "fail_reason": "提現失敗原因", "empty": "暫無提現記錄", "BANK_TRANSFER_receive_bank": "收款銀行", "BANK_TRANSFER_receive_bank_placeholder": "請選擇收款銀行", "BANK_TRANSFER_fill_info_tips": "請填寫銀行轉賬的收款信息", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "例如***********", "payeeInfo_documentId_hint": "請輸入CPF/CNPJ（個人/企業稅號）", "payeeInfo_address_title": "地址", "payeeInfo_email_title": "郵箱", "payeeInfo_email_placeholder": "例如1234566{0}gmail.com", "payeeInfo_email_hint": "請輸入郵箱，符合郵箱基本格式", "payeeInfo_phone_title": "手機號碼", "WALLET_accountNo_title": "{0}賬號", "WALLET_accountNo_placeholder_SA": "例如************", "WALLET_accountNo_placeholder_EG": "例如***********", "WALLET_accountNo_placeholder_AE": "例如+971-*********", "WALLET_accountNo_placeholder_TR": "例如**********", "WALLET_accountNo_placeholder_PH": "例如***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "例如mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "例如***********", "WALLET_accountNo_placeholder_BR_PagBank": "例如************{0}123.com", "WALLET_accountNo_placeholder_ID": "例如***********", "WALLET_accountNo_placeholder_MY": "例如**********", "WALLET_accountNo_placeholder_TH": "例如**********", "WALLET_accountNo_placeholder_VN": "例如***********", "WALLET_accountNo_hint_SA": "請輸入錢包綁定的手機號，9665開頭的12位數字", "WALLET_accountNo_hint_EG": "請輸入錢包綁定的手機號，01開頭的11位數字", "WALLET_accountNo_hint_EG_2": "請輸入錢包綁定的手機號，01開頭的11位數字，或國家/地區代碼加 10 位數字", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "收款方手機號--必填--錢包綁定的手機號，01開頭的11位數字或201開頭的12位數字", "WALLET_accountNo_hint_AE": "請輸入錢包綁定的手機號，格式必須滿足+國家編碼-手機號", "WALLET_accountNo_hint_TR": "請輸入錢包綁定的手機號，10位數字或PL加10位數字", "WALLET_accountNo_hint_PH": "請輸入錢包綁定的手機號，09開頭11位數字", "WALLET_accountNo_hint_PH_LAZADAPH": "請輸入錢包綁定的手機號，09開頭11位數字或郵箱", "WALLET_accountNo_hint_BR_MERCADOPAGO": "請輸入錢包綁定的用戶賬戶（郵箱或id）", "WALLET_accountNo_hint_BR_PIX": "請輸入錢包綁定的對應賬號", "WALLET_accountNo_hint_BR_PagBank": "請輸入錢包綁定的郵箱，最長60位", "WALLET_accountNo_hint_ID": "請輸入錢包綁定的賬號，08開頭的9～14位數字", "WALLET_accountNo_hint_MY": "請輸入錢包綁定的賬號，0開頭的10/11位數字", "WALLET_accountNo_hint_TH": "請輸入錢包綁定的賬號，0開頭的10位數字", "WALLET_accountNo_hint_VN": "請輸入錢包綁定的賬號，84開頭的11位數字", "WALLET_fullName_title": "收款方姓名", "WALLET_fullName_placeholder": "例如<PERSON> Evan<PERSON>", "WALLET_fullName_placeholder_AE": "例如Li <PERSON>", "WALLET_fullName_placeholder_PH": "例如<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "例如<PERSON> Evan<PERSON>", "WALLET_fullName_hint": "請輸入你的英文全名", "WALLET_lastName_title": "收款方姓氏", "WALLET_lastName_placeholder_EG": "例如Evangelista", "WALLET_lastName_hint": "請填寫你的姓氏", "WALLET_accountType_title": "{0}綁定賬號類型", "WALLET_accountType_placeholder_BR_PIX": "請選擇綁定賬號類型", "WALLET_accountType_option_E_PIX": "email", "WALLET_accountType_option_P_PIX": "phone", "WALLET_accountType_option_C_PIX": "CPF/CNPJ（個人/企業稅號）", "WALLET_accountType_option_B_PIX": "EVP", "WALLET_accountNo_placeholder_BR_PIX_B": "例如123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "SWIFT賬號", "SWIFT_accountNo_placeholder_SA": "例如************************", "SWIFT_accountNo_placeholder_AE": "例如***********************", "SWIFT_accountNo_placeholder_KW": "例如******************************", "SWIFT_accountNo_placeholder_QA": "例如*****************************", "SWIFT_accountNo_placeholder_TR": "例如**************************", "SWIFT_accountNo_placeholder_BH": "例如**********************", "SWIFT_accountNo_placeholder_YE": "例如**********", "SWIFT_accountNo_placeholder_IQ": "例如***********************", "SWIFT_accountNo_placeholder_IL": "例如***********************", "SWIFT_accountNo_placeholder_PS": "例如**********", "SWIFT_accountNo_placeholder_AZ": "例如****************************", "SWIFT_accountNo_placeholder_LB": "例如****************************", "SWIFT_accountNo_placeholder_MA": "例如**********", "SWIFT_accountNo_placeholder_PH": "例如**********", "SWIFT_accountNo_placeholder_BR": "例如*****************************", "SWIFT_accountNo_placeholder_EG": "例如*****************************", "SWIFT_accountNo_placeholder_JO": "例如******************************", "SWIFT_accountNo_hint_SA": "請輸入SWIFT賬號，SA開頭的24位字符", "SWIFT_accountNo_hint_AE": "請輸入SWIFT賬號，AE開頭的23位字符", "SWIFT_accountNo_hint_KW": "請輸入SWIFT賬號，KW開頭的30位字符", "SWIFT_accountNo_hint_QA": "請輸入SWIFT賬號，QA開頭的29位字符", "SWIFT_accountNo_hint_TR": "請輸入SWIFT賬號，TR開頭的26位字符", "SWIFT_accountNo_hint_BH": "請輸入SWIFT賬號，BH開頭的22位字符", "SWIFT_accountNo_hint_YE": "請輸入SWIFT賬號，小於等於34位數字和字母", "SWIFT_accountNo_hint_IQ": "請輸入SWIFT賬號，IQ開頭的23位字符", "SWIFT_accountNo_hint_IL": "請輸入SWIFT賬號，IL開頭的23位字符", "SWIFT_accountNo_hint_PS": "請輸入SWIFT賬號，小於等於34位數字和字母", "SWIFT_accountNo_hint_AZ": "請輸入SWIFT賬號，AZ開頭的28位字符", "SWIFT_accountNo_hint_LB": "請輸入SWIFT賬號，LB開頭的28位字符", "SWIFT_accountNo_hint_MA": "請輸入SWIFT賬號，小於等於34位數字和字母", "SWIFT_accountNo_hint_PH": "請輸入SWIFT賬號，小於等於34位數字和字母", "SWIFT_accountNo_hint_BR": "請輸入SWIFT賬號，BR開頭的29位字符", "SWIFT_accountNo_hint_EG": "請輸入SWIFT賬號，EG開頭的29位字符", "SWIFT_accountNo_hint_JO": "請輸入SWIFT賬號，JO開頭的30位字符", "SWIFT_fullName_title": "收款方姓名", "SWIFT_fullName_placeholder": "例如Li <PERSON>", "SWIFT_fullName_hint": "請輸入你的英文全名", "SWIFT_bankCode_title": "銀行編號（SWIFTCode）", "SWIFT_bankCode_placeholder": "例如SCBLHKHH", "SWIFT_bankCode_hint": "請輸入銀行編號，8或11位，允許數字和大寫字母", "CARD_accountNo_title": "銀行賬號", "CARD_accountNo_placeholder_EG": "例如***********", "CARD_accountNo_placeholder_TR": "例如****************", "CARD_accountNo_hint_EG": "請輸入銀行賬戶，長度>=8", "CARD_accountNo_hint_TR": "請輸入銀行卡號16位數字，僅支持借記卡", "CARD_fullName_title": "收款方姓名", "CARD_fullName_placeholder": "例如<PERSON> Evan<PERSON>", "CARD_fullName_hint": "請輸入你的英文全名", "CARRIER_BILLING_accountNo_title": "手機號", "CARRIER_BILLING_accountNo_placeholder": "例如63**********", "CARRIER_BILLING_accountNo_hint": "請輸入電話號碼", "CASH_accountNo_title": "{0}賬號", "CASH_accountNo_placeholder": "例如***********", "CASH_accountNo_hint": "請輸入{0}賬號，0開頭的11位數字", "BANK_TRANSFER_accountNo_title_SA": "銀行賬號（IBAN）", "BANK_TRANSFER_accountNo_title_AE": "銀行賬號（IBAN）", "BANK_TRANSFER_accountNo_title_TR": "銀行賬號（IBAN）", "BANK_TRANSFER_accountNo_title": "銀行賬號", "BANK_TRANSFER_accountNo_title_EG": "銀行賬號", "BANK_TRANSFER_accountNo_title_KW": "銀行賬號", "BANK_TRANSFER_accountNo_title_QA": "銀行賬號", "BANK_TRANSFER_accountNo_title_MA": "銀行賬號（RIB）", "BANK_TRANSFER_accountNo_title_PH": "銀行賬號", "BANK_TRANSFER_accountNo_title_BR": "銀行賬號", "BANK_TRANSFER_accountNo_placeholder_SA": "例如************************", "BANK_TRANSFER_accountNo_placeholder_EG": "例如************", "BANK_TRANSFER_accountNo_placeholder_AE": "例如***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "例如******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "例如*****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "例如**************************", "BANK_TRANSFER_accountNo_placeholder_MA": "例如123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "例如************", "BANK_TRANSFER_accountNo_placeholder_BR": "例如***********", "BANK_TRANSFER_accountNo_placeholder_JO": "例如******************************", "BANK_TRANSFER_accountNo_placeholder_MY": "例如**********", "BANK_TRANSFER_accountNo_placeholder_TH": "例如*********", "BANK_TRANSFER_accountNo_placeholder_ID": "例如***********", "BANK_TRANSFER_accountNo_hint_SA": "請輸入SA+22位字母，數字的組合", "BANK_TRANSFER_accountNo_hint_EG": "請輸入IBAN和本地銀行賬號，IBAN;29位字符（EG+27位數字）", "BANK_TRANSFER_accountNo_hint_AE": "請輸入AE+21位數字,AE必須大寫", "BANK_TRANSFER_accountNo_hint_KW": "請輸入長度<=50位，IBAN格式", "BANK_TRANSFER_accountNo_hint_QA": "請輸入長度<=50位，IBAN格式", "BANK_TRANSFER_accountNo_hint_TR": "請輸入TR開頭加24位數字，IBAN格式", "BANK_TRANSFER_accountNo_hint_MA": "請輸入RIB賬戶代碼24位數字", "BANK_TRANSFER_accountNo_hint_PH": "請輸入6~128位數字", "BANK_TRANSFER_accountNo_hint_BR": "請輸入4~15位數字", "BANK_TRANSFER_accountNo_hint_JO": "請輸入JO開頭的30位數字字母的組合", "BANK_TRANSFER_accountNo_hint_MY": "請輸入最多35位的賬號", "BANK_TRANSFER_accountNo_hint_TH": "請輸入10～18位數字賬號", "BANK_TRANSFER_accountNo_hint_ID": "請輸入數字賬號", "BANK_TRANSFER_bankBranch_title": "銀行網點號", "BANK_TRANSFER_bankBranch_placeholder_BR": "例如1234", "BANK_TRANSFER_bankBranch_hint_BR": "請輸入銀行網點號，4~6位數字", "BANK_TRANSFER_address_placeholder_SA": "例如aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "例如ALICI STREET KADIKOY ISTANBUL", "BANK_TRANSFER_address_placeholder_KW": "例如kuwait xx street", "BANK_TRANSFER_address_hint_SA": "請輸入地址，長度限制10~200字符，只允許數字、點、大小寫字母，空格", "BANK_TRANSFER_address_hint_AE": "請輸入地址，長度限制10~200字符，1~200位字符，銀行會進行風控校驗", "BANK_TRANSFER_address_hint_KW": "請輸入地址，長度<=255", "BANK_TRANSFER_fullName_placeholder_TR": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_PH": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_BR": "例如Antonio", "BANK_TRANSFER_fullName_placeholder_MA": "例如Jorge", "BANK_TRANSFER_fullName_placeholder_KW": "例如<PERSON> kin", "BANK_TRANSFER_phone_placeholder_PH": "例如***********", "BANK_TRANSFER_phone_placeholder_BR": "例如*************", "BANK_TRANSFER_phone_hint_PH": "請輸入你的移動電話號碼，09開頭11位的數字 （必須加0）", "BANK_TRANSFER_phone_hint_BR": "請輸入你的移動電話號碼，55開頭的12-13位數字", "BANK_TRANSFER_bankCode_title_MY": "銀行編號（SWIFTCode） ", "BANK_TRANSFER_bankCode_placeholder_MY": "例如CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "請輸入8-11位數字和字母的組合", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT代碼", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "請輸入8或11個大寫字母數字的組合", "BANK_TRANSFER_city_title_SA": "城市", "BANK_TRANSFER_city_placeholder_SA": "例如New York", "BANK_TRANSFER_city_hint_SA": "請輸入城市名稱，最長35位", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "例如：001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "請輸入24位數字的組合", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "出生日期", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "例如********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "請輸入出生日期", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "證件類型", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "請選擇證件類型", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "證件ID", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "例如NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "例如ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "請輸入證件id", "AGENT_accountNo_Whatsapp_title_ALL": "Whatsapp號碼", "AGENT_accountNo_Whatsapp_placeholder_ALL": "例如+90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "請輸入你的Whatsapp賬號", "AGENT_accountNo_vodafone_title_EG": "沃達豐賬號", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "區塊鏈名稱", "USDT_address_title_ALL": "發幣地址", "USDT_address_placeholder_ALL": "例如11111111111111111111111111", "USDT_address_hint_ALL": "請輸入1、3、bc開頭的地址", "USDT_address_hint_common": "請輸入你的usdt地址"}