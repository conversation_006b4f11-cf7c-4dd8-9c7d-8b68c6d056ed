<template>
  <div ref="loadMoreRef"></div>
</template>
<script setup>
import { onMounted, onUnmounted, ref } from "vue";
const emit = defineEmits(["loadMore"]);
const props = defineProps({
  root: {
    type: Element,
    default: null,
  },
});
const loadMoreRef = ref();
let observer = null;
const initObserver = () => {
  observer = new IntersectionObserver(
    (entries) => {
      const entry = entries[0];
      if (entry?.isIntersecting) {
        emit("loadMore");
      }
    },
    {
      root: props.root,
    }
  );
  observer.observe(loadMoreRef.value);
};

onMounted(() => {
  initObserver();
});

onUnmounted(() => {
  observer?.disconnect();
  observer = null;
});
</script>
