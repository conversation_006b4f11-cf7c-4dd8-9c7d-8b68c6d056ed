<template>
  <div
    class="list-button"
    @click="handelOpen()"
    v-track:click
    trace-key="singer_button_click"
    :track-params="JSON.stringify({ singer_button_name: 3 })"
  ></div>
  <van-overlay :show="showDialog" :lock-scroll="false" z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <div class="inner">
        <div class="top-bg"></div>
        <div class="box">
          <BorderBox>
            <div class="box-content">
              <div class="box-title">
                <BlockTitle
                  :show-line="true"
                  :font-size="18"
                  :lineWidth="21"
                  :iconGap="8"
                  :lineGap="2"
                  icon-size="small"
                  :title="$t(`singer.registration_record`)"
                ></BlockTitle>
              </div>
              <div class="desc">{{ $t(`singer.review_note`) }}</div>
              <div class="table">
                <div class="table-header">
                  <div class="table-header-item">
                    {{ $t(`singer.submission_time`) }}
                  </div>
                  <div class="table-header-item">
                    {{ $t(`singer.my_work`) }}
                  </div>
                  <div class="table-header-item">
                    {{ $t(`singer.submission_status_title`) }}
                  </div>
                </div>
                <div v-if="list.length > 0" class="table-body">
                  <div class="table-cell" v-for="item in list">
                    <div class="time">
                      <div>{{ item.create_time?.split(" ")[0] }}</div>
                      <div>{{ item.create_time?.split(" ")[1] }}</div>
                    </div>
                    <div class="audio">
                      <div class="audio-play">
                        <AudioPlayer
                          v-if="showDialog"
                          :url="item.media_url"
                        ></AudioPlayer>
                      </div>
                    </div>
                    <div class="state">
                      <div
                        class="state-tag state-0"
                        :class="formatStateClass(item.status)"
                      >
                        <span>{{ formatStateText(item.status) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <empty v-else :tips="$t('common.common_content_yet')"></empty>
              </div>
              <div class="box-bottom-cover"></div>
            </div>
          </BorderBox>
        </div>
        <div class="close-btn" @click="showDialog = false"></div>
      </div>
    </div>
  </van-overlay>
</template>
<script setup>
import { onMounted, ref } from "vue";
import BlockTitle from "./BlockTitle.vue";
import BorderBox from "./BorderBox.vue";
import AudioPlayer from "./AudioPlayer.vue";
import singerApi from "@/api/activity.js";
import { t } from "@/i18n/index.js";

const list = ref([]);
const showDialog = ref(false);
const handelOpen = () => {
  fetchData();
  showDialog.value = true;
};

const formatStateClass = (state) => {
  switch (state) {
    case 0:
      return `state-0`;
    case 1:
      return `state-1`;
    case 2:
      return `state-2`;
    case 3:
      return `state-3`;
    default:
      return `state-0`;
  }
};

const formatStateText = (state) => {
  switch (state) {
    case 0:
      return t("singer.submission_status_awaiting_review");
    case 1:
      return t("singer.submission_status_review_passed");
    case 2:
      return t("singer.submission_status_review_failed");
    case 3:
      return t("singer.submission_status_expired");
    default:
      return t("singer.submission_status_expired");
  }
};

const fetchData = async () => {
  const res = await singerApi.singer_register_record();
  if (res.code === 200) {
    list.value = res.data || [];
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.list-button {
  width: px2rem(35);
  height: px2rem(35);
  border-radius: px2rem(35);
  position: fixed;
  right: px2rem(7);
  top: px2rem(176);
  background-color: rgba(255, 255, 255, 0.34);
  background-image: url("@/assets/images/activity/singer/icon-list.png");
  background-size: px2rem(30) px2rem(30);
  background-position: center center;
  z-index: 99;
}
.base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: show 0.3s 1;
    .top-bg {
      background-image: url("@/assets/images/activity/singer/dialog-top.png");
      background-size: 100% 100%;
      width: px2rem(388);
      height: px2rem(98);
    }
    .box {
      margin-top: px2rem(-6);
      width: px2rem(324);
      .box-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .box-title {
          width: px2rem(277);
          padding-top: px2rem(24);
        }
        .desc {
          width: px2rem(226);
          margin-top: px2rem(17);
          color: #ff00f1;
          text-align: center;
          font-family: Gilroy;
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 700;
          line-height: 100%; /* 12px */
        }
        .table {
          width: px2rem(270);
          margin-top: px2rem(23);
          .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .table-header-item {
              display: flex;
              padding: px2rem(5) px2rem(8);
              width: px2rem(82);
              height: px2rem(34);
              justify-content: center;
              align-items: center;
              border-radius: px2rem(4);
              border: px2rem(1) solid #fff;
              background: linear-gradient(
                180deg,
                rgba(255, 255, 255, 0.34) 0%,
                rgba(154, 70, 255, 0.34) 100%
              );
              text-align: center;
              font-family: Gilroy;
              font-size: px2rem(12);
              font-style: normal;
              font-weight: 700;
              line-height: 100%; /* 12px */
              background: linear-gradient(180deg, #fff 13.29%, #d247ff 100%);
              background-clip: text;
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .table-body {
            // max-height: px2rem(160);
            height: px2rem(160);
            overflow-y: auto;
            padding-bottom: px2rem(53);
            margin-top: px2rem(27);
            .table-cell {
              display: flex;
              justify-content: center;
              margin-bottom: px2rem(12);
              .time {
                color: #d8bdff;
                font-family: Gilroy;
                font-size: px2rem(10);
                font-style: normal;
                font-weight: 500;
                text-align: center;
                line-height: 124%; /* 12.4px */
                width: 33.33%;
                flex-grow: 1;
              }
              .audio {
                width: 33.33%;
                flex-grow: 1;
                display: flex;
                justify-content: center;
                .audio-play {
                  width: px2rem(28);
                  height: px2rem(28);
                  flex-shrink: 0;
                }
              }
              .state {
                width: 33.33%;
                flex-grow: 1;
                display: flex;
                justify-content: center;
                .state-0 {
                  border: 2px solid #f4a9ff;
                  background: linear-gradient(
                    180deg,
                    rgba(255, 97, 240, 0.5) 13.41%,
                    rgba(148, 48, 255, 0.5) 77.63%
                  );
                  span {
                    background: linear-gradient(180deg, #fff 0%, #d1a3ff 100%);
                  }
                }
                .state-1 {
                  border: 2px solid #a9ffd4;
                  background: linear-gradient(
                    180deg,
                    rgba(97, 255, 205, 0.6) 13.41%,
                    rgba(0, 255, 47, 0.6) 81.73%
                  );
                  span {
                    background: linear-gradient(180deg, #fff 0%, #a3ffb7 100%);
                  }
                }
                .state-2 {
                  border: 2px solid #ffaca9;
                  background: linear-gradient(
                    180deg,
                    rgba(255, 97, 147, 0.6) 13.41%,
                    rgba(255, 48, 48, 0.6) 77.63%
                  );
                  span {
                    background: linear-gradient(180deg, #fff 0%, #ffa3a3 100%);
                  }
                }
                .state-3 {
                  border: 2px solid #c4c4c4;
                  background: linear-gradient(
                    180deg,
                    rgba(180, 180, 180, 0.4) 13.41%,
                    rgba(60, 46, 75, 0.4) 77.63%
                  );
                  span {
                    background: linear-gradient(
                      180deg,
                      #fff 15.72%,
                      #a3a3a3 100%
                    );
                  }
                }
                .state-tag {
                  width: px2rem(72);
                  height: px2rem(26);
                  padding: 0 px2rem(12);
                  flex-shrink: 0;
                  border-radius: px2rem(50);
                  text-align: center;
                  font-family: Gilroy;
                  font-size: px2rem(9);
                  font-style: normal;
                  font-weight: 500;
                  line-height: 100%; /* 9px */
                  display: flex;
                  justify-content: center;
                  align-items: center;

                  span {
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                  }
                }
              }
            }
          }
        }
        .box-bottom-cover {
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 100%;
          height: px2rem(53);
          border-radius: 0px 0px px2rem(7) px2rem(7);
          background: linear-gradient(
            180deg,
            rgba(32, 2, 71, 0) -24%,
            #24044c 44.02%
          );
        }
      }
    }
    .close-btn {
      width: px2rem(48);
      height: px2rem(48);
      margin-top: px2rem(26);
      flex-shrink: 0;
      background-image: url("@/assets/images/activity/singer/icon-close.svg");
      background-size: 100% 100%;
    }
  }
}

.rtl-html {
  .list-button {
    right: unset;
    left: px2rem(7);
  }
}
</style>
