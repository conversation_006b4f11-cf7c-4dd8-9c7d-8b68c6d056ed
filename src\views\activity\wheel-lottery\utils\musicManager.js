import clickSrc from "@/assets/images/wheel-lottery/music/click.mp3";
import prizeSrc from "@/assets/images/wheel-lottery/music/prize.mp3";
import roundSrc from "@/assets/images/wheel-lottery/music/round.mp3";
import targetSrc from "@/assets/images/wheel-lottery/music/target.mp3";

class MusicManager {
  constructor() {
    // 使用单个AudioContext实例
    this.audioContext = new (window.AudioContext ||
      window.webkitAudioContext)();

    // 存储当前播放源
    this.currentSources = {
      click: null,
      prize: null,
      round: null,
      target: null,
    };

    // 存储解码后的音频缓冲区
    this.audioBuffers = {};

    // 预加载所有音频
    this.preloadAudios();
  }

  // 预加载所有音频资源
  async preloadAudios() {
    try {
      const audioUrls = {
        click: clickSrc,
        prize: prizeSrc,
        round: roundSrc,
        target: targetSrc,
      };

      for (const [type, url] of Object.entries(audioUrls)) {
        const buffer = await this.fetchAndDecode(url);
        this.audioBuffers[type] = buffer;
      }
    } catch (error) {
      console.error("音频预加载失败:", error);
    }
  }

  // 获取并解码音频
  async fetchAndDecode(url) {
    const response = await fetch(url);
    if (!response.ok) throw new Error(`音频加载失败: ${url}`);
    const arrayBuffer = await response.arrayBuffer();
    return this.audioContext.decodeAudioData(arrayBuffer);
  }

  // 播放音频
  play(type) {
    // 停止当前同类型音频（防止重叠播放）
    this.stop(type);

    // 获取缓存中的音频
    const buffer = this.audioBuffers[type];
    if (!buffer) {
      console.warn(`未找到音频缓存: ${type}`);
      return;
    }

    // 创建播放源
    const source = this.audioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(this.audioContext.destination);

    // 存储当前播放源
    this.currentSources[type] = source;

    // 设置播放结束清理
    source.onended = () => {
      if (this.currentSources[type] === source) {
        this.currentSources[type] = null;
      }
    };

    // 开始播放
    source.start(0);
  }

  // 停止播放
  stop(type) {
    const source = this.currentSources[type];
    if (source) {
      try {
        source.stop();
        source.disconnect();
      } catch (e) {
        console.warn("停止音频时出错:", e);
      }
      this.currentSources[type] = null;
    }
  }

  // 具体播放方法
  playClick() {
    this.play("click");
  }
  stopClick() {
    this.stop("click");
  }

  playPrize() {
    this.play("prize");
  }
  stopPrize() {
    this.stop("prize");
  }

  playRound() {
    this.play("round");
  }
  stopRound() {
    this.stop("round");
  }

  playTarget() {
    this.play("target");
  }
  stopTarget() {
    this.stop("target");
  }
}

export default new MusicManager();
