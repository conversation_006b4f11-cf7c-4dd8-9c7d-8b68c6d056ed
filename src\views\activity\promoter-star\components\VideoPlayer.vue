<template>
  <div class="video-item">
    <div class="cover">
      <img v-if="cover" :src="cover" />
    </div>
    <div
      class="play-icon"
      @click="handlePlay()"
      v-track:click
      trace-key="star_ambassador_demo_play"
    ></div>
  </div>
  <video class="hidden" :muted="true" preload="auto">
    <source :src="url" />
  </video>
  <Teleport to="body">
    <van-overlay :show="showDialog" z-index="1001" @click="handleStop()">
      <div class="video-base-modal-wrapper">
        <div class="wrapper" @click.stop>
          <div class="close-btn" @click="handleStop()">
            <svg-icon icon="close" color="#fff"></svg-icon>
          </div>
          <video
            ref="videoRef"
            width="200"
            :src="url"
            controls
            preload="auto"
            :autoplay="false"
          >
            <source :src="url" />
          </video>
        </div>
      </div>
    </van-overlay>
  </Teleport>
</template>
<script setup>
import { computed, nextTick, ref, getCurrentInstance } from "vue";
const videoCoverRef = ref();
const videoRef = ref();
const { proxy } = getCurrentInstance();

const props = defineProps({
  url: {
    type: String,
    default: "",
  },
  cover: {
    type: String,
    default: "",
  },
  iconSize: {
    type: Number,
    default: 36,
  },
});

const showDialog = ref(false);
const realIconSize = computed(() => `${proxy.$pxToRemPx(props.iconSize)}px`);

const handleLoadeddata = () => {
  videoCoverRef.value.pause();
};

const handleCanplay = () => {
  // 尝试自动播放（需要muted属性）
  videoCoverRef.value
    .play()
    .then(() => {
      videoCoverRef.value.pause();
    })
    .catch((e) => {
      // 自动播放被阻止，但仍会加载第一帧
      videoCoverRef.value.pause();
    });
};

const handlePlay = () => {
  showDialog.value = true;
  nextTick(() => {
    videoRef.value.currentTime = 0;
    videoRef.value.play();
  });
};
const handleStop = () => {
  videoRef.value.pause();
  showDialog.value = false;
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.video-item {
  width: 100%;
  height: 100%;
  border-radius: px2rem(4);
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background: var(--black-4, rgba(0, 0, 0, 0.04));
  .cover {
    position: absolute;
    inset: 0;
    & > img {
      width: 101%;
      height: 100%;
      object-fit: cover;
      margin-left: -0.5%;
      z-index: 2;
    }

    & > video {
      z-index: 1;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &::after {
      content: "";
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.16);
    }
  }
  .play-icon {
    width: v-bind(realIconSize);
    height: v-bind(realIconSize);
    position: relative;
    z-index: 2;
    background-image: url("@/assets/images/icon/icon-play.svg");
    background-size: 100% 100%;
  }
}

@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.video-base-modal-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: show 0.3s 1;
  .wrapper {
    position: relative;
  }
  .close-btn {
    z-index: 9;
    width: px2rem(24);
    height: px2rem(24);
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: px2rem(12);
    position: absolute;
    right: px2rem(10);
    top: px2rem(10);
  }
  video {
    border-radius: px2rem(10);
    width: 80vw;
    max-height: 80vh;
  }
}

.hidden {
  position: fixed;
  z-index: -1;
  left: -9990px;
  opacity: 0;
}
</style>
