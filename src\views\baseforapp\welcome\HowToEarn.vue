<template>
  <div class="app-container">
    <header-bar back-icon-show
                :show-title="false"
                :content-padding="false"
                close-type="close"
    />
    <header>
      <img class="header-title" :src="getImageUrl('title.png')" alt="">
      <canvas class="ami" id="pagCanvas"></canvas>
    </header>
    <main>
      <div class="cell-wrap" v-for="(tip, idx) in tips" :key="idx">
        <div class="cell-header">{{ tip.title }}</div>
        <div :class="['cell-body', {mb: !tip.nomb}]">
          <img :src="tip.img" alt="">
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { getCurrentInstance, onMounted } from 'vue'
import { i18n } from '@/i18n/index.js'
import { AnimationPAGInit } from '@/utils/util.js'

const { proxy } = getCurrentInstance()
const tm = i18n.global.tm

const lang = proxy.$languageFile

const tips = [
  {
    title: tm('base_total.how_to_earn_msg')[0],
    img: getImageUrl('tips1.jpg'),
  },
  {
    title: tm('base_total.how_to_earn_msg')[1],
    img: getImageUrl('tips2.jpg'),
  },
  {
    title: tm('base_total.how_to_earn_msg')[2],
    img: getImageUrl('tips3.jpg'),
  },
  {
    title: tm('base_total.how_to_earn_msg')[3],
    img: getImageUrl('tips4.jpg'),
  },
  {
    title: tm('base_total.how_to_earn_msg')[4],
    img: getImageUrl('tips5.jpg'),
    nomb: true,
  },
  {
    title: tm('base_total.how_to_earn_msg')[5],
    img: getImageUrl('tips6.jpg'),
  },
]

const pagCanvas = '#pagCanvas'
let animationObj = null;
const animationUrl = 'https://siya-packet.obs.ap-southeast-3.myhuaweicloud.com/mgmt/development/test/lns9obvrhd9kw4zbbu5acfz5zu43xews.pag'
const playAnimation = async () => {
  animationObj = await AnimationPAGInit(animationUrl, pagCanvas)
  animationObj.setRepeatCount(0);
  await animationObj.play();
}


function getImageUrl(name) {
  return new URL(`../../../assets/images/how-to-earn/${lang}/${name}`, import.meta.url).href
}

onMounted(() => {
  playAnimation()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  position: relative;
  background: linear-gradient(180deg, #FF498D 0%, #FF4B97 100%);
}

header {
  margin-bottom: px2rem(184);
  font-size: 0;

  .header-title {
    width: 100%;
  }

  .ami {
    position: absolute;
    top: px2rem(88);
    left: 0;
    width: px2rem(390);
    height: px2rem(560);
  }
}

main {
  position: relative;
  z-index: 1;
  padding: 0 px2rem(18) px2rem(20);
}

.cell-wrap {
  overflow: hidden;
  background: #FFFDF8;
  border: 3px solid #FFF7E7;
  border-radius: $radius24;

  &:not(:last-child) {
    margin-bottom: px2rem(20);
  }

  .cell-header {
    padding: $gap16 px2rem(20);
    margin-bottom: $gap16;
    font-size: px2rem(20);
    font-weight: $fontWeightBold;
    line-height: px2rem(24);
    text-align: center;
    color: #6B4309;
    background: url("@/assets/images/how-to-earn/cell-header-bg.jpg") bottom/cover no-repeat;
  }

  .cell-body {
    font-size: 0;

    &.mb {
      margin-bottom: $gap16;
    }

    img {
      width: 100%;
    }
  }
}
</style>
