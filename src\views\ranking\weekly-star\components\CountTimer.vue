<template>
  <div class="count-wrap isolated">
    <div class="cell">{{ !days || days <= 0 ? 0 : days }}</div>
    <div class="cell">D</div>
    <div class="cell">{{ !hours || hours <= 0 ? 0 : hours }}</div>
    <div class="cell">H</div>
    <div class="cell">{{ !minutes || minutes <= 0 ? 0 : minutes }}</div>
    <div class="cell">M</div>
    <div class="cell">{{ !seconds || seconds <= 0 ? 0 : seconds }}</div>
    <div class="cell">S</div>
  </div>
</template>

<script setup>
import { onUnmounted, ref, watch } from "vue";
import dayjs from "dayjs";

const emit = defineEmits(["update"]);
const props = defineProps({
  time: {
    type: Array,
    default: [],
  },
});

let timer = null;
const days = ref(0);
const hours = ref(0);
const minutes = ref(0);
const seconds = ref(0);
const nowTime = ref();
// 当前时间

const clearTimer = () => {
  clearTimeout(timer);
  timer = null;
};
const updateCountdown = (isAdd = true) => {
  if (isAdd) nowTime.value = dayjs(nowTime.value).add(1, "second");
  const timeDiff = dayjs(props.time[1]).diff(nowTime.value);
  if (timeDiff < 0) {
    emit("update");
    return false;
  }

  days.value = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  hours.value = Math.floor(
    (timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
  );
  minutes.value = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
  seconds.value = Math.floor((timeDiff % (1000 * 60)) / 1000);
  timer = setTimeout(updateCountdown, 1000);
};

watch(
  () => props.time,
  (value) => {
    clearTimer();
    nowTime.value = value[0];
    updateCountdown(false);
  }
);

onUnmounted(() => {
  clearTimer();
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.count-wrap {
  display: flex;
  text-align: center;
  .cell {
    height: px2rem(30);
    width: px2rem(30);
    font-size: $fontSize17;
    line-height: px2rem(30);
    color: #fff;
    font-weight: 700;
    font-family: "DomaineDisp";
    &:nth-child(2n -1) {
      background-image: url("@/assets/images/ranking/weekly-star/time-border.png");
      background-size: 100%;
      background-repeat: no-repeat;
      font-size: $fontSize13;
    }
  }
}
</style>
