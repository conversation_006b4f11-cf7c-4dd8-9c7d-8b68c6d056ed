<template>
  <div class="app-container flex">
    <header class="flex">
      <div class="header-left flex">
        <img src="@/assets/images/invite/logo.png" alt="" class="logo" />
        <gap :gap="8"></gap>
        <div class="inner">
          <img src="@/assets/images/invite/siya.png" alt="" class="siya" />
          <span>{{ $t("invite.link_user_rewards") }}</span>
        </div>
      </div>
      <div class="header-btn" @click="handleDownLoad">
        {{ $t("common.common_download") }}
      </div>
    </header>
    <div class="invite-wrap flex">
      <img :src="linkData.avatar" alt="" class="avatar" />
      <gap :gap="8"></gap>
      <div
        class="invite-right flex"
        :class="[currentLang === 'ar' ? 'radius-right ' : 'radius-left']"
      >
        <div class="content">
          {{ $t("invite.link_invite_male_desc", [linkData.nick_name]) }}
        </div>
        <div class="btn" @click="handleDownLoad">
          {{ $t("common.common_download") }}
        </div>
        <div class="invite-inner flex" @click="handleCopy">
          {{ $t("invite.link_invite_code") }} {{ inviteCode }}
          <img
            src="@/assets/images/invite/icon-coyp.png"
            alt=""
            class="copy-icon"
          />
        </div>
      </div>
      <div class="adorn-box flex">
        <img
          src="@/assets/images/invite/adorn.png"
          alt=""
          :class="{ 'ar-img': currentLang === 'ar' }"
        />
      </div>
    </div>
    <main>
      <div class="video-wrap">
        <video
          :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/video/invite/${languageFileMap[currentLang]}/male.mp4`"
          loop
          poster="@/assets/images/invite/poster.png"
          preload
          controls
          autoplay
          :muted="isMuted"
          id="video"
          playsinline
        ></video>
      </div>
      <div class="picture-wrap">
        <img
          :src="`https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/invite/${languageFileMap[currentLang]}/picture-${i}.jpg`"
          alt=""
          v-for="i in 6"
          :key="i"
        />
      </div>
    </main>
  </div>
</template>

<script setup>
import { i18n, resetDocumentRtl, languageFileMap } from "@/i18n/index.js";
import inviteApi from "@/api/invite.js";
import clipboard from "@/utils/clipboard";
import { isAndroid } from "@/utils/util.js";
import { nextTick, ref } from "vue";

const t = i18n.global.t;
const appKeyEnvMap = {
  1: "n8jcly",
  2: "rrdg9z",
  3: "rrdg9z",
};
let that = null;
let appKey = "";
const visible = ref(false);
const isMuted = ref(true);
const currentLang = ref("en");

const linkData = ref([]);
const inviteCode = ref("");

const getUrlChannelCode = () => {
  const urlParams = new URLSearchParams(window.location.search);
  // const channelCode = urlParams.get("channelCode");
  const channelCode = urlParams.get("t").substring(7);
  return channelCode;
};

const getUrlHash = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const hash = urlParams.get("t").substring(1, 7);
  return hash;
};

const getAppKey = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const env = urlParams.get("t").substring(0, 1);
  return appKeyEnvMap[env];
};

const loadScript = () => {
  return new Promise((resolve, reject) => {
    if (!appKey) reject("APPKEY is null");
    const script = document.createElement("script");
    script.src = `https://res.openinstalljs.com/openinstall-${appKey}.js`;
    script.type = "text/javascript";
    script.onload = resolve;
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

const handleCopy = (event) => {
  if (!inviteCode.value) return;
  clipboard(inviteCode.value, event, t("guild.copy_success")).then(() => {
    copyPopupRef.value.open();
  });
};

const handleDownLoad = () => {
  if (!that) return false;
  that.wakeupOrInstall();
};

const fetchData = async (code) => {
  if (!code) return;
  const res = await inviteApi.link_exchange_rate({ invite_code: code });
  if (res.code === 200) {
    linkData.value = res.data;
  }
};

const initVideoPlay = () => {
  nextTick(() => {
    const video = document.getElementById("video");
    video.addEventListener("play", function () {
      if (video.webkitEnterFullscreen) {
        isMuted.value = false;
        video.setAttribute("muted", false);
        video.webkitEnterFullscreen();
      } else {
        // 非 iOS 设备使用标准全屏 API
        // if (video.requestFullscreen) {
        //   video.requestFullscreen();
        // } else if (video.webkitRequestFullscreen) {
        //   video.webkitRequestFullscreen();
        // } else if (video.msRequestFullscreen) {
        //   video.msRequestFullscreen();
        // }
      }
    });
  });
};

const init = async () => {
  const res = await inviteApi.short_link_restore({
    link_hash: getUrlHash(),
    channel_code: getUrlChannelCode(),
    os: isAndroid() ? 1 : 2,
  });
  if (res.code === 200) {
    fetchData(res.data.shareCode);
    inviteCode.value = res.data.shareCode;
    appKey = res.data.app_key;
    i18n.global.locale = res.data.lang || "en";
    currentLang.value = res.data.lang || "en";
    resetDocumentRtl();
    visible.value = true;
    initVideoPlay();
    loadScript()
      .then(() => {
        console.log("OpenInstall SDK 加载成功");
        new OpenInstall(
          {
            appKey, //appKey为openinstall为应用分配的唯一id（必须传入）
            channelCode: res.data.channelCode, //传入渠道编号，优先级大于链接上拼接的渠道编号
            fastInstall: false, //快速安装功能开关，true为开启，false为关闭
            onready: function () {
              //初始化成功回调方法。当初始化完成后，会自动进入
              this.schemeWakeup(); //尝试使用scheme打开App（主要用于Android以及iOS的QQ环境中）
              that = this;
              // this.wakeupOrInstall();
              // button = document.getElementById("downloadButton"); //为button对象绑定对应id的元素
              // // m.wakeupOrInstall();
              // button.onclick = function () {
              //   //对应button的点击事件
              //   m.wakeupOrInstall(); //此方法为scheme、Universal Link唤醒以及引导下载的作用（必须调用且不可额外自行跳转下载）
              //   return false;
              // };
            },
          },
          res.data
        );
      })
      .catch((error) => {
        console.error("OpenInstall SDK 加载失败:", error);
      });
  }
};

init();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: #5f56ed;
  padding-top: px2rem(76);
  position: relative;
  header {
    width: 100%;
    background: none;
    height: px2rem(64);
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(12);
    position: fixed;
    top: 0;
    left: 0;
    flex-wrap: nowrap;
    z-index: 101;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: px2rem(64);
      opacity: 0.5;
      z-index: -1;
      background: #ffffff52;
    }

    .header-left {
      flex: 1;
      flex-wrap: nowrap;
      .logo {
        width: px2rem(48);
        height: px2rem(48);
      }
      .inner {
        display: flex;
        flex-direction: column;
        font-size: $fontSize13;
        color: $white;
        .siya {
          width: px2rem(48);
          height: px2rem(23);
          margin-bottom: px2rem(2);
        }
      }
    }
    .header-btn {
      height: px2rem(34);
      line-height: px2rem(34);
      background: #fff;
      min-width: px2rem(84);
      padding: 0 px2rem(4);
      border-radius: px2rem(34);
      animation: spin-head 1.2s ease-in-out infinite;
      text-align: center;
      color: #5f56ed;
      font-weight: bold;
      font-size: $fontSize15;
      white-space: nowrap;
    }
  }
  .invite-wrap {
    width: 100%;
    padding: px2rem(12);
    position: relative;
    font-size: $fontSize15;
    align-items: flex-start;
    .avatar {
      width: px2rem(56);
      height: px2rem(56);
      border-radius: 50%;
      object-fit: cover;
    }
    .invite-right {
      background-color: $white;
      border-radius: px2rem(24);
      padding: px2rem(13) px2rem(10);
      width: px2rem(279);
      flex-direction: column;
      .btn {
        background: linear-gradient(180deg, #ffc933 0%, #fe9318 100%);
        font-size: px2rem(24);
        font-weight: bold;
        height: px2rem(50);
        width: px2rem(224);
        border-radius: px2rem(50);
        line-height: px2rem(50);
        text-align: center;
        padding: 0 px2rem(12);
        color: #fff;
        margin: px2rem(12) auto;
      }
      .invite-inner {
        color: #ff7423;
        @include centerL;
        .copy-icon {
          height: px2rem(20);
          width: px2rem(20);
        }
      }
    }
    .adorn-box {
      position: absolute;
      bottom: 0;
      left: 0;
      justify-content: flex-end;
      pointer-events: none;
      width: 100%;
      img {
        height: px2rem(43);
        width: px2rem(53);
      }
      .ar-img {
        transform: scale(-1);
      }
    }
    .radius-right {
      border-top-right-radius: px2rem(8);
    }
    .radius-left {
      border-top-left-radius: px2rem(8);
    }
  }
  main {
    width: 100%;
    padding: px2rem(12) px2rem(14);
    .title {
      width: 100%;
      min-height: px2rem(100);
      margin-bottom: px2rem(14);
    }
    .video-wrap {
      width: 100%;
      border-radius: px2rem(30);
      overflow: hidden;
      background: #837bff;
      margin: 0 0 px2rem(24);
      min-height: px2rem(610);
      border: px2rem(4) solid #837bff;
      display: flex;
      position: relative;
      video {
        width: 100%;
      }
    }
    .picture-wrap {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      img {
        height: px2rem(230);
        width: auto;
        border-radius: px2rem(20);
        margin-bottom: px2rem(16);
      }
    }
  }
}

@keyframes spin-head {
  0% {
    transform: scale(1);
  }
  10% {
    transform: scale(1);
  }
  20% {
    transform: scale(1.2);
  }
  40% {
    transform: rotate(10deg) scale(1.2);
  }
  60% {
    transform: rotate(-10deg) scale(1.2);
  }
  80% {
    transform: scale(1.2);
  }
  90% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
