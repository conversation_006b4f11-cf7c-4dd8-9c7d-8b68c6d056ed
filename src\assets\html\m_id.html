<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="og:title" content="Temanmu mengundangmu ke Siya" />
    <meta property="og:description"
        content="Ngobrol gratis dengan cewek-cewek di sekitarmu, cari teman jadi makin cepat! Download lewat link dan isi kode undangan untuk klaim bonus pemula!" />
    <meta property="og:image"
        content="https://siya-packet-kodo-acc.siyachat.com/siya_H5/images/invite/share-male.png" />
    <script async>
        try {
            const envMap = {
                1: 'https://server.siyatest.com/i/m',
                2: 'https://server-gray.siyachat.com/i/m',
                3: 'https://siyachat.com/i/m',
            }
            const url = location.href;
            const regex = /\?(.*)/;
            const match = url.match(regex);

            const urlParams = new URLSearchParams(window.location.search);
            const env = urlParams.get("t").substring(0, 1);
            const host = envMap[env];
            if (match && host) {
                const queryString = match[1];
                window.location.href = `${host}?${queryString}`
            }
        } catch (error) {
            console.error(error)
        }
    </script>
</head>

</html>