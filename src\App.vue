<template>
  <!-- 顶部安全区 -->
<!--  <div class="van-safe-area-top"></div>-->

  <router-view v-slot="{ Component }">
    <keep-alive>
      <component :is="Component" :key="$route.fullPath" v-if="$route.meta.keepAlive"/>
    </keep-alive>
    <component :is="Component" :key="$route.fullPath" v-if="!$route.meta.keepAlive"/>
  </router-view>

  <!-- 底部安全区 -->
<!--  <div class="van-safe-area-bottom"></div>-->
</template>


<script setup>
import { getCurrentInstance, onMounted } from 'vue'
import { resetDocumentRtl, setPageTitle18n } from '@/i18n/index.js'
import { useRoute } from 'vue-router'

const { proxy } = getCurrentInstance()
const route = useRoute()

onMounted(() => {
  // proxy.$app.config.globalProperties.$languageFile = checkLang()
  resetDocumentRtl()
  setPageTitle18n(route.name)
})
</script>


<style lang="scss">
@import '@/assets/scss/common.scss';

#app {
  min-height: 100vh;
  margin: 0 auto;
  background-color: $bgColorB7;
}
</style>
