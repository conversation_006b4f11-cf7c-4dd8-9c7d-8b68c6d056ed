<template>
  <div class="block">
    <block-box :title="$t(`promoter_star.participation_method`)" :icon="Icon">
      <div class="block-content">
        <div class="tip">1:{{ $t(`promoter_star.publish_talent_video`) }}</div>
        <div class="tip">
          2:{{ $t(`promoter_star.select_tags`, ["@Siya"]) }}
        </div>
        <div
          class="example"
          :style="{
            backgroundImage: `url(${getImageUrl('example.jpg')})`,
          }"
        ></div>
      </div>
    </block-box>
  </div>
</template>
<script setup>
import BlockBox from "./BlockBox.vue";
import { checkLang } from "@/i18n/index.js";
import Icon from "@/assets/images/promoter-star/icon-2.png";

const lang = checkLang();
function getImageUrl(name) {
  return new URL(
    `../../../../assets/images/promoter-star/${lang}/${name}`,
    import.meta.url
  ).href;
}
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.block {
  padding: 0 px2rem(12);
  padding-top: px2rem(24);
  .block-content {
    padding: px2rem(8);
    border-radius: 12px;
    background: #e2f7ff;
  }
  .tip {
    color: #145057;
    font-family: Gilroy;
    font-size: px2rem(17);
    margin-bottom: px2rem(20);
    font-style: normal;
    font-weight: 500;
    line-height: 132%;
  }
  .example {
    width: px2rem(326);
    height: px2rem(274);
    background-size: 100% 100%;
    border-radius: px2rem(8);
  }
}
</style>
