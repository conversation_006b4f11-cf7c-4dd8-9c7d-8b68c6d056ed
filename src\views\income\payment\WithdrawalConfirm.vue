<template>
  <div class="app-container">
    <header-bar is-scroll-change-bg />

    <main>
      <form-display
          :current-item="currentItem"
          @change="handleChange"
          @edit="handleEdit"
      />

      <van-button class="submit-button"
                  type="primary"
                  block
                  @click="handleSubmit"
      >
        {{ $t('common.common_submit') }}
      </van-button>
    </main>

    <footer>
      <div class="tips-title">{{ $t('withdraw.withdrawal_rule_title') }}</div>
      <div class="tips-detail flex"
           v-for="(text, idx) in $tm('withdraw.withdrawal_rule_detail')" :key="idx"
      >
        <div class="tips-step">{{idx + 1}}.</div>
        <div>{{text}}</div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
import { storeToRefs } from 'pinia'
import { i18n } from '@/i18n/index.js'
import { useRouter } from 'vue-router'
import { useWithdrawStore } from '@/store/index.js'
import withdrawApi from '@/api/withdraw.js'
import FormDisplay from './components/FormDisplay.vue'

const t = i18n.global.t
const { proxy } = getCurrentInstance()
const router = useRouter()
const withdrawStore = useWithdrawStore()

const {
  currentChannelInfo: currentItem,
} = storeToRefs(withdrawStore)

const handleChange = () => {
  router.go(-1)
}
const handleEdit = () => {
  router.push({ name: 'WithdrawalForm' })
}

const handleSubmit = async () => {
  const params = {
    product_id: withdrawStore.currentChannelInfo.id,
    payment: withdrawStore.currentChannelInfo.payment,
    cfg_bid: withdrawStore.currentChannelInfo.bid,
  }
  const response = await withdrawApi.withdraw(params)
  if (response.code === 200) {
    router.go(-2)
  }
}
</script>

<style scoped lang="scss">
@import "./assets/payment.scss";

.app-container {
  padding: $gap8 $gap16 $pageBottomPadding;
}

footer {
  padding: $gap8 0;

  .tips-title {
    margin-bottom: $gap16;
    font-weight: $fontWeightBold;
    color: $fontColorB3;

    @include fontSize13;
  }

  .tips-detail {
    flex-wrap: nowrap;
    align-items: stretch;
    color: $fontColorB3;

    @include fontSize11;

    .tips-step {
      flex: none;
      width: px2rem(12);
    }
  }
}
</style>
