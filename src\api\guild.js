import request from '@/utils/request'

export default {
  my_guild(query) {
    return request({
      url: '/client_web/guild/my_guild_new',
      method: 'get',
      params: query
    })
  },
  member_list(query) {
    return request({
      url: '/client_web/guild/member_list',
      method: 'get',
      params: query
    })
  },
  member_list_new(query) {
    return request({
      url: '/client_web/guild/member_list_new',
      method: 'get',
      params: query
    })
  },
  member_info(query) {
    return request({
      url: '/client_web/guild/member_detail',
      method: 'get',
      params: query
    }) 
  },

  // 主播周工资
  room_inspire(query) {
    return request({
      url: '/client_web/guild/room_inspire',
      method: 'get',
      params: query
    })
  },
  imcome_detail(query) {
    return request({
      url: '/client_web/guild/imcome_detail',
      method: 'get',
      params: query
    }) 
  },
  // 房主任务规则
  task_rule(query) {
    return request({
      url: '/client_web/room_task/info',
      method: 'get',
      params: query
    })
  }
}
