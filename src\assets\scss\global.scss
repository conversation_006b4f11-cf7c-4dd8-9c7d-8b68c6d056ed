@import "config/reset";
@import "config/vant";
@import "rtlClass";
@import "font";

html,
body {
  padding: 0;
  margin: 0;
  font-size: calc(100vw / 10);
  -webkit-tap-highlight-color: transparent;
  -webkit-overflow-scrolling: touch;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
  font-family: Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei,
    Arial, sans-serif;
}

body {
  font-family: <PERSON><PERSON>;
}

// 禁止ios长按显示出原图
img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.app-container {
  min-height: 100vh;
  //font-size: 0.4266666667rem;
}

.page-container {
  font-size: 16px;
}

.isolated {
  direction: ltr;
  unicode-bidi: isolate;
}

@media screen and (min-width: 480px) {
  .app-container {
    width: 100%;
    min-height: 100%;
    font-size: 16px;
  }
}
