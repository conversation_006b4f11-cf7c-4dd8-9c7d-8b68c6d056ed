<template>
  <div class="swipe">
    <van-swipe
      dir="ltr"
      ref="swipeRef"
      :show-indicators="false"
      class="swipe-item"
    >
      <van-swipe-item
        v-for="(reward, index) in rewards"
        :key="index"
        class="swipe-item"
      >
        <div class="swipe-item-inner" :dir="lang === 'ar' ? 'rtl' : ''">
          <div class="swipe-box">
            <div class="background">
              <div class="background-top"></div>
              <div class="background-padding"></div>
            </div>
            <div class="swipe-content">
              <div class="title">
                {{ $t("recharge_ranking.top_reward", [index + 1]) }}
              </div>
              <div class="rewards">
                <div
                  class="reward"
                  v-for="(item, index) in reward"
                  :key="index"
                >
                  <div class="reward-cover">
                    <PrizeBox
                      :img="
                        item.reward_type === 1
                          ? CoinIcon
                          : item.reward_img || CoinIcon
                      "
                      :tag="formatTag(item)"
                    ></PrizeBox>
                  </div>
                  <div class="reward-name">{{ item.reward_name }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
    <div
      class="arrow-left"
      @click="handlePrev()"
      v-track:click
      trace-key="r_recharge_button_click"
      :track-params="JSON.stringify({ active_button_name: 3 })"
    >
      <svg-icon icon="recharge-swiper-left" color="#FFA411"></svg-icon>
    </div>
    <div
      class="arrow-right"
      @click="handleNext()"
      v-track:click
      trace-key="r_recharge_button_click"
      :track-params="JSON.stringify({ active_button_name: 3 })"
    >
      <svg-icon icon="recharge-swiper-right" color="#FFA411"></svg-icon>
    </div>
  </div>
</template>
<script setup>
import CoinIcon from "@/assets/images/common/coin.svg";
import PrizeBox from "./PrizeBox.vue";
import { ref } from "vue";
import { i18n, getLang } from "@/i18n/index.js";
const t = i18n.global.t;
const props = defineProps({
  rewards: {
    type: Array,
    default: [],
  },
});
const lang = getLang();
const swipeRef = ref();

const handlePrev = () => {
  swipeRef.value.prev();
};
const handleNext = () => {
  swipeRef.value.next();
};
const formatTag = (item) => {
  if (item.reward_type === 1) {
    return lang === "ar" ? `${item.num}X` : `X${item.num}`;
  }
  return item.duration > 1
    ? t("common.common_days", [item.duration])
    : t("common.common_day", [item.duration]);
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.swipe {
  position: relative;
}
.swipe-item {
  height: px2rem(197);
  overflow: visible;
}
.swipe-item-inner {
  width: 100%;
  min-height: px2rem(197);
  display: flex;
  justify-content: center;
  align-items: center;
  .swipe-box {
    position: relative;
    width: px2rem(278);
    min-height: px2rem(197);
    flex-shrink: 0;
    .background {
      width: px2rem(278);
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      .background-top {
        z-index: 4;
        width: px2rem(346);
        height: px2rem(126);
        position: absolute;
        top: px2rem(-42);
        left: px2rem(-34);
        background-image: url("@/assets/images/activity/recharge-ranking/dialog-top.png"); /* 中间部分的图片 */
        background-size: 100% 100%;
        transform: scale(0.8);
      }
      .background-padding {
        flex-grow: 1;
        background-image: url("@/assets/images/activity/recharge-ranking/reward-box-2.png"); /* 中间部分的图片 */
        background-repeat: repeat-y; /* 垂直重复 */
        background-position: center left;
        background-size: 100%;
      }
    }

    .background::before {
      content: "";
      width: 100%;
      height: px2rem(85); /* 顶部图片的高度 */
      background-image: url("@/assets/images/activity/recharge-ranking/reward-box-1.png"); /* 顶部图片 */
      background-repeat: no-repeat;
      background-position: top left;
      background-size: 100% 100%;
      margin-bottom: px2rem(-1);
    }

    .background::after {
      content: "";
      width: 100%;
      height: px2rem(85); /* 顶部图片的高度 */
      background-image: url("@/assets/images/activity/recharge-ranking/reward-box-1.png"); /* 使用相同的顶部图片 */
      background-repeat: no-repeat;
      background-position: top left;
      background-size: 100% 100%;
      transform: scaleY(-1); /* 垂直翻转 */
      margin-top: px2rem(-1);
    }

    .swipe-content {
      position: relative;
      .title {
        text-align: center;
        font-family: Gilroy;
        font-size: px2rem(20);
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        background: linear-gradient(0deg, #fb0 0%, #fff 84.13%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-top: px2rem(17);
      }
      .rewards {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        margin-top: px2rem(20);
        gap: px2rem(30);
        .reward {
          width: px2rem(66);
          .reward-cover {
            width: px2rem(66);
            height: px2rem(66);
          }
          .reward-name {
            color: #e3857b;
            text-align: center;
            font-family: Gilroy;
            font-size: px2rem(12);
            font-style: normal;
            font-weight: 700;
            line-height: 100%; /* 12px */
            margin-top: px2rem(12);
          }
        }
      }
    }
  }
}

.arrow-left,
.arrow-right {
  width: px2rem(28);
  height: px2rem(28);
  font-size: px2rem(28);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.arrow-left {
  left: px2rem(18);
}
.arrow-right {
  right: px2rem(18);
}
.lang-es {
  .swipe-content {
    .title {
      font-size: px2rem(16) !important;
    }
  }
}
</style>
