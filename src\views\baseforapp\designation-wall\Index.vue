<template>
  <div class="app-container flex">
    <header-bar fontColor="#fff" fixedBgColor="#000" closeType="close"/>
    <div class="title">{{ $t("base_total.designation_wall_what_is") }}</div>
    <div class="desc">
      {{ $t("base_total.designation_wall_usage_instructions") }}
    </div>
    <img
      src="@/assets/images/designation-wall/nameplate.png"
      class="nameplate"
      alt=""
    />
    <div class="img-text">
      {{ $t("base_total.designation_wall_nameplate_card") }}
    </div>
    <img
      src="@/assets/images/designation-wall/data-page.png"
      class="data-page"
      alt=""
    />
    <div class="img-text">
      {{ $t("base_total.designation_wall_profile_page") }}
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter } from "vue-router";

const t = i18n.global.t;
const router = useRouter();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  background: #000;
  flex-direction: column;
  color: #fff;
  padding: $gap16 $gap24;
  font-size: $fontSize13;
  text-align: center;
  .title {
    font-size: $fontSize17;
    font-weight: 700;
    margin-bottom: px2rem(8);
  }
  .desc {
    color: #ffffff80;
  }
  .nameplate {
    width: 100%;
    margin: px2rem(32) 0 px2rem(12) 0;
  }
  .data-page {
    width: px2rem(305);
    margin: px2rem(32) 0 px2rem(12) 0;
  }
}
::v-deep(.icon-back) {
  color: $white;
}
</style>
