<template>
  <div class="app-container flex">
    <header>
      <header-bar close-type="close"/>
      <div class="balance-wrap">
        <div class="balance-title">Diamonds</div>
        <div class="balance-amount flex">
          <span>{{ numberWithCommas(config.balance) }}</span>
          <gap :gap="6"></gap>
          <img class="icon-diamond" src="@/assets/images/common/diamond.png" alt="">
        </div>
      </div>
      <div class="header-padding"></div>
    </header>
    <div class="list-title">Diamonds Exchange</div>
    <div class="list-wrap">
      <div class="list-cell flex"
           v-for="(cell, idx) in config.items" :key="idx"
      >
        <div class="cell-left flex">
          <img class="icon-dollar" src="@/assets/images/common/dollar.png" alt="">
          <gap :gap="4"></gap>
          <span>{{ cell.price }}</span>
        </div>
        <div class="cell-right flex" @click="checkExchange(cell)">
          <img class="icon-diamond" src="@/assets/images/common/diamond.png" alt="">
          <gap :gap="2"></gap>
          <span>{{ cell.diamond }}</span>
        </div>
      </div>
    </div>
    <footer class="flex">
      <div class="box-item flex" @click="jumpPage(1)">
        <img class="box-icon" src="@/assets/images/icon/icon-question.svg" alt="">
        <gap :gap="4"></gap>
        <span>Feedback</span>
      </div>
      <div class="box-item flex" @click="jumpPage(2)">
        <img class="box-icon" src="@/assets/images/icon/icon-contact.svg" alt="">
        <gap :gap="4"></gap>
        <span>Contact us</span>
      </div>
    </footer>
  </div>

  <!--  兑换确认弹窗  -->
  <van-overlay :show="exchangeDialogShow" @click="toggleExchangeDialog(false)">
    <div class="check-exchange" @click.stop>
      <div class="check-bg"></div>
      <div class="check-header flex">
        <img class="check-img" src="@/assets/images/withdraw/exchange-dollar.png" alt="">
        <div class="check-title">Exchange Related</div>
        <div class="check-content">
          Consumption {{ numberWithCommas(currentChooseItem.diamond) }} Diamonds Exchange {{ currentChooseItem.price }} income
        </div>
      </div>
      <div class="check-button-wrap flex">
        <div class="check-button" @click="toggleExchangeDialog(false)">Cancel</div>
        <div class="check-button active" @click="exchangePost">Exchange</div>
      </div>
    </div>
  </van-overlay>

  <!--  性别认证弹窗  -->
  <base-modal :show="verifyDialogShow"
              conform-text="To verify"
              show-cancel
              @confirm="jumpPage(3)"
              @update:show="toggleVerifyDialog"
  >
    <template #title>Not verified</template>
    <template #content>Profit can only be extract after gender verification is completed.</template>
  </base-modal>

  <!--  其他提示弹窗  -->
  <base-modal :show="hintDialogShow"
              @confirm="toggleHintDialog(false)"
              @update:show="toggleHintDialog"
  >
    <template #title>{{ hintDialogMsg }}</template>
  </base-modal>

  <!--  联系客服弹窗  -->
  <base-modal :show="contactDialogShow"
              @confirm="toggleContactDialog(false)"
              @update:show="toggleContactDialog"
  >
    <template #title>
      <div>Official mailbox</div>
      <div><EMAIL></div>
    </template>
  </base-modal>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import walletApi from '@/api/wallet.js'
import { numberWithCommas } from '@/utils/util.js'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()

const config = ref({
  balance: 0,
  items: [],
})
const getConfig = async () => {
  const response = await walletApi.withdraw_list()
  if (response.code === 200) {
    config.value = response.data
  }
}

const checkExchange = (cell) => {
  currentChooseItem.value = cell
  toggleExchangeDialog(true)
}

const currentChooseItem = ref({})
const exchangePost = async () => {
  const params = {
    id: currentChooseItem.value.id,
  }
  const response = await walletApi.withdraw(params)
  if (response.code === 200) {
    toggleExchangeDialog(false)
    proxy.$toast('success')
    getConfig()
  } else {
    toggleExchangeDialog(false)
    // 未认证
    if (response.code === 1001) {
      toggleVerifyDialog(true)
      // 当前地区不支持提现
    } else if (response.code === 1002) {
      hintDialogMsg.value = 'You nationality not able to exchange income. Please contact Siya official if there are any queries.'
      toggleHintDialog(true)
    } else {
      proxy.$toast(response.msg)
    }
  }
}

const jumpPage = (type) => {
  switch (type) {
    case 1:
      router.push({
        name: 'Feedback',
        query: { type: 3 }
      })
      break
    case 2:
      toggleContactDialog(true)
      break
    case 3:
      toggleVerifyDialog(false)
      proxy.$siyaApp('idenAuthentication', { type: 1 })
      break
  }
}

const exchangeDialogShow = ref(false)
const toggleExchangeDialog = (bool) => {
  exchangeDialogShow.value = bool
}

const verifyDialogShow = ref(false)
const toggleVerifyDialog = (bool) => {
  verifyDialogShow.value = bool
}

const hintDialogShow = ref(false)
const hintDialogMsg = ref('')
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool
}

const contactDialogShow = ref(false)
const toggleContactDialog = (bool) => {
  contactDialogShow.value = bool
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  flex-direction: column;
  height: 100vh;
}

header {
  position: relative;
  width: 100vw;
  background: url("@/assets/images/withdraw/bg_f.png") bottom/cover no-repeat;

  .header-padding {
    height: px2rem(155);
  }

  .balance-wrap {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: px2rem(358);
    height: px2rem(150);
    margin: 0 auto;

    @include centerV;
  }

  .balance-title {
    font-size: px2rem(13);
    line-height: px2rem(16);
    text-align: center;
    color: rgba(0, 0, 0, .4);
  }

  .balance-amount {
    font-size: px2rem(40);
    font-weight: $fontWeightBold;
    color: rgba(0, 0, 0, .8);
  }

  .icon-diamond {
    width: px2rem(40);
    height: px2rem(40);
  }
}

.list-title {
  width: 100vw;
  padding: px2rem(12) px2rem(24) 0;
  font-size: px2rem(13);
  line-height: px2rem(34);
  color: $fontColorB3;
}

.list-wrap {
  flex: 1;
  width: 100vw;
  overflow-y: scroll;
  padding: $gap8 0;

  .list-cell {
    justify-content: space-between;
    padding: px2rem(14.5) $gap16;
    margin: 0 $gap16;
    background-color: $white;

    &:first-child {
      padding-top: $gap16;
      border-top-left-radius: $radius24;
      border-top-right-radius: $radius24;
    }

    &:last-child {
      padding-bottom: $gap16;
      border-bottom-left-radius: $radius24;
      border-bottom-right-radius: $radius24;
    }
  }

  .cell-left {
    font-size: px2rem(17);
    font-weight: $fontWeightBold;

    .icon-dollar {
      width: px2rem(30);
      height: px2rem(30);
    }
  }

  .cell-right {
    min-width: px2rem(103);
    height: px2rem(34);
    padding: 0 px2rem(8);
    font-size: px2rem(15);
    color: $mainColor;
    background: #5F56ED1A;
    border-radius: px2rem(25);

    .icon-diamond {
      width: px2rem(26);
      height: px2rem(26);
    }
  }
}

footer {
  width: 100vw;
  padding: $gap8 0;

  .box-item {
    flex: 1;
    justify-content: center;
    font-size: px2rem(13);
  }

  .box-icon {
    width: px2rem(20);
    height: px2rem(20);
  }
}

.check-exchange {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-top-left-radius: px2rem(26);
  border-top-right-radius: px2rem(26);
  overflow: hidden;

  .check-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: px2rem(100);
    background: linear-gradient(175.89deg, rgba(95, 86, 237, 0.16) 3.35%, rgba(95, 86, 237, 0) 96.66%);
  }

  .check-header {
    flex-direction: column;
    margin-bottom: px2rem(22);
  }

  .check-img {
    width: px2rem(100);
    height: px2rem(100);
  }

  .check-title {
    margin-bottom: $gap8;
    font-size: px2rem(20);
    font-weight: $fontWeightBold;
    line-height: px2rem(24);
    color: $fontColorB0;
  }

  .check-content {
    font-size: px2rem(13);
    color: $fontColorB3;
  }

  .check-button-wrap {
    padding: $gap16;
  }

  .check-button {
    flex: 1;
    height: px2rem(50);
    font-size: px2rem(17);
    line-height: px2rem(50);
    color: $mainColor;
    text-align: center;
    background-color: #F8F7FF;
    border-radius: $radius16;

    &.active {
      margin-left: px2rem(10);
      color: $white;
      background-color: $mainColor;
    }
  }
}
</style>
