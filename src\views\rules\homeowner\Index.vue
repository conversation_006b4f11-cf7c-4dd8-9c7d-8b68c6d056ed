<template>
  <div class="app-container flex">
    <header-bar
      :statusBarPadding="6"
      :title="t('ranking.ranking_rule')"
      close-type="close"
      fontColor="#fff"
      fixedBgColor="#a29fce"
    />
    <div class="rule-content" v-html="formattedRuleText"></div>
    <div class="room-content">
      <div
        v-for="(item, index) in roomList"
        :key="index"
        :class="{
          room: true,
          gold: index === 0,
          silver: index === 1,
          bronze: index === 2,
        }"
      >
        <div class="title">{{ item.title }}</div>
        <div class="reward">
          <div class="reward-item" v-for="jtem in item.infos" :key="jtem">
            <span class="right-icon"> {{ jtem.duration }}{{ t('rules.room_day') }} </span>
            <img :src="jtem.icon" alt="" />
            <p>{{ jtem.gift_name }}（x{{ jtem.num }}）</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed } from "vue";
import { i18n } from "@/i18n/index.js";
import guildApi from '@/api/guild.js'

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const roomList = ref([
  {
    title: t("rules.room_gold"),
  },
  {
    title: t("rules.room_silver"),
  },
  {
    title: t("rules.room_bronze"),
  },
]);

const formattedRuleText = computed(() => {
  const lines = t("rules.room_task_rule").split("\n");
  const totalLines = lines.length;
  return lines
    .map((line, index) => {
      let style = "";
      const isSpecialIndex =
        index === totalLines - 2 || // 倒数第二条
        index === totalLines - 3 || // 倒数第三条
        index === totalLines - 4; // 倒数第四条

      if (index === 0) {
        style = `color:#FFF;font-size:${proxy.$pxToRemPx(
          15
        )}px;font-weight:700;`;
      } else if (index === 1 || index === 3) {
        style = `color:#FFF;font-size:${proxy.$pxToRemPx(
          13
        )}px;font-weight:700;margin-top:${proxy.$pxToRemPx(12)}px`;
      } else {
        style = `color:#FFF;font-size:${proxy.$pxToRemPx(
          11
        )}px;font-weight:500;`;
      }
      if (isSpecialIndex) {
        style += `margin-top:${proxy.$pxToRemPx(12)}px;`;
      }
      return `<div style="${style}">${line}</div>`;
    })
    .join("");
});
onMounted(async () => {
  const getUserInfoResult = await proxy.$siyaApp('getUserInfo')
  guildApi.task_rule({room_id:getUserInfoResult.data.roomId}).then(res=>{
    res.data.tip_info.map((item,index)=>{
      roomList.value[index].infos = item.infos
      roomList.value[index].room_level = item.room_level
    })
  })
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.app-container {
  background-color: #a29fce;
  color: #fff;
  .rule-content {
    padding: px2rem(8) px2rem(16);
    margin-bottom: px2rem(16);
  }
  .room-content {
    width: 100vw;
    padding: 0 px2rem(16);
    .room {
      width: 100%;
      border-radius: px2rem(16);
      margin-bottom: px2rem(24);
      padding: px2rem(16) px2rem(20);
    }
    .title {
      height: px2rem(26);
      text-align: center;
      font-family: Gilroy;
      font-size: px2rem(12);
      font-style: italic;
      font-weight: 900;
      line-height: px2rem(26);
    }
    .reward {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: px2rem(0) px2rem(24) ;
      padding-bottom: px2rem(16);
      .reward-item {
        width: px2rem(90);
        height: px2rem(90);
        background: url("@/assets/images/ranking/weekly-star/reward-bg.png")
          top/contain no-repeat;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-top: px2rem(4);
        margin-top: px2rem(16);
        .right-icon {
          border-radius: 0px 8px;
          padding: px2rem(2) px2rem(5);
          background: linear-gradient(90deg, #ffea64 0%, #ffd395 100%),
            linear-gradient(90deg, #ffca64 0%, #ff976b 100%);
          font-size: px2rem(9);
          color: #9b401f;
          font-weight: 500;
          position: absolute;
          right: 0;
          top: 0;
        }
        img {
          width: px2rem(44);
          height: px2rem(44);
        }
        p {
          font-size: px2rem(10);
          color: #fff;
          font-weight: 500;
          margin-top: px2rem(2);
          padding: 0 px2rem(4);
          text-align: center;
          max-width: px2rem(92);
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .gold {
      border: 1px solid #fff4c1;
      background: #6d4646;
      box-shadow: 0 0 px2rem(12) 0 #ffd16e inset,
        0 0 px2rem(28) 0 rgba(255, 178, 0, 0.7);
      .title {
        background: url("@/assets/images/rules/homeowner/gold.png") top/contain
          no-repeat;
      }
    }
    .silver {
      border: 1px solid #c1e7ff;
      background: #3d4460;
      box-shadow: 0px 0px 12px 0px #6ea6ff inset, 0px 0px 28px 0px #98b3ff;
      .title {
        background: url("@/assets/images/rules/homeowner/silver.png")
          top/contain no-repeat;
      }
    }
    .bronze {
      border: 1px solid #fff4c1;
      background: #524a4a;
      box-shadow: 0px 0px 12px 0px rgba(255, 247, 231, 0.58) inset,
        0px 0px 28px 0px rgba(255, 214, 120, 0.17);
      .title {
        background: url("@/assets/images/rules/homeowner/bronze.png")
          top/contain no-repeat;
      }
    }
  }
}
::v-deep(.header-bar .header-bar-fixed .header-content .title) {
  text-align: center;
}
</style>
