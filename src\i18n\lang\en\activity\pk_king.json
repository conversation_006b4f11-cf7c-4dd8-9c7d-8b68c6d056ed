{"event_name": "PK King", "event_time": "From July 29 00:00:00 to August 11 23:59:59 ({0})", "first_cycle": "July 29 00:00:00 - August 4 23:59:59 ({0})", "second_cycle": "August 5 00:00:00 - August 11 23:59:59 ({0})", "task": "Task", "daily_tasks": "Daily Tasks", "daily_task_frequency": "Can be completed once a day", "weekly_tasks": "Weekly Task", "weekly_task_frequency": "Can only be completed once per cycle", "task_personal_pk_1000": "Your PK value reaches 1000", "task_win_pk_1000": "Win a PK (total PK value ≥ 1000)", "task_pk_contribution_1000": "PK contribution value reaches 1000", "task_room_pk_10min": "Your room starts a PK (PK duration ≥ 10 minutes, total PK value > 0)", "task_room_pk_value_5000": "Your room PK value reaches 5000", "task_room_pk_users_5": "Your room PK number of user >=5", "task_personal_pk_50k": "Your PK value reaches 50K", "task_personal_pk_100k": "Your PK value reaches 100K", "task_personal_pk_200k": "Your PK value reaches 200K", "task_room_pk_100k": "Your room PK value reaches 100K", "task_room_pk_200k": "Your room PK value reaches 200K", "task_room_pk_500k": "Your room PK value reaches 500K", "task_pk_contribution_50k": "PK contribution value reaches 50K", "task_pk_contribution_100k": "PK contribution value reaches 100K", "task_pk_contribution_200k": "PK contribution value reaches 200K", "go": "GO", "claim": "claim", "claimed": "claimed", "congratulations": "Congratulations!", "task_reward_sent": "The task reward has been sent to your backpack", "ok": "OK", "user_ranking": "User ranking", "pk_value_ranking": "PK value ranking", "pk_value_ranking_desc": "This ranking counts the total PK value of users in each period, and the top 10 users can get rewards", "weekly_rewards": "Weekly rewards", "top_1_3_share": "The TOP1-10 users will share the diamond pool in proportion", "top_1": "TOP1", "top_2": "TOP2", "top_3": "TOP3", "pk_contribution_ranking": "PK contribution ranking", "pk_contribution_ranking_desc": "This ranking counts the total PK value contributed by users in each period, and the top 10 contributors can receive rewards", "room_ranking": "Room ranking", "room_ranking_desc": "This ranking counts the total PK value of your room in each cycle, and the top 10 room owners can get rewards", "room_top_1_3_share": "The TOP1-10 room owners will share the prize pool in proportion", "rules": "Rules", "rewards": "Rewards", "task_rewards": "Task Rewards", "task_rewards_desc": "After completing the tasks, you need to return to the activity page to claim the reward", "event_rules_label": "Activity time", "first_cycle_label": "First cycle", "second_cycle_label": "Second cycle", "task_rewards_rule": "2. Task Rewards: After completing the tasks, you need to return to the activity page to claim the reward\nDaily Tasks: Can be completed once a day\nWeekly Tasks: Can only be completed once per cycle", "ranking_rewards": "Ranking rewards", "ranking_rewards_desc": "Ranking rewards will be distributed to the backpack of the winning user after the activity ends.", "feedback": "If you have any feedback about the activity, please click the button at the top of the page to provide your valuable suggestions", "prize_pool_description_title": "Prize Pool Description", "prize_pool_description_content_1": "A certain percentage of all users' PK values will enter the diamond prize pool. The higher the PK value, the larger the prize pool. The TOP1-10 users will share the diamond pool in proportion", "prize_pool_description_content_2": "A certain percentage of all room' PK values will enter the diamond prize pool. The higher the PK value, the larger the prize pool. The TOP1-10 room owners will share the prize pool in proportion"}