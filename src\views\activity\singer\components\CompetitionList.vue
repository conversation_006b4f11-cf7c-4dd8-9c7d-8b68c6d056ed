<!-- 参赛列表 -->
<template>
  <div class="competition-block">
    <div class="competition-list">
      <div class="competition-item" v-for="item in rankList">
        <div class="userinfo">
          <div class="avatar">
            <frame-avatar
              :avatar="item?.avatar"
              :frame="item?.frame"
              :size="34"
            ></frame-avatar>
          </div>
          <gap :gap="10"></gap>
          <div class="nickname">{{ item?.nickname }}</div>
        </div>
        <div class="music-cover"></div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import singer<PERSON><PERSON> from "@/api/activity.js";

const props = defineProps({
  stage: {
    type: Number,
    default: 2,
  },
  title: {
    type: String,
    default: "",
  },
  desc: {
    type: String,
    default: "",
  },
});

const rankList = ref([]);
const fetchData = async () => {
  const res = await singer<PERSON>pi.singer_stage_rank({
    stage: props.stage,
  });
  if (res.code === 200) {
    rankList.value = res.data?.rank_list || [];
  }
};

onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.competition-block {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-bottom: px2rem(45);
  .competition-list {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    width: 100%;
    padding-left: px2rem(24);
    margin-top: px2rem(35);
    .competition-item {
      width: px2rem(173);
      height: px2rem(71);
      display: flex;
      align-items: center;
      margin-bottom: px2rem(7);
      .userinfo {
        width: px2rem(154);
        height: px2rem(50);
        display: flex;
        padding: px2rem(8) px2rem(17) px2rem(8) px2rem(10);
        align-items: center;
        border-radius: px2rem(8);
        border: px2rem(1) solid #fff;
        background: rgba(255, 255, 255, 0.16);
        .nickname {
          color: var(---White, #fff);
          font-family: Gilroy;
          font-size: px2rem(12);
          font-style: normal;
          font-weight: 500;
          line-height: 124%; /* 14.88px */
        }
      }
      .music-cover {
        width: px2rem(48.81);
        height: px2rem(53.447);
        transform: rotate(51.914deg);
        margin-left: px2rem(-30);
        aspect-ratio: 48.81/53.45;
        background: url("@/assets/images/activity/singer/music.png") 100% /
          cover no-repeat;
      }
    }
  }
}
</style>
