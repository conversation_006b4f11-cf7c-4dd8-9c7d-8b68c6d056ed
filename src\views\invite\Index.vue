<template>
  <div class="invite-container">
    <header-bar
      back-icon-show
      :show-title="false"
      :contentPadding="true"
      :close-type="closeType"
    />
    <div v-if="marqueeList.length > 0" class="invite-marquee">
      <invite-marquee :list="marqueeList" :speed="25" />
    </div>
    <div class="top-banner">
      <img :src="getImageUrl('banner-title.png')" alt="" :draggable="false" />
    </div>
    <main-block
      :max-bonus="inviterInfo?.max_bonus_per_friend || 0"
      :gender="gender"
      :invite-code="inviterInfo?.invite_code"
      :is-blacklisted="isBlacklisted"
    ></main-block>
    <sub-box>
      <div class="sub-box-title">{{ $t("invite.my_rewards") }}</div>
      <div class="my-rewords">
        <div class="my-reword">
          <div class="my-reword-num">
            <span>{{ formatCount(inviterInfo?.daily_total_bonus ?? 0) }}</span>
            <Coin :gender="gender"></Coin>
          </div>
          <div class="my-reword-desc">{{ $t("invite.reward_today") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-reword">
          <div class="my-reword-num">
            <span>{{
              formatCount(inviterInfo?.monthly_total_bonus ?? 0)
            }}</span>
            <Coin :gender="gender"></Coin>
          </div>
          <div class="my-reword-desc">{{ $t("invite.reward_month") }}</div>
        </div>
        <div class="line"></div>
        <div class="my-reword">
          <div class="my-reword-num">
            <span>{{ formatCount(inviterInfo?.total_bonus ?? 0) }}</span>
            <Coin :gender="gender"></Coin>
          </div>
          <div class="my-reword-desc">{{ $t("invite.reward_total") }}</div>
        </div>
      </div>
    </sub-box>
    <sub-box>
      <div class="sub-box-tabs">
        <page-tab :tab-items="tabItems" v-model="initialTab"></page-tab>
      </div>
      <swiper
        class="main"
        :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
        :modules="modules"
        slides-per-view="auto"
        :space-between="0"
        @swiper="setControlledSwiper"
        @slideChange="handleSwipeChange"
      >
        <swiper-slide>
          <my-task></my-task>
        </swiper-slide>
        <swiper-slide>
          <my-invitation></my-invitation>
        </swiper-slide>
      </swiper>
    </sub-box>
    <div class="rule-btn" @click="toRulePage"><div></div></div>
  </div>
</template>
<script setup>
import { checkLang, i18n } from "@/i18n/index.js";
import { useRouter, useRoute } from "vue-router";
import PageTab from "./components/PageTab.vue";
import {
  onMounted,
  ref,
  getCurrentInstance,
  computed,
  watch,
  nextTick,
  provide,
} from "vue";
import MainBlock from "./components/MainBlock.vue";
import SubBox from "./components/SubBox.vue";
import MyTask from "./components/MyTask.vue";
import MyInvitation from "./components/MyInvitation.vue";
import InviteMarquee from "./components/InviteMarquee.vue";
import inviteApi from "@/api/invite.js";
import Coin from "./components/Coin.vue";
import { formatCount } from "@/utils/util";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Controller } from "swiper/modules";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute()
const lang = checkLang();
const t = i18n.global.t;
const modules = [Controller];
const controlledSwiper = ref(null);
const closeType = ref("close");
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;
};

function getImageUrl(name) {
  return new URL(`../../assets/images/invite/${lang}/${name}`, import.meta.url)
    .href;
}

const toRulePage = () => {
  router.push({
    name: "InviteRule",
    query: {
      gender: gender.value,
    },
  });
};
const initialTab = ref(0);
const tabItems = ref([
  {
    title: t("invite.limited_time_task"),
  },
  {
    title: t("invite.my_invites"),
  },
]);
const inviterInfo = ref(null);
const marqueeList = ref([]);
const gender = computed(() => inviterInfo.value?.invite_user_info?.gender ?? 1);
provide("userGender", gender);
const isBlacklisted = ref(false);

const checkIsBlacklisted = async () => {
  const res = await inviteApi.check_is_blacklisted();
  if (res.code === 200) {
    return res.data?.is_blacklisted === 1;
  }
  return true;
};

const getInviterInfo = async () => {
  const res = await inviteApi.inviter_info();
  if (res.code === 200) {
    inviterInfo.value = res.data;
  }
};

const getMarquee = async () => {
  const res = await inviteApi.marquee({ rand_count: 30 });
  if (res.code === 200) {
    marqueeList.value = (res.data.list || []).map((item) => ({
      ...item.user_info,
    }));
  }
};

const fetchData = async () => {
  await getInviterInfo();
  await getMarquee();
};

let isChange = false;
const handleSwipeChange = ({ activeIndex }) => {
  isChange = true;
  initialTab.value = activeIndex;
  nextTick(() => {
    isChange = false;
  });
};

watch(
  () => initialTab.value,
  (value) => {
    if (!isChange) {
      controlledSwiper.value?.slideTo(value);
    }
  }
);

onMounted(async () => {
  const isBack = route.query.isBack;
  if (isBack === 'true') {
    closeType.value = "back";
  } else {
    closeType.value = "close";
  }
  isBlacklisted.value = await checkIsBlacklisted();
  if (isBlacklisted.value === true) {
    proxy.$toast(t("invite.invitation_unavailable"));
    setTimeout(() => {
      proxy.$siyaApp("closeWebview");
    }, 1500);
    return;
  }
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.invite-container {
  min-height: 100vh;
  background-image: url("@/assets/images/invite/index-bg.png");
  background-size: 100% px2rem(440);
  background-repeat: no-repeat;
  background-color: #ffe3c1;
  padding-bottom: px2rem(20);
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .invite-marquee {
    margin-top: px2rem(-32);
  }

  .top-banner {
    width: 100%;
    margin-top: px2rem(2);
    img {
      width: 100%;
    }
  }

  .sub-box-title {
    position: relative;
    z-index: 3;
    display: flex;
    width: px2rem(331);
    justify-content: center;
    align-items: center;
    gap: px2rem(12);

    color: #642628;
    text-align: center;

    /* T20/B */
    font-family: Gilroy;
    font-size: px2rem(20);
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    margin: 0 auto;
    margin-top: px2rem(16);
    height: px2rem(24);
    &::before {
      content: "";
      width: px2rem(40);
      height: px2rem(2);
      flex-shrink: 0;
      background: linear-gradient(90deg, #ffebef 0%, #e9c1c1 100%);
    }
    &::after {
      content: "";
      width: px2rem(40);
      height: px2rem(2);
      flex-shrink: 0;
      background: linear-gradient(90deg, #e9c1c1 0%, #ffebef 100%);
    }
  }

  .my-rewords {
    margin: px2rem(16) px2rem(12);
    position: relative;
    z-index: 2;

    display: flex;
    padding: px2rem(16) px2rem(6);
    gap: px2rem(6);
    justify-content: center;
    align-items: center;
    flex: 1 0 0;
    border-radius: px2rem(12);
    background: #fff8ea;

    .line {
      width: px2rem(1);
      height: px2rem(32);
      background: #f0d8d8;
    }

    .my-reword {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      flex-grow: 1;
      .my-reword-num {
        color: #642628;
        font-family: Gilroy;
        font-size: px2rem(24);
        font-style: normal;
        font-weight: 900;
        line-height: normal;
        height: px2rem(30);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: px2rem(2);
      }
      .my-reword-desc {
        margin-top: px2rem(3);
        color: #7e4141;
        text-align: center;

        /* T13/R */
        font-family: Gilroy;
        font-size: px2rem(13);
        // height: px2rem(32);
        font-style: normal;
        font-weight: 500;
        line-height: 124%; /* 16.12px */
      }
    }
  }

  .sub-box-tabs {
    position: relative;
    z-index: 3;
    padding-top: px2rem(16);
  }
}
.rule-btn {
  position: fixed;
  right: 0;
  z-index: 99;
  top: px2rem(230);
  width: px2rem(40);
  height: px2rem(32);
  border-radius: px2rem(100) 0px 0px px2rem(100);
  border: px2rem(2) solid #ffe69b;
  background: linear-gradient(180deg, #ff8a69 0%, #ff586f 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    width: px2rem(24);
    height: px2rem(24);
    background-image: url("@/assets/images/invite/rule.png");
    background-size: 100% 100%;
  }
}
.rtl-html {
  .sub-box-title {
    flex-direction: row-reverse;
  }
  .rule-btn {
    right: unset !important;
    left: 0;
    border-radius: 0 px2rem(100) px2rem(100) 0;
  }
}
</style>
