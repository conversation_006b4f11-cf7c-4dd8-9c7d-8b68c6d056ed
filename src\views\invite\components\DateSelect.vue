<template>
  <div class="date-btn" @click="showDatePicker = true">
    <span>{{ showDate }}</span>
    <span class="icon-date"></span>
  </div>
  <Teleport to="body">
    <van-popup position="bottom" v-model:show="showDatePicker" class="pop">
      <div class="pop-icon"></div>
      <div class="pop-title">{{ $t("invite.select_time") }}</div>
      <van-date-picker
        ref="datePickerRef"
        v-model="date"
        :show-toolbar="false"
        :max-date="new Date()"
        :min-date="new Date(new Date().setMonth(new Date().getMonth() - 5))"
        :columns-type="['year', 'month']"
        type="date"
      />
      <div class="bottun-box" :class="isiOS() ? 'pb26' : ''">
        <van-button class="comfirm-btn" type="primary" @click="handleComfirm">{{
          $t("common.common_comfirm")
        }}</van-button>
      </div>
    </van-popup>
  </Teleport>
</template>
<script setup>
import { computed, ref } from "vue";
import { isiOS } from "@/utils/util";

const emit = defineEmits(["confirm", "update:modelValue"]);
const props = defineProps({
  modelValue: {
    type: Array,
    required: true,
  },
});
const datePickerRef = ref();
const showDatePicker = ref(false);
const date = ref(props.modelValue);
const showDate = computed(() => {
  if (props.modelValue.length !== 2) {
    return "-";
  }
  return `${props.modelValue[0]}-${props.modelValue[1]}`;
});

const handleComfirm = () => {
  const { selectedValues } = datePickerRef.value?.confirm();
  emit("update:modelValue", selectedValues);
  emit("confirm", selectedValues);
  showDatePicker.value = false;
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.date-btn {
  display: flex;
  height: px2rem(28);
  padding: 0 px2rem(12);
  justify-content: center;
  align-items: center;
  gap: px2rem(2);
  border-radius: px2rem(100);
  background: #f5f1ea;
  width: fit-content;

  color: #7e4141;

  /* T11/R */
  font-family: Gilroy;
  font-size: px2rem(11);
  font-style: normal;
  font-weight: 500;
  line-height: 120%; /* 13.2px */

  .icon-date {
    display: flex;
    width: px2rem(16);
    height: px2rem(16);
    justify-content: center;
    align-items: center;
    background-image: url("@/assets/images/invite/icon-date.svg");
    background-size: px2rem(12) px2rem(11);
    background-repeat: no-repeat;
    background-position: center center;
  }
}
.pb26 {
  padding-bottom: px2rem(26);
}
.pop {
  padding: px2rem(16);
  border-radius: px2rem(26) px2rem(26) 0 0;
  padding-top: 0;

  .pop-icon {
    height: px2rem(25);
    display: flex;
    justify-content: center;
    align-items: center;
    &::after {
      content: "";
      width: px2rem(36);
      height: px2rem(5);
      border-radius: px2rem(5);
      background: rgba(60, 60, 67, 0.3);
    }
  }
  .pop-title {
    height: px2rem(50);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(---B0-Black, #000);
    text-align: center;

    /* T17/B */
    font-family: Gilroy;
    font-size: px2rem(17);
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }
  .bottun-box {
    padding-top: px2rem(16);
    .comfirm-btn {
      width: 100%;
      font-weight: 700;
    }
  }
}
</style>
