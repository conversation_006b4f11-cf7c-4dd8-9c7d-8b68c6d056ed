<template>
  <div class="container flex">
    <header-bar
      :title="t('rules.pk_detail')"
      close-type="close"
      fontColor="#fff"
      fixedBgColor="#9156E0"
      isScrollChangeBg
    >
      <template #right>
        <img
          class="close"
          @click="handleClose"
          src="@/assets/images/game/close.png"
          alt=""
      /></template>
    </header-bar>
    <div class="content">
      <div class="rule-content" :style="showTable?'':'margin-bottom:0'" v-html="formattedRuleText1"></div>
      <div class="pk-content" v-if="showTable">
        <table style="width: 100%">
          <tr class="table-tr">
            <th v-for="item in pkList" :key="item">{{ item.title }}</th>
          </tr>
          <tr class="table-tr" v-for="item in pkList" :key="item">
            <td>{{ item.infos }}</td>
            <td>
              <img width="50" :src="item.redWeapon" alt="" />
            </td>
            <td>
              <img width="50" :src="item.blueWeapon" alt="" />
            </td>
          </tr>
        </table>
      </div>
      <div class="rule-content" :style="showTable?'':'padding-top:0'" v-html="formattedRuleText2"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, getCurrentInstance, computed } from "vue";
import { i18n } from "@/i18n/index.js";
import baseForAppApi from "@/api/baseforapp.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
const pkList = ref([
  {
    title: t("rules.pk_value"),
  },
  {
    title: t("rules.pk_redWeapon"),
  },
  {
    title: t("rules.pk_blueWeapon"),
  },
]);
const showTable = ref(false);
const formattedRuleText1 = computed(() => {
  const lines = t("rules.pk_info1");
  return lines.replace(/\n/g, "<br>");
});
const formattedRuleText2 = computed(() => {
  const lines = t("rules.pk_info2");
  return lines.replace(/\n/g, "<br>");
});
const handleClose = () => {
  proxy.$siyaApp("closeWebview");
};
onMounted(async () => {
  const getUserInfoResult = await proxy.$siyaApp("getUserInfo");
  baseForAppApi
    .pk_rule({ room_id: getUserInfoResult.data.roomId })
    .then((res) => {
      if (res.data && Array.isArray(res.data.items) && res.data.items.length > 0) {
        showTable.value = true;
      } else {
        showTable.value = false;
      }
      const list = res.data?.items.map((item, index) => {
        return {
          title: pkList.value[index]?.title || "",
          infos: `${item.minValue}-${item.maxValue}`,
          redWeapon: item.redWeapon.pic,
          blueWeapon: item.blueWeapon.pic,
        };
      });
      pkList.value = list;
    });
});
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.container {
  background-image: url("@/assets/images/rules/pk/bg.jpg"),
    linear-gradient(#a87cc6, #a87cc6);
  background-repeat: no-repeat, no-repeat;
  background-position: top center, top center;
  color: #fff;
  flex-direction: column;
  height: 100vh;
  .close {
    width: px2rem(24);
    height: px2rem(24);
  }
  .content {
    flex: 1;
    overflow-y: scroll;
    padding-bottom: px2rem(10);
    position: relative;
  }
  .rule-content {
    padding: px2rem(8) px2rem(16) 0 px2rem(16);
    margin-bottom: px2rem(18);
    font-size: $fontSize13;
    line-height: 124%;
  }
  .pk-content {
    padding: 0 px2rem(24);
    width: 100vw;
  }
}
table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: px2rem(8);
  border: 0.5px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 0px 80px 0px rgba(161, 0, 255, 0.4) inset;
  background: #302e51;
  position: relative;
  width: 100%;
}
.table-tr {
  text-align: center;
  font-size: $fontSize11;
  font-weight: 700;
  width: 100%;
  color: #fff;
}
.table-tr th {
  color: rgba(255, 255, 255, 0.7);
  font-size: px2rem(10);
  font-weight: 400;
}
.table-tr th,
.table-tr td {
  position: relative;
  padding: px2rem(12) px2rem(8);
  width: 33.3%;
}

/* 给单元格添加右侧边框 */
.table-tr td:not(:last-child),
.table-tr th:not(:last-child) {
  border-right: 0.5px solid rgba(255, 255, 255, 0.1);
}

/* 给单元格添加底部边框（包含 th） */
.table-tr:not(:last-child) td,
.table-tr:not(:last-child) th {
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
}

::v-deep(.header-bar .header-bar-fixed .header-content .title) {
  text-align: center;
}
</style>
