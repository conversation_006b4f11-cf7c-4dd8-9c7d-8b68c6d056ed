import ta from "@/utils/thinkingdata";

// 节流时间调整，默认100ms
IntersectionObserver.prototype["THROTTLE_TIMEOUT"] = 300;

export default class Exposure {
  constructor() {
    this._observer = null;
    this.init();
  }

  init() {
    const self = this;

    // 实例化监听
    this._observer = new IntersectionObserver(
      function (entries, observer) {
        entries.forEach((entry) => {
          // 出现在视窗内
          if (entry.isIntersecting) {
            // 获取参数
            const traceKey = entry.target.attributes["trace-key"].value;
            const traceVal =
              entry.target.attributes["track-params"]?.value || "{}";

            // 曝光之后取消观察
            self._observer.unobserve(entry.target);
            self.track(traceKey, JSON.parse(traceVal));
          }
        });
      },
      {
        root: null,
        rootMargin: "0px",
        threshold: 0.5, // 元素出现面积，0 - 1，这里当元素出现一半以上则进行曝光
      }
    );
  }

  /**
   * 元素添加监听
   *
   * @param {*} entry
   * @memberof Exposure
   */
  add(entry) {
    this._observer && this._observer.observe(entry.el);
  }
  /**
   * 埋点上报
   * @memberof Exposure
   */
  track(traceKey, traceVal) {
    console.log(
      "上报曝光埋点===>",
      `事件名:${traceKey}`,
      `属性：${JSON.stringify(traceVal)}`
    );
    ta.track(
      traceKey, //事件名称
      traceVal
    );
  }
}
