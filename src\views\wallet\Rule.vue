<template>
  <div class="app-container">
    <header class="flex">
      <div class="cell">
       <div>
         <div class="title">{{ tabList[0] }}</div>
         <div class="flex">
           <img class="icon-coin" src="@/assets/images/common/coin.svg" alt="">
           <gap :gap="2" />
           <span class="amount">{{ numberWithCommas(config.coin) }}</span>
         </div>
       </div>
      </div>
      <div class="line"></div>
      <div class="cell">
        <div>
          <div class="title">{{ tabList[1] }}</div>
          <div class="flex">
            <img class="icon-coin" src="@/assets/images/common/coin.svg" alt="">
            <gap :gap="2" />
            <span class="amount">{{ numberWithCommas(config.send_coin) }}</span>
          </div>
        </div>
      </div>
    </header>

    <main v-html="config.explain"></main>
  </div>
</template>

<script setup>
import walletApi from '@/api/wallet.js'
import { ref, onMounted } from 'vue'
import { numberWithCommas } from '@/utils/util.js'
import { i18n } from '@/i18n/index.js'

const t = i18n.global.t

const tabList = [
  t('base_total.wallet_rule_title_1'),
  t('base_total.wallet_rule_title_2'),
]

const config = ref({
  coin: 0,
  send_coin: 0,
  explain: '',
})

const getConfig = async () => {
  const response = await walletApi.balance()
  if (response.code === 200) {
    config.value = response.data
  }
}

onMounted(() => {
  getConfig()
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  padding-top: px2rem(8);
}

header {
  height: px2rem(142);
  margin: 0 $gap16;
  padding: $gap16;
  background: linear-gradient(98.94deg, #F6F5FF 0%, #D6D5E4 34%, #F5F4FF 100%);
  border-radius: $radius16;

  .cell {
    flex: 1;
    @include centerV;
    //width: 50%;

    .title {
      margin-bottom: px2rem(6);
      font-size: px2rem(13);
      line-height: px2rem(16);
      color: rgba($fontColorB0, .5);
    }

    .icon-coin {
      width: px2rem(24);
      height: px2rem(24);
    }

    .amount {
      font-size: px2rem(26);
      font-weight: $fontWeightBold;
      line-height: px2rem(31);
      color: rgba($fontColorB0, .8);
    }
  }

  .line {
    width: px2rem(1);
    height: px2rem(54);
    background-color: $bgColorB5;
  }
}

main {
  padding: px2rem(24);
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: px2rem(13);
  line-height: px2rem(18);
  color: $fontColorB3;
}
</style>
