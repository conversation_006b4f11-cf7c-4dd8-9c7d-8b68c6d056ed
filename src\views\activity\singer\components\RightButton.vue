<template>
  <div
    class="right-button"
    v-track:click
    trace-key="singer_button_click"
    :track-params="JSON.stringify({ singer_button_name: 1 })"
  >
    <div class="right-button-text" @click="toRulePage()">
      {{ $t(`singer.rules_title`) }}
    </div>
  </div>
</template>
<script setup>
import { useRouter } from "vue-router";
const router = useRouter();

const toRulePage = () => {
  router.push({
    name: "SingerR<PERSON>",
  });
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.right-button {
  height: px2rem(30);
  width: fit-content;
  position: fixed;
  right: px2rem(-2);
  top: px2rem(217);
  z-index: 999;
  border-radius: px2rem(30) 0 0 px2rem(30);
  border: 1px solid #fff;
  background: linear-gradient(
    180deg,
    #ffa798 0%,
    #cd1ff6 47.12%,
    #6600f5 83.17%
  );
  box-shadow: 0px -2.454px 2.454px 0px rgba(44, 2, 104, 0.64) inset,
    0px 1.84px 1.84px 0px rgba(255, 255, 255, 0.66) inset;
  display: flex;
  align-items: center;
  padding: 0 px2rem(4) 0 px2rem(12);
  .right-button-text {
    color: #fff;
    text-align: center;
    text-shadow: 0px px2rem(4) px2rem(4) rgba(0, 0, 0, 0.25);
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 700;
    line-height: 124%; /* 14.88px */
  }
}

.rtl-html {
  .right-button {
    right: unset;
    left: px2rem(-2);
    border-radius: 0 px2rem(30) px2rem(30) 0;
    .right-button-text {
      padding: 0 px2rem(12) 0 px2rem(4);
    }
  }
}
</style>
