<template>
  <div class="rewards-items">
    <swiper
      class="swiper"
      :dir="['ar'].includes(i18n.global.locale) ? 'rtl' : 'ltr'"
      :modules="modules"
      :loop="list.length > 1"
      :autoplay="list.length > 1 ? {
        delay: 1500,
        disableOnInteraction: false,
        pauseOnMouseEnter: false,
        waitForTransition: true,
      } : false"
      :space-between="5"
      @swiper="setControlledSwiper"
    >
      <swiper-slide v-for="(reward, index) in list" :key="index">
        <div class="reward-item">
          <img v-if="reward.reward_img" :src="reward.reward_img" />
          <div class="reward-cover"></div>
          <div class="tag" v-if="showTag">{{ formatTag(reward) }}</div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>
<script setup>
import { Swiper, SwiperSlide } from "swiper/vue";
import { Controller, Autoplay } from "swiper/modules";
import { ref, computed, watch, nextTick } from "vue";
import { i18n, getLang, t } from "@/i18n/index.js";
const props = defineProps({
  rewards: {
    type: [Object, Array],
    default: [],
  },
  showTag: {
    type: Boolean,
    default: true,
  },
});
const modules = [Controller, Autoplay];
const controlledSwiper = ref(null);

const list = computed(() => {
  if (!props.rewards) return [];
  if (Array.isArray(props.rewards)) {
    return props.rewards;
  }
  return [props.rewards];
});
const setControlledSwiper = (swiper) => {
  controlledSwiper.value = swiper;

  // 确保在数据加载后重新启动自动播放
  nextTick(() => {
    if (swiper && list.value.length > 1) {
      swiper.autoplay.start();
    }
  });
};

// 监听数据变化，重新启动自动播放
watch(
  () => list.value,
  (newList) => {
    if (controlledSwiper.value && newList.length > 1) {
      nextTick(() => {
        controlledSwiper.value.update();
        controlledSwiper.value.autoplay.start();
      });
    }
  },
  { immediate: true }
);

const formatTag = (item) => {
  const lang = getLang();
  if ([1, 2, 5].includes(item.reward_type)) {
    return lang === "ar" ? `${item.num}X` : `X${item.num}`;
  }
  return item.duration > 1
    ? t("common.common_days", [item.duration])
    : t("common.common_day", [item.duration]);
};
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rewards-items,
.swiper {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
}
.reward-item {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: px2rem(6);
  overflow: hidden;
  padding: px2rem(4);
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 2;
    position: relative;
  }
  .reward-cover {
    position: absolute;
    inset: 0;
    background-image: url("@/assets/images/activity/singer/prize-bg.png"); /* 中间部分的图片 */
    background-size: 100% 100%;
  }
  .tag {
    position: absolute;
    z-index: 3;
    top: 0;
    right: 0;
    min-width: px2rem(32);
    padding: px2rem(1) px2rem(4);
    border-radius: 0px px2rem(8);
    background: linear-gradient(90deg, #ffea64 0%, #ffd395 100%),
      linear-gradient(90deg, #ffca64 0%, #ff976b 100%);

    overflow: hidden;
    color: #9b401f;
    text-align: center;
    text-overflow: ellipsis;
    font-family: Gilroy;
    font-size: px2rem(8);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* 8px */
  }
}
</style>
