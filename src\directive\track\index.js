import Click from "./click";
import Exposure from "./exposure";

// 实例化曝光和点击
const exp = new Exposure();
const cli = new Click();

export default {
  mounted(el, binding) {
    // 获取指令参数
    const { arg } = binding;
    arg.split("|").forEach((item) => {
      // 点击
      if (item === "click") {
        cli.add({ el });
      } else if (item === "exposure") {
        exp.add({ el });
      }
    });
  },
};
