<template>
  <div class="frame-avatar">
    <img class="user-avatar" :src="avatar || defaultAvatar" />
    <template v-if="frame">
      <div
        class="frame"
        v-if="isSvga(frame.src)"
        :style="{ transform: `scale(${scale})` }"
      >
        <svga-player :url="frame.src"></svga-player>
      </div>
      <img
        v-else
        class="frame"
        :src="frame.src"
        :style="{ transform: `scale(${scale})` }"
      />
    </template>
  </div>
</template>
<script setup>
import defaultAvatar from "@/assets/images/common/default-avatar.png";
import { computed, getCurrentInstance } from "vue";
const { proxy } = getCurrentInstance();

const props = defineProps({
  avatar: {
    type: String,
  },
  size: {
    type: Number,
    default: 40,
  },
  frame: {
    type: Object,
    default: () => null,
  },
});
const isSvga = (url = "") => {
  return url.endsWith(".svga");
};
const realSize = computed(() => `${proxy.$pxToRemPx(props.size)}px`);
const scale = computed(() => props.frame?.rate || 1.3);
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.frame-avatar {
  width: v-bind(realSize);
  height: v-bind(realSize);
  flex-shrink: 0;
  position: relative;
  .user-avatar {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    border-radius: 50%;
    object-fit: cover;
  }

  .frame {
    width: 100%;
    height: 100%;
    position: absolute;
    inset: 0;
  }
}
</style>
