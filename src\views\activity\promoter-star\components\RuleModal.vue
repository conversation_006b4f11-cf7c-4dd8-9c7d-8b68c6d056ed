<template>
  <van-overlay :show="dialogShow" @touchmove.prevent z-index="1001">
    <div class="base-modal-wrapper" @click.stop>
      <div class="show-animation">
        <div class="linear-bg"></div>
        <div class="container">
          <div class="title">
            {{ $t(`promoter_star.prize_pool_description`) }}
          </div>
          <div class="wrapper-content" @touchmove.prevent>
            <div class="content-text">
              <div>
                {{ $t(`promoter_star.guaranteed_prize_pool_description`) }}
              </div>
            </div>
          </div>
          <div class="wrapper-button active" @click="handleCancel">
            {{ $t("common.common_ok") }}
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<script setup>
import { ref, watch } from "vue";

const emit = defineEmits(["update:show", "confirm"]);
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const dialogShow = ref(false);

watch(
  () => props.show,
  (val) => {
    dialogShow.value = !!val;
  }
);

const handleCancel = () => {
  emit("update:show", false);
};
</script>

<style scoped lang="scss">
@import "@/assets/scss/common.scss";

@keyframes show {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
.base-modal-wrapper {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  .show-animation {
    animation: show 0.3s 1;
    width: px2rem(300);
    padding: px2rem(32) $gap16 $gap16;
    background: #ffffff;
    border-radius: px2rem(26);
  }

  .linear-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: px2rem(100);
    background: linear-gradient(
      180deg,
      rgba(95, 86, 237, 0.16) 0%,
      rgba(95, 86, 237, 0) 100%
    );
  }

  .wrapper-bg {
    position: absolute;
    left: 0;
    top: px2rem(-48);
    width: px2rem(300);
    height: px2rem(168);
  }

  .icon-close {
    position: absolute;
    top: px2rem(10);
    right: px2rem(10);
    width: px2rem(24);
    height: px2rem(24);
  }

  .container {
    position: relative;
    z-index: 1;
    flex-direction: column;

    .title {
      text-align: center;

      /* T17/B */
      font-family: Gilroy;
      font-size: px2rem(17);
      font-style: normal;
      font-weight: 700;
      line-height: normal;
    }
  }

  .wrapper-content {
    position: relative;
    margin-top: px2rem(8);
  }

  .content-text {
    color: var(---B3, #999);
    text-align: center;

    /* T13/R */
    font-family: Gilroy;
    font-size: px2rem(13);
    font-style: normal;
    font-weight: 500;
    line-height: 124%; /* 16.12px */

    @include fontSize15;

    div {
      margin-bottom: $gap16;
    }
  }

  .content-shadow {
    position: absolute;
    bottom: px2rem(-2);
    left: 0;
    width: 100%;
    height: px2rem(44);
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.1) 0%,
      #ffffff 100%
    );
  }

  .wrapper-button {
    height: px2rem(34);
    margin-top: px2rem(26);
    font-size: px2rem(17);
    line-height: px2rem(34);
    color: $fontColorB3;
    text-align: center;
    border-radius: $radius16;

    color: var(---White, #fff);
    text-align: center;

    /* T17/B */
    font-family: Gilroy;
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;

    &.active {
      height: px2rem(50);
      line-height: px2rem(50);
      color: $white;
      background-color: #5f56ed;
    }
  }
}
</style>
