<template>
  <div class="page-container-rich-text" v-html="content"></div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import privacy_policy from '@/views/agreement/text-ios/privacy_policy.js'
import terms_of_service from '@/views/agreement/text-ios/terms_of_service.js'
import recharge_agreement from '@/views/agreement/text-ios/recharge_agreement.js'
import child_safety_policy from '@/views/agreement/text/child_safety_policy.js'
import platform_guidelines from '@/views/agreement/text/platform_guidelines.js'

const route = useRoute()

const content = ref('')

const typeMap = {
  1: privacy_policy,
  2: terms_of_service,
  3: recharge_agreement,
  4: child_safety_policy,
  5: platform_guidelines,
}

onMounted(() => {
  const type = route.query.type

  content.value = typeMap[type]
})

</script>

<style scoped lang="scss">
.page-container-rich-text {
  width: 100%;
  background: #fff;
  padding:  8px;
  box-sizing: border-box;
  //line-height: 1.5;
  //font-size: 15px;
  overflow-x: hidden;
  word-break: break-word;
  img{
    max-width: 100%;
  }
  table {
    font-family: verdana,arial,sans-serif;
    font-size:11px;
    color:#333333;
    border-width: 1px;
    border-color: #666666;
    border-collapse: collapse;
  }
  table th {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #dedede;
  }
  table td {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #ffffff;
  }
}
</style>
