{"WALLET_accountNo_placeholder_ID": "Por eje<PERSON>lo,***********", "WALLET_accountNo_placeholder_MY": "Por ejemplo,**********", "WALLET_accountNo_placeholder_TH": "Por eje<PERSON>lo,**********", "WALLET_accountNo_placeholder_VN": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, ***********", "WALLET_accountNo_hint_ID": "Por favor, introduzca su número de cuenta vinculado a la billetera: 9 a 14 dígitos a partir de 08", "WALLET_accountNo_hint_MY": "Por favor, introduzca el número de cuenta vinculado a su billetera: 10 u 11 dígitos que comienzan con 0", "WALLET_accountNo_hint_TH": "Por favor, introduzca su número de cuenta vinculado a la cartera: 10 dígitos que comienzan con 0", "WALLET_accountNo_hint_VN": "Por favor, introduzca su número de cuenta vinculado a la cartera: 11 dígitos que comienzan con 84", "WALLET_fullName_placeholder": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>", "BANK_TRANSFER_accountNo_title": "Cuenta bancaria", "BANK_TRANSFER_accountNo_placeholder_MY": "<PERSON><PERSON> <PERSON><PERSON><PERSON>,**********", "BANK_TRANSFER_accountNo_placeholder_TH": "<PERSON><PERSON> <PERSON><PERSON><PERSON>,*********", "BANK_TRANSFER_accountNo_placeholder_ID": "Por eje<PERSON>lo,***********", "BANK_TRANSFER_accountNo_hint_MY": "Introduzca un número de cuenta con un máximo de 35 caracteres.", "BANK_TRANSFER_accountNo_hint_TH": "Por favor, introduzca un número de cuenta de 10-18 dígitos", "BANK_TRANSFER_accountNo_hint_ID": "Introduzca un número de cuenta numérico.", "BANK_TRANSFER_bankCode_title_MY": "Código bancar<PERSON> (Código SWIFT)", "BANK_TRANSFER_bankCode_placeholder_MY": "<PERSON><PERSON> e<PERSON><PERSON><PERSON>, CITIHK0001", "BANK_TRANSFER_bankCode_hint_MY": "Introduzca una combinación de letras y números de 8 a 11 caracteres.", "withdrawal_rule_title": "Instrucciones de retiro", "withdrawal_rule_detail": ["Tras una solicitud de retiro exitosa, se espera que los fondos lleguen en un plazo de 1 a 7 días hábiles", "Si no llegan dentro del plazo estimado, confirme que su cuenta receptora sea correcta y espere pacientemente o contacte con el servicio de atención al cliente", "Se aplican diferentes comisiones por retiro según el monto a retirar, y el monto específico dependerá del monto real recibido", "Si cancela su cuenta o se le bloquea debido a infracciones durante el período de retiro, congelaremos su solicitud de retiro"], "withdrawal_select_methods_tips": "Para retirar su dinero, debe vincular su método de pago. Seleccione su método de pago y asegúrese de que su información de pago sea verdadera y válida.", "service_fee": "cargo por manejo: {0}", "withdrawal_methods": "Método de pago", "withdrawal_information": "Información de retiro", "withdrawal_info_check": "Por favor, confirme su información de retiro.", "estimated_withdrawal_amount": "Monto estimado de retiro", "estimated_amount_received": "Monto estimado a recibir", "estimated_service_fee": "Cargo por manipulación estimado", "current_selected": "Selección actual", "completed": "Ya lleno", "need_complete": "Por completar", "withdrawal_amount": "Monto del retiro", "total_withdrawal_amount": "Monto de retiro acumulado", "withdrawal_pending": "Retiro en curso", "withdrawal_success": "<PERSON><PERSON><PERSON>oso", "withdrawal_fail": "<PERSON><PERSON><PERSON> fallido", "fail_reason": "No se recomienda el retiro", "empty": "Sin registro de retiros", "BANK_TRANSFER_receive_bank": "Banco beneficiario", "BANK_TRANSFER_receive_bank_placeholder": "Seleccione el banco del beneficiario", "BANK_TRANSFER_fill_info_tips": "Por favor, complete su información de pago", "payeeInfo_documentId_title": "CPF/CNPJ", "payeeInfo_documentId_placeholder": "p. ej. ***********", "payeeInfo_documentId_hint": "Por favor, introduzca el CPF/CNPJ (Número de Identificación Fiscal Individual/Corporativo)", "payeeInfo_address_title": "Dirección", "payeeInfo_email_title": "Correo electrónico", "payeeInfo_email_placeholder": "p. ej.1234566{0}gmail.com", "payeeInfo_email_hint": "Por favor, introduzca el formato de correo electrónico correcto", "payeeInfo_phone_title": "Número de teléfono", "WALLET_accountNo_title": "{0} cuenta", "WALLET_accountNo_placeholder_SA": "p. ej.************", "WALLET_accountNo_placeholder_EG": "p. ej. **********4", "WALLET_accountNo_placeholder_AE": "p. ej. +971-*********", "WALLET_accountNo_placeholder_TR": "p. ej. **********", "WALLET_accountNo_placeholder_PH": "p. ej. ***********", "WALLET_accountNo_placeholder_BR_MERCADOPAGO": "por ejemplo,mercadopago_user{0}gmail.com", "WALLET_accountNo_placeholder_BR_PIX": "p. ej. ***********", "WALLET_accountNo_placeholder_BR_PagBank": "p. ej.************{0}123.com", "WALLET_accountNo_hint_SA": "Por favor ingrese el número de teléfono vinculado a su billetera, 12 dígitos comenzando con 9665", "WALLET_accountNo_hint_EG": "Por favor ingrese el número de teléfono vinculado a su billetera, 11 dígitos comenzando con 01", "WALLET_accountNo_hint_EG_2": "Por favor ingrese el número de teléfono vinculado a su billetera, 12 dígitos comenzando con 01 o el código del país más 10 dígitos", "WALLET_accountNo_hint_EG_MEEZA_NETWORK": "Número de teléfono del pagador (obligatorio): el número de teléfono vinculado a la billetera, 11 dígitos que comienzan con 01 o 201.", "WALLET_accountNo_hint_AE": "Por favor ingrese el número de teléfono vinculado a su billetera. El formato debe ser", "WALLET_accountNo_hint_TR": "Por favor ingrese el número de teléfono vinculado a su billetera, 10 dígitos o PL más 10 dígitos", "WALLET_accountNo_hint_PH": "Por favor ingrese el número de teléfono vinculado a su billetera, 11 dígitos comenzando con 09", "WALLET_accountNo_hint_PH_LAZADAPH": "Por favor ingrese el número de teléfono vinculado a su billetera, 11 dígitos comenzando con 09 o correo electrónico", "WALLET_accountNo_hint_BR_MERCADOPAGO": "Por favor ingrese la cuenta de usuario (correo electrónico o ID) vinculada a la billetera", "WALLET_accountNo_hint_BR_PIX": "Por favor ingrese la cuenta correspondiente vinculada a la billetera", "WALLET_accountNo_hint_BR_PagBank": "Por favor, introduzca la dirección de correo electrónico vinculada a su billetera, hasta 60 caracteres", "WALLET_fullName_title": "Nombre del beneficiario", "WALLET_fullName_placeholder_AE": "por e<PERSON><PERSON><PERSON>, <PERSON>", "WALLET_fullName_placeholder_PH": "por e<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "WALLET_fullName_placeholder_BR": "por ejemplo <PERSON>", "WALLET_fullName_hint": "Por favor ingrese su nombre completo en inglés", "WALLET_lastName_title": "Apellido del beneficiario", "WALLET_lastName_placeholder_EG": "ej. <PERSON><PERSON><PERSON>", "WALLET_lastName_hint": "Por favor, introduzca su apellido", "WALLET_accountType_title": "{0} Tipo de cuenta vinculante", "WALLET_accountType_placeholder_BR_PIX": "Seleccione el tipo de cuenta vinculante", "WALLET_accountType_option_E_PIX": "Correo electrónico", "WALLET_accountType_option_P_PIX": "Teléfono", "WALLET_accountType_option_C_PIX": "CPF/CNPJ (número fiscal personal/corporativo)", "WALLET_accountType_option_B_PIX": "VPE", "WALLET_accountNo_placeholder_BR_PIX_B": "p. ej. 123e4567-e89b-12d3-a456-************", "SWIFT_accountNo_title": "Cuenta SWIFT", "SWIFT_accountNo_placeholder_SA": "p. ej. SA44200000**********1234", "SWIFT_accountNo_placeholder_AE": "p. ej. AE46009000000**********", "SWIFT_accountNo_placeholder_KW": "p. ej. ******************************", "SWIFT_accountNo_placeholder_QA": "p. ej. *****************************", "SWIFT_accountNo_placeholder_TR": "p. ej. TR3200100099999**********0", "SWIFT_accountNo_placeholder_BH": "p. ej. **********************", "SWIFT_accountNo_placeholder_YE": "p. ej. **********", "SWIFT_accountNo_placeholder_IQ": "p. ej. ***********************", "SWIFT_accountNo_placeholder_IL": "p. ej. ***********************", "SWIFT_accountNo_placeholder_PS": "p. ej. **********", "SWIFT_accountNo_placeholder_AZ": "p. ej. AZ77VTBA000000000**********0", "SWIFT_accountNo_placeholder_LB": "p. ej. ****************************", "SWIFT_accountNo_placeholder_MA": "p. ej. **********", "SWIFT_accountNo_placeholder_PH": "p. ej. **********", "SWIFT_accountNo_placeholder_BR": "p. ej. ******************************", "SWIFT_accountNo_placeholder_EG": "p. ej. *****************************", "SWIFT_accountNo_placeholder_JO": "Por ejemplo JO71CBJO00000000000**********0", "SWIFT_accountNo_hint_SA": "Por favor ingrese su cuenta SWIFT, 24 caracteres que comienzan con SA", "SWIFT_accountNo_hint_AE": "Por favor ingrese su cuenta SWIFT, 23 caracteres que comienzan con AE", "SWIFT_accountNo_hint_KW": "Por favor ingrese su cuenta SWIFT, 30 caracteres comenzando con KW", "SWIFT_accountNo_hint_QA": "Por favor ingrese su cuenta SWIFT, 29 caracteres que comienzan con QA", "SWIFT_accountNo_hint_TR": "Por favor ingrese su cuenta SWIFT, 26 caracteres que comienzan con TR", "SWIFT_accountNo_hint_BH": "Por favor ingrese su cuenta SWIFT, 22 caracteres que comienzan con BH", "SWIFT_accountNo_hint_YE": "Por favor ingrese su cuenta SWIFT, 34 dígitos y letras o menos", "SWIFT_accountNo_hint_IQ": "Por favor ingrese su cuenta SWIFT, 23 caracteres que comienzan con IQ", "SWIFT_accountNo_hint_IL": "Por favor ingrese su cuenta SWIFT, 23 caracteres que comienzan con IL", "SWIFT_accountNo_hint_PS": "Por favor ingrese su cuenta SWIFT, 34 dígitos y letras o menos", "SWIFT_accountNo_hint_AZ": "Por favor ingrese su cuenta SWIFT, 28 caracteres comenzando con AZ", "SWIFT_accountNo_hint_LB": "Por favor ingrese su cuenta SWIFT, 28 caracteres que comienzan con LB", "SWIFT_accountNo_hint_MA": "Por favor ingrese su cuenta SWIFT, 34 dígitos y letras o menos", "SWIFT_accountNo_hint_PH": "Por favor, introduzca su cuenta SWIFT, 34 dígitos y letras o menos", "SWIFT_accountNo_hint_BR": "Por favor, introduzca su cuenta SWIFT, 29 caracteres que empiezan por BR", "SWIFT_accountNo_hint_EG": "Por favor ingrese su cuenta SWIFT, 29 caracteres que comienzan con EG", "SWIFT_accountNo_hint_JO": "Por favor ingrese su cuenta SWIFT, 30 caracteres comenzando con JO", "SWIFT_fullName_title": "Nombre del beneficiario", "SWIFT_fullName_placeholder": "por e<PERSON><PERSON><PERSON>, <PERSON>", "SWIFT_fullName_hint": "Por favor ingrese su nombre completo en inglés", "SWIFT_bankCode_title": "Número de banco (código SWIFT)", "SWIFT_bankCode_placeholder": "p. ej. SCBLHKHH", "SWIFT_bankCode_hint": "Por favor ingrese el código del banco, 8 u 11 dígitos, se permiten números y letras mayús<PERSON>s", "CARD_accountNo_title": "Cuenta bancaria", "CARD_accountNo_placeholder_EG": "p. ej. ***********", "CARD_accountNo_placeholder_TR": "p. ej. ****************", "CARD_accountNo_hint_EG": "Por favor ingrese la cuenta bancaria, longitud>=8", "CARD_accountNo_hint_TR": "Por favor ingrese el número de tarjeta bancaria de 16 dígitos, solo se admiten tarjetas de débito", "CARD_fullName_title": "Nombre del beneficiario", "CARD_fullName_placeholder": "por ejemplo <PERSON>", "CARD_fullName_hint": "Por favor ingrese su nombre completo en inglés", "CARRIER_BILLING_accountNo_title": "Número de teléfono", "CARRIER_BILLING_accountNo_placeholder": "p. ej.************", "CARRIER_BILLING_accountNo_hint": "Por favor, introduzca el número de teléfono.", "CASH_accountNo_title": "{0}cuenta", "CASH_accountNo_placeholder": "p. ej. ***********", "CASH_accountNo_hint": "Por favor ingrese su cuenta{0}, 11 dígitos comenzando con 0", "BANK_TRANSFER_accountNo_title_SA": "<PERSON><PERSON><PERSON> (IBAN)", "BANK_TRANSFER_accountNo_title_AE": "<PERSON><PERSON><PERSON> (IBAN)", "BANK_TRANSFER_accountNo_title_TR": "<PERSON><PERSON><PERSON> (IBAN)", "BANK_TRANSFER_accountNo_title_EG": "Cuenta bancaria", "BANK_TRANSFER_accountNo_title_KW": "Cuenta bancaria", "BANK_TRANSFER_accountNo_title_QA": "Cuenta bancaria", "BANK_TRANSFER_accountNo_title_MA": "Cuenta bancar<PERSON> (RIB)", "BANK_TRANSFER_accountNo_title_PH": "Cuenta bancaria", "BANK_TRANSFER_accountNo_title_BR": "Cuenta bancaria", "BANK_TRANSFER_accountNo_placeholder_SA": "p. ej. ************************", "BANK_TRANSFER_accountNo_placeholder_EG": "p. ej.************", "BANK_TRANSFER_accountNo_placeholder_AE": "p. ej. ***********************", "BANK_TRANSFER_accountNo_placeholder_KW": "p. ej. ******************************", "BANK_TRANSFER_accountNo_placeholder_QA": "p. ej. *****************************", "BANK_TRANSFER_accountNo_placeholder_TR": "p. ej. **************************", "BANK_TRANSFER_accountNo_placeholder_MA": "por ejemplo, 123456789876543212345678", "BANK_TRANSFER_accountNo_placeholder_PH": "p. ej. ************", "BANK_TRANSFER_accountNo_placeholder_BR": "p. ej. ***********", "BANK_TRANSFER_accountNo_placeholder_JO": "p. ej. ******************************", "BANK_TRANSFER_accountNo_hint_SA": "Por favor, introduzca una combinación de SA+22 letras y números", "BANK_TRANSFER_accountNo_hint_EG": "Por favor, introduzca el IBAN y la cuenta bancaria local, IBAN; 29 caracteres (EG + 27 dígitos)", "BANK_TRANSFER_accountNo_hint_AE": "Por favor ingrese AE+21 dígitos, AE debe estar en mayúscula", "BANK_TRANSFER_accountNo_hint_KW": "Por favor, introduzca una longitud de menos de 50 caracteres, en formato IBAN", "BANK_TRANSFER_accountNo_hint_QA": "Por favor, introduzca una longitud de menos de 50 caracteres, en formato IBAN", "BANK_TRANSFER_accountNo_hint_TR": "Por favor, introduzca un número que empiece por TR más 24 dígitos, en formato IBAN", "BANK_TRANSFER_accountNo_hint_MA": "Por favor, introduzca el código de cuenta RIB de 24 dígitos.", "BANK_TRANSFER_accountNo_hint_PH": "Por favor ingrese de 6 a 128 dígitos", "BANK_TRANSFER_accountNo_hint_BR": "Por favor ingrese de 4 a 15 dígitos", "BANK_TRANSFER_accountNo_hint_JO": "Por favor, introduzca una combinación de 30 caracteres de números y letras que comiencen con JO", "BANK_TRANSFER_bankBranch_title": "Número de sucursal bancaria", "BANK_TRANSFER_bankBranch_placeholder_BR": "p. ej. 1234", "BANK_TRANSFER_bankBranch_hint_BR": "Por favor ingrese el número de sucursal bancaria, de 4 a 6 dígitos", "BANK_TRANSFER_address_placeholder_SA": "p. ej. aabbcccccccc", "BANK_TRANSFER_address_placeholder_AE": "por ejemplo, ALICI STREET KADIKOY ESTAMBUL", "BANK_TRANSFER_address_placeholder_KW": "p. ej. calle Kuwait xx", "BANK_TRANSFER_address_hint_SA": "Por favor ingrese la dirección, con un límite de longitud de 10 a 200 caracteres, permitiendo solo números, puntos, letras mayúsculas y minúsculas y espacios.", "BANK_TRANSFER_address_hint_AE": "Por favor ingrese la dirección, con un límite de longitud de 1 a 200 caracteres, el banco realizará verificaciones de control de riesgos.", "BANK_TRANSFER_address_hint_KW": "Por favor ingrese su dirección, longitud<=255", "BANK_TRANSFER_fullName_placeholder_TR": "por e<PERSON><PERSON><PERSON>, Antonio", "BANK_TRANSFER_fullName_placeholder_PH": "por e<PERSON><PERSON><PERSON>, Antonio", "BANK_TRANSFER_fullName_placeholder_BR": "por e<PERSON><PERSON><PERSON>, Antonio", "BANK_TRANSFER_fullName_placeholder_MA": "p. ej. <PERSON>", "BANK_TRANSFER_fullName_placeholder_KW": "p.ej. <PERSON>", "BANK_TRANSFER_phone_placeholder_PH": "p. ej. ***********", "BANK_TRANSFER_phone_placeholder_BR": "p. ej. *************", "BANK_TRANSFER_phone_hint_PH": "Por favor ingrese su número de teléfono móvil, de 0 a 9 dígitos comenzando con 11 (debe agre<PERSON><PERSON> 0)", "BANK_TRANSFER_phone_hint_BR": "Por favor ingrese su número de teléfono móvil, 12-13 d<PERSON><PERSON><PERSON> comenzando con 55", "BANK_TRANSFER_bankBranch_title_SA": "SWIFT code", "BANK_TRANSFER_bankBranch_placeholder_SA": "********", "BANK_TRANSFER_bankBranch_hint_SA": "Please enter a combination of 8 or 11 uppercase letters and numbers", "BANK_TRANSFER_city_title_SA": "City", "BANK_TRANSFER_city_placeholder_SA": "For example: New York", "BANK_TRANSFER_city_hint_SA": "Please enter the city name, up to 35 characters", "DLOCAL_BANK_TRANSFER_accountNo_placeholder_MA": "For example: 001123211111111111111111", "DLOCAL_BANK_TRANSFER_accountNo_hint_MA": "Please enter a combination of 24 digits", "DLOCAL_BANK_TRANSFER_birthday_title_AE": "Date of birth", "DLOCAL_BANK_TRANSFER_birthday_placeholder_AE": "例如********", "DLOCAL_BANK_TRANSFER_birthday_hint_AE": "For example: ********", "DLOCAL_BANK_TRANSFER_documentType_title_AE": "Document type", "DLOCAL_BANK_TRANSFER_documentType_placeholder_AE": "Please select the type of identification", "DLOCAL_BANK_TRANSFER_documentType_option_ID_AE": "ID", "DLOCAL_BANK_TRANSFER_documentType_option_PASS_AE": "PASS", "DLOCAL_BANK_TRANSFER_documentId_title_AE": "Identification number", "DLOCAL_BANK_TRANSFER_documentId_placeholder_ID_AE": "For example: NNN-NNNN-NNNNNNN-N", "DLOCAL_BANK_TRANSFER_documentId_placeholder_PASS_AE": "For example: ABCD1234", "DLOCAL_BANK_TRANSFER_documentId_hint_AE": "Please enter your identification number", "AGENT_accountNo_Whatsapp_title_ALL": "Número de WhatsApp", "AGENT_accountNo_Whatsapp_placeholder_ALL": "<PERSON><PERSON> <PERSON> +90 111 111 11 11", "AGENT_accountNo_Whatsapp_hint_ALL": "Por favor ingrese su cuenta de WhatsApp", "AGENT_accountNo_vodafone_title_EG": "Cuenta Vodafone", "AGENT_fullName_title_ID": "Payee ID", "AGENT_fullName_placeholder_ID": "1111111", "AGENT_fullName_hint_ID": "Please enter the payee id", "USDT_blockchain_title_ALL": "Nombre de la cadena de bloques", "USDT_address_title_ALL": "Dirección de emisión de monedas", "USDT_address_placeholder_ALL": "Por ejemplo 11111111111111111111111111", "USDT_address_hint_ALL": "Por favor, introduzca una dirección que empiece por 1, 3 o bc", "USDT_address_hint_common": "Por favor ingrese su dirección USDT"}