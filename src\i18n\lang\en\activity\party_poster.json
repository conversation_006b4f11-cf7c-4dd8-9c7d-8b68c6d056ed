{"k_1": "Throw a party Win awesome prizes!", "k_2": "The room party function is now online. 🎉🎉🎉 You have the opportunity to get the following rewards by hosting a party.", "k_3": "You can get additional honorary titles if you host the party between 7.28-8.10.", "k_4": "Gift heat value", "k_5": "requirements", "k_6": "room owner", "k_7": "Party requirement", "k_8": "Rewards for room owner", "k_9": "Top1-3 Rewards", "k_10": "The number of users entering the room reaches 20", "k_11": "The number of gift-giving users reaches 5", "k_12": "The room heat value reaches 30,000", "k_13": "Party Support King", "k_14": "Party Supporter", "k_15": "Party Charm King", "k_16": "Party Fascination", "k_17": "Note: You can receive up to 5 times per week", "k_18": "Extra reward", "k_19": "Number of qualified parties", "k_20": "Note: You can get additional honorary title by hosting parties that meet the requirements of Reward 2 between July 28 and August 10. The rewards will be distributed on August 4 and August 11.", "k_21": "Party play guide", "k_22": "Talent Party", "k_23": "Mic allocation", "k_24": "The host has a fixed microphone position 1, and microphones 2-6 are exclusive to contestants. Audience members are not allowed to use the microphone.", "k_25": "Competition process", "k_26": "Contestants sing one by one, 1-2 songs each. The host introduces the contestants, and the whole audience is silent during the singing to maintain order.", "k_27": "Interactive voting", "k_28": "After the singing, the audience can vote on the public screen or send gifts to add points for their favorite contestants.", "k_29": "Promotion/elimination", "k_30": "After each round of singing, the contestant with the least votes or gifts will be eliminated, and the rest will advance. Finally, the champion, runner-up and third place will be selected.", "k_31": "PK Party", "k_32": "Open PK mode", "k_33": "open microphone PK or team PK", "k_34": "PK process", "k_35": "host announces the start of PK, PK players conduct PK", "k_36": "Result announcement", "k_37": "countdown ends, the system automatically settles the PK score, the host announces the winner and the punishment or reward content (such as the winner sings a designated song, the loser performs a talent show).", "k_38": "Interesting interaction", "k_39": "the loser can be set to accept interesting punishments to enhance the entertainment atmosphere.", "k_40": "Game Party", "k_41": "Mic allocation", "k_42": "microphone No. 1 is the host, and microphones 2-8 are free to participate in small games (such as you draw and I guess, idiom chain, who is the undercover, etc.).", "k_43": "Game process", "k_44": "the host introduces the rules of this round of games and allocates microphones to the participants (such as who answers the question first will go to the microphone).", "k_45": "Rotation mechanism", "k_46": "after each game, the outstanding performer retains the microphone, and the loser leaves the microphone to let new players join.", "k_47": "Interaction method", "k_48": "the audience can guess or cheer for the players through the public screen, or be invited to the microphone as an \"off-site military advisor\" to participate in specific links.", "k_49": "3. Game Party"}