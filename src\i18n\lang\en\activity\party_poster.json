{"title": "Throw a party Win awesome prizes!", "description": "The room party function is now online. 🎉🎉🎉 You have the opportunity to get the following rewards by hosting a party. You can get additional honorary titles if you host the party between 7.28-8.10.", "gift_heat_requirements_1": "The number of users entering the room reaches 20", "gift_heat_requirements_2": "The number of gift-giving users reaches 5", "gift_heat_requirements_3": "The room heat value reaches 30,000", "party_requirement_1": "Party Support King", "party_requirement_2": "Party Supporter", "party_requirement_3": "Party Charm King", "party_requirement_4": "Party Fascination", "room_owner_rewards_1": "Top1-3 Rewards", "room_owner_rewards_note": "You can receive up to 5 times per week", "extra_reward_condition": "Number of qualified parties", "extra_reward_rewards": "Rewards for room owner", "extra_reward_note": "You can get additional honorary title by hosting parties that meet the requirements of Reward 2 between July 28 and August 10. The rewards will be distributed on August 4 and August 11.", "talent_party_mic_allocation": "The host has a fixed microphone position 1, and microphones 2-6 are exclusive to contestants. Audience members are not allowed to use the microphone.", "talent_party_competition_process": "Contestants sing one by one, 1-2 songs each. The host introduces the contestants, and the whole audience is silent during the singing to maintain order.", "talent_party_interactive_voting": "After the singing, the audience can vote on the public screen or send gifts to add points for their favorite contestants.", "talent_party_promotion_elimination": "After each round of singing, the contestant with the least votes or gifts will be eliminated, and the rest will advance. Finally, the champion, runner-up and third place will be selected.", "pk_party_oppk_mode": "Open microphone PK or team PK", "pk_party_pk_process": "Host announces the start of PK, PK players conduct PK", "pk_party_result_announcement": "Countdown ends, the system automatically settles the PK score, the host announces the winner and the punishment or reward content (such as the winner sings a designated song, the loser performs a talent show).", "pk_party_interesting_interaction": "The loser can be set to accept interesting punishments to enhance the entertainment atmosphere.", "game_party_mic_allocation": "Microphone No. 1 is the host, and microphones 2-8 are free to participate in small games (such as you draw and I guess, idiom chain, who is the undercover, etc.).", "game_party_game_process": "The host introduces the rules of this round of games and allocates microphones to the participants (such as who answers the question first will go to the microphone).", "game_party_rotation_mechanism": "After each game, the outstanding performer retains the microphone, and the loser leaves the microphone to let new players join.", "game_party_interaction_method": "The audience can guess or cheer for the players through the public screen, or be invited to the microphone as an 'off-site military advisor' to participate in specific links"}