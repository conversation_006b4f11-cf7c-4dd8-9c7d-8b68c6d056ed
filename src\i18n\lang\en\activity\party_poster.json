{"title": "Throw a party Win awesome prizes!", "intro_desc": "The room party function is now online. 🎉🎉🎉 You have the opportunity to get the following rewards by hosting a party.", "extra_title_period": "You can get additional honorary titles if you host the party between 7.28-8.10.", "gift_heat_requirements": "Gift heat value requirements", "room_owner_rewards": "Rewards for room owner", "party_requirements": "Party requirement", "room_owner_rewards_duplicate": "Rewards for room owner", "top_rewards": "Top1-3 Rewards", "requirement_users_20": "The number of users entering the room reaches 20", "requirement_gift_users_5": "The number of gift-giving users reaches 5", "requirement_heat_30k": "The room heat value reaches 30,000", "title_support_king": "Party Support King", "title_supporter": "Party Supporter", "title_charm_king": "Party Charm King", "title_fascination": "Party Fascination", "note_weekly_limit": "Note: You can receive up to 5 times per week", "extra_reward": "Extra reward", "qualified_parties_count": "Number of qualified parties", "note_extra_title": "Note: You can get additional honorary title by hosting parties that meet the requirements of Reward 2 between July 28 and August 10. The rewards will be distributed on August 4 and August 11.", "play_guide_title": "Party play guide", "talent_party_title": "Talent Party", "talent_mic_allocation": "Mic allocation", "talent_mic_desc": "The host has a fixed microphone position 1, and microphones 2-6 are exclusive to contestants. Audience members are not allowed to use the microphone.", "talent_competition_process": "Competition process", "talent_competition_desc": "Contestants sing one by one, 1-2 songs each. The host introduces the contestants, and the whole audience is silent during the singing to maintain order.", "talent_interactive_voting": "Interactive voting", "talent_voting_desc": "After the singing, the audience can vote on the public screen or send gifts to add points for their favorite contestants.", "talent_promotion_elimination": "Promotion/elimination", "talent_elimination_desc": "After each round of singing, the contestant with the least votes or gifts will be eliminated, and the rest will advance. Finally, the champion, runner-up and third place will be selected.", "pk_party_title": "PK Party", "pk_open_mode": "Open PK mode", "pk_mode_desc": "open microphone PK or team PK", "pk_process": "PK process", "pk_process_desc": "host announces the start of PK, PK players conduct PK", "pk_result_announcement": "Result announcement", "pk_result_desc": "countdown ends, the system automatically settles the PK score, the host announces the winner and the punishment or reward content (such as the winner sings a designated song, the loser performs a talent show).", "pk_interesting_interaction": "Interesting interaction", "pk_interaction_desc": "the loser can be set to accept interesting punishments to enhance the entertainment atmosphere.", "game_party_title": "Game Party", "game_mic_allocation": "Mic allocation", "game_mic_desc": "microphone No. 1 is the host, and microphones 2-8 are free to participate in small games (such as you draw and I guess, idiom chain, who is the undercover, etc.).", "game_process": "Game process", "game_process_desc": "the host introduces the rules of this round of games and allocates microphones to the participants (such as who answers the question first will go to the microphone).", "game_rotation_mechanism": "Rotation mechanism", "game_rotation_desc": "after each game, the outstanding performer retains the microphone, and the loser leaves the microphone to let new players join.", "game_interaction_method": "Interaction method", "game_interaction_desc": "the audience can guess or cheer for the players through the public screen, or be invited to the microphone as an \"off-site military advisor\" to participate in specific links.", "game_party_number": "3. Game Party"}