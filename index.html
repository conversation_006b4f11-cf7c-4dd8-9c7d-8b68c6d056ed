<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <link rel="icon" type="image/svg+xml" href="/logo.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover">
        <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <title>Siya</title>
    </head>

    <style>
        #app {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .loadEffect {
            width: 100px;
            height: 100px;
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.4);
        }

        .loadEffect span {
            display: inline-block;
            width: 30px;
            height: 10px;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            background: #999;
            position: absolute;
            -webkit-animation: load 1.04s ease infinite;
        }

        @-webkit-keyframes load {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0.2;
            }
        }

        .loadEffect span:nth-child(1) {
            left: 0;
            top: 50%;
            margin-top: -5px;
            -webkit-animation-delay: 0.13s;
        }

        .loadEffect span:nth-child(2) {
            left: 10px;
            top: 20px;
            -webkit-transform: rotate(45deg);
            -webkit-animation-delay: 0.26s;
        }

        .loadEffect span:nth-child(3) {
            left: 50%;
            top: 10px;
            margin-left: -15px;
            -webkit-transform: rotate(90deg);
            -webkit-animation-delay: 0.39s;
        }

        .loadEffect span:nth-child(4) {
            top: 20px;
            right: 10px;
            -webkit-transform: rotate(135deg);
            -webkit-animation-delay: 0.52s;
        }

        .loadEffect span:nth-child(5) {
            right: 0;
            top: 50%;
            margin-top: -5px;
            -webkit-transform: rotate(180deg);
            -webkit-animation-delay: 0.65s;
        }

        .loadEffect span:nth-child(6) {
            right: 10px;
            bottom: 20px;
            -webkit-transform: rotate(225deg);
            -webkit-animation-delay: 0.78s;
        }

        .loadEffect span:nth-child(7) {
            bottom: 10px;
            left: 50%;
            margin-left: -15px;
            -webkit-transform: rotate(270deg);
            -webkit-animation-delay: 0.91s;
        }

        .loadEffect span:nth-child(8) {
            bottom: 20px;
            left: 10px;
            -webkit-transform: rotate(315deg);
            -webkit-animation-delay: 1.04s;
        }
    </style>
    <body>
    <div id="app">
        <div class="loadEffect">
            <span></span> <span></span> <span></span> <span></span> <span></span> <span></span> <span></span> <span></span>
        </div>
    </div>
    <script>
        localStorage.setItem("enter_time", Date.now());
    </script>
    <script type="module" src="/src/main.js"></script>
    </body>
</html>
