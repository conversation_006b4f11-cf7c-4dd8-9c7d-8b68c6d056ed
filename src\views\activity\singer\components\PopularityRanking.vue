<template>
  <div class="good-voice">
    <VoteSearch
      :source="2"
      :show-vote-btn="activityInfo?.stage !== 10"
    ></VoteSearch>
    <div class="desc-box">
      <div class="desc">
        {{ $t(`singer.popularity_ranking_description`) }}
      </div>
      <div class="prizes">
        <div class="prize" v-for="item in rewards">
          <div class="prize-box">
            <PrizeBox
              :img="
                item.reward_type === 1 ? CoinIcon : item.reward_img || CoinIcon
              "
              :tag="formatTag(item)"
            ></PrizeBox>
          </div>
          <div class="prize-name">{{ item.reward_name }}</div>
        </div>
      </div>
    </div>
    <Ranking
      :rank-type="1"
      :show-audio-btn="false"
      :show-vote-btn="activityInfo?.stage !== 10"
    ></Ranking>
  </div>
</template>
<script setup>
import VoteSearch from "./VoteSearch.vue";
import Ranking from "./Ranking.vue";
import PrizeBox from "./PrizeBox.vue";
import singer<PERSON>pi from "@/api/activity.js";
import { onMounted, ref, inject } from "vue";
import { i18n, getLang } from "@/i18n/index.js";
import CoinIcon from "@/assets/images/common/coin.svg";
const t = i18n.global.t;

const activityInfo = inject("activityInfo", null);
const rewards = ref([]);
const fetchData = async () => {
  const res = await singerApi.singer_rewards();
  if (res.code === 200) {
    rewards.value = res.data?.popularity || [];
  }
};
const formatTag = (item) => {
  const lang = getLang();
  if (item.reward_type === 1) {
    return lang === "ar" ? `${item.num}X` : `X${item.num}`;
  }
  return item.duration > 1
    ? t("common.common_days", [item.duration])
    : t("common.common_day", [item.duration]);
};
onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.good-voice {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-top: px2rem(45);
  background-image: url("@/assets/images/activity/singer/bg.png");
  background-size: 100% px2rem(434);
  background-repeat: no-repeat;
  background-position: 0 px2rem(239);
}
.desc-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: auto;
  padding-top: px2rem(31);
  padding-bottom: px2rem(25);
  .desc {
    display: inline-flex;
    padding: px2rem(10);
    justify-content: center;
    align-items: center;
    border-radius: px2rem(6);
    background: rgba(255, 255, 255, 0.12);
    color: rgba(216, 189, 255, 0.55);
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(12);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* px2rem(12) */
    width: px2rem(291);
  }
}
.prizes {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: px2rem(281);
  flex-wrap: wrap;
  gap: px2rem(10);
}
.prize {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: px2rem(35);
  .prize-box {
    width: px2rem(70);
    height: px2rem(70);
  }
  .prize-name {
    color: #fff;
    text-align: center;
    font-family: Gilroy;
    font-size: px2rem(14);
    font-style: normal;
    font-weight: 500;
    line-height: 100%; /* px2rem(14) */
    margin-top: px2rem(12);
  }
}
</style>
