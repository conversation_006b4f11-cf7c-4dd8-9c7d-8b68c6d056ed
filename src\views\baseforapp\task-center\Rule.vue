<template>
  <div class="app-container page-task-center-rule flex">
    <header-bar class="header-bar" font-color="#fff" close-type="close">
      <img class="img-box" src="@/assets/images/vip/box.png" alt="">
    </header-bar>

    <main>
      <div class="rule-wrap">
        <div class="rule-title">{{ $t('base_total.task_center_task_title') }}</div>
        <div class="rule-detail-wrap">
          <div class="rule-detail"
               v-for="(cell, idx) in $tm('base_total.task_center_task_detail')" :key="idx"
          >
            {{cell}}
          </div>
        </div>
        <div class="rule-title">{{ $t('base_total.task_center_reward_title') }}</div>
        <div class="rule-detail-wrap">
          <div class="rule-detail"
               v-for="(cell, idx) in $tm('base_total.task_center_reward_detail')" :key="idx"
          >
            {{cell}}
          </div>
        </div>
        <div class="rule-title" v-if="showTask">{{ $t('base_total.task_official') }}</div>
        <div class="rule-detail-wrap" v-if="showTask">
          <div class="rule-detail"
               v-for="(cell, idx) in $tm('base_total.task_official_content')" :key="idx"
          >
            {{cell}}
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import baseApi from '@/api/baseforapp.js'

const showTask = ref(false)
onMounted(() => {
  baseApi.task_office().then(res => {
    showTask.value = res.data
  }) 
})
</script>

<style scoped lang="scss">
@import '@/assets/scss/common.scss';

.app-container {
  flex-direction: column;
  height: 100vh;
  background: url("@/assets/images/vip/rule-bg.png") top/contain no-repeat, #FFB763;
}

main {
  flex: 1;
  position: relative;
  z-index: $ZIndexHeader + 1;
  width: 100vw;
  overflow-y: scroll;
  padding: $gap12 $gap16;
}

.header-bar {
  position: relative;
}

.img-box {
  position: absolute;
  bottom: px2rem(-45);
  right: px2rem(16);
  width: px2rem(100);
  height: px2rem(100);
}

.rule-wrap {
  position: relative;
  padding: $gap16 $gap16 1px;
  background-color: #FFF8F4;
  border-radius: $radius16;

  &::after {
    content: '';
    position: absolute;
    top: px2rem(-4);
    right: px2rem(-4);
    bottom: px2rem(-4);
    left: px2rem(-4);
    border: 1px solid #FFFFFFA3;
    border-radius: px2rem(20);
  }

  .rule-title {
    margin-bottom: px2rem(12);
    font-size: px2rem(17);
    font-weight: $fontWeightBold;
    line-height: px2rem(21);
    color: #673808;
  }

  .rule-detail {
    margin-bottom: $gap16;
    font-size: px2rem(15);
    line-height: px2rem(20);
    color: #8C4A08;

    &:last-child {
      margin-bottom: px2rem(24);
    }
  }
}
</style>
