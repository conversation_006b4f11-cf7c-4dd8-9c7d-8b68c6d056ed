<template>
  <div class="coin-number-wrap flex" dir="ltr">
    <span>{{ formatNum.val }}</span>
    <span class="unit"
          :style="{fontSize: unitFontSize > 0 ? `${unitFontSize}px` : 'inherit'}"
    >{{ formatNum.unit }}</span>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, ref, watch } from 'vue'
import { numberWithUnit } from '@/utils/util';

const { proxy } = getCurrentInstance()

const props = defineProps({
  num: {
    type: Number,
    default: 0,
  },
  decimal: {
    type: Number,
    default: 2,
  },
  plus: {
    type: Boolean,
    default: false,
  },
  unitSize: {
    type: Number,
    default: 0,
  }
})

const unitFontSize = ref(0)
watch(() => props.unitSize, (val) => {
  unitFontSize.value = proxy.$pxToRemPx(val)
}, {
  immediate: true,
})

const numVal = computed(() => {
  return props.plus ? `+${props.num}` : props.num
})

const formatNum = computed(() => {
  return numberWithUnit(numVal.value, props.decimal)
})
</script>

<style scoped lang="scss">
.coin-number-wrap {
  align-items: flex-end !important;
}
</style>
