<template>
  <div class="rule-content">
    <div class="content-block">
      <div class="cell">
        <div class="label first">1.{{ $t("pk_king.event_rules_label") }}</div>
        <div class="content">
          {{ $t("pk_king.event_time", [timeZone]) }}
        </div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.first_cycle_label") }}</div>
        <div class="content">{{ $t("pk_king.first_cycle", [timeZone]) }}</div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.second_cycle_label") }}</div>
        <div class="content">{{ $t("pk_king.second_cycle", [timeZone]) }}</div>
      </div>
    </div>
    <div class="content-block">
      <div class="cell">
        <div class="label first">2. {{ $t("pk_king.task_rewards") }}</div>
        <div class="content">
          {{ $t("pk_king.task_rewards_desc") }}
        </div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.daily_tasks") }}</div>
        <div class="content">{{ $t("pk_king.daily_task_frequency") }}</div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.weekly_tasks") }}</div>
        <div class="content">{{ $t("pk_king.weekly_task_frequency") }}</div>
      </div>
    </div>
    <div class="content-block">
      <div class="cell">
        <div class="label first">3. {{ $t("pk_king.ranking_rewards") }}</div>
        <div class="content">
          {{ $t("pk_king.ranking_rewards_desc") }}
        </div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.pk_value_ranking") }}</div>
        <div class="content">
          {{ $t("pk_king.pk_value_ranking_desc") }}
        </div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.pk_contribution_ranking") }}</div>
        <div class="content">
          {{ $t("pk_king.pk_contribution_ranking_desc") }}
        </div>
      </div>
      <div class="cell">
        <div class="label">{{ $t("pk_king.room_ranking") }}</div>
        <div class="content">
          {{ $t("pk_king.room_ranking_desc") }}
        </div>
      </div>
    </div>
    <div class="content-block">
      <div class="cell">
        <div class="content">
          {{ $t("pk_king.feedback") }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  timeZone: {
    type: String,
    default: "UTC+8",
  },
});
</script>
<style lang="scss" scoped>
@import "@/assets/scss/common.scss";
.rule-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  padding: 0 px2rem(22);
  padding-top: px2rem(23);
  padding-bottom: px2rem(60);

  .content-block {
    width: 100%;
    border-radius: px2rem(8);
    border: 0.5px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.16);
    padding: px2rem(22) px2rem(10);
    margin-bottom: px2rem(16);
    &:last-child {
      margin-bottom: 0;
    }
    .cell {
      margin-bottom: px2rem(7);
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        width: fit-content;
        padding: px2rem(4) px2rem(8);
        margin-bottom: px2rem(3);
        border-radius: px2rem(20);
        background: #5a0a9e;
        color: #e5c7ff;
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 500;
        line-height: normal;
      }
      .first {
        background: #b500ff;
        color: #ffdf9c;
      }
      .content {
        color: #cb8ffe;
        font-family: Gilroy;
        font-size: px2rem(13);
        font-style: normal;
        font-weight: 500;
        line-height: 150%; /* 19.5px */
      }
    }
  }
}
</style>
