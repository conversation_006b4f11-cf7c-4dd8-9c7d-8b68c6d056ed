import { getCurrentInstance } from "vue";
import { buildURL, getFbPid } from "@/utils/adjustscript.js";

export function isAndroid() {
  var userAgent = navigator.userAgent.toLowerCase();
  return /android/.test(userAgent);
}

export function isiOS() {
  var userAgent = navigator.userAgent.toLowerCase();
  return /iphone|ipad|ipod/.test(userAgent);
}

export const pointData = (share_name, share_type = 1, lang_type) => {
  const { proxy } = getCurrentInstance();
  return {
    share_type,
    share_name,
    lang_type: lang_type || proxy.$languageFile,
    device_type: isiOS() ? 1 : 2,
  };
};

export const downloadByAdjust = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const p0 = urlParams.get("p0");
  const p1 = urlParams.get("p1");
  const p2 = urlParams.get("p2");
  const p3 = urlParams.get("p3");
  const p4 = urlParams.get("p4");
  const p5 = urlParams.get("p5");
  const p6 = urlParams.get("p6");
  const fbPid = getFbPid();
  const fbClickId = urlParams.get("fbclid");
  const url = buildURL(p0, p1, p2, p3, p4, p5, p6, fbClickId, fbPid);
  window.location.href = url;
};

export const download = (adjustUrl) => {
  const iosLinkUrl = "https://apps.apple.com/app/id6738165319"; // 替换为实际的iOS应用ID
  const androidLinkUrl =
    "https://play.google.com/store/apps/details?id=com.zr.siya"; // 替换为实际的Android应用包名
  const huaweiUrl = "appmarket://details?id=com.zr.siya"; // 替换为实际的华为应用包名
  const oppoUrl = "oppomarket://details?packagename=com.zr.siya"; // 替换为实际的OPPO应用包名
  const vivoUrl = "vivomarket://details?id=com.zr.siya"; // 替换为实际的vivo应用包名
  const xiaomiUrl = "mimarket://details?id=com.zr.siya"; // 替换为实际的小米应用包名
  if (isiOS()) {
    window.location.href = iosLinkUrl;
  } else if (isAndroid()) {
    const urlParams = new URLSearchParams(window.location.search);
    const p0 = urlParams.get("p0");
    if (p0) {
      window.location.href = adjustUrl;
    } else {
      window.location.href = androidLinkUrl;
    }
  }
};

export const downloadOrAdjust = () => {
  const iosLinkUrl = "https://apps.apple.com/app/id6738165319"; // 替换为实际的iOS应用ID
  const androidLinkUrl =
    "https://play.google.com/store/apps/details?id=com.zr.siya"; // 替换为实际的Android应用包名
  const huaweiUrl = "appmarket://details?id=com.zr.siya"; // 替换为实际的华为应用包名
  const oppoUrl = "oppomarket://details?packagename=com.zr.siya"; // 替换为实际的OPPO应用包名
  const vivoUrl = "vivomarket://details?id=com.zr.siya"; // 替换为实际的vivo应用包名
  const xiaomiUrl = "mimarket://details?id=com.zr.siya"; // 替换为实际的小米应用包名
  if (isiOS()) {
    window.location.href = iosLinkUrl;
  } else if (isAndroid()) {
    const urlParams = new URLSearchParams(window.location.search);
    const p0 = urlParams.get("p0");
    if (p0) {
      downloadByAdjust();
    } else {
      window.location.href = androidLinkUrl;
    }
  }
};
