<template>
  <div
    class="app-container flex"
    :class="
      hasIn(weekData, 'is_change_skin')
        ? weekData?.is_change_skin
          ? 'ph-skin'
          : 'common-skin'
        : ''
    "
  >
    <header>
      <header-bar
        :contentPadding="true"
        :backIconShow="false"
        fontColor="#fff"
        close-type="close"
      >
      </header-bar>
      <div class="head-title-wrap">
        <img
          v-if="hasIn(weekData, 'is_change_skin')"
          :src="
            weekData?.is_change_skin
              ? getLangImageUrl('head-title-ph.png')
              : getLangImageUrl('head-title.png')
          "
          alt=""
          :class="weekData?.is_change_skin ? 'head-title-ph' : 'head-title'"
        />
      </div>

      <div class="time-wrap flex">
        <count-timer :time="weekData?.this_week?.time" @update="refresh" />
      </div>
      <div class="nav-fame-hall">
        <img
          :src="getLangImageUrl('nav-hall.png')"
          alt=""
          v-show="!weekData?.is_change_skin"
          v-track:click
          trace-key="click_hall_of_fame"
          @click="jumpPage('RankingWeeklyStarFameHall')"
        />
      </div>
      <div class="nav-milestone">
        <div class="icon-bg" @click="jumpPage('RankingWeeklyStarMilestone')">
          {{ $t("ranking.ranking_weekly_star_rewards") }}
        </div>
      </div>
    </header>
    <main>
      <div class="tab-wrap flex">
        <div
          class="tab flex"
          :class="{
            'active-tab': currentTab === 1,
            'pt-fontSize': lang === 'pt',
          }"
          @click="tabChange(1)"
        >
          {{ $t("ranking.ranking_weekly_star_receiving") }}
        </div>
        <gap :gap="4"></gap>
        <div
          class="tab flex"
          :class="{ 'active-tab': currentTab === 2 }"
          @click="tabChange(2)"
        >
          {{ $t("ranking.ranking_weekly_star_send") }}
        </div>
      </div>
      <div
        class="content-desc"
        v-html="
          currentTab === 1
            ? $t('ranking.ranking_weekly_star_receiving_desc')
            : $t('ranking.ranking_weekly_star_send_desc', [
                `<img src='${getImageUrl(
                  'coin.png'
                )}' style='height:${$pxToRemPx(
                  10
                )}px; transform: translateY(${$pxToRemPx(2)}px)' />`,
                `<img src='${getImageUrl(
                  'star-value.png'
                )}' style='width: ${$pxToRemPx(
                  10
                )}px;transform: translateY(${$pxToRemPx(2)}px)'/>`,
              ])
        "
      ></div>
      <div class="award-wrap flex">
        <div
          class="award-item flex"
          v-for="(item, idx) in weekData?.this_week?.gift_list"
          :key="idx"
          :class="{
            'active-rank': currentRank === item.type,
            'active-send-star': currentTab === 2,
          }"
          @click="rankChange(item.type)"
        >
          <img :src="item.url" alt="" />
          <div class="worth-wrap">
            <img
              src="@/assets/images/common/coin.png"
              alt=""
              class="icon-img"
            />
            {{ item.coin }}
          </div>
        </div>
      </div>
      <title-bar :title="$t('ranking.ranking_weekly_star_ranking')" />
      <div class="ranking-wrap">
        <div
          v-if="currentTab === 1"
          class="exposure"
          v-track:exposure
          trace-key="weekly_star_receive_exposure"
          :track-params="JSON.stringify({ weekly_star_receive_from: from })"
        ></div>
        <div
          class="exposure"
          v-track:exposure
          v-if="currentTab === 2"
          trace-key="weekly_star_send_exposure"
        ></div>
        <div class="ranking-top-wrap">
          <div :class="`top-bg top-bg${i}`" v-for="i in 3" :index="i">
            <div class="avatar-wrap">
              <img
                :src="rankData?.rank[i - 1]?.avatar_frame"
                v-if="rankData?.rank[i - 1]?.avatar_frame"
                alt=""
                class="border-img"
              />
              <img
                :src="rankData?.rank[i - 1]?.avatar"
                v-if="rankData?.rank[i - 1]?.avatar"
                alt=""
                class="avatar-img"
              />
            </div>
            <div class="name ellipsis">{{ rankData?.rank[i - 1]?.name }}</div>
            <div class="award-gift-wrap flex">
              <img
                :src="
                  currentTab === 1
                    ? rankData?.rank[i - 1]?.gift_url
                    : getImageUrl('star-value.png')
                "
                v-if="
                  (currentTab === 2 && rankData?.rank[i - 1]) ||
                  rankData?.rank[i - 1]?.gift_url
                "
                alt=""
              />
              <gap :gap="2"></gap>
              {{ rankData?.rank[i - 1]?.num }}
            </div>
          </div>
        </div>
        <div class="ranking-list">
          <div
            class="list-item flex"
            v-for="(item, idx) in rankData?.rank.slice(3)"
          >
            <div class="index">{{ idx + 4 }}</div>
            <div class="avatar-wrap">
              <img
                :src="item.avatar_frame"
                v-if="item.avatar_frame"
                alt=""
                class="border-img"
              />
              <img :src="item.avatar" alt="" class="avatar-img" />
            </div>
            <div class="name">{{ item.name }}</div>
            <div class="item-gift-wrap flex">
              <img
                :src="
                  currentTab === 1
                    ? item?.gift_url
                    : getImageUrl('star-value.png')
                "
                v-if="currentTab === 2 || item?.gift_url"
                alt=""
              />
              <gap :gap="2"></gap>
              x{{ item?.num }}
            </div>
          </div>
        </div>
      </div>
      <div class="next-week flex">
        <div class="left flex">
          <img :src="getLangImageUrl('next-week.png')" alt="" />
          <div class="time">{{ weekData?.next_week?.time.join("-") }}</div>
        </div>
        <gap :gap="4"></gap>
        <div class="right flex">
          <div
            class="right-item flex"
            v-for="(item, idx) in weekData?.next_week?.gift_list"
            :key="idx"
          >
            <img :src="item.url" class="gift-url" alt="" />
            <div class="bottom flex">
              <img src="@/assets/images/common/coin.png" alt="" />
              <gap :gap="4"></gap>
              <span>{{ item.coin }}</span>
            </div>
          </div>
        </div>
      </div>
    </main>
    <footer>
      <div class="list-item flex">
        <div class="index">{{ rankData?.current?.rank || "-" }}</div>
        <gap :gap="8"></gap>
        <div class="avatar-wrap">
          <img
            :src="rankData?.current?.avatar_frame"
            alt=""
            v-if="rankData?.current?.avatar_frame"
            class="border-img"
          />
          <img :src="rankData?.current?.avatar" alt="" class="avatar-img" />
        </div>
        <div class="name">{{ rankData?.current?.name }}</div>
        <div class="item-gift-wrap flex">
          <img
            :src="
              currentTab === 1
                ? rankData?.current?.gift_url
                : getImageUrl('star-value.png')
            "
            alt=""
          />
          <gap :gap="2"></gap>
          x{{ rankData?.current?.num }}
        </div>
      </div>
    </footer>
    <van-loading size="32" class="loading" type="spinner" v-if="loading" />
  </div>
  <rule-modal
    :show="hintDialogShow"
    @confirm="toggleHintDialog(false)"
    @update:show="toggleHintDialog"
  >
  </rule-modal>
</template>

<script setup>
import { ref, getCurrentInstance, unref } from "vue";
import { i18n } from "@/i18n/index.js";
import { useRouter, useRoute } from "vue-router";
import { hasIn } from "lodash";
import rankingApi from "@/api/ranking.js";
import { preloadImage } from "@/utils/util.js";
import TitleBar from "./components/TitleBar.vue";
import RuleModal from "./components/RuleModal.vue";
import CountTimer from "./components/CountTimer.vue";

const { proxy } = getCurrentInstance();
const t = i18n.global.t;
const router = useRouter();
const route = useRoute();
const { from } = route.query;

const loading = ref(false);
const currentTab = ref(1);
const currentRank = ref(1);
const weekData = ref();
const rankData = ref();

const hintDialogShow = ref(false);
const toggleHintDialog = (bool) => {
  hintDialogShow.value = bool;
};

const jumpPage = (page) => {
  router.push({
    name: page,
  });
};

const tabChange = (tab) => {
  if (unref(loading)) return;
  if (tab === unref(currentTab)) return;
  currentTab.value = tab;
  fetchWeekData();
  fetchRankData();
};
const rankChange = (tab) => {
  if (unref(loading)) return;
  if (tab === currentRank.value || currentTab.value === 2) return;
  currentRank.value = tab;
  fetchRankData();
};

const refresh = () => {
  currentTab.value = 1;
  fetchWeekData();
  fetchRankData();
};

const lang = proxy.$languageFile;
const $pxToRemPx = proxy.$pxToRemPx;
function getLangImageUrl(name) {
  return new URL(
    `../../../assets/images/ranking/weekly-star/${lang}/${name}`,
    import.meta.url
  ).href;
}

function getImageUrl(name) {
  return new URL(
    `../../../assets/images/ranking/weekly-star/${name}`,
    import.meta.url
  ).href;
}

const fetchRankData = async () => {
  try {
    loading.value = true;
    const result = await rankingApi.week_star_rank({
      type: unref(currentTab),
      sub_type: unref(currentRank),
    });
    loading.value = false;
    rankData.value = result.data;
  } catch (error) {
    loading.value = false;
  }
};

const preloadTitleImg = () => {
  preloadImage(getLangImageUrl("head-title.png"));
  preloadImage(getLangImageUrl("head-title-ph.png"));
};

const fetchWeekData = async () => {
  const result = await rankingApi.week_star_time({
    type: unref(currentTab),
  });
  const _thisWeek = result.data.this_week.gift_list;
  currentRank.value = _thisWeek[0].type;
  result.data.this_week.gift_list.splice(0, 2, _thisWeek[1], _thisWeek[0]);
  weekData.value = result.data;
  // weekData.value = { ...result.data, is_change_skin: false };
};

preloadTitleImg();
fetchWeekData();
fetchRankData();
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.common-skin {
  background: url("@/assets/images/ranking/weekly-star/bg-head.jpg") top left
      no-repeat,
    url("@/assets/images/ranking/weekly-star/bg-common.png") top left repeat;
  background-size: 100%;
}

// 菲律宾独立日换肤
.ph-skin {
  background: url("@/assets/images/ranking/weekly-star/bg-head-ph.png") top left
      no-repeat,
    url("@/assets/images/ranking/weekly-star/bg-common-ph.png") top left repeat;
  background-size: 100%;
}

.app-container {
  flex-direction: column;
  background-color: #190a33;
  // background: url("@/assets/images/ranking/weekly-star/bg-head.jpg") top left
  //     no-repeat,
  //   url("@/assets/images/ranking/weekly-star/bg-common.png") top left repeat;
  // background-color: #190a33;
  // background-size: 100%;

  .loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  header {
    width: 100%;
    position: relative;
    .head-title-wrap {
      min-height: px2rem(80);
      .head-title {
        height: px2rem(80);
        display: block;
        margin: 0 auto;
      }
      .head-title-ph {
        height: px2rem(130);
        display: block;
        margin: px2rem(-24) auto;
      }
    }

    .time-wrap {
      margin: px2rem(8) auto 0;
      justify-content: center;
    }
    .nav-milestone {
      position: absolute;
      top: px2rem(48);
      display: flex;
      justify-content: flex-end;
      width: 100%;
      z-index: 1001;
      pointer-events: none;
      .icon-bg {
        background: url("@/assets/images/ranking/weekly-star/rewards-icon.png")
          no-repeat;
        background-size: 100%;
        font-size: px2rem(9);
        font-weight: 700;
        color: #fff;
        height: px2rem(40);
        width: px2rem(40);
        margin: 0 px2rem(16);
        pointer-events: all;
        text-shadow: 2px 0px 6px #9e5200;
        // -webkit-text-stroke: 0.5px #9E5200;
        display: flex;
        justify-content: center;
        align-items: flex-end;
      }
    }
    .nav-fame-hall {
      position: absolute;
      top: px2rem(140);
      top: v-bind(
        "proxy.$isHalf?proxy.$pxToRemPx(110)+'px':proxy.$pxToRemPx(140)+'px'"
      );
      display: flex;
      justify-content: flex-end;
      width: 100%;
      padding: 0 px2rem(2);
      img {
        height: px2rem(34);
        margin: 0 px2rem(2);
      }
    }
  }
  main {
    width: 100%;
    padding: px2rem(10) px2rem(16) px2rem(88);
    flex: 1;
    position: relative;
    .tab-wrap {
      flex-direction: row;
      font-size: $fontSize13;
      justify-content: center;
      line-height: 1;
      color: $white;
      font-weight: $fontWeightBold;
      .tab {
        height: px2rem(34);
        width: px2rem(167);
        justify-content: center;
        align-items: center;
        text-align: center;
        background-image: url("@/assets/images/ranking/weekly-star/tab-inactive.png");
        background-repeat: no-repeat;
        background-size: 100%;
      }
      .active-tab {
        text-shadow: 0px 0px 6px #ff98fa;
        background-image: url("@/assets/images/ranking/weekly-star/tab-active.png");
      }
    }
    .pt-fontSize {
      font-size: px2rem(12);
    }
    .content-desc {
      border-radius: px2rem(6);
      padding: px2rem(6);
      border: 0.5px solid #ccaeff4d;
      color: #c7a9ffb2;
      font-size: $fontSize10;
      margin: px2rem(12) auto;
      justify-content: flex-start;
      width: px2rem(314);
    }
    .award-wrap {
      justify-content: center;
      flex-direction: row;
      .award-item {
        align-items: center;
        flex-direction: column;
        transform: scale(0.7);
        opacity: 0.6;
        min-height: px2rem(64);
        img {
          width: px2rem(80);
        }
        .worth-wrap {
          color: #ffda6d;
          font-size: $fontSize15;
          font-weight: 700;
          .icon-img {
            width: px2rem(15);
            height: px2rem(15);
          }
        }
        &:nth-child(2) {
          margin: 0 px2rem(24);
        }
      }
      .active-rank {
        transform: scale(1);
        transition: all 0.3s ease;
        opacity: 1;
      }
      .active-send-star {
        transform: scale(0.7);
        opacity: 1;
      }
    }
    .ranking-wrap {
      width: 100%;
      .ranking-top-wrap {
        position: relative;
        height: px2rem(214);
        .top-bg {
          position: absolute;
          background-position: center top;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          width: px2rem(110);
          height: px2rem(161);
          z-index: 100;
          top: px2rem(55);
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: center;
          padding-top: px2rem(20);
          color: $white;
          font-size: $fontSize11;
          &:first-child {
            padding-top: px2rem(40);
            width: px2rem(217);
            height: px2rem(252);
            top: px2rem(-50);
            left: 50%;
            z-index: 10;
            transform: translateX(-50%);
            background-image: url("@/assets/images/ranking/weekly-star/top1.png");
          }
          &:nth-child(2) {
            left: 0;
            background-image: url("@/assets/images/ranking/weekly-star/top2.png");
          }
          &:last-child {
            right: 0;
            background-image: url("@/assets/images/ranking/weekly-star/top3.png");
          }
          .avatar-wrap {
            width: px2rem(70);
            height: px2rem(70);
            position: relative;
            font-weight: $fontWeightBold;
            display: flex;
            align-items: center;
            justify-content: center;
            .border-img {
              position: absolute;
              top: 0;
              left: 0;
              width: px2rem(70);
              height: px2rem(70);
              z-index: 100;
            }
            .avatar-img {
              z-index: 10;
              border-radius: 50%;
              width: px2rem(50);
              height: px2rem(50);
              object-fit: cover;
            }
          }
          .name {
            margin-top: px2rem(-5);
            width: px2rem(95);
            text-align: center;
          }
          .award-gift-wrap {
            font-weight: $fontWeightBold;
            text-shadow: 0px 0px 6px #dd00ff;
            & > img {
              width: px2rem(18);
              height: px2rem(18);
            }
          }
        }
      }
      .ranking-list {
        padding: px2rem(12) 0;
        .list-item {
          width: 100%;
          flex-direction: row;
          padding: px2rem(10) px2rem(12);
          color: $white;
          font-size: $fontSize17;
          flex-wrap: nowrap;
          .index {
            min-width: px2rem(24);
            text-align: center;
            font-family: "GilroyItalic";
          }
          .avatar-wrap {
            width: px2rem(36);
            height: px2rem(36);
            position: relative;
            .border-img {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) scale(1.2);
              width: px2rem(36);
              height: px2rem(36);

              z-index: 100;
            }
            .avatar-img {
              width: px2rem(36);
              height: px2rem(36);
              border-radius: 50%;
              object-fit: cover;
            }
          }
          .name {
            font-size: $fontSize13;
            margin: 0 px2rem(12);
            flex: 1;
            word-break: break-word;
          }
          .item-gift-wrap {
            align-items: flex-start;
            word-break: break-all;
            text-shadow: 0px 0px 6px #dd00ff;
            flex-wrap: nowrap;
            font-weight: $fontWeightBold;
            & > img {
              width: px2rem(24);
              height: px2rem(24);
            }
          }
        }
      }
    }
    .next-week {
      width: px2rem(350);
      height: px2rem(90);
      padding: px2rem(15) 0;
      margin: px2rem(8) auto px2rem(22);
      background: url("@/assets/images/ranking/weekly-star/next-week-bg.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      justify-content: center;
      .left {
        flex-direction: column;
        align-items: flex-start;
        img {
          height: px2rem(42);
        }
        .time {
          font-size: px2rem(10);
          color: #ffffff70;
        }
      }
      .right {
        .right-item {
          flex-direction: column;
          align-items: center;
          border-radius: px2rem(8);
          border: 0.5px solid rgba(255, 255, 255, 0.2);
          background: rgba(0, 0, 0, 0.24);
          width: px2rem(62);
          height: px2rem(62);
          margin: 0 px2rem(2);
          flex-wrap: nowrap;
          .gift-url {
            width: px2rem(45);
            height: px2rem(45);
          }
          .bottom {
            img {
              width: px2rem(12);
              height: px2rem(12);
            }
            span {
              color: #ffda6d;
              font-size: px2rem(12);
            }
          }
        }
      }
    }
  }
  footer {
    width: 100%;
    padding: 0 px2rem(16);
    height: px2rem(88);
    box-shadow: 0px 0px 30px 0px #5d40b1 inset;
    border-top-left-radius: px2rem(16);
    border-top-right-radius: px2rem(16);
    display: flex;
    align-items: center;
    background-color: #20113c;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    .list-item {
      width: 100%;
      flex-direction: row;
      padding: px2rem(10) px2rem(12);
      color: $white;
      font-size: $fontSize17;
      flex-wrap: nowrap;
      .index {
        min-width: px2rem(24);
        text-align: center;
        font-family: "GilroyItalic";
      }
      .avatar-wrap {
        width: px2rem(36);
        height: px2rem(36);
        position: relative;
        .border-img {
          position: absolute;
          top: 50%;
          left: 50%;
          width: px2rem(36);
          height: px2rem(36);
          left: 50%;
          transform: translate(-50%, -50%) scale(1.2);
          z-index: 100;
          object-fit: cover;
        }
        .avatar-img {
          z-index: 10;
          width: px2rem(36);
          height: px2rem(36);
          border-radius: 50%;
        }
      }
      .name {
        font-size: $fontSize13;
        margin: 0 px2rem(12);
        flex: 1;
        word-break: break-word;
      }
      .item-gift-wrap {
        align-items: flex-start;
        word-break: break-all;
        text-shadow: 0px 0px 6px #dd00ff;
        flex-wrap: nowrap;
        font-weight: $fontWeightBold;
        & > img {
          width: px2rem(24);
          height: px2rem(24);
        }
      }
    }
  }
}
</style>
