<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9091_18200)">
<g filter="url(#filter0_iii_9091_18200)">
<circle cx="9.99995" cy="10" r="9.28571" fill="url(#paint0_radial_9091_18200)"/>
<circle cx="9.99995" cy="10" r="9.28571" fill="url(#paint1_radial_9091_18200)"/>
</g>
<g filter="url(#filter1_di_9091_18200)">
<circle cx="10.0004" cy="10.0005" r="7.14286" fill="url(#paint2_radial_9091_18200)"/>
<circle cx="10.0004" cy="10.0005" r="7.14286" fill="url(#paint3_radial_9091_18200)"/>
<circle cx="10.0004" cy="10.0005" r="7.14286" fill="url(#paint4_radial_9091_18200)"/>
</g>
<g filter="url(#filter2_di_9091_18200)">
<path d="M13.757 9.17938C13.757 11.3898 11.9906 13.1582 10.0035 13.1582C8.01634 13.1582 6.25 11.3898 6.25 9.17939C6.25 6.90607 8.73047 6.45638 9.96801 8.02812C9.98566 8.05053 10.0207 8.04998 10.0377 8.02711C11.2513 6.39819 13.757 6.95087 13.757 9.17938Z" fill="url(#paint5_linear_9091_18200)" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_iii_9091_18200" x="0.714233" y="0.219056" width="18.5714" height="19.5619" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.825397"/>
<feGaussianBlur stdDeviation="0.247619"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.945098 0 0 0 0 0.564706 0 0 0 0 0.0352941 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9091_18200"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.825397"/>
<feGaussianBlur stdDeviation="0.247619"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_9091_18200" result="effect2_innerShadow_9091_18200"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.412698"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.868 0 0 0 0 0.4 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_9091_18200" result="effect3_innerShadow_9091_18200"/>
</filter>
<filter id="filter1_di_9091_18200" x="2.85754" y="2.8576" width="14.2857" height="15.0244" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.61561"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.76 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9091_18200"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9091_18200" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.738732"/>
<feGaussianBlur stdDeviation="0.923415"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.782479 0 0 0 0 0.443533 0 0 0 0 0.0456394 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_9091_18200"/>
</filter>
<filter id="filter2_di_9091_18200" x="3.88354" y="5.9035" width="12.2399" height="10.8044" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.18323"/>
<feGaussianBlur stdDeviation="1.18323"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.982078 0 0 0 0 0.62102 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9091_18200"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9091_18200" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.591616"/>
<feGaussianBlur stdDeviation="1.77485"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_9091_18200"/>
</filter>
<radialGradient id="paint0_radial_9091_18200" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(9.99995 10) rotate(90) scale(9.28571)">
<stop offset="0.630208" stop-color="#FFE27C"/>
<stop offset="1" stop-color="#F9D46E"/>
</radialGradient>
<radialGradient id="paint1_radial_9091_18200" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(4.38172 2.12992) rotate(51.0278) scale(8.93281)">
<stop stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial_9091_18200" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.0004 6.68334) rotate(89.9994) scale(9.96069)">
<stop stop-color="#FFCA6E"/>
<stop offset="1" stop-color="#FFAC20"/>
</radialGradient>
<radialGradient id="paint3_radial_9091_18200" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(6.4118 3.8049) rotate(54.0903) scale(0.661421 2.21688)">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint4_radial_9091_18200" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(13.6901 15.997) rotate(61.8237) scale(3.6929 11.8051)">
<stop stop-color="white" stop-opacity="0.69"/>
<stop offset="0.280503" stop-color="white" stop-opacity="0.28"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint5_linear_9091_18200" x1="10.0035" y1="7.08673" x2="10.0035" y2="13.1582" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFEFC5" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0.46"/>
</linearGradient>
<clipPath id="clip0_9091_18200">
<rect width="20" height="20" fill="white"/>
</clipPath>
</defs>
</svg>
