<template>
  <div class="app-container flex">
    <button
      class="flex"
      @click="downloadOrAdjust"
      v-track:click
      trace-key="fb_share_h5_click"
      :track-params="JSON.stringify(pointData('PH_落地页'))"
    >
      <img
        :src="
          proxy.$languageFile === 'ar'
            ? 'https://siya-packet-kodo-acc.siyachat.com/landing-page/logo-ar.png'
            : 'https://siya-packet-kodo-acc.siyachat.com/landing-page/logo.png'
        "
        alt=""
        class="logo"
      />
      <canvas class="download-btn" id="pagCanvas"></canvas>
    </button>
    <main
      v-track:exposure
      trace-key="fb_share_h5_expo"
      :track-params="JSON.stringify(pointData('PH_落地页'))"
    >
      <img
        src="https://siya-packet-kodo-acc.siyachat.com/landing-page/title.png"
        @click="downloadOrAdjust"
        v-track:click
        trace-key="fb_share_h5_click"
        :track-params="JSON.stringify(pointData('PH_落地页'))"
        alt=""
        class="title"
      />
      <div class="video-wrap">
        <video
          src="https://siya-packet-kodo-acc.siyachat.com/landing-page/video.mp4"
          loop
          poster="https://siya-packet-kodo-acc.siyachat.com/landing-page/poster.png"
          preload
          autoplay
          :muted="true"
          id="video"
          playsinline
        ></video>
      </div>
      <div
        class="picture-wrap"
        @click="downloadOrAdjust"
        v-track:click
        trace-key="fb_share_h5_click"
        :track-params="JSON.stringify(pointData('PH_落地页'))"
      >
        <img
          :src="`https://siya-packet-kodo-acc.siyachat.com/landing-page/picture-${i}.png`"
          alt=""
          v-for="i in 6"
          :key="i"
        />
      </div>
    </main>
  </div>
  <!-- <noscript>
    <img
      height="1"
      width="1"
      style="display: none"
      src="https://www.facebook.com/tr?id=1602733770433444&ev=PageView&noscript=1"
    />
  </noscript> -->
</template>

<script setup>
import { onMounted, nextTick, getCurrentInstance } from "vue";
import { i18n } from "@/i18n/index.js";
import { AnimationPAGInitLocal } from "@/utils/util.js";
import { downloadOrAdjust, pointData } from "./utils.js";

const t = i18n.global.t;
const { proxy } = getCurrentInstance();
let animationObj = null;

const playAnimation = async () => {
  animationObj = await AnimationPAGInitLocal("button.pag", pagCanvas);
  animationObj.setRepeatCount(0);
  await animationObj.play();
};

onMounted(() => {
  playAnimation();
  nextTick(() => {
    const video = document.getElementById("video");
    video.play().catch((error) => {
      console.error("播放失败:", error);
    });
    video.addEventListener("click", () => {
      video.setAttribute("controls", "");
    });
  });
});

// !(function (f, b, e, v, n, t, s) {
//   if (f.fbq) return;
//   n = f.fbq = function () {
//     n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
//   };
//   if (!f._fbq) f._fbq = n;
//   n.push = n;
//   n.loaded = !0;
//   n.version = "2.0";
//   n.queue = [];
//   t = b.createElement(e);
//   t.async = !0;
//   t.src = v;
//   s = b.getElementsByTagName(e)[0];
//   s.parentNode.insertBefore(t, s);
// })(
//   window,
//   document,
//   "script",
//   "https://connect.facebook.net/en_US/fbevents.js"
// );
// fbq("init", "1602733770433444");
// fbq("track", "PageView");
</script>

<style lang="scss" scoped>
@import "@/assets/scss/common.scss";

.app-container {
  flex-direction: column;
  background: #5f56ed;
  padding-top: px2rem(64);
  position: relative;
  button {
    width: 100%;
    background: none;
    height: px2rem(64);
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 0 px2rem(12);
    position: fixed;
    top: 0;
    left: 0;
    flex-wrap: nowrap;
    z-index: 101;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: px2rem(64);
      opacity: 0.5;
      z-index: -1;
      background: #ffffff52;
    }
    .logo {
      height: px2rem(48);
    }
    .download-btn {
      height: 100%;
    }
  }
  main {
    width: 100%;
    padding: px2rem(24) px2rem(14);
    .title {
      width: 100%;
      margin-bottom: px2rem(14);
    }
    .video-wrap {
      width: 100%;
      border-radius: px2rem(30);
      overflow: hidden;
      background: #837bff;
      margin: 0 0 px2rem(40);
      min-height: px2rem(610);
      border: px2rem(4) solid #837bff;
      display: flex;
      position: relative;
      video {
        width: 100%;
      }
    }
    .picture-wrap {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      img {
        height: px2rem(230);
        width: auto;
        margin-bottom: px2rem(16);
      }
    }
  }
}
button {
  /* 移除所有浏览器默认样式 */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  /* 移除默认边框和背景 */
  border: none;
  background: none;

  /* 移除内边距和外边距 */
  padding: 0;
  margin: 0;

  /* 移除焦点时的轮廓线 */
  outline: none;

  /* 使文本样式继承自父元素 */
  color: inherit;
  font: inherit;
  text-align: inherit;
  text-decoration: none;

  /* 重置光标样式 */
  cursor: pointer;

  /* 其他可能需要重置的属性 */
  letter-spacing: inherit;
  word-spacing: inherit;
  text-transform: inherit;
}
</style>
